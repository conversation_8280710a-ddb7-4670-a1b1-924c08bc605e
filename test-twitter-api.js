#!/usr/bin/env node

/**
 * Test script to check if Twitter API is working
 */

const TWITTER_API_BASE_URL = 'https://api.twitterapi.io';
const TWITTER_API_KEY = '96e428bef7fa4f078dab3b6c9678b774'; // From your .env file

async function testTwitterAPI() {
  console.log('🔄 Testing Twitter API connection...');
  
  try {
    // Test with a well-known Twitter account
    const testUsername = 'elonmusk';
    const url = `${TWITTER_API_BASE_URL}/twitter/user/info?username=${testUsername}`;
    
    console.log(`📡 Making request to: ${url}`);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'X-API-Key': TWITTER_API_KEY,
        'Content-Type': 'application/json',
      },
    });

    console.log(`📊 Response status: ${response.status}`);
    console.log(`📊 Response headers:`, Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ Twitter API error (${response.status}): ${errorText}`);
      return false;
    }

    const data = await response.json();
    console.log('✅ Twitter API success!');
    console.log('📄 Response data:', JSON.stringify(data, null, 2));
    return true;
    
  } catch (error) {
    console.error('❌ Twitter API test failed:', error);
    return false;
  }
}

// Run the test
testTwitterAPI().then(success => {
  if (success) {
    console.log('🎉 Twitter API is working correctly!');
    process.exit(0);
  } else {
    console.log('💥 Twitter API test failed!');
    process.exit(1);
  }
});
