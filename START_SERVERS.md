# 🚀 How to Start BuddyChip Servers

## Quick Start (Recommended)

Run both servers at once:
```bash
pnpm dev
```

This will start:
- **Backend Server** on `http://localhost:3000`
- **Frontend Web App** on `http://localhost:3001`

## Manual Start (Alternative)

If you prefer to run them separately:

### Terminal 1 - Backend Server
```bash
pnpm dev:server
```
Should show: `Ready on http://localhost:3000`

### Terminal 2 - Frontend Web App  
```bash
pnpm dev:web
```
Should show: `Ready on http://localhost:3001`

## Verify Everything is Working

1. **Check Backend**: Open `http://localhost:3000` - should see a page
2. **Check Frontend**: Open `http://localhost:3001` - should see your app
3. **Test Account Addition**: Try adding a Twitter account like "elonmusk"

## What I Fixed

✅ **Updated tRPC URL**: Now correctly points to `http://localhost:3000/trpc`
✅ **Enhanced CORS Headers**: Allows requests from `http://localhost:3001`
✅ **Added Request Logging**: You'll see tRPC requests in the server console
✅ **Added Comprehensive Error Logging**: Detailed logs throughout the account addition flow

## Expected Console Output

### Backend Server Console:
```
🔄 tRPC request received: { method: 'POST', url: '...', origin: 'http://localhost:3001' }
🚀 Starting account addition process for: elonmusk
👤 User ID: Admin
🔍 Checking feature limits for MONITORED_ACCOUNTS...
📊 Feature check result: { allowed: true, currentUsage: 0, limit: 5 }
...
```

### Frontend Browser Console:
```
🚀 Frontend: Starting account addition for: elonmusk
📡 Frontend: Making tRPC call to add account...
✅ Frontend: Account added successfully: { success: true, account: {...} }
```

## Troubleshooting

### If you still get CORS errors:
1. Make sure both servers are running
2. Check the URLs in browser address bar
3. Clear browser cache and try again

### If you get "User not found" errors:
1. Make sure you're logged in with Clerk
2. Check that your user exists in the database

### If you get Twitter API errors:
1. Check that the Twitter API key is valid
2. Try with a well-known account like "elonmusk" or "twitter"

## Next Steps

Once both servers are running:
1. Go to `http://localhost:3001`
2. Try adding an account
3. Watch both console outputs for detailed logs
4. Report back what you see!

The detailed logging will show us exactly where any remaining issues occur.
