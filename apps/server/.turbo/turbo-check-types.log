
> server@0.1.0 check-types /Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/server
> tsc --noEmit

src/lib/benji-agent.ts(8,20): error TS2305: Module '"./ai-providers"' has no exported member 'getModelByPlan'.
src/lib/user-service.ts(8,15): error TS2305: Module '"@prisma/client"' has no exported member 'User'.
src/lib/user-service.ts(8,21): error TS2305: Module '"@prisma/client"' has no exported member 'SubscriptionPlan'.
src/lib/user-service.ts(8,39): error TS2305: Module '"@prisma/client"' has no exported member 'PlanFeature'.
src/lib/user-service.ts(148,47): error TS7006: Parameter 'f' implicitly has an 'any' type.
src/routers/user.ts(9,27): error TS2305: Module '"@/lib/trpc"' has no exported member 'router'.
src/routers/user.ts(27,43): error TS2339: Property 'users' does not exist on type '() => Promise<ClerkClient>'.
src/routers/user.ts(57,44): error TS7006: Parameter 'f' implicitly has an 'any' type.
src/routers/user.ts(153,29): error TS2339: Property 'users' does not exist on type '() => Promise<ClerkClient>'.
 ELIFECYCLE  Command failed with exit code 2.
