{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/node_modules__pnpm_04490921._.js", "server/edge/chunks/[root-of-the-server]__0b2e03bd._.js", "server/edge/chunks/apps_server_edge-wrapper_ad858a04.js", "server/edge/chunks/_c3a1ca16._.js", "server/edge/chunks/68ae3_@clerk_shared_dist_e1abcbbc._.js", "server/edge/chunks/b3412_@clerk_backend_dist_334fdccb._.js", "server/edge/chunks/9aefa_@clerk_nextjs_dist_esm_33e6bb40._.js", "server/edge/chunks/node_modules__pnpm_fac582cc._.js", "server/edge/chunks/[root-of-the-server]__2cba886a._.js", "server/edge/chunks/apps_server_edge-wrapper_c6be066d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "mNHcatpDmk4MWgw+Eip1DI7GrGS0xdzMy8LqgS7YzK0=", "__NEXT_PREVIEW_MODE_ID": "c79ed7fe13efce0d75d2e3a1d432ef9b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "86121ed8ec1ad19d35f39f8c4aefa764949b78b28fb102c9e06fab899b1847c0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5c33b0f1f966497a607333852e85593176bf105e2550d18bdc0a10aafe29b908"}}}, "sortedMiddleware": ["/"], "functions": {}}