const CHUNK_PUBLIC_PATH = "server/app/trpc/[trpc]/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules__pnpm_ee005039._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__d39556bf._.js");
runtime.loadChunk("server/chunks/c4c98_next_a8e11e60._.js");
runtime.loadChunk("server/chunks/66bbb_@trpc_server_dist_bd371a73._.js");
runtime.loadChunk("server/chunks/ba893_zod_dist_esm_c2dd6950._.js");
runtime.loadChunk("server/chunks/f7c00_zod-to-json-schema_dist_esm_e8724186._.js");
runtime.loadChunk("server/chunks/b6274_ai_dist_index_mjs_ca86dbd7._.js");
runtime.loadChunk("server/chunks/da47e_@ai-sdk_openai_dist_index_mjs_df6791a1._.js");
runtime.loadChunk("server/chunks/68ae3_@clerk_shared_dist_40edc254._.js");
runtime.loadChunk("server/chunks/b3412_@clerk_backend_dist_e6791fc4._.js");
runtime.loadChunk("server/chunks/9aefa_@clerk_nextjs_dist_esm_106a6bd6._.js");
runtime.loadChunk("server/chunks/node_modules__pnpm_37ebaeb2._.js");
runtime.getOrInstantiateRuntimeModule("[project]/apps/server/.next-internal/server/app/trpc/[trpc]/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.27.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/apps/server/src/app/trpc/[trpc]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.27.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/apps/server/src/app/trpc/[trpc]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
