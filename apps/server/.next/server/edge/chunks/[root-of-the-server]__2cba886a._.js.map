{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/apps/server/src/middleware.ts"], "sourcesContent": ["import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'\nimport { NextResponse } from \"next/server\";\n\n// Define routes that should be protected (require authentication)\nconst isProtectedRoute = createRouteMatcher([\n  '/trpc(.*)',\n  '/api/webhooks/clerk(.*)', // Allow webhook to pass through\n])\n\nexport default clerkMiddleware(async (auth, req) => {\n  // Add CORS headers\n  const res = NextResponse.next()\n  res.headers.append('Access-Control-Allow-Credentials', \"true\")\n  res.headers.append('Access-Control-Allow-Origin', process.env.CORS_ORIGIN || \"*\")\n  res.headers.append('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')\n  res.headers.append(\n    'Access-Control-Allow-Headers',\n    'Content-Type, Authorization'\n  )\n\n  // For API routes, we'll let the individual route handlers deal with auth\n  // since tRPC has its own auth handling\n  if (isProtectedRoute(req)) {\n    // Just continue - let tRPC handle the auth\n  }\n\n  return res\n})\n\nexport const config = {\n  matcher: [\n    // Skip Next.js internals and all static files, unless found in search params\n    '/((?!_next|[^?]*\\\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',\n    // Always run for API routes\n    '/(api|trpc)(.*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AAAA;;;AAEA,kEAAkE;AAClE,MAAM,mBAAmB,CAAA,GAAA,kYAAA,CAAA,qBAAkB,AAAD,EAAE;IAC1C;IACA;CACD;uCAEc,CAAA,GAAA,qYAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,MAAM;IAC1C,mBAAmB;IACnB,MAAM,MAAM,sYAAA,CAAA,eAAY,CAAC,IAAI;IAC7B,IAAI,OAAO,CAAC,MAAM,CAAC,oCAAoC;IACvD,IAAI,OAAO,CAAC,MAAM,CAAC,+BAA+B,QAAQ,GAAG,CAAC,WAAW,IAAI;IAC7E,IAAI,OAAO,CAAC,MAAM,CAAC,gCAAgC;IACnD,IAAI,OAAO,CAAC,MAAM,CAChB,gCACA;IAGF,yEAAyE;IACzE,uCAAuC;IACvC,IAAI,iBAAiB,MAAM;IACzB,2CAA2C;IAC7C;IAEA,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;QACP,6EAA6E;QAC7E;QACA,4BAA4B;QAC5B;KACD;AACH"}}]}