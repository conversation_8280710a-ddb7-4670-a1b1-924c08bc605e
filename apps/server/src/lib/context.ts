import type { NextRequest } from "next/server";
import { auth, clerkClient } from "@clerk/nextjs/server";
import { prisma } from "./db-utils";
import { getOrCreateUser } from "./user-service";

export async function createContext(req: NextRequest) {
  console.log('🔍 Context: Creating context for request');
  console.log('🍪 Context: Request cookies:', req.headers.get('cookie'));
  console.log('🌐 Context: Request origin:', req.headers.get('origin'));
  console.log('🔑 Context: Authorization header:', req.headers.get('authorization'));

  const authResult = await auth();
  console.log('👤 Context: Auth result:', { userId: authResult.userId });

  // If user is authenticated, ensure they exist in our database using the existing service
  if (authResult.userId) {
    try {
      // Get user info from Clerk for user creation
      const clerkUser = await clerkClient.users.getUser(authResult.userId);
      await getOrCreateUser(authResult.userId, {
        email: clerkUser.emailAddresses[0]?.emailAddress,
        name: `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim() || 'User',
        avatar: clerkUser.imageUrl,
      });
      console.log('✅ Context: User ensured to exist in database');
    } catch (error) {
      console.error('❌ Context: Error ensuring user exists:', error);
      // Continue anyway - the user service will handle missing users gracefully
    }
  }

  return {
    userId: authResult.userId,
    prisma,
    req,
  };
}



export type Context = Awaited<ReturnType<typeof createContext>>;
