import type { NextRequest } from "next/server";
import { auth, clerkClient } from "@clerk/nextjs/server";
import { prisma } from "./db-utils";

export async function createContext(req: NextRequest) {
  console.log('🔍 Context: Creating context for request');
  console.log('🍪 Context: Request cookies:', req.headers.get('cookie'));
  console.log('🌐 Context: Request origin:', req.headers.get('origin'));
  console.log('🔑 Context: Authorization header:', req.headers.get('authorization'));

  const authResult = await auth();
  console.log('👤 Context: Auth result:', { userId: authResult.userId });

  // If user is authenticated, ensure they exist in our database
  if (authResult.userId) {
    await ensureUserExists(authResult.userId);
  }

  return {
    userId: authResult.userId,
    prisma,
    req,
  };
}

async function ensureUserExists(clerkUserId: string) {
  try {
    console.log('🔍 Context: Checking if user exists in database:', clerkUserId);

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { id: clerkUserId }
    });

    if (existingUser) {
      console.log('✅ Context: User already exists in database');
      return existingUser;
    }

    console.log('👤 Context: User not found, creating new user...');

    // Get user info from Clerk
    const clerkUser = await clerkClient.users.getUser(clerkUserId);
    console.log('📋 Context: Clerk user info:', {
      id: clerkUser.id,
      email: clerkUser.emailAddresses[0]?.emailAddress,
      firstName: clerkUser.firstName,
      lastName: clerkUser.lastName
    });

    // Get the default plan (reply-guy)
    const defaultPlan = await prisma.subscriptionPlan.findFirst({
      where: { name: 'reply-guy' }
    });

    if (!defaultPlan) {
      console.error('❌ Context: Default plan (reply-guy) not found');
      throw new Error('Default subscription plan not found');
    }

    // Create the user
    const newUser = await prisma.user.create({
      data: {
        id: clerkUserId,
        email: clerkUser.emailAddresses[0]?.emailAddress || '',
        name: `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim() || 'User',
        avatar: clerkUser.imageUrl,
        planId: defaultPlan.id,
        isAdmin: false, // New users are not admin by default
        createdAt: new Date(),
        updatedAt: new Date(),
        lastActiveAt: new Date(),
      }
    });

    console.log('✅ Context: New user created successfully:', {
      id: newUser.id,
      email: newUser.email,
      name: newUser.name,
      planId: newUser.planId
    });

    return newUser;

  } catch (error) {
    console.error('❌ Context: Error ensuring user exists:', error);
    // Don't throw here - let the request continue even if user creation fails
    // The downstream code will handle the missing user appropriately
  }
}

export type Context = Awaited<ReturnType<typeof createContext>>;
