import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'
import { NextResponse } from "next/server";

// Define routes that should be protected (require authentication)
const isProtectedRoute = createRouteMatcher([
  '/trpc(.*)',
  '/api/webhooks/clerk(.*)', // Allow webhook to pass through
])

export default clerkMiddleware(async (auth, req) => {
  // Add CORS headers
  const res = NextResponse.next()
  res.headers.append('Access-Control-Allow-Credentials', "true")
  res.headers.append('Access-Control-Allow-Origin', process.env.CORS_ORIGIN || "*")
  res.headers.append('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')
  res.headers.append(
    'Access-Control-Allow-Headers',
    'Content-Type, Authorization'
  )

  // For API routes, we'll let the individual route handlers deal with auth
  // since tRPC has its own auth handling
  if (isProtectedRoute(req)) {
    // Just continue - let tRPC handle the auth
  }

  return res
})

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
}
