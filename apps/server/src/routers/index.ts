import {
  publicProcedure,
  createTRPCRouter,
} from "../lib/trpc";
import { benjiRouter } from "./benji";
import { userRouter } from "./user";
import { mentionsRouter } from "./mentions";
import { accountsRouter } from "./accounts";
import { twitterRouter } from "./twitter";

export const appRouter = createTRPCRouter({
  healthCheck: publicProcedure.query(() => {
    return "OK";
  }),
  benji: benjiRouter,
  user: userRouter,
  mentions: mentionsRouter,
  accounts: accountsRouter,
  twitter: twitterRouter,
});

export type AppRouter = typeof appRouter;
