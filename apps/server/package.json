{"name": "server", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "biome check src/", "lint:fix": "biome check --write src/", "format": "biome format --write src/", "check-types": "tsc --noEmit", "db:push": "prisma db push --schema ./prisma/schema/schema.prisma", "db:studio": "prisma studio --schema ./prisma/schema/schema.prisma", "db:generate": "prisma generate --schema ./prisma/schema/schema.prisma", "db:migrate": "prisma migrate dev --schema ./prisma/schema/schema.prisma", "db:seed": "tsx ./prisma/seed.ts", "db:reset": "prisma migrate reset --schema ./prisma/schema/schema.prisma"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@clerk/backend": "^2.1.0", "@clerk/nextjs": "^6.22.0", "@openrouter/ai-sdk-provider": "^0.7.2", "@prisma/client": "^6.9.0", "@sentry/nextjs": "^9.30.0", "@sentry/node": "^9.30.0", "@trpc/client": "^11.4.2", "@trpc/server": "^11.4.2", "@upstash/ratelimit": "^2.0.5", "@vercel/kv": "^3.0.0", "ai": "^4.3.16", "dotenv": "^16.5.0", "next": "15.3.0", "svix": "^1.67.0", "zod": "^3.25.67"}, "trustedDependencies": ["supabase"], "devDependencies": {"@types/node": "^20", "@types/react": "^19", "prisma": "^6.9.0", "tsx": "^4.20.3", "typescript": "^5"}}