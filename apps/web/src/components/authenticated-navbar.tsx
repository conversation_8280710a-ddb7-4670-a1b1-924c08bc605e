'use client'

import { Bo<PERSON>, <PERSON> } from "lucide-react"
import ProfileDropdown from "./profile-dropdown"
import Link from "next/link"

interface AuthenticatedNavbarProps {
  currentPage?: 'dashboard' | 'profile' | 'reply-guy' | 'copium'
}

export default function AuthenticatedNavbar({ currentPage = 'dashboard' }: AuthenticatedNavbarProps) {
  return (
    <nav className="mb-8 p-4 border border-app-stroke rounded-lg bg-app-card shadow-md">
      <div className="container mx-auto flex justify-between items-center">
        <Link 
          href="/dashboard" 
          className="text-2xl font-semibold text-app-highlight border-b-2 border-app-highlight"
        >
          LOGO
        </Link>
        <div className="flex space-x-4 md:space-x-6 text-sm md:text-base">
          <Link 
            href="/reply-guy" 
            className={`transition-colors flex items-center ${
              currentPage === 'reply-guy' 
                ? 'text-app-main font-semibold' 
                : 'text-app-headline hover:text-app-main'
            }`}
          >
            <Bot className="w-4 h-4 mr-1 md:mr-2" /> REPLY GUY
          </Link>
          <Link 
            href="#" 
            className={`transition-colors flex items-center ${
              currentPage === 'copium' 
                ? 'text-app-main font-semibold' 
                : 'text-app-headline hover:text-app-main'
            }`}
          >
            <Search className="w-4 h-4 mr-1 md:mr-2" /> COPIUM
          </Link>
          <ProfileDropdown />
        </div>
      </div>
    </nav>
  )
}