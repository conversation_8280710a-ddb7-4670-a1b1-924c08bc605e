"use client"

import type React from "react"
import { useUser } from '@clerk/nextjs'
import { redirect } from 'next/navigation'
import { useState } from "react"
import { Lightbulb, LightbulbOff, Send, ExternalLink, Loader2, Plus } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import AuthenticatedNavbar from "@/components/authenticated-navbar"
import { trpc } from "@/utils/trpc"
import { toast } from "sonner"

// Type definitions for the dashboard data
type MonitoredAccount = {
  id: string
  handle: string
  status: "Monitored" | "Not Monitored"
  light: "Green" | "Red"
  totalMentions: number
  recentMentions: number
}

type DashboardMention = {
  id: string
  handle: string
  content: string
  timestamp: Date
  platform: "X" | "Twitter"
  authorName: string
  link: string
}

export default function DashboardPage() {
  const { user, isLoaded } = useUser()
  const [replyUrl, setReplyUrl] = useState("")
  const [generatedReply, setGeneratedReply] = useState("")
  const [showAddAccount, setShowAddAccount] = useState(false)
  const [newAccountHandle, setNewAccountHandle] = useState("")

  // tRPC queries
  const { data: monitoredAccounts, isLoading: accountsLoading, refetch: refetchAccounts } = trpc.accounts.getMonitored.useQuery()
  const { data: latestMentions, isLoading: mentionsLoading } = trpc.mentions.getLatest.useQuery()
  
  // tRPC mutations
  const extractTweetMutation = trpc.twitter.extractTweetContent.useMutation()
  const generateReplyMutation = trpc.benji.generateQuickReply.useMutation()
  const addAccountMutation = trpc.accounts.add.useMutation()

  if (!isLoaded) {
    return <div className="min-h-screen bg-app-background flex items-center justify-center">
      <div className="text-app-headline">Loading...</div>
    </div>
  }
  
  if (!user) {
    redirect('/sign-in')
  }

  const handleAddAccount = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newAccountHandle.trim()) return

    console.log('🚀 Frontend: Starting account addition for:', newAccountHandle.trim())

    try {
      console.log('📡 Frontend: Making tRPC call to add account...')
      const result = await addAccountMutation.mutateAsync({ handle: newAccountHandle.trim() })
      console.log('✅ Frontend: Account added successfully:', result)

      toast.success("Account added successfully!")
      setNewAccountHandle("")
      setShowAddAccount(false)
      refetchAccounts()
    } catch (error) {
      console.error('❌ Frontend: Error adding account:', error)

      // More detailed error logging
      if (error && typeof error === 'object') {
        console.error('🔴 Frontend: Error details:', {
          message: (error as any).message,
          code: (error as any).code,
          data: (error as any).data,
          shape: (error as any).shape
        })
      }

      // Show more specific error message if available
      const errorMessage = (error as any)?.message || "Failed to add account. Please try again."
      toast.error(errorMessage)
    }
  }

  const handleQuickReply = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!replyUrl.trim()) return

    try {
      // First extract tweet content
      const tweetData = await extractTweetMutation.mutateAsync({ url: replyUrl })
      
      // Then generate a reply
      const reply = await generateReplyMutation.mutateAsync({
        tweetUrl: replyUrl,
        tweetContent: tweetData.content
      })
      
      setGeneratedReply(reply.response)
      toast.success("Reply generated successfully!")
    } catch (error) {
      console.error('Error generating reply:', error)
      toast.error("Failed to generate reply. Please try again.")
    }
  }

  const formatTimeAgo = (date: Date) => {
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes} minutes ago`
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60)
      return `${hours} hour${hours === 1 ? '' : 's'} ago`
    } else {
      const days = Math.floor(diffInMinutes / 1440)
      return `${days} day${days === 1 ? '' : 's'} ago`
    }
  }

  return (
    <div className="min-h-screen p-4 md:p-8 font-sans bg-app-background text-app-headline">
      <header className="text-center mb-8">
        <h1 className="text-4xl md:text-5xl font-bold tracking-wider text-app-headline">BUDDYCHIP DASHBOARD</h1>
      </header>
      <AuthenticatedNavbar currentPage="dashboard" />

      <main className="space-y-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Monitored Accounts Section */}
          <Card className="bg-app-card border-app-stroke text-app-headline shadow-md">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-app-headline">MONITORED ACCOUNTS</h2>
                <Button
                  size="sm"
                  onClick={() => setShowAddAccount(!showAddAccount)}
                  className={`
                    relative overflow-hidden rounded-lg px-3 py-2 text-sm font-medium
                    transition-all duration-300 ease-in-out transform
                    ${showAddAccount 
                      ? 'bg-purple-600 text-white shadow-lg scale-105 border-purple-600' 
                      : 'bg-white text-gray-700 hover:bg-purple-600 hover:text-white border-gray-300 hover:border-purple-600'
                    }
                    hover:shadow-md hover:scale-105
                    focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2
                    group
                  `}
                >
                  <div className="flex items-center space-x-1">
                    <Plus className={`w-4 h-4 transition-transform duration-300 ${showAddAccount ? 'rotate-45' : 'group-hover:scale-110'}`} />
                    <span className="hidden sm:inline">Add</span>
                  </div>
                </Button>
              </div>
              
              {/* Add Account Form */}
              {showAddAccount && (
                <div className="mb-4 p-3 bg-app-background rounded-md border border-app-stroke/50">
                  <form onSubmit={handleAddAccount} className="flex space-x-2">
                    <Input
                      type="text"
                      placeholder="Enter Twitter handle (e.g., @elonmusk)"
                      value={newAccountHandle}
                      onChange={(e) => setNewAccountHandle(e.target.value)}
                      className="flex-1 bg-app-card border-app-stroke text-app-headline placeholder:text-app-headline placeholder:opacity-60 focus:border-app-main"
                    />
                    <Button
                      type="submit"
                      size="sm"
                      disabled={addAccountMutation.isPending || !newAccountHandle.trim()}
                      className="bg-app-main text-app-secondary hover:bg-app-highlight disabled:bg-app-main/50"
                    >
                      {addAccountMutation.isPending ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        "Add"
                      )}
                    </Button>
                  </form>
                </div>
              )}

              {accountsLoading ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="w-6 h-6 animate-spin text-app-main" />
                  <span className="ml-2 text-app-headline">Loading accounts...</span>
                </div>
              ) : (
                <ul className="space-y-3">
                  {monitoredAccounts?.length === 0 ? (
                    <li className="text-sm text-app-headline opacity-60 text-center py-4">
                      No monitored accounts yet. Add some to get started!
                    </li>
                  ) : (
                    monitoredAccounts?.map((account, index) => (
                      <li key={account.id} className="text-sm">
                        <div className="flex items-center justify-between">
                          <span className="text-app-headline">
                            {index + 1}. {account.handle} - {account.light} Light
                          </span>
                          {account.light === "Green" ? (
                            <Lightbulb className="w-5 h-5 text-app-main" />
                          ) : (
                            <LightbulbOff className="w-5 h-5 text-app-highlight" />
                          )}
                        </div>
                        <div
                          className={`text-xs ml-4 ${
                            account.status === "Monitored" ? "text-app-main" : "text-app-highlight"
                          }`}
                        >
                          ({account.status}) - {account.totalMentions} total mentions
                        </div>
                      </li>
                    ))
                  )}
                </ul>
              )}
            </CardContent>
          </Card>

          {/* Latest 10 Mentions Section */}
          <Card className="bg-app-card border-app-stroke text-app-headline shadow-md">
            <CardContent className="p-6">
              <h2 className="text-lg font-semibold mb-4 text-app-headline">LATEST 10 MENTIONS (Sorted by Date)</h2>
              {mentionsLoading ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="w-6 h-6 animate-spin text-app-main" />
                  <span className="ml-2 text-app-headline">Loading mentions...</span>
                </div>
              ) : (
                <div className="space-y-3 max-h-80 overflow-y-auto">
                  {latestMentions?.length === 0 ? (
                    <div className="text-sm text-app-headline opacity-60 text-center py-8">
                      No mentions found. Start monitoring accounts to see mentions here!
                    </div>
                  ) : (
                    latestMentions?.map((mention) => (
                      <div key={mention.id} className="p-3 bg-app-background rounded-md border border-app-stroke/50">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-app-headline">{mention.handle}</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-xs text-app-headline opacity-70">
                              {formatTimeAgo(new Date(mention.timestamp))}
                            </span>
                            <a 
                              href={mention.link} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="hover:text-app-main transition-colors"
                            >
                              <ExternalLink className="w-3 h-3 text-app-headline opacity-50 hover:opacity-100" />
                            </a>
                          </div>
                        </div>
                        <p className="text-xs text-app-headline opacity-90">{mention.content}</p>
                        {mention.bullishScore && (
                          <div className="mt-1">
                            <span className="text-xs text-app-main">
                              Bullish Score: {mention.bullishScore}/100
                            </span>
                          </div>
                        )}
                      </div>
                    ))
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Quick Reply Section */}
        <Card className="bg-app-card border-app-stroke text-app-headline shadow-md">
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-3 text-center text-app-headline">QUICK REPLY</h2>
            <p className="text-app-headline opacity-90 text-center leading-relaxed max-w-2xl mx-auto mb-6">
              Paste a link from x.com or twitter.com, we will get info about the specific post, and uses the{" "}
              <span className="font-semibold text-app-main">ANSWER-ENGINE AGENT</span> to craft a response.
            </p>

            <form onSubmit={handleQuickReply} className="max-w-2xl mx-auto space-y-4">
              <div className="flex space-x-2">
                <Input
                  type="url"
                  placeholder="Paste X.com or Twitter.com link here..."
                  value={replyUrl}
                  onChange={(e) => setReplyUrl(e.target.value)}
                  className="flex-1 bg-app-background border-app-stroke text-app-headline placeholder:text-app-headline placeholder:opacity-60 focus:border-app-main"
                />
                <Button
                  type="submit"
                  disabled={extractTweetMutation.isPending || generateReplyMutation.isPending || !replyUrl.trim()}
                  className="bg-app-main text-app-secondary hover:bg-app-highlight disabled:bg-app-main/50"
                >
                  {extractTweetMutation.isPending || generateReplyMutation.isPending ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Send className="w-4 h-4" />
                  )}
                </Button>
              </div>
            </form>

            {generatedReply && (
              <div className="mt-6 max-w-2xl mx-auto">
                <h3 className="text-sm font-semibold text-app-headline mb-2">Generated Reply:</h3>
                <div className="p-4 bg-app-background rounded-md border border-app-stroke">
                  <p className="text-app-headline text-sm">{generatedReply}</p>
                </div>
                <div className="flex justify-end mt-2 space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    className="border-app-stroke text-app-headline hover:bg-app-main hover:text-app-secondary"
                    onClick={() => setGeneratedReply("")}
                  >
                    Clear
                  </Button>
                  <Button size="sm" className="bg-app-main text-app-secondary hover:bg-app-highlight">
                    Use Reply
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  )
}