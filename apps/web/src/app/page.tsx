"use client"

import { useUser } from '@clerk/nextjs'
import { redirect } from 'next/navigation'
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Bot, Search, Twitter, Zap, Target, TrendingUp } from "lucide-react"
import Link from "next/link"

export default function LandingPage() {
  const { user, isLoaded } = useUser()
  
  // Redirect authenticated users to dashboard
  if (isLoaded && user) {
    redirect('/dashboard')
  }

  return (
    <div className="min-h-screen bg-app-background text-app-headline">
      {/* Header */}
      <header className="border-b border-app-stroke bg-app-card">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-app-highlight">BuddyChip</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/sign-in">
                <Button variant="outline" className="border-app-stroke text-app-headline hover:bg-app-main hover:text-app-secondary">
                  Sign In
                </Button>
              </Link>
              <Link href="/sign-up">
                <Button className="bg-app-main text-app-secondary hover:bg-app-highlight">
                  Get Started
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-app-headline mb-6">
            AI-Powered Social Media
            <span className="block text-app-main">Reply Assistant</span>
          </h1>
          <p className="text-xl text-app-headline opacity-80 mb-8 max-w-2xl mx-auto">
            Monitor mentions, generate intelligent responses, and grow your social presence with AI that understands your brand voice.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/sign-up">
              <Button size="lg" className="bg-app-main text-app-secondary hover:bg-app-highlight px-8 py-3 text-lg">
                Start Free Trial
              </Button>
            </Link>
            <Link href="/sign-in">
              <Button variant="outline" size="lg" className="border-app-stroke text-app-headline hover:bg-app-main hover:text-app-secondary px-8 py-3 text-lg">
                Sign In
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 bg-app-card">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-center text-app-headline mb-12">
            Everything you need to dominate social media
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="bg-app-background border-app-stroke">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-app-main/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Bot className="w-6 h-6 text-app-main" />
                </div>
                <h3 className="text-xl font-semibold text-app-headline mb-3">AI Reply Generation</h3>
                <p className="text-app-headline opacity-70">
                  Generate contextual, brand-appropriate replies to any mention or comment using advanced AI.
                </p>
              </CardContent>
            </Card>
            
            <Card className="bg-app-background border-app-stroke">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-app-main/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Search className="w-6 h-6 text-app-main" />
                </div>
                <h3 className="text-xl font-semibold text-app-headline mb-3">Smart Monitoring</h3>
                <p className="text-app-headline opacity-70">
                  Track mentions across X/Twitter automatically and never miss an opportunity to engage.
                </p>
              </CardContent>
            </Card>
            
            <Card className="bg-app-background border-app-stroke">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-app-main/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <TrendingUp className="w-6 h-6 text-app-main" />
                </div>
                <h3 className="text-xl font-semibold text-app-headline mb-3">Growth Analytics</h3>
                <p className="text-app-headline opacity-70">
                  Track your engagement metrics and see how AI-powered replies boost your social presence.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-app-headline mb-6">
            Ready to transform your social media?
          </h2>
          <p className="text-xl text-app-headline opacity-80 mb-8">
            Join thousands of creators and businesses using BuddyChip to scale their social presence.
          </p>
          <Link href="/sign-up">
            <Button size="lg" className="bg-app-main text-app-secondary hover:bg-app-highlight px-8 py-3 text-lg">
              Get Started Free
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-app-stroke bg-app-card py-8">
        <div className="max-w-6xl mx-auto px-4 text-center">
          <p className="text-app-headline opacity-70">
            © 2025 BuddyChip. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  )
}