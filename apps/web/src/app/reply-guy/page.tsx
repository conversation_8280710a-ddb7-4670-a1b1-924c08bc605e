"use client"
import { useState } from "react"
import {
  <PERSON>r,
  <PERSON><PERSON>,
  Search,
  MessageSquare,
  Zap,
  Trash2,
  ChevronDown,
  ChevronUp,
  Lightbulb,
  LightbulbOff,
  Loader2,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Textarea } from "@/components/ui/textarea"
import { trpc } from "@/utils/trpc"
import { toast } from "sonner"

// Type definitions
type ReplyGuyMention = {
  id: string
  handle: string
  date: string
  content: string
  platform: "X" | "Twitter"
  authorName: string
  link: string
  responses: Array<{
    id: string
    content: string
    model?: string | null
    confidence?: number | null
    rating?: number | null
    used: boolean
    createdAt: Date
  }>
}

type MonitoredAccount = {
  id: string
  handle: string
  status: "Monitored" | "Not Monitored"
  light: "Green" | "Red"
}

export default function ReplyGuyPage() {
  const [showMonitoredAccounts, setShowMonitoredAccounts] = useState(false)
  const [selectedResponse, setSelectedResponse] = useState<Record<string, string>>({})

  // tRPC queries
  const { data: mentions, isLoading: mentionsLoading, refetch: refetchMentions } = trpc.mentions.getAll.useQuery({ limit: 20 })
  const { data: monitoredAccounts, isLoading: accountsLoading } = trpc.accounts.getMonitored.useQuery()

  // tRPC mutations
  const generateResponseMutation = trpc.benji.generateMentionResponse.useMutation()
  const enhanceMentionMutation = trpc.mentions.enhance.useMutation()
  const deleteMentionMutation = trpc.mentions.delete.useMutation()

  const handleGenerateAIAnswer = async (mention: ReplyGuyMention) => {
    try {
      const response = await generateResponseMutation.mutateAsync({
        mentionId: mention.id,
        mentionContent: mention.content,
        authorInfo: {
          name: mention.authorName,
          handle: mention.handle
        }
      })
      
      // Refetch mentions to get the new response
      await refetchMentions()
      toast.success("AI response generated successfully!")
    } catch (error) {
      console.error('Error generating AI response:', error)
      toast.error("Failed to generate AI response. Please try again.")
    }
  }

  const handleEnhanceMention = async (mentionId: string) => {
    try {
      await enhanceMentionMutation.mutateAsync({ id: mentionId })
      await refetchMentions()
      toast.success("Mention enhanced successfully!")
    } catch (error) {
      console.error('Error enhancing mention:', error)
      toast.error("Failed to enhance mention. Please try again.")
    }
  }

  const handleDeleteMention = async (mentionId: string) => {
    try {
      await deleteMentionMutation.mutateAsync({ id: mentionId })
      await refetchMentions()
      toast.success("Mention deleted successfully!")
    } catch (error) {
      console.error('Error deleting mention:', error)
      toast.error("Failed to delete mention. Please try again.")
    }
  }

  const handleResponseChange = (mentionId: string, responseContent: string) => {
    setSelectedResponse(prev => ({
      ...prev,
      [mentionId]: responseContent
    }))
  }

  return (
    <div className="min-h-screen p-4 md:p-8 font-sans bg-app-background text-app-headline">
      <header className="text-center mb-8">
        <h1 className="text-4xl md:text-5xl font-bold tracking-wider text-app-headline">REPLY GUY</h1>
      </header>

      <nav className="mb-8 p-4 border border-app-stroke rounded-lg bg-app-card shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <a href="/" className="text-2xl font-semibold text-app-headline hover:text-app-main transition-colors">
            LOGO
          </a>
          <div className="flex space-x-4 md:space-x-6 text-sm md:text-base">
            <a
              href="/reply-guy"
              className="text-app-highlight font-semibold flex items-center border-b-2 border-app-highlight"
            >
              <Bot className="w-4 h-4 mr-1 md:mr-2" /> REPLY GUY
            </a>
            <a href="#" className="text-app-headline hover:text-app-main transition-colors flex items-center">
              <Search className="w-4 h-4 mr-1 md:mr-2" /> COPIUM
            </a>
            <a href="#" className="text-app-headline hover:text-app-main transition-colors flex items-center">
              <User className="w-4 h-4 mr-1 md:mr-2" /> PROFILE
            </a>
          </div>
        </div>
      </nav>

      <section className="mb-8">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="border-app-stroke bg-app-card hover:bg-app-main hover:text-app-secondary text-app-headline"
              onClick={() => setShowMonitoredAccounts(!showMonitoredAccounts)}
            >
              Monitored Accounts
              {showMonitoredAccounts ? (
                <ChevronUp className="ml-2 h-4 w-4" />
              ) : (
                <ChevronDown className="ml-2 h-4 w-4" />
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-64 bg-app-card border-app-stroke text-app-headline shadow-lg">
            <DropdownMenuLabel className="text-app-headline opacity-75">Currently Monitored</DropdownMenuLabel>
            <DropdownMenuSeparator className="bg-app-stroke opacity-50" />
            {accountsLoading ? (
              <DropdownMenuItem disabled className="flex justify-center items-center">
                <Loader2 className="w-4 h-4 animate-spin text-app-main" />
                <span className="ml-2">Loading...</span>
              </DropdownMenuItem>
            ) : monitoredAccounts?.length === 0 ? (
              <DropdownMenuItem disabled className="text-center opacity-60">
                No monitored accounts
              </DropdownMenuItem>
            ) : (
              monitoredAccounts?.map((account) => (
                <DropdownMenuItem
                  key={account.id}
                  className="flex justify-between items-center hover:bg-app-main hover:text-app-secondary focus:bg-app-main focus:text-app-secondary"
                >
                  <span>{account.handle}</span>
                  {account.light === "Green" ? (
                    <Lightbulb className="w-4 h-4 text-app-main" />
                  ) : (
                    <LightbulbOff className="w-4 h-4 text-app-highlight" />
                  )}
                </DropdownMenuItem>
              ))
            )}
            <DropdownMenuSeparator className="bg-app-stroke opacity-50" />
            <DropdownMenuItem className="hover:bg-app-main hover:text-app-secondary focus:bg-app-main focus:text-app-secondary">
              Manage Accounts...
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </section>

      <main className="space-y-6">
        {mentionsLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-8 h-8 animate-spin text-app-main" />
            <span className="ml-3 text-app-headline">Loading mentions...</span>
          </div>
        ) : mentions?.mentions?.length === 0 ? (
          <div className="text-center py-10 text-app-headline opacity-60">
            <p className="text-lg">No mentions to display.</p>
            <p>Check back later or adjust your monitored accounts.</p>
          </div>
        ) : (
          mentions?.mentions?.map((mention) => (
          <Card key={mention.id} className="bg-app-card border-app-stroke text-app-headline rounded-lg shadow-md">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg text-app-headline">{mention.handle}</CardTitle>
                  <p className="text-xs text-app-headline opacity-70">
                    {mention.date} via {mention.platform}
                  </p>
                </div>
                <div className="flex space-x-1 md:space-x-2">
                  <Button variant="ghost" size="sm" className="text-app-main hover:text-app-highlight px-2">
                    <MessageSquare className="w-4 h-4 mr-1" /> Reply
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`px-2 transition-colors ${
                      mention.responses?.length > 0
                        ? "text-app-highlight hover:text-app-main"
                        : "text-app-headline opacity-40 cursor-not-allowed"
                    }`}
                    onClick={() => mention.responses?.length > 0 && handleEnhanceMention(mention.id)}
                    disabled={enhanceMentionMutation.isPending || mention.responses?.length === 0}
                  >
                    {enhanceMentionMutation.isPending ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin mr-1" />
                        Enhancing...
                      </>
                    ) : (
                      <>
                        <Zap className="w-4 h-4 mr-1" /> Enhance
                      </>
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-app-highlight hover:text-app-main px-2"
                    onClick={() => handleDeleteMention(mention.id)}
                    disabled={deleteMentionMutation.isPending}
                  >
                    {deleteMentionMutation.isPending ? (
                      <Loader2 className="w-4 h-4 animate-spin mr-1" />
                    ) : (
                      <Trash2 className="w-4 h-4 mr-1" />
                    )}
                    Delete
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-app-headline opacity-90 leading-relaxed mb-4">{mention.content}</p>
              {mention.responses && mention.responses.length > 0 && (
                <div className="mt-4 space-y-3">
                  {mention.responses.map((response, index) => (
                    <div key={response.id} className="p-3 bg-app-background rounded-md border border-app-stroke">
                      <div className="flex items-center justify-between mb-2">
                        <p className="text-sm font-semibold text-app-main">
                          AI Generated Response {mention.responses.length > 1 ? `(${index + 1})` : ''}
                        </p>
                        {response.confidence && (
                          <span className="text-xs text-app-headline opacity-70">
                            Confidence: {Math.round(response.confidence * 100)}%
                          </span>
                        )}
                      </div>
                      <Textarea
                        value={selectedResponse[mention.id] || response.content}
                        onChange={(e) => handleResponseChange(mention.id, e.target.value)}
                        className="bg-app-card border-app-stroke text-app-headline text-sm min-h-[80px]"
                        rows={3}
                      />
                      <div className="flex justify-between items-center mt-2">
                        <div className="text-xs text-app-headline opacity-60">
                          {response.model && `Model: ${response.model}`}
                          {response.used && (
                            <span className="ml-2 text-app-main">• Used</span>
                          )}
                        </div>
                        <Button size="sm" className="bg-app-main text-app-secondary hover:bg-app-highlight">
                          Use this reply
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-end pt-3">
              <Button
                onClick={() => handleGenerateAIAnswer(mention)}
                disabled={generateResponseMutation.isPending}
                className="bg-app-main text-app-secondary hover:bg-app-highlight"
              >
                {generateResponseMutation.isPending ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Zap className="w-4 h-4 mr-2" /> Generate AI Answer
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
          ))
        )}
      </main>
    </div>
  )
}