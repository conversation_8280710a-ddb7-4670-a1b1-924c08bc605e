{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/utils/trpc.ts"], "sourcesContent": ["import { QueryCache, QueryClient } from '@tanstack/react-query';\nimport { createTR<PERSON><PERSON>lient, httpBatchLink } from '@trpc/client';\nimport { createTRPCReact } from '@trpc/react-query';\nimport type { AppRouter } from '../../../server/src/routers';\nimport { toast } from 'sonner';\n\nexport const queryClient = new QueryClient({\n  queryCache: new QueryCache({\n    onError: (error) => {\n      toast.error(error.message, {\n        action: {\n          label: \"retry\",\n          onClick: () => {\n            queryClient.invalidateQueries();\n          },\n        },\n      });\n    },\n  }),\n});\n\nexport const trpc = createTRPCReact<AppRouter>();\n\nexport const trpcClient = trpc.createClient({\n  links: [\n    httpBatchLink({\n      url: process.env.NODE_ENV === 'production'\n        ? '/trpc' // Use relative URL in production\n        : 'http://localhost:3000/trpc', // Full URL for development\n      headers() {\n        // Add auth headers if needed\n        return {};\n      },\n    }),\n  ],\n});\n\n"], "names": [], "mappings": ";;;;;AA0BW;AA1BX;AAAA;AACA;AAAA;AACA;AAAA;AAEA;;;;;AAEO,MAAM,cAAc,IAAI,yPAAA,CAAA,cAAW,CAAC;IACzC,YAAY,IAAI,wPAAA,CAAA,aAAU,CAAC;QACzB,SAAS,CAAC;YACR,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,EAAE;gBACzB,QAAQ;oBACN,OAAO;oBACP,SAAS;wBACP,YAAY,iBAAiB;oBAC/B;gBACF;YACF;QACF;IACF;AACF;AAEO,MAAM,OAAO,CAAA,GAAA,8XAAA,CAAA,kBAAe,AAAD;AAE3B,MAAM,aAAa,KAAK,YAAY,CAAC;IAC1C,OAAO;QACL,CAAA,GAAA,qUAAA,CAAA,gBAAa,AAAD,EAAE;YACZ,KAAK,6EAED;YACJ;gBACE,6BAA6B;gBAC7B,OAAO,CAAC;YACV;QACF;KACD;AACH", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/components/theme-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\n\nexport function ThemeProvider({\n  children,\n  ...props\n}: React.ComponentProps<typeof NextThemesProvider>) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKO,SAAS,cAAc,EAC5B,QAAQ,EACR,GAAG,OAC6C;IAChD,qBAAO,sYAAC,2RAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC;KALgB", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useTheme } from \"next-themes\";\nimport { Toaster as Son<PERSON>, type ToasterProps } from \"sonner\";\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme();\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  );\n};\n\nexport { Toaster };\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,2RAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,sYAAC,2QAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf;GAjBM;;QACyB,2RAAA,CAAA,WAAQ;;;KADjC", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/components/providers.tsx"], "sourcesContent": ["\"use client\";\n\nimport { QueryClientProvider } from \"@tanstack/react-query\";\nimport { ReactQueryDevtools } from \"@tanstack/react-query-devtools\";\nimport { queryClient, trpc, trpcClient } from \"@/utils/trpc\";\nimport { ThemeProvider } from \"./theme-provider\";\nimport { Toaster } from \"./ui/sonner\";\n\n\nexport default function Providers({\n  children\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <ThemeProvider\n      attribute=\"class\"\n      defaultTheme=\"system\"\n      enableSystem\n      disableTransitionOnChange\n    >\n      <trpc.Provider client={trpcClient} queryClient={queryClient}>\n        <QueryClientProvider client={queryClient}>\n          {children}\n          <ReactQueryDevtools />\n        </QueryClientProvider>\n      </trpc.Provider>\n      <Toaster richColors />\n    </ThemeProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AASe,SAAS,UAAU,EAChC,QAAQ,EAGT;IACC,qBACE,sYAAC,yJAAA,CAAA,gBAAa;QACZ,WAAU;QACV,cAAa;QACb,YAAY;QACZ,yBAAyB;;0BAEzB,sYAAC,sIAAA,CAAA,OAAI,CAAC,QAAQ;gBAAC,QAAQ,sIAAA,CAAA,aAAU;gBAAE,aAAa,sIAAA,CAAA,cAAW;0BACzD,cAAA,sYAAC,yRAAA,CAAA,sBAAmB;oBAAC,QAAQ,sIAAA,CAAA,cAAW;;wBACrC;sCACD,sYAAC,4WAAA,CAAA,qBAAkB;;;;;;;;;;;;;;;;0BAGvB,sYAAC,oJAAA,CAAA,UAAO;gBAAC,UAAU;;;;;;;;;;;;AAGzB;KArBwB", "debugId": null}}]}