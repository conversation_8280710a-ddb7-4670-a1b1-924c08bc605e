{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/components/ui/avatar.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,sYAAC,sXAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sYAAC,sXAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,sYAAC,sXAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,sYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,sYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,sYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,sYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,sYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,sYAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/components/ui/separator.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AACA;AAEA;;;;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,sYAAC,yXAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,sYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 384, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/components/profile-dropdown.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { useUser, useClerk } from '@clerk/nextjs'\nimport { User, Settings, LogOut, ChevronDown } from 'lucide-react'\n\nexport default function ProfileDropdown() {\n  const { user, isLoaded } = useUser()\n  const { signOut } = useClerk()\n  const [isOpen, setIsOpen] = useState(false)\n  const dropdownRef = useRef<HTMLDivElement>(null)\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n    }\n  }, [])\n\n  if (!isLoaded || !user) {\n    return (\n      <a href=\"/sign-in\" className=\"text-app-headline hover:text-app-main transition-colors flex items-center\">\n        <User className=\"w-4 h-4 mr-1 md:mr-2\" /> LOGIN\n      </a>\n    )\n  }\n\n  const handleSignOut = () => {\n    signOut()\n    setIsOpen(false)\n  }\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      {/* Profile Button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        onMouseEnter={() => setIsOpen(true)}\n        className=\"text-app-headline hover:text-app-main transition-colors flex items-center space-x-2 group\"\n      >\n        <div className=\"flex items-center space-x-2\">\n          <img \n            src={user.imageUrl} \n            alt=\"Profile\" \n            className=\"w-6 h-6 md:w-8 md:h-8 rounded-full border border-app-stroke\"\n          />\n          <span className=\"hidden md:block\">\n            {user.firstName || user.emailAddresses[0].emailAddress}\n          </span>\n          <ChevronDown className={`w-3 h-3 transition-transform ${isOpen ? 'rotate-180' : ''}`} />\n        </div>\n      </button>\n\n      {/* Dropdown Menu */}\n      {isOpen && (\n        <div \n          className=\"absolute right-0 mt-2 w-64 bg-app-card border border-app-stroke rounded-lg shadow-lg z-50\"\n          onMouseLeave={() => setIsOpen(false)}\n        >\n          {/* User Info Header */}\n          <div className=\"px-4 py-3 border-b border-app-stroke\">\n            <div className=\"flex items-center space-x-3\">\n              <img \n                src={user.imageUrl} \n                alt=\"Profile\" \n                className=\"w-10 h-10 rounded-full border border-app-stroke\"\n              />\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-app-headline truncate\">\n                  {user.firstName && user.lastName \n                    ? `${user.firstName} ${user.lastName}` \n                    : user.firstName || 'User'\n                  }\n                </p>\n                <p className=\"text-xs text-app-headline opacity-70 truncate\">\n                  {user.emailAddresses[0]?.emailAddress}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* Menu Items */}\n          <div className=\"py-2\">\n            <a\n              href=\"/profile\"\n              className=\"flex items-center px-4 py-2 text-sm text-app-headline hover:bg-app-background transition-colors\"\n              onClick={() => setIsOpen(false)}\n            >\n              <User className=\"w-4 h-4 mr-3\" />\n              View Profile\n            </a>\n            \n            <a\n              href=\"/dashboard\"\n              className=\"flex items-center px-4 py-2 text-sm text-app-headline hover:bg-app-background transition-colors\"\n              onClick={() => setIsOpen(false)}\n            >\n              <Settings className=\"w-4 h-4 mr-3\" />\n              Dashboard\n            </a>\n\n            <div className=\"border-t border-app-stroke my-1\"></div>\n            \n            <button\n              onClick={handleSignOut}\n              className=\"w-full flex items-center px-4 py-2 text-sm text-app-highlight hover:bg-app-background transition-colors\"\n            >\n              <LogOut className=\"w-4 h-4 mr-3\" />\n              Sign Out\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,0SAAA,CAAA,UAAO,AAAD;IACjC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD;IAC3B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,sWAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,cAAc,CAAA,GAAA,sWAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,uCAAuC;IACvC,CAAA,GAAA,sWAAA,CAAA,YAAS,AAAD;qCAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC9E,UAAU;gBACZ;YACF;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;6CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;oCAAG,EAAE;IAEL,IAAI,CAAC,YAAY,CAAC,MAAM;QACtB,qBACE,sYAAC;YAAE,MAAK;YAAW,WAAU;;8BAC3B,sYAAC,yRAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;gBAAyB;;;;;;;IAG/C;IAEA,MAAM,gBAAgB;QACpB;QACA,UAAU;IACZ;IAEA,qBACE,sYAAC;QAAI,WAAU;QAAW,KAAK;;0BAE7B,sYAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,cAAc,IAAM,UAAU;gBAC9B,WAAU;0BAEV,cAAA,sYAAC;oBAAI,WAAU;;sCACb,sYAAC;4BACC,KAAK,KAAK,QAAQ;4BAClB,KAAI;4BACJ,WAAU;;;;;;sCAEZ,sYAAC;4BAAK,WAAU;sCACb,KAAK,SAAS,IAAI,KAAK,cAAc,CAAC,EAAE,CAAC,YAAY;;;;;;sCAExD,sYAAC,2SAAA,CAAA,cAAW;4BAAC,WAAW,CAAC,6BAA6B,EAAE,SAAS,eAAe,IAAI;;;;;;;;;;;;;;;;;YAKvF,wBACC,sYAAC;gBACC,WAAU;gBACV,cAAc,IAAM,UAAU;;kCAG9B,sYAAC;wBAAI,WAAU;kCACb,cAAA,sYAAC;4BAAI,WAAU;;8CACb,sYAAC;oCACC,KAAK,KAAK,QAAQ;oCAClB,KAAI;oCACJ,WAAU;;;;;;8CAEZ,sYAAC;oCAAI,WAAU;;sDACb,sYAAC;4CAAE,WAAU;sDACV,KAAK,SAAS,IAAI,KAAK,QAAQ,GAC5B,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,KAAK,SAAS,IAAI;;;;;;sDAGxB,sYAAC;4CAAE,WAAU;sDACV,KAAK,cAAc,CAAC,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;kCAOjC,sYAAC;wBAAI,WAAU;;0CACb,sYAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,sYAAC,yRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAInC,sYAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,sYAAC,iSAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIvC,sYAAC;gCAAI,WAAU;;;;;;0CAEf,sYAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,sYAAC,iSAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;GApHwB;;QACK,0SAAA,CAAA,UAAO;QACd,0SAAA,CAAA,WAAQ;;;KAFN", "debugId": null}}, {"offset": {"line": 649, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/components/authenticated-navbar.tsx"], "sourcesContent": ["'use client'\n\nimport { Bo<PERSON>, <PERSON> } from \"lucide-react\"\nimport ProfileDropdown from \"./profile-dropdown\"\nimport Link from \"next/link\"\n\ninterface AuthenticatedNavbarProps {\n  currentPage?: 'dashboard' | 'profile' | 'reply-guy' | 'copium'\n}\n\nexport default function AuthenticatedNavbar({ currentPage = 'dashboard' }: AuthenticatedNavbarProps) {\n  return (\n    <nav className=\"mb-8 p-4 border border-app-stroke rounded-lg bg-app-card shadow-md\">\n      <div className=\"container mx-auto flex justify-between items-center\">\n        <Link \n          href=\"/dashboard\" \n          className=\"text-2xl font-semibold text-app-highlight border-b-2 border-app-highlight\"\n        >\n          LOGO\n        </Link>\n        <div className=\"flex space-x-4 md:space-x-6 text-sm md:text-base\">\n          <Link \n            href=\"/reply-guy\" \n            className={`transition-colors flex items-center ${\n              currentPage === 'reply-guy' \n                ? 'text-app-main font-semibold' \n                : 'text-app-headline hover:text-app-main'\n            }`}\n          >\n            <Bot className=\"w-4 h-4 mr-1 md:mr-2\" /> REPLY GUY\n          </Link>\n          <Link \n            href=\"#\" \n            className={`transition-colors flex items-center ${\n              currentPage === 'copium' \n                ? 'text-app-main font-semibold' \n                : 'text-app-headline hover:text-app-main'\n            }`}\n          >\n            <Search className=\"w-4 h-4 mr-1 md:mr-2\" /> COPIUM\n          </Link>\n          <ProfileDropdown />\n        </div>\n      </div>\n    </nav>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AAJA;;;;;AAUe,SAAS,oBAAoB,EAAE,cAAc,WAAW,EAA4B;IACjG,qBACE,sYAAC;QAAI,WAAU;kBACb,cAAA,sYAAC;YAAI,WAAU;;8BACb,sYAAC,wWAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;8BACX;;;;;;8BAGD,sYAAC;oBAAI,WAAU;;sCACb,sYAAC,wWAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAW,CAAC,oCAAoC,EAC9C,gBAAgB,cACZ,gCACA,yCACJ;;8CAEF,sYAAC,uRAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAyB;;;;;;;sCAE1C,sYAAC,wWAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAW,CAAC,oCAAoC,EAC9C,gBAAgB,WACZ,gCACA,yCACJ;;8CAEF,sYAAC,6RAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAyB;;;;;;;sCAE7C,sYAAC,2JAAA,CAAA,UAAe;;;;;;;;;;;;;;;;;;;;;;AAK1B;KApCwB", "debugId": null}}, {"offset": {"line": 751, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/app/profile/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON><PERSON><PERSON><PERSON> } from \"@/components/ui/card\"\nimport { User<PERSON>ro<PERSON>le, useUser, useClerk } from '@clerk/nextjs'\nimport { redirect } from 'next/navigation'\nimport { trpc } from '@/utils/trpc'\nimport { useState } from \"react\"\nimport Link from \"next/link\"\nimport {\n  User,\n  Shield,\n  CreditCard,\n  Mail,\n  Link2,\n  MoreHorizontal,\n  PlusCircle,\n  ChevronLeft,\n  UploadCloud,\n  Globe,\n  CheckCircle,\n  AlertTriangle,\n} from \"lucide-react\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { Button } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Input } from \"@/components/ui/input\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from \"@/components/ui/dropdown-menu\"\nimport AuthenticatedNavbar from \"@/components/authenticated-navbar\"\n\ntype ProfileSection = \"profile\" | \"security\" | \"billing\"\n\nexport default function UserProfilePage() {\n  const { user, isLoaded } = useUser()\n  const { signOut } = useClerk()\n  const { data: userProfile, isLoading: profileLoading, refetch } = trpc.user.getProfile.useQuery()\n  const { data: usage } = trpc.user.getUsage.useQuery()\n  const updateProfile = trpc.user.updateProfile.useMutation()\n  \n  const [activeSection, setActiveSection] = useState<ProfileSection>(\"profile\")\n  const [username, setUsername] = useState(\"\")\n  const [isEditingUsername, setIsEditingUsername] = useState(false)\n\n  if (!isLoaded || profileLoading) {\n    return (\n      <div className=\"min-h-screen bg-app-background flex items-center justify-center\">\n        <div className=\"text-app-headline\">Loading profile...</div>\n      </div>\n    )\n  }\n  \n  if (!user) {\n    redirect('/sign-in')\n  }\n\n  const handleUpdateUsername = async () => {\n    try {\n      await updateProfile.mutateAsync({ name: username })\n      await refetch()\n      setIsEditingUsername(false)\n    } catch (error) {\n      console.error('Failed to update username:', error)\n    }\n  }\n\n  const renderSectionContent = () => {\n    switch (activeSection) {\n      case \"profile\":\n        return <ProfileSectionContent />\n      case \"security\":\n        return <SecuritySectionContent />\n      case \"billing\":\n        return <BillingSectionContent />\n      default:\n        return null\n    }\n  }\n\n  const ProfileSectionContent = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"bg-app-card border-app-stroke shadow-sm\">\n        <CardHeader>\n          <CardTitle className=\"text-app-headline\">Profile</CardTitle>\n          <CardDescription className=\"text-app-headline/70\">\n            This is how others will see you on the site.\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex items-center space-x-4\">\n            <Avatar className=\"h-20 w-20 border-2 border-app-main\">\n              <AvatarImage src={user.imageUrl} alt={user.firstName || 'Profile'} />\n              <AvatarFallback className=\"bg-app-main text-app-secondary text-2xl\">\n                {user.firstName?.charAt(0) || user.emailAddresses[0]?.emailAddress.charAt(0) || 'U'}\n              </AvatarFallback>\n            </Avatar>\n            <div className=\"space-y-1\">\n              <h3 className=\"text-xl font-semibold text-app-headline\">\n                {userProfile?.user.name || user.firstName + ' ' + (user.lastName || '') || 'User'}\n              </h3>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"border-app-stroke text-app-headline hover:bg-app-main hover:text-app-secondary\"\n              >\n                <UploadCloud className=\"w-4 h-4 mr-2\" /> Change photo\n              </Button>\n            </div>\n          </div>\n          <Separator className=\"bg-app-stroke/20\" />\n          <div>\n            <label htmlFor=\"username\" className=\"block text-sm font-medium text-app-headline mb-1\">\n              Display Name\n            </label>\n            {isEditingUsername ? (\n              <div className=\"flex items-center space-x-2\">\n                <Input\n                  id=\"username\"\n                  type=\"text\"\n                  value={username}\n                  onChange={(e) => setUsername(e.target.value)}\n                  className=\"bg-app-background border-app-stroke text-app-headline placeholder:text-app-headline/60\"\n                />\n                <Button\n                  onClick={handleUpdateUsername}\n                  disabled={updateProfile.isPending}\n                  className=\"bg-app-main text-app-secondary hover:bg-app-highlight\"\n                >\n                  {updateProfile.isPending ? 'Saving...' : 'Save'}\n                </Button>\n                <Button\n                  variant=\"ghost\"\n                  onClick={() => setIsEditingUsername(false)}\n                  className=\"text-app-headline hover:bg-app-main/20\"\n                >\n                  Cancel\n                </Button>\n              </div>\n            ) : (\n              <div className=\"flex items-center justify-between p-2 rounded-md hover:bg-app-background\">\n                <span className=\"text-app-headline\">\n                  {userProfile?.user.name || user.firstName + ' ' + (user.lastName || '') || 'Not set'}\n                </span>\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => {\n                    setUsername(userProfile?.user.name || user.firstName + ' ' + (user.lastName || '') || '')\n                    setIsEditingUsername(true)\n                  }}\n                  className=\"text-app-main hover:text-app-highlight\"\n                >\n                  Edit\n                </Button>\n              </div>\n            )}\n          </div>\n        </CardContent>\n      </Card>\n\n      <Card className=\"bg-app-card border-app-stroke shadow-sm\">\n        <CardHeader>\n          <CardTitle className=\"text-app-headline\">Contact Information</CardTitle>\n          <CardDescription className=\"text-app-headline/70\">\n            Manage your email addresses and connected accounts.\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          {/* Email Addresses */}\n          <div>\n            <h4 className=\"font-medium text-app-headline mb-2\">Email addresses</h4>\n            <div className=\"space-y-2\">\n              {user.emailAddresses.map((emailItem, index) => (\n                <div key={index} className=\"flex items-center justify-between p-2 rounded-md hover:bg-app-background\">\n                  <div className=\"flex items-center space-x-3\">\n                    <Mail className=\"w-5 h-5 text-app-main\" />\n                    <div>\n                      <span className=\"text-app-headline\">{emailItem.emailAddress}</span>\n                      <div className=\"flex items-center space-x-2\">\n                        {user.primaryEmailAddressId === emailItem.id && (\n                          <Badge className=\"bg-app-main text-app-secondary\">Primary</Badge>\n                        )}\n                        <span className=\"flex items-center text-xs text-green-600\">\n                          <CheckCircle className=\"w-3 h-3 mr-1\" /> Verified\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          <Separator className=\"bg-app-stroke/20\" />\n\n          {/* Connected Accounts */}\n          <div>\n            <h4 className=\"font-medium text-app-headline mb-2\">Connected accounts</h4>\n            <div className=\"space-y-2\">\n              {user.externalAccounts.map((account, index) => (\n                <div key={index} className=\"flex items-center justify-between p-2 rounded-md hover:bg-app-background\">\n                  <div className=\"flex items-center space-x-3\">\n                    <Globe className=\"w-5 h-5 text-blue-500\" />\n                    <div>\n                      <span className=\"font-medium text-app-headline capitalize\">{account.provider}</span>\n                      <p className=\"text-xs text-app-headline/70\">{account.emailAddress}</p>\n                    </div>\n                  </div>\n                  <Button variant=\"ghost\" size=\"sm\" className=\"text-app-highlight hover:text-app-main\">\n                    Disconnect\n                  </Button>\n                </div>\n              ))}\n            </div>\n            <Button\n              variant=\"outline\"\n              className=\"w-full mt-4 border-app-stroke text-app-headline hover:bg-app-main hover:text-app-secondary\"\n            >\n              <Link2 className=\"w-4 h-4 mr-2\" /> Connect another account\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Subscription Details */}\n      {userProfile?.plan && (\n        <Card className=\"bg-app-card border-app-stroke shadow-sm\">\n          <CardHeader>\n            <CardTitle className=\"text-app-headline\">Subscription Plan</CardTitle>\n            <CardDescription className=\"text-app-headline/70\">\n              Your current plan and usage details.\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"bg-app-main/10 border border-app-main/20 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h3 className=\"font-semibold text-app-headline text-lg\">\n                    {userProfile.plan.displayName}\n                  </h3>\n                  <p className=\"text-app-headline opacity-70\">\n                    ${userProfile.plan.price}/month\n                  </p>\n                  <p className=\"text-sm text-app-headline opacity-70 mt-1\">\n                    {userProfile.plan.description}\n                  </p>\n                </div>\n                <Button className=\"bg-app-main text-app-secondary hover:bg-app-highlight\">\n                  Upgrade Plan\n                </Button>\n              </div>\n            </div>\n\n            {/* Usage Stats */}\n            {usage && (\n              <div className=\"space-y-3\">\n                <h4 className=\"font-medium text-app-headline\">Current Usage</h4>\n                {usage.map((item: any) => (\n                  <div key={item.feature} className=\"flex items-center justify-between p-3 bg-app-background rounded-md\">\n                    <div>\n                      <span className=\"text-app-headline font-medium\">\n                        {item.feature.replace('_', ' ').toLowerCase().replace(/\\b\\w/g, (l: string) => l.toUpperCase())}\n                      </span>\n                      <p className=\"text-sm text-app-headline opacity-70\">\n                        {item.currentUsage} / {item.limit === -1 ? '∞' : item.limit}\n                      </p>\n                    </div>\n                    {item.limit !== -1 && (\n                      <div className=\"w-20 h-2 bg-app-stroke rounded-full\">\n                        <div \n                          className=\"h-2 bg-app-main rounded-full\" \n                          style={{ width: `${Math.min((item.currentUsage / item.limit) * 100, 100)}%` }}\n                        />\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n\n  const SecuritySectionContent = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"bg-app-card border-app-stroke shadow-sm\">\n        <CardHeader>\n          <CardTitle className=\"text-app-headline\">Account Security</CardTitle>\n          <CardDescription className=\"text-app-headline/70\">\n            Manage your password, two-factor authentication, and security settings.\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"clerk-profile-container\">\n            <UserProfile \n              appearance={{\n                elements: {\n                  card: 'bg-transparent shadow-none border-0',\n                  navbar: 'hidden',\n                  pageScrollBox: 'p-0',\n                  page: 'bg-transparent',\n                  rootBox: 'w-full',\n                  profileSection: 'bg-app-background border border-app-stroke rounded-lg p-4 mb-4',\n                  profileSectionTitle: 'text-app-headline',\n                  profileSectionContent: 'text-app-headline',\n                  formButtonPrimary: 'bg-app-main hover:bg-app-highlight text-app-secondary',\n                  formFieldLabel: 'text-app-headline',\n                  formFieldInput: 'border-app-stroke focus:border-app-main bg-app-background',\n                  identityPreview: 'bg-app-background border border-app-stroke',\n                  accordionTriggerButton: 'text-app-headline hover:bg-app-background',\n                },\n              }}\n            />\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const BillingSectionContent = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"bg-app-card border-app-stroke shadow-sm\">\n        <CardHeader>\n          <CardTitle className=\"text-app-headline\">Current Plan</CardTitle>\n        </CardHeader>\n        <CardContent className=\"flex items-center justify-between\">\n          <div>\n            <p className=\"font-semibold text-app-headline\">{userProfile?.plan.displayName || 'Loading...'}</p>\n            <p className=\"text-app-headline/70\">\n              ${userProfile?.plan.price || '0'}/month • Next billing date coming soon\n            </p>\n          </div>\n          <Button className=\"bg-app-main text-app-secondary hover:bg-app-highlight\">\n            Manage Subscription\n          </Button>\n        </CardContent>\n      </Card>\n      \n      <Card className=\"bg-app-card border-app-stroke shadow-sm\">\n        <CardHeader>\n          <CardTitle className=\"text-app-headline\">Payment Methods</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"text-center py-8\">\n            <CreditCard className=\"w-12 h-12 text-app-headline/50 mx-auto mb-4\" />\n            <p className=\"text-app-headline/70 mb-4\">No payment methods added yet</p>\n            <Button\n              variant=\"outline\"\n              className=\"border-app-stroke text-app-headline hover:bg-app-main hover:text-app-secondary\"\n            >\n              <PlusCircle className=\"w-4 h-4 mr-2\" /> Add payment method\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Quick Actions */}\n      <Card className=\"bg-app-card border-app-stroke shadow-sm\">\n        <CardHeader>\n          <CardTitle className=\"text-app-headline\">Account Actions</CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-3\">\n          <Button\n            onClick={() => signOut()}\n            variant=\"outline\"\n            className=\"w-full border-app-highlight text-app-highlight hover:bg-app-highlight hover:text-app-secondary\"\n          >\n            Sign Out\n          </Button>\n          <Button\n            variant=\"outline\"\n            className=\"w-full border-app-stroke text-app-headline hover:bg-app-main hover:text-app-secondary\"\n          >\n            Export Account Data\n          </Button>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  return (\n    <div className=\"min-h-screen p-4 md:p-8 font-sans bg-app-background text-app-headline\">\n      <AuthenticatedNavbar currentPage=\"profile\" />\n      \n      <header className=\"mb-6 flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-app-headline\">Settings</h1>\n          <p className=\"text-app-headline opacity-70\">Manage your account and preferences</p>\n        </div>\n        <Button\n          variant=\"outline\"\n          asChild\n          className=\"border-app-stroke bg-app-card hover:bg-app-main hover:text-app-secondary text-app-headline\"\n        >\n          <Link href=\"/dashboard\">\n            <ChevronLeft className=\"w-4 h-4 mr-2\" />\n            Back to Dashboard\n          </Link>\n        </Button>\n      </header>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n        {/* Left Sidebar Navigation */}\n        <aside className=\"lg:col-span-1\">\n          <nav className=\"sticky top-8 bg-app-card p-4 rounded-lg border border-app-stroke shadow-md space-y-1\">\n            <Button\n              variant={activeSection === \"profile\" ? \"secondary\" : \"ghost\"}\n              onClick={() => setActiveSection(\"profile\")}\n              className={`w-full justify-start text-base p-3 ${activeSection === \"profile\" ? \"bg-app-main text-app-secondary hover:bg-app-highlight\" : \"text-app-headline hover:bg-app-main/20\"}`}\n            >\n              <User className=\"w-5 h-5 mr-3\" /> Profile\n            </Button>\n            <Button\n              variant={activeSection === \"security\" ? \"secondary\" : \"ghost\"}\n              onClick={() => setActiveSection(\"security\")}\n              className={`w-full justify-start text-base p-3 ${activeSection === \"security\" ? \"bg-app-main text-app-secondary hover:bg-app-highlight\" : \"text-app-headline hover:bg-app-main/20\"}`}\n            >\n              <Shield className=\"w-5 h-5 mr-3\" /> Security\n            </Button>\n            <Button\n              variant={activeSection === \"billing\" ? \"secondary\" : \"ghost\"}\n              onClick={() => setActiveSection(\"billing\")}\n              className={`w-full justify-start text-base p-3 ${activeSection === \"billing\" ? \"bg-app-main text-app-secondary hover:bg-app-highlight\" : \"text-app-headline hover:bg-app-main/20\"}`}\n            >\n              <CreditCard className=\"w-5 h-5 mr-3\" /> Billing\n            </Button>\n          </nav>\n        </aside>\n\n        {/* Main Content Area */}\n        <main className=\"lg:col-span-3\">{renderSectionContent()}</main>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AA7BA;;;;;;;;;;;;;;AAiCe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,0SAAA,CAAA,UAAO,AAAD;IACjC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD;IAC3B,MAAM,EAAE,MAAM,WAAW,EAAE,WAAW,cAAc,EAAE,OAAO,EAAE,GAAG,sIAAA,CAAA,OAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ;IAC/F,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,sIAAA,CAAA,OAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ;IACnD,MAAM,gBAAgB,sIAAA,CAAA,OAAI,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW;IAEzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,sWAAA,CAAA,WAAQ,AAAD,EAAkB;IACnE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sWAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,sWAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,IAAI,CAAC,YAAY,gBAAgB;QAC/B,qBACE,sYAAC;YAAI,WAAU;sBACb,cAAA,sYAAC;gBAAI,WAAU;0BAAoB;;;;;;;;;;;IAGzC;IAEA,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,8UAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,cAAc,WAAW,CAAC;gBAAE,MAAM;YAAS;YACjD,MAAM;YACN,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,uBAAuB;QAC3B,OAAQ;YACN,KAAK;gBACH,qBAAO,sYAAC;;;;;YACV,KAAK;gBACH,qBAAO,sYAAC;;;;;YACV,KAAK;gBACH,qBAAO,sYAAC;;;;;YACV;gBACE,OAAO;QACX;IACF;IAEA,MAAM,wBAAwB,kBAC5B,sYAAC;YAAI,WAAU;;8BACb,sYAAC,kJAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,sYAAC,kJAAA,CAAA,aAAU;;8CACT,sYAAC,kJAAA,CAAA,YAAS;oCAAC,WAAU;8CAAoB;;;;;;8CACzC,sYAAC,kJAAA,CAAA,kBAAe;oCAAC,WAAU;8CAAuB;;;;;;;;;;;;sCAIpD,sYAAC,kJAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,sYAAC;oCAAI,WAAU;;sDACb,sYAAC,oJAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,sYAAC,oJAAA,CAAA,cAAW;oDAAC,KAAK,KAAK,QAAQ;oDAAE,KAAK,KAAK,SAAS,IAAI;;;;;;8DACxD,sYAAC,oJAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB,KAAK,SAAS,EAAE,OAAO,MAAM,KAAK,cAAc,CAAC,EAAE,EAAE,aAAa,OAAO,MAAM;;;;;;;;;;;;sDAGpF,sYAAC;4CAAI,WAAU;;8DACb,sYAAC;oDAAG,WAAU;8DACX,aAAa,KAAK,QAAQ,KAAK,SAAS,GAAG,MAAM,CAAC,KAAK,QAAQ,IAAI,EAAE,KAAK;;;;;;8DAE7E,sYAAC,oJAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;;sEAEV,sYAAC,2SAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;8CAI9C,sYAAC,uJAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,sYAAC;;sDACC,sYAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAmD;;;;;;wCAGtF,kCACC,sYAAC;4CAAI,WAAU;;8DACb,sYAAC,mJAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,WAAU;;;;;;8DAEZ,sYAAC,oJAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU,cAAc,SAAS;oDACjC,WAAU;8DAET,cAAc,SAAS,GAAG,cAAc;;;;;;8DAE3C,sYAAC,oJAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS,IAAM,qBAAqB;oDACpC,WAAU;8DACX;;;;;;;;;;;iEAKH,sYAAC;4CAAI,WAAU;;8DACb,sYAAC;oDAAK,WAAU;8DACb,aAAa,KAAK,QAAQ,KAAK,SAAS,GAAG,MAAM,CAAC,KAAK,QAAQ,IAAI,EAAE,KAAK;;;;;;8DAE7E,sYAAC,oJAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;wDACP,YAAY,aAAa,KAAK,QAAQ,KAAK,SAAS,GAAG,MAAM,CAAC,KAAK,QAAQ,IAAI,EAAE,KAAK;wDACtF,qBAAqB;oDACvB;oDACA,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASX,sYAAC,kJAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,sYAAC,kJAAA,CAAA,aAAU;;8CACT,sYAAC,kJAAA,CAAA,YAAS;oCAAC,WAAU;8CAAoB;;;;;;8CACzC,sYAAC,kJAAA,CAAA,kBAAe;oCAAC,WAAU;8CAAuB;;;;;;;;;;;;sCAIpD,sYAAC,kJAAA,CAAA,cAAW;4BAAC,WAAU;;8CAErB,sYAAC;;sDACC,sYAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,sYAAC;4CAAI,WAAU;sDACZ,KAAK,cAAc,CAAC,GAAG,CAAC,CAAC,WAAW,sBACnC,sYAAC;oDAAgB,WAAU;8DACzB,cAAA,sYAAC;wDAAI,WAAU;;0EACb,sYAAC,yRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,sYAAC;;kFACC,sYAAC;wEAAK,WAAU;kFAAqB,UAAU,YAAY;;;;;;kFAC3D,sYAAC;wEAAI,WAAU;;4EACZ,KAAK,qBAAqB,KAAK,UAAU,EAAE,kBAC1C,sYAAC,mJAAA,CAAA,QAAK;gFAAC,WAAU;0FAAiC;;;;;;0FAEpD,sYAAC;gFAAK,WAAU;;kGACd,sYAAC,kTAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;mDAVxC;;;;;;;;;;;;;;;;8CAoBhB,sYAAC,uJAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CAGrB,sYAAC;;sDACC,sYAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,sYAAC;4CAAI,WAAU;sDACZ,KAAK,gBAAgB,CAAC,GAAG,CAAC,CAAC,SAAS,sBACnC,sYAAC;oDAAgB,WAAU;;sEACzB,sYAAC;4DAAI,WAAU;;8EACb,sYAAC,2RAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,sYAAC;;sFACC,sYAAC;4EAAK,WAAU;sFAA4C,QAAQ,QAAQ;;;;;;sFAC5E,sYAAC;4EAAE,WAAU;sFAAgC,QAAQ,YAAY;;;;;;;;;;;;;;;;;;sEAGrE,sYAAC,oJAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;4DAAK,WAAU;sEAAyC;;;;;;;mDAR7E;;;;;;;;;;sDAcd,sYAAC,oJAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;;8DAEV,sYAAC,+RAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;gBAOzC,aAAa,sBACZ,sYAAC,kJAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,sYAAC,kJAAA,CAAA,aAAU;;8CACT,sYAAC,kJAAA,CAAA,YAAS;oCAAC,WAAU;8CAAoB;;;;;;8CACzC,sYAAC,kJAAA,CAAA,kBAAe;oCAAC,WAAU;8CAAuB;;;;;;;;;;;;sCAIpD,sYAAC,kJAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,sYAAC;oCAAI,WAAU;8CACb,cAAA,sYAAC;wCAAI,WAAU;;0DACb,sYAAC;;kEACC,sYAAC;wDAAG,WAAU;kEACX,YAAY,IAAI,CAAC,WAAW;;;;;;kEAE/B,sYAAC;wDAAE,WAAU;;4DAA+B;4DACxC,YAAY,IAAI,CAAC,KAAK;4DAAC;;;;;;;kEAE3B,sYAAC;wDAAE,WAAU;kEACV,YAAY,IAAI,CAAC,WAAW;;;;;;;;;;;;0DAGjC,sYAAC,oJAAA,CAAA,SAAM;gDAAC,WAAU;0DAAwD;;;;;;;;;;;;;;;;;gCAO7E,uBACC,sYAAC;oCAAI,WAAU;;sDACb,sYAAC;4CAAG,WAAU;sDAAgC;;;;;;wCAC7C,MAAM,GAAG,CAAC,CAAC,qBACV,sYAAC;gDAAuB,WAAU;;kEAChC,sYAAC;;0EACC,sYAAC;gEAAK,WAAU;0EACb,KAAK,OAAO,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,IAAc,EAAE,WAAW;;;;;;0EAE7F,sYAAC;gEAAE,WAAU;;oEACV,KAAK,YAAY;oEAAC;oEAAI,KAAK,KAAK,KAAK,CAAC,IAAI,MAAM,KAAK,KAAK;;;;;;;;;;;;;oDAG9D,KAAK,KAAK,KAAK,CAAC,mBACf,sYAAC;wDAAI,WAAU;kEACb,cAAA,sYAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO,GAAG,KAAK,GAAG,CAAC,AAAC,KAAK,YAAY,GAAG,KAAK,KAAK,GAAI,KAAK,KAAK,CAAC,CAAC;4DAAC;;;;;;;;;;;;+CAb1E,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2BtC,MAAM,yBAAyB,kBAC7B,sYAAC;YAAI,WAAU;sBACb,cAAA,sYAAC,kJAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,sYAAC,kJAAA,CAAA,aAAU;;0CACT,sYAAC,kJAAA,CAAA,YAAS;gCAAC,WAAU;0CAAoB;;;;;;0CACzC,sYAAC,kJAAA,CAAA,kBAAe;gCAAC,WAAU;0CAAuB;;;;;;;;;;;;kCAIpD,sYAAC,kJAAA,CAAA,cAAW;kCACV,cAAA,sYAAC;4BAAI,WAAU;sCACb,cAAA,sYAAC,yZAAA,CAAA,cAAW;gCACV,YAAY;oCACV,UAAU;wCACR,MAAM;wCACN,QAAQ;wCACR,eAAe;wCACf,MAAM;wCACN,SAAS;wCACT,gBAAgB;wCAChB,qBAAqB;wCACrB,uBAAuB;wCACvB,mBAAmB;wCACnB,gBAAgB;wCAChB,gBAAgB;wCAChB,iBAAiB;wCACjB,wBAAwB;oCAC1B;gCACF;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQZ,MAAM,wBAAwB,kBAC5B,sYAAC;YAAI,WAAU;;8BACb,sYAAC,kJAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,sYAAC,kJAAA,CAAA,aAAU;sCACT,cAAA,sYAAC,kJAAA,CAAA,YAAS;gCAAC,WAAU;0CAAoB;;;;;;;;;;;sCAE3C,sYAAC,kJAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,sYAAC;;sDACC,sYAAC;4CAAE,WAAU;sDAAmC,aAAa,KAAK,eAAe;;;;;;sDACjF,sYAAC;4CAAE,WAAU;;gDAAuB;gDAChC,aAAa,KAAK,SAAS;gDAAI;;;;;;;;;;;;;8CAGrC,sYAAC,oJAAA,CAAA,SAAM;oCAAC,WAAU;8CAAwD;;;;;;;;;;;;;;;;;;8BAM9E,sYAAC,kJAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,sYAAC,kJAAA,CAAA,aAAU;sCACT,cAAA,sYAAC,kJAAA,CAAA,YAAS;gCAAC,WAAU;0CAAoB;;;;;;;;;;;sCAE3C,sYAAC,kJAAA,CAAA,cAAW;sCACV,cAAA,sYAAC;gCAAI,WAAU;;kDACb,sYAAC,ySAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,sYAAC;wCAAE,WAAU;kDAA4B;;;;;;kDACzC,sYAAC,oJAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;;0DAEV,sYAAC,ySAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;8BAO/C,sYAAC,kJAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,sYAAC,kJAAA,CAAA,aAAU;sCACT,cAAA,sYAAC,kJAAA,CAAA,YAAS;gCAAC,WAAU;0CAAoB;;;;;;;;;;;sCAE3C,sYAAC,kJAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,sYAAC,oJAAA,CAAA,SAAM;oCACL,SAAS,IAAM;oCACf,SAAQ;oCACR,WAAU;8CACX;;;;;;8CAGD,sYAAC,oJAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;IAQT,qBACE,sYAAC;QAAI,WAAU;;0BACb,sYAAC,+JAAA,CAAA,UAAmB;gBAAC,aAAY;;;;;;0BAEjC,sYAAC;gBAAO,WAAU;;kCAChB,sYAAC;;0CACC,sYAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,sYAAC;gCAAE,WAAU;0CAA+B;;;;;;;;;;;;kCAE9C,sYAAC,oJAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,OAAO;wBACP,WAAU;kCAEV,cAAA,sYAAC,wWAAA,CAAA,UAAI;4BAAC,MAAK;;8CACT,sYAAC,2SAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAM9C,sYAAC;gBAAI,WAAU;;kCAEb,sYAAC;wBAAM,WAAU;kCACf,cAAA,sYAAC;4BAAI,WAAU;;8CACb,sYAAC,oJAAA,CAAA,SAAM;oCACL,SAAS,kBAAkB,YAAY,cAAc;oCACrD,SAAS,IAAM,iBAAiB;oCAChC,WAAW,CAAC,mCAAmC,EAAE,kBAAkB,YAAY,0DAA0D,0CAA0C;;sDAEnL,sYAAC,yRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAEnC,sYAAC,oJAAA,CAAA,SAAM;oCACL,SAAS,kBAAkB,aAAa,cAAc;oCACtD,SAAS,IAAM,iBAAiB;oCAChC,WAAW,CAAC,mCAAmC,EAAE,kBAAkB,aAAa,0DAA0D,0CAA0C;;sDAEpL,sYAAC,6RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAErC,sYAAC,oJAAA,CAAA,SAAM;oCACL,SAAS,kBAAkB,YAAY,cAAc;oCACrD,SAAS,IAAM,iBAAiB;oCAChC,WAAW,CAAC,mCAAmC,EAAE,kBAAkB,YAAY,0DAA0D,0CAA0C;;sDAEnL,sYAAC,ySAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;kCAM7C,sYAAC;wBAAK,WAAU;kCAAiB;;;;;;;;;;;;;;;;;;AAIzC;GAnZwB;;QACK,0SAAA,CAAA,UAAO;QACd,0SAAA,CAAA,WAAQ;;;KAFN", "debugId": null}}]}