(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/.pnpm/@clerk+nextjs@6.22.0_next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0_42d1807e423217c6736c669ff09b1861/node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/9aefa_@clerk_nextjs_dist_esm_app-router_ae6acb1d._.js",
  "static/chunks/9aefa_@clerk_nextjs_dist_esm_app-router_client_keyless-creator-reader_a9065e3f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@clerk+nextjs@6.22.0_next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0_42d1807e423217c6736c669ff09b1861/node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@clerk+nextjs@6.22.0_next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0_42d1807e423217c6736c669ff09b1861/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/9aefa_@clerk_nextjs_dist_esm_app-router_f87ffdb2._.js",
  "static/chunks/9aefa_@clerk_nextjs_dist_esm_app-router_keyless-actions_8ba2526d.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@clerk+nextjs@6.22.0_next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0_42d1807e423217c6736c669ff09b1861/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@tanstack+query-devtools@5.80.0/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/HH7B3BHX.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/97880_@tanstack_query-devtools_build_64b00092._.js",
  "static/chunks/97880_@tanstack_query-devtools_build_DevtoolsComponent_HH7B3BHX_a9065e3f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@tanstack+query-devtools@5.80.0/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/HH7B3BHX.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@tanstack+query-devtools@5.80.0/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/JZI2RDCT.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/97880_@tanstack_query-devtools_build_2d949b41._.js",
  "static/chunks/97880_@tanstack_query-devtools_build_DevtoolsPanelComponent_JZI2RDCT_a9065e3f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@tanstack+query-devtools@5.80.0/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/JZI2RDCT.js [app-client] (ecmascript)");
    });
});
}}),
}]);