{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,sYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,sYAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,sYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,sYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,sYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,sYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/components/profile-dropdown.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { useUser, useClerk } from '@clerk/nextjs'\nimport { User, Settings, LogOut, ChevronDown } from 'lucide-react'\n\nexport default function ProfileDropdown() {\n  const { user, isLoaded } = useUser()\n  const { signOut } = useClerk()\n  const [isOpen, setIsOpen] = useState(false)\n  const dropdownRef = useRef<HTMLDivElement>(null)\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n    }\n  }, [])\n\n  if (!isLoaded || !user) {\n    return (\n      <a href=\"/sign-in\" className=\"text-app-headline hover:text-app-main transition-colors flex items-center\">\n        <User className=\"w-4 h-4 mr-1 md:mr-2\" /> LOGIN\n      </a>\n    )\n  }\n\n  const handleSignOut = () => {\n    signOut()\n    setIsOpen(false)\n  }\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      {/* Profile Button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        onMouseEnter={() => setIsOpen(true)}\n        className=\"text-app-headline hover:text-app-main transition-colors flex items-center space-x-2 group\"\n      >\n        <div className=\"flex items-center space-x-2\">\n          <img \n            src={user.imageUrl} \n            alt=\"Profile\" \n            className=\"w-6 h-6 md:w-8 md:h-8 rounded-full border border-app-stroke\"\n          />\n          <span className=\"hidden md:block\">\n            {user.firstName || user.emailAddresses[0].emailAddress}\n          </span>\n          <ChevronDown className={`w-3 h-3 transition-transform ${isOpen ? 'rotate-180' : ''}`} />\n        </div>\n      </button>\n\n      {/* Dropdown Menu */}\n      {isOpen && (\n        <div \n          className=\"absolute right-0 mt-2 w-64 bg-app-card border border-app-stroke rounded-lg shadow-lg z-50\"\n          onMouseLeave={() => setIsOpen(false)}\n        >\n          {/* User Info Header */}\n          <div className=\"px-4 py-3 border-b border-app-stroke\">\n            <div className=\"flex items-center space-x-3\">\n              <img \n                src={user.imageUrl} \n                alt=\"Profile\" \n                className=\"w-10 h-10 rounded-full border border-app-stroke\"\n              />\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-app-headline truncate\">\n                  {user.firstName && user.lastName \n                    ? `${user.firstName} ${user.lastName}` \n                    : user.firstName || 'User'\n                  }\n                </p>\n                <p className=\"text-xs text-app-headline opacity-70 truncate\">\n                  {user.emailAddresses[0]?.emailAddress}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* Menu Items */}\n          <div className=\"py-2\">\n            <a\n              href=\"/profile\"\n              className=\"flex items-center px-4 py-2 text-sm text-app-headline hover:bg-app-background transition-colors\"\n              onClick={() => setIsOpen(false)}\n            >\n              <User className=\"w-4 h-4 mr-3\" />\n              View Profile\n            </a>\n            \n            <a\n              href=\"/dashboard\"\n              className=\"flex items-center px-4 py-2 text-sm text-app-headline hover:bg-app-background transition-colors\"\n              onClick={() => setIsOpen(false)}\n            >\n              <Settings className=\"w-4 h-4 mr-3\" />\n              Dashboard\n            </a>\n\n            <div className=\"border-t border-app-stroke my-1\"></div>\n            \n            <button\n              onClick={handleSignOut}\n              className=\"w-full flex items-center px-4 py-2 text-sm text-app-highlight hover:bg-app-background transition-colors\"\n            >\n              <LogOut className=\"w-4 h-4 mr-3\" />\n              Sign Out\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,0SAAA,CAAA,UAAO,AAAD;IACjC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD;IAC3B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,sWAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,cAAc,CAAA,GAAA,sWAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,uCAAuC;IACvC,CAAA,GAAA,sWAAA,CAAA,YAAS,AAAD;qCAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC9E,UAAU;gBACZ;YACF;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;6CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;oCAAG,EAAE;IAEL,IAAI,CAAC,YAAY,CAAC,MAAM;QACtB,qBACE,sYAAC;YAAE,MAAK;YAAW,WAAU;;8BAC3B,sYAAC,yRAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;gBAAyB;;;;;;;IAG/C;IAEA,MAAM,gBAAgB;QACpB;QACA,UAAU;IACZ;IAEA,qBACE,sYAAC;QAAI,WAAU;QAAW,KAAK;;0BAE7B,sYAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,cAAc,IAAM,UAAU;gBAC9B,WAAU;0BAEV,cAAA,sYAAC;oBAAI,WAAU;;sCACb,sYAAC;4BACC,KAAK,KAAK,QAAQ;4BAClB,KAAI;4BACJ,WAAU;;;;;;sCAEZ,sYAAC;4BAAK,WAAU;sCACb,KAAK,SAAS,IAAI,KAAK,cAAc,CAAC,EAAE,CAAC,YAAY;;;;;;sCAExD,sYAAC,2SAAA,CAAA,cAAW;4BAAC,WAAW,CAAC,6BAA6B,EAAE,SAAS,eAAe,IAAI;;;;;;;;;;;;;;;;;YAKvF,wBACC,sYAAC;gBACC,WAAU;gBACV,cAAc,IAAM,UAAU;;kCAG9B,sYAAC;wBAAI,WAAU;kCACb,cAAA,sYAAC;4BAAI,WAAU;;8CACb,sYAAC;oCACC,KAAK,KAAK,QAAQ;oCAClB,KAAI;oCACJ,WAAU;;;;;;8CAEZ,sYAAC;oCAAI,WAAU;;sDACb,sYAAC;4CAAE,WAAU;sDACV,KAAK,SAAS,IAAI,KAAK,QAAQ,GAC5B,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,KAAK,SAAS,IAAI;;;;;;sDAGxB,sYAAC;4CAAE,WAAU;sDACV,KAAK,cAAc,CAAC,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;kCAOjC,sYAAC;wBAAI,WAAU;;0CACb,sYAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,sYAAC,yRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAInC,sYAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,sYAAC,iSAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIvC,sYAAC;gCAAI,WAAU;;;;;;0CAEf,sYAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,sYAAC,iSAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;GApHwB;;QACK,0SAAA,CAAA,UAAO;QACd,0SAAA,CAAA,WAAQ;;;KAFN", "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/components/authenticated-navbar.tsx"], "sourcesContent": ["'use client'\n\nimport { Bo<PERSON>, <PERSON> } from \"lucide-react\"\nimport ProfileDropdown from \"./profile-dropdown\"\nimport Link from \"next/link\"\n\ninterface AuthenticatedNavbarProps {\n  currentPage?: 'dashboard' | 'profile' | 'reply-guy' | 'copium'\n}\n\nexport default function AuthenticatedNavbar({ currentPage = 'dashboard' }: AuthenticatedNavbarProps) {\n  return (\n    <nav className=\"mb-8 p-4 border border-app-stroke rounded-lg bg-app-card shadow-md\">\n      <div className=\"container mx-auto flex justify-between items-center\">\n        <Link \n          href=\"/dashboard\" \n          className=\"text-2xl font-semibold text-app-highlight border-b-2 border-app-highlight\"\n        >\n          LOGO\n        </Link>\n        <div className=\"flex space-x-4 md:space-x-6 text-sm md:text-base\">\n          <Link \n            href=\"/reply-guy\" \n            className={`transition-colors flex items-center ${\n              currentPage === 'reply-guy' \n                ? 'text-app-main font-semibold' \n                : 'text-app-headline hover:text-app-main'\n            }`}\n          >\n            <Bot className=\"w-4 h-4 mr-1 md:mr-2\" /> REPLY GUY\n          </Link>\n          <Link \n            href=\"#\" \n            className={`transition-colors flex items-center ${\n              currentPage === 'copium' \n                ? 'text-app-main font-semibold' \n                : 'text-app-headline hover:text-app-main'\n            }`}\n          >\n            <Search className=\"w-4 h-4 mr-1 md:mr-2\" /> COPIUM\n          </Link>\n          <ProfileDropdown />\n        </div>\n      </div>\n    </nav>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AAJA;;;;;AAUe,SAAS,oBAAoB,EAAE,cAAc,WAAW,EAA4B;IACjG,qBACE,sYAAC;QAAI,WAAU;kBACb,cAAA,sYAAC;YAAI,WAAU;;8BACb,sYAAC,wWAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;8BACX;;;;;;8BAGD,sYAAC;oBAAI,WAAU;;sCACb,sYAAC,wWAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAW,CAAC,oCAAoC,EAC9C,gBAAgB,cACZ,gCACA,yCACJ;;8CAEF,sYAAC,uRAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAyB;;;;;;;sCAE1C,sYAAC,wWAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAW,CAAC,oCAAoC,EAC9C,gBAAgB,WACZ,gCACA,yCACJ;;8CAEF,sYAAC,6RAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAyB;;;;;;;sCAE7C,sYAAC,2JAAA,CAAA,UAAe;;;;;;;;;;;;;;;;;;;;;;AAK1B;KApCwB", "debugId": null}}, {"offset": {"line": 603, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport type React from \"react\"\nimport { useUser } from '@clerk/nextjs'\nimport { redirect } from 'next/navigation'\nimport { useState } from \"react\"\nimport { Lightbulb, LightbulbOff, Send, ExternalLink, Loader2, Plus } from \"lucide-react\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Card, CardContent } from \"@/components/ui/card\"\nimport AuthenticatedNavbar from \"@/components/authenticated-navbar\"\nimport { trpc } from \"@/utils/trpc\"\nimport { toast } from \"sonner\"\n\n// Type definitions for the dashboard data\ntype MonitoredAccount = {\n  id: string\n  handle: string\n  status: \"Monitored\" | \"Not Monitored\"\n  light: \"Green\" | \"Red\"\n  totalMentions: number\n  recentMentions: number\n}\n\ntype DashboardMention = {\n  id: string\n  handle: string\n  content: string\n  timestamp: Date\n  platform: \"X\" | \"Twitter\"\n  authorName: string\n  link: string\n}\n\nexport default function DashboardPage() {\n  const { user, isLoaded } = useUser()\n  const [replyUrl, setReplyUrl] = useState(\"\")\n  const [generatedReply, setGeneratedReply] = useState(\"\")\n  const [showAddAccount, setShowAddAccount] = useState(false)\n  const [newAccountHandle, setNewAccountHandle] = useState(\"\")\n\n  // tRPC queries\n  const { data: monitoredAccounts, isLoading: accountsLoading, refetch: refetchAccounts } = trpc.accounts.getMonitored.useQuery()\n  const { data: latestMentions, isLoading: mentionsLoading } = trpc.mentions.getLatest.useQuery()\n  \n  // tRPC mutations\n  const extractTweetMutation = trpc.twitter.extractTweetContent.useMutation()\n  const generateReplyMutation = trpc.benji.generateQuickReply.useMutation()\n  const addAccountMutation = trpc.accounts.add.useMutation()\n\n  if (!isLoaded) {\n    return <div className=\"min-h-screen bg-app-background flex items-center justify-center\">\n      <div className=\"text-app-headline\">Loading...</div>\n    </div>\n  }\n  \n  if (!user) {\n    redirect('/sign-in')\n  }\n\n  const handleAddAccount = async (e: React.FormEvent) => {\n    e.preventDefault()\n    if (!newAccountHandle.trim()) return\n\n    try {\n      await addAccountMutation.mutateAsync({ handle: newAccountHandle.trim() })\n      toast.success(\"Account added successfully!\")\n      setNewAccountHandle(\"\")\n      setShowAddAccount(false)\n      refetchAccounts()\n    } catch (error) {\n      console.error('Error adding account:', error)\n      toast.error(\"Failed to add account. Please try again.\")\n    }\n  }\n\n  const handleQuickReply = async (e: React.FormEvent) => {\n    e.preventDefault()\n    if (!replyUrl.trim()) return\n\n    try {\n      // First extract tweet content\n      const tweetData = await extractTweetMutation.mutateAsync({ url: replyUrl })\n      \n      // Then generate a reply\n      const reply = await generateReplyMutation.mutateAsync({\n        tweetUrl: replyUrl,\n        tweetContent: tweetData.content\n      })\n      \n      setGeneratedReply(reply.response)\n      toast.success(\"Reply generated successfully!\")\n    } catch (error) {\n      console.error('Error generating reply:', error)\n      toast.error(\"Failed to generate reply. Please try again.\")\n    }\n  }\n\n  const formatTimeAgo = (date: Date) => {\n    const now = new Date()\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))\n    \n    if (diffInMinutes < 60) {\n      return `${diffInMinutes} minutes ago`\n    } else if (diffInMinutes < 1440) {\n      const hours = Math.floor(diffInMinutes / 60)\n      return `${hours} hour${hours === 1 ? '' : 's'} ago`\n    } else {\n      const days = Math.floor(diffInMinutes / 1440)\n      return `${days} day${days === 1 ? '' : 's'} ago`\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen p-4 md:p-8 font-sans bg-app-background text-app-headline\">\n      <header className=\"text-center mb-8\">\n        <h1 className=\"text-4xl md:text-5xl font-bold tracking-wider text-app-headline\">BUDDYCHIP DASHBOARD</h1>\n      </header>\n      <AuthenticatedNavbar currentPage=\"dashboard\" />\n\n      <main className=\"space-y-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          {/* Monitored Accounts Section */}\n          <Card className=\"bg-app-card border-app-stroke text-app-headline shadow-md\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h2 className=\"text-lg font-semibold text-app-headline\">MONITORED ACCOUNTS</h2>\n                <Button\n                  size=\"sm\"\n                  onClick={() => setShowAddAccount(!showAddAccount)}\n                  className={`\n                    relative overflow-hidden rounded-lg px-3 py-2 text-sm font-medium\n                    transition-all duration-300 ease-in-out transform\n                    ${showAddAccount \n                      ? 'bg-purple-600 text-white shadow-lg scale-105' \n                      : 'bg-gradient-to-r from-blue-50 to-purple-50 text-gray-700 hover:from-purple-100 hover:to-blue-100'\n                    }\n                    border border-gray-200 hover:border-purple-300 hover:shadow-md\n                    focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2\n                    group\n                  `}\n                >\n                  <div className=\"flex items-center space-x-1\">\n                    <Plus className={`w-4 h-4 transition-transform duration-300 ${showAddAccount ? 'rotate-45' : 'group-hover:scale-110'}`} />\n                    <span className=\"hidden sm:inline\">Add</span>\n                  </div>\n                </Button>\n              </div>\n              \n              {/* Add Account Form */}\n              {showAddAccount && (\n                <div className=\"mb-4 p-3 bg-app-background rounded-md border border-app-stroke/50\">\n                  <form onSubmit={handleAddAccount} className=\"flex space-x-2\">\n                    <Input\n                      type=\"text\"\n                      placeholder=\"Enter Twitter handle (e.g., @elonmusk)\"\n                      value={newAccountHandle}\n                      onChange={(e) => setNewAccountHandle(e.target.value)}\n                      className=\"flex-1 bg-app-card border-app-stroke text-app-headline placeholder:text-app-headline placeholder:opacity-60 focus:border-app-main\"\n                    />\n                    <Button\n                      type=\"submit\"\n                      size=\"sm\"\n                      disabled={addAccountMutation.isPending || !newAccountHandle.trim()}\n                      className=\"bg-app-main text-app-secondary hover:bg-app-highlight disabled:bg-app-main/50\"\n                    >\n                      {addAccountMutation.isPending ? (\n                        <Loader2 className=\"w-4 h-4 animate-spin\" />\n                      ) : (\n                        \"Add\"\n                      )}\n                    </Button>\n                  </form>\n                </div>\n              )}\n\n              {accountsLoading ? (\n                <div className=\"flex items-center justify-center py-4\">\n                  <Loader2 className=\"w-6 h-6 animate-spin text-app-main\" />\n                  <span className=\"ml-2 text-app-headline\">Loading accounts...</span>\n                </div>\n              ) : (\n                <ul className=\"space-y-3\">\n                  {monitoredAccounts?.length === 0 ? (\n                    <li className=\"text-sm text-app-headline opacity-60 text-center py-4\">\n                      No monitored accounts yet. Add some to get started!\n                    </li>\n                  ) : (\n                    monitoredAccounts?.map((account, index) => (\n                      <li key={account.id} className=\"text-sm\">\n                        <div className=\"flex items-center justify-between\">\n                          <span className=\"text-app-headline\">\n                            {index + 1}. {account.handle} - {account.light} Light\n                          </span>\n                          {account.light === \"Green\" ? (\n                            <Lightbulb className=\"w-5 h-5 text-app-main\" />\n                          ) : (\n                            <LightbulbOff className=\"w-5 h-5 text-app-highlight\" />\n                          )}\n                        </div>\n                        <div\n                          className={`text-xs ml-4 ${\n                            account.status === \"Monitored\" ? \"text-app-main\" : \"text-app-highlight\"\n                          }`}\n                        >\n                          ({account.status}) - {account.totalMentions} total mentions\n                        </div>\n                      </li>\n                    ))\n                  )}\n                </ul>\n              )}\n            </CardContent>\n          </Card>\n\n          {/* Latest 10 Mentions Section */}\n          <Card className=\"bg-app-card border-app-stroke text-app-headline shadow-md\">\n            <CardContent className=\"p-6\">\n              <h2 className=\"text-lg font-semibold mb-4 text-app-headline\">LATEST 10 MENTIONS (Sorted by Date)</h2>\n              {mentionsLoading ? (\n                <div className=\"flex items-center justify-center py-4\">\n                  <Loader2 className=\"w-6 h-6 animate-spin text-app-main\" />\n                  <span className=\"ml-2 text-app-headline\">Loading mentions...</span>\n                </div>\n              ) : (\n                <div className=\"space-y-3 max-h-80 overflow-y-auto\">\n                  {latestMentions?.length === 0 ? (\n                    <div className=\"text-sm text-app-headline opacity-60 text-center py-8\">\n                      No mentions found. Start monitoring accounts to see mentions here!\n                    </div>\n                  ) : (\n                    latestMentions?.map((mention) => (\n                      <div key={mention.id} className=\"p-3 bg-app-background rounded-md border border-app-stroke/50\">\n                        <div className=\"flex items-center justify-between mb-2\">\n                          <span className=\"text-sm font-medium text-app-headline\">{mention.handle}</span>\n                          <div className=\"flex items-center space-x-2\">\n                            <span className=\"text-xs text-app-headline opacity-70\">\n                              {formatTimeAgo(new Date(mention.timestamp))}\n                            </span>\n                            <a \n                              href={mention.link} \n                              target=\"_blank\" \n                              rel=\"noopener noreferrer\"\n                              className=\"hover:text-app-main transition-colors\"\n                            >\n                              <ExternalLink className=\"w-3 h-3 text-app-headline opacity-50 hover:opacity-100\" />\n                            </a>\n                          </div>\n                        </div>\n                        <p className=\"text-xs text-app-headline opacity-90\">{mention.content}</p>\n                        {mention.bullishScore && (\n                          <div className=\"mt-1\">\n                            <span className=\"text-xs text-app-main\">\n                              Bullish Score: {mention.bullishScore}/100\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                    ))\n                  )}\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Quick Reply Section */}\n        <Card className=\"bg-app-card border-app-stroke text-app-headline shadow-md\">\n          <CardContent className=\"p-6\">\n            <h2 className=\"text-xl font-semibold mb-3 text-center text-app-headline\">QUICK REPLY</h2>\n            <p className=\"text-app-headline opacity-90 text-center leading-relaxed max-w-2xl mx-auto mb-6\">\n              Paste a link from x.com or twitter.com, we will get info about the specific post, and uses the{\" \"}\n              <span className=\"font-semibold text-app-main\">ANSWER-ENGINE AGENT</span> to craft a response.\n            </p>\n\n            <form onSubmit={handleQuickReply} className=\"max-w-2xl mx-auto space-y-4\">\n              <div className=\"flex space-x-2\">\n                <Input\n                  type=\"url\"\n                  placeholder=\"Paste X.com or Twitter.com link here...\"\n                  value={replyUrl}\n                  onChange={(e) => setReplyUrl(e.target.value)}\n                  className=\"flex-1 bg-app-background border-app-stroke text-app-headline placeholder:text-app-headline placeholder:opacity-60 focus:border-app-main\"\n                />\n                <Button\n                  type=\"submit\"\n                  disabled={extractTweetMutation.isPending || generateReplyMutation.isPending || !replyUrl.trim()}\n                  className=\"bg-app-main text-app-secondary hover:bg-app-highlight disabled:bg-app-main/50\"\n                >\n                  {extractTweetMutation.isPending || generateReplyMutation.isPending ? (\n                    <Loader2 className=\"w-4 h-4 animate-spin\" />\n                  ) : (\n                    <Send className=\"w-4 h-4\" />\n                  )}\n                </Button>\n              </div>\n            </form>\n\n            {generatedReply && (\n              <div className=\"mt-6 max-w-2xl mx-auto\">\n                <h3 className=\"text-sm font-semibold text-app-headline mb-2\">Generated Reply:</h3>\n                <div className=\"p-4 bg-app-background rounded-md border border-app-stroke\">\n                  <p className=\"text-app-headline text-sm\">{generatedReply}</p>\n                </div>\n                <div className=\"flex justify-end mt-2 space-x-2\">\n                  <Button\n                    size=\"sm\"\n                    variant=\"outline\"\n                    className=\"border-app-stroke text-app-headline hover:bg-app-main hover:text-app-secondary\"\n                    onClick={() => setGeneratedReply(\"\")}\n                  >\n                    Clear\n                  </Button>\n                  <Button size=\"sm\" className=\"bg-app-main text-app-secondary hover:bg-app-highlight\">\n                    Use Reply\n                  </Button>\n                </div>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      </main>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;AAkCe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,0SAAA,CAAA,UAAO,AAAD;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sWAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,sWAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,sWAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,sWAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,eAAe;IACf,MAAM,EAAE,MAAM,iBAAiB,EAAE,WAAW,eAAe,EAAE,SAAS,eAAe,EAAE,GAAG,sIAAA,CAAA,OAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ;IAC7H,MAAM,EAAE,MAAM,cAAc,EAAE,WAAW,eAAe,EAAE,GAAG,sIAAA,CAAA,OAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ;IAE7F,iBAAiB;IACjB,MAAM,uBAAuB,sIAAA,CAAA,OAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,WAAW;IACzE,MAAM,wBAAwB,sIAAA,CAAA,OAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,WAAW;IACvE,MAAM,qBAAqB,sIAAA,CAAA,OAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW;IAExD,IAAI,CAAC,UAAU;QACb,qBAAO,sYAAC;YAAI,WAAU;sBACpB,cAAA,sYAAC;gBAAI,WAAU;0BAAoB;;;;;;;;;;;IAEvC;IAEA,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,8UAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,MAAM,mBAAmB,OAAO;QAC9B,EAAE,cAAc;QAChB,IAAI,CAAC,iBAAiB,IAAI,IAAI;QAE9B,IAAI;YACF,MAAM,mBAAmB,WAAW,CAAC;gBAAE,QAAQ,iBAAiB,IAAI;YAAG;YACvE,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,oBAAoB;YACpB,kBAAkB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,EAAE,cAAc;QAChB,IAAI,CAAC,SAAS,IAAI,IAAI;QAEtB,IAAI;YACF,8BAA8B;YAC9B,MAAM,YAAY,MAAM,qBAAqB,WAAW,CAAC;gBAAE,KAAK;YAAS;YAEzE,wBAAwB;YACxB,MAAM,QAAQ,MAAM,sBAAsB,WAAW,CAAC;gBACpD,UAAU;gBACV,cAAc,UAAU,OAAO;YACjC;YAEA,kBAAkB,MAAM,QAAQ;YAChC,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,MAAM,IAAI;QAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAE9E,IAAI,gBAAgB,IAAI;YACtB,OAAO,GAAG,cAAc,YAAY,CAAC;QACvC,OAAO,IAAI,gBAAgB,MAAM;YAC/B,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;YACzC,OAAO,GAAG,MAAM,KAAK,EAAE,UAAU,IAAI,KAAK,IAAI,IAAI,CAAC;QACrD,OAAO;YACL,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;YACxC,OAAO,GAAG,KAAK,IAAI,EAAE,SAAS,IAAI,KAAK,IAAI,IAAI,CAAC;QAClD;IACF;IAEA,qBACE,sYAAC;QAAI,WAAU;;0BACb,sYAAC;gBAAO,WAAU;0BAChB,cAAA,sYAAC;oBAAG,WAAU;8BAAkE;;;;;;;;;;;0BAElF,sYAAC,+JAAA,CAAA,UAAmB;gBAAC,aAAY;;;;;;0BAEjC,sYAAC;gBAAK,WAAU;;kCACd,sYAAC;wBAAI,WAAU;;0CAEb,sYAAC,kJAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,sYAAC,kJAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,sYAAC;4CAAI,WAAU;;8DACb,sYAAC;oDAAG,WAAU;8DAA0C;;;;;;8DACxD,sYAAC,oJAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,IAAM,kBAAkB,CAAC;oDAClC,WAAW,CAAC;;;oBAGV,EAAE,iBACE,iDACA,mGACH;;;;kBAIH,CAAC;8DAED,cAAA,sYAAC;wDAAI,WAAU;;0EACb,sYAAC,yRAAA,CAAA,OAAI;gEAAC,WAAW,CAAC,0CAA0C,EAAE,iBAAiB,cAAc,yBAAyB;;;;;;0EACtH,sYAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;;;;;;;;;;;;wCAMxC,gCACC,sYAAC;4CAAI,WAAU;sDACb,cAAA,sYAAC;gDAAK,UAAU;gDAAkB,WAAU;;kEAC1C,sYAAC,mJAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wDACnD,WAAU;;;;;;kEAEZ,sYAAC,oJAAA,CAAA,SAAM;wDACL,MAAK;wDACL,MAAK;wDACL,UAAU,mBAAmB,SAAS,IAAI,CAAC,iBAAiB,IAAI;wDAChE,WAAU;kEAET,mBAAmB,SAAS,iBAC3B,sYAAC,wSAAA,CAAA,UAAO;4DAAC,WAAU;;;;;mEAEnB;;;;;;;;;;;;;;;;;wCAOT,gCACC,sYAAC;4CAAI,WAAU;;8DACb,sYAAC,wSAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,sYAAC;oDAAK,WAAU;8DAAyB;;;;;;;;;;;iEAG3C,sYAAC;4CAAG,WAAU;sDACX,mBAAmB,WAAW,kBAC7B,sYAAC;gDAAG,WAAU;0DAAwD;;;;;uDAItE,mBAAmB,IAAI,CAAC,SAAS,sBAC/B,sYAAC;oDAAoB,WAAU;;sEAC7B,sYAAC;4DAAI,WAAU;;8EACb,sYAAC;oEAAK,WAAU;;wEACb,QAAQ;wEAAE;wEAAG,QAAQ,MAAM;wEAAC;wEAAI,QAAQ,KAAK;wEAAC;;;;;;;gEAEhD,QAAQ,KAAK,KAAK,wBACjB,sYAAC,mSAAA,CAAA,YAAS;oEAAC,WAAU;;;;;yFAErB,sYAAC,6SAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;sEAG5B,sYAAC;4DACC,WAAW,CAAC,aAAa,EACvB,QAAQ,MAAM,KAAK,cAAc,kBAAkB,sBACnD;;gEACH;gEACG,QAAQ,MAAM;gEAAC;gEAAK,QAAQ,aAAa;gEAAC;;;;;;;;mDAhBvC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;0CA2B/B,sYAAC,kJAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,sYAAC,kJAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,sYAAC;4CAAG,WAAU;sDAA+C;;;;;;wCAC5D,gCACC,sYAAC;4CAAI,WAAU;;8DACb,sYAAC,wSAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,sYAAC;oDAAK,WAAU;8DAAyB;;;;;;;;;;;iEAG3C,sYAAC;4CAAI,WAAU;sDACZ,gBAAgB,WAAW,kBAC1B,sYAAC;gDAAI,WAAU;0DAAwD;;;;;uDAIvE,gBAAgB,IAAI,CAAC,wBACnB,sYAAC;oDAAqB,WAAU;;sEAC9B,sYAAC;4DAAI,WAAU;;8EACb,sYAAC;oEAAK,WAAU;8EAAyC,QAAQ,MAAM;;;;;;8EACvE,sYAAC;oEAAI,WAAU;;sFACb,sYAAC;4EAAK,WAAU;sFACb,cAAc,IAAI,KAAK,QAAQ,SAAS;;;;;;sFAE3C,sYAAC;4EACC,MAAM,QAAQ,IAAI;4EAClB,QAAO;4EACP,KAAI;4EACJ,WAAU;sFAEV,cAAA,sYAAC,6SAAA,CAAA,eAAY;gFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sEAI9B,sYAAC;4DAAE,WAAU;sEAAwC,QAAQ,OAAO;;;;;;wDACnE,QAAQ,YAAY,kBACnB,sYAAC;4DAAI,WAAU;sEACb,cAAA,sYAAC;gEAAK,WAAU;;oEAAwB;oEACtB,QAAQ,YAAY;oEAAC;;;;;;;;;;;;;mDArBnC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAmClC,sYAAC,kJAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,sYAAC,kJAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,sYAAC;oCAAG,WAAU;8CAA2D;;;;;;8CACzE,sYAAC;oCAAE,WAAU;;wCAAkF;wCACE;sDAC/F,sYAAC;4CAAK,WAAU;sDAA8B;;;;;;wCAA0B;;;;;;;8CAG1E,sYAAC;oCAAK,UAAU;oCAAkB,WAAU;8CAC1C,cAAA,sYAAC;wCAAI,WAAU;;0DACb,sYAAC,mJAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC3C,WAAU;;;;;;0DAEZ,sYAAC,oJAAA,CAAA,SAAM;gDACL,MAAK;gDACL,UAAU,qBAAqB,SAAS,IAAI,sBAAsB,SAAS,IAAI,CAAC,SAAS,IAAI;gDAC7F,WAAU;0DAET,qBAAqB,SAAS,IAAI,sBAAsB,SAAS,iBAChE,sYAAC,wSAAA,CAAA,UAAO;oDAAC,WAAU;;;;;yEAEnB,sYAAC,yRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gCAMvB,gCACC,sYAAC;oCAAI,WAAU;;sDACb,sYAAC;4CAAG,WAAU;sDAA+C;;;;;;sDAC7D,sYAAC;4CAAI,WAAU;sDACb,cAAA,sYAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;sDAE5C,sYAAC;4CAAI,WAAU;;8DACb,sYAAC,oJAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS,IAAM,kBAAkB;8DAClC;;;;;;8DAGD,sYAAC,oJAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,WAAU;8DAAwD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtG;GAlSwB;;QACK,0SAAA,CAAA,UAAO;;;KADZ", "debugId": null}}]}