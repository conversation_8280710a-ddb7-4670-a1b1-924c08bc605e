{"node": {"7f10e4ae00327b7b73643bcf84245ec80c2c4a3242": {"workers": {"app/dashboard/page": {"moduleId": "[project]/apps/web/.next-internal/server/app/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.22.0_next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0_42d1807e423217c6736c669ff09b1861/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.22.0_next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0_42d1807e423217c6736c669ff09b1861/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/dashboard/page": "action-browser"}}, "7fc0bf80e77e1e0cca1fd98a72fac72eec01f51c7f": {"workers": {"app/dashboard/page": {"moduleId": "[project]/apps/web/.next-internal/server/app/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.22.0_next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0_42d1807e423217c6736c669ff09b1861/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.22.0_next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0_42d1807e423217c6736c669ff09b1861/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/dashboard/page": "action-browser"}}, "7fcfe2502b630a5c1e06e13334b0b5f5b27f4bb35e": {"workers": {"app/dashboard/page": {"moduleId": "[project]/apps/web/.next-internal/server/app/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.22.0_next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0_42d1807e423217c6736c669ff09b1861/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.22.0_next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0_42d1807e423217c6736c669ff09b1861/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/dashboard/page": "action-browser"}}, "7fbd9d22e79fb7a6114a8602ead04a8902523cd377": {"workers": {"app/dashboard/page": {"moduleId": "[project]/apps/web/.next-internal/server/app/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.22.0_next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0_42d1807e423217c6736c669ff09b1861/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.22.0_next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0_42d1807e423217c6736c669ff09b1861/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/dashboard/page": "action-browser"}}}, "edge": {}, "encryptionKey": "mzIinH1YWNyMD7WtBtBW66vi+sBEHUpA1+aIXbmr4q8="}