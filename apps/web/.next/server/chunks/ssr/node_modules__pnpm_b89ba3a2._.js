module.exports = {

"[project]/node_modules/.pnpm/@clerk+nextjs@6.22.0_next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0_42d1807e423217c6736c669ff09b1861/node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/9aefa_@clerk_nextjs_dist_esm_app-router_f3b0954b._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@clerk+nextjs@6.22.0_next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0_42d1807e423217c6736c669ff09b1861/node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@clerk+nextjs@6.22.0_next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0_42d1807e423217c6736c669ff09b1861/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/9aefa_@clerk_nextjs_dist_esm_app-router_e69f4ddd._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@clerk+nextjs@6.22.0_next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0_42d1807e423217c6736c669ff09b1861/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@tanstack+query-devtools@5.80.0/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/HH7B3BHX.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/97880_@tanstack_query-devtools_build_907b1bb4._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@tanstack+query-devtools@5.80.0/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/HH7B3BHX.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@tanstack+query-devtools@5.80.0/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/JZI2RDCT.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/97880_@tanstack_query-devtools_build_9c090203._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@tanstack+query-devtools@5.80.0/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/JZI2RDCT.js [app-ssr] (ecmascript)");
    });
});
}}),

};