{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/use-sync-external-store%401.5.0_react%4019.1.0/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n      didWarnOld18Alpha ||\n        void 0 === React.startTransition ||\n        ((didWarnOld18Alpha = !0),\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"\n        ));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) ||\n          (console.error(\n            \"The result of getSnapshot should be cached to avoid an infinite loop\"\n          ),\n          (didWarnUncachedGetSnapshot = !0));\n      }\n      cachedValue = useState({\n        inst: { value: value, getSnapshot: getSnapshot }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(\n        function () {\n          inst.value = value;\n          inst.getSnapshot = getSnapshot;\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n        },\n        [subscribe, value, getSnapshot]\n      );\n      useEffect(\n        function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          return subscribe(function () {\n            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          });\n        },\n        [subscribe]\n      );\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1,\n      shim =\n        \"undefined\" === typeof window ||\n        \"undefined\" === typeof window.document ||\n        \"undefined\" === typeof window.document.createElement\n          ? useSyncExternalStore$1\n          : useSyncExternalStore$2;\n    exports.useSyncExternalStore =\n      void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAED;AACA,oEACE,AAAC;IACC,SAAS,GAAG,CAAC,EAAE,CAAC;QACd,OAAO,AAAC,MAAM,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM,IAAI,CAAC,KAAO,MAAM,KAAK,MAAM;IACxE;IACA,SAAS,uBAAuB,SAAS,EAAE,WAAW;QACpD,qBACE,KAAK,MAAM,MAAM,eAAe,IAChC,CAAC,AAAC,oBAAoB,CAAC,GACvB,QAAQ,KAAK,CACX,iMACD;QACH,IAAI,QAAQ;QACZ,IAAI,CAAC,4BAA4B;YAC/B,IAAI,cAAc;YAClB,SAAS,OAAO,gBACd,CAAC,QAAQ,KAAK,CACZ,yEAED,6BAA6B,CAAC,CAAE;QACrC;QACA,cAAc,SAAS;YACrB,MAAM;gBAAE,OAAO;gBAAO,aAAa;YAAY;QACjD;QACA,IAAI,OAAO,WAAW,CAAC,EAAE,CAAC,IAAI,EAC5B,cAAc,WAAW,CAAC,EAAE;QAC9B,gBACE;YACE,KAAK,KAAK,GAAG;YACb,KAAK,WAAW,GAAG;YACnB,uBAAuB,SAAS,YAAY;gBAAE,MAAM;YAAK;QAC3D,GACA;YAAC;YAAW;YAAO;SAAY;QAEjC,UACE;YACE,uBAAuB,SAAS,YAAY;gBAAE,MAAM;YAAK;YACzD,OAAO,UAAU;gBACf,uBAAuB,SAAS,YAAY;oBAAE,MAAM;gBAAK;YAC3D;QACF,GACA;YAAC;SAAU;QAEb,cAAc;QACd,OAAO;IACT;IACA,SAAS,uBAAuB,IAAI;QAClC,IAAI,oBAAoB,KAAK,WAAW;QACxC,OAAO,KAAK,KAAK;QACjB,IAAI;YACF,IAAI,YAAY;YAChB,OAAO,CAAC,SAAS,MAAM;QACzB,EAAE,OAAO,OAAO;YACd,OAAO,CAAC;QACV;IACF;IACA,SAAS,uBAAuB,SAAS,EAAE,WAAW;QACpD,OAAO;IACT;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,IAAI,uQACF,WAAW,eAAe,OAAO,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,IACzD,WAAW,MAAM,QAAQ,EACzB,YAAY,MAAM,SAAS,EAC3B,kBAAkB,MAAM,eAAe,EACvC,gBAAgB,MAAM,aAAa,EACnC,oBAAoB,CAAC,GACrB,6BAA6B,CAAC,GAC9B,OACE,gBAAgB,OAAO,UACvB,gBAAgB,OAAO,OAAO,QAAQ,IACtC,gBAAgB,OAAO,OAAO,QAAQ,CAAC,aAAa,GAChD,yBACA;IACR,QAAQ,oBAAoB,GAC1B,KAAK,MAAM,MAAM,oBAAoB,GAAG,MAAM,oBAAoB,GAAG;IACvE,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/use-sync-external-store%401.5.0_react%4019.1.0/node_modules/use-sync-external-store/shim/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n"], "names": [], "mappings": "AAAA;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/dequal%402.0.3/node_modules/dequal/lite/index.mjs"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nexport function dequal(foo, bar) {\n\tvar ctor, len;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n"], "names": [], "mappings": ";;;AAAA,IAAI,MAAM,OAAO,SAAS,CAAC,cAAc;AAElC,SAAS,OAAO,GAAG,EAAE,GAAG;IAC9B,IAAI,MAAM;IACV,IAAI,QAAQ,KAAK,OAAO;IAExB,IAAI,OAAO,OAAO,CAAC,OAAK,IAAI,WAAW,MAAM,IAAI,WAAW,EAAE;QAC7D,IAAI,SAAS,MAAM,OAAO,IAAI,OAAO,OAAO,IAAI,OAAO;QACvD,IAAI,SAAS,QAAQ,OAAO,IAAI,QAAQ,OAAO,IAAI,QAAQ;QAE3D,IAAI,SAAS,OAAO;YACnB,IAAI,CAAC,MAAI,IAAI,MAAM,MAAM,IAAI,MAAM,EAAE;gBACpC,MAAO,SAAS,OAAO,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI;YAC1C;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,CAAC,QAAQ,OAAO,QAAQ,UAAU;YACrC,MAAM;YACN,IAAK,QAAQ,IAAK;gBACjB,IAAI,IAAI,IAAI,CAAC,KAAK,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,KAAK,OAAO,OAAO;gBACjE,IAAI,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG,OAAO;YAC7D;YACA,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,KAAK;QACpC;IACD;IAEA,OAAO,QAAQ,OAAO,QAAQ;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/dequal%402.0.3/node_modules/dequal/dist/index.mjs"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nfunction find(iter, tar, key) {\n\tfor (key of iter.keys()) {\n\t\tif (dequal(key, tar)) return key;\n\t}\n}\n\nexport function dequal(foo, bar) {\n\tvar ctor, len, tmp;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ctor === Set) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len;\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!bar.has(tmp)) return false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === Map) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len[0];\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!dequal(len[1], bar.get(tmp))) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === ArrayBuffer) {\n\t\t\tfoo = new Uint8Array(foo);\n\t\t\tbar = new Uint8Array(bar);\n\t\t} else if (ctor === DataView) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo.getInt8(len) === bar.getInt8(len));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ArrayBuffer.isView(foo)) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo[len] === bar[len]);\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n"], "names": [], "mappings": ";;;AAAA,IAAI,MAAM,OAAO,SAAS,CAAC,cAAc;AAEzC,SAAS,KAAK,IAAI,EAAE,GAAG,EAAE,GAAG;IAC3B,KAAK,OAAO,KAAK,IAAI,GAAI;QACxB,IAAI,OAAO,KAAK,MAAM,OAAO;IAC9B;AACD;AAEO,SAAS,OAAO,GAAG,EAAE,GAAG;IAC9B,IAAI,MAAM,KAAK;IACf,IAAI,QAAQ,KAAK,OAAO;IAExB,IAAI,OAAO,OAAO,CAAC,OAAK,IAAI,WAAW,MAAM,IAAI,WAAW,EAAE;QAC7D,IAAI,SAAS,MAAM,OAAO,IAAI,OAAO,OAAO,IAAI,OAAO;QACvD,IAAI,SAAS,QAAQ,OAAO,IAAI,QAAQ,OAAO,IAAI,QAAQ;QAE3D,IAAI,SAAS,OAAO;YACnB,IAAI,CAAC,MAAI,IAAI,MAAM,MAAM,IAAI,MAAM,EAAE;gBACpC,MAAO,SAAS,OAAO,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI;YAC1C;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,SAAS,KAAK;YACjB,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;gBAC1B,OAAO;YACR;YACA,KAAK,OAAO,IAAK;gBAChB,MAAM;gBACN,IAAI,OAAO,OAAO,QAAQ,UAAU;oBACnC,MAAM,KAAK,KAAK;oBAChB,IAAI,CAAC,KAAK,OAAO;gBAClB;gBACA,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,OAAO;YAC3B;YACA,OAAO;QACR;QAEA,IAAI,SAAS,KAAK;YACjB,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;gBAC1B,OAAO;YACR;YACA,KAAK,OAAO,IAAK;gBAChB,MAAM,GAAG,CAAC,EAAE;gBACZ,IAAI,OAAO,OAAO,QAAQ,UAAU;oBACnC,MAAM,KAAK,KAAK;oBAChB,IAAI,CAAC,KAAK,OAAO;gBAClB;gBACA,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,OAAO;oBAClC,OAAO;gBACR;YACD;YACA,OAAO;QACR;QAEA,IAAI,SAAS,aAAa;YACzB,MAAM,IAAI,WAAW;YACrB,MAAM,IAAI,WAAW;QACtB,OAAO,IAAI,SAAS,UAAU;YAC7B,IAAI,CAAC,MAAI,IAAI,UAAU,MAAM,IAAI,UAAU,EAAE;gBAC5C,MAAO,SAAS,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC;YAClD;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,YAAY,MAAM,CAAC,MAAM;YAC5B,IAAI,CAAC,MAAI,IAAI,UAAU,MAAM,IAAI,UAAU,EAAE;gBAC5C,MAAO,SAAS,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI;YACtC;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,CAAC,QAAQ,OAAO,QAAQ,UAAU;YACrC,MAAM;YACN,IAAK,QAAQ,IAAK;gBACjB,IAAI,IAAI,IAAI,CAAC,KAAK,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,KAAK,OAAO,OAAO;gBACjE,IAAI,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG,OAAO;YAC7D;YACA,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,KAAK;QACpC;IACD;IAEA,OAAO,QAAQ,OAAO,QAAQ;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/client-boundary/PromisifiedAuthProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@clerk/clerk-react';\nimport { useDerivedAuth } from '@clerk/clerk-react/internal';\nimport type { InitialState } from '@clerk/types';\nimport { useRouter } from 'next/compat/router';\nimport React from 'react';\n\nconst PromisifiedAuthContext = React.createContext<Promise<InitialState> | InitialState | null>(null);\n\nexport function PromisifiedAuthProvider({\n  authPromise,\n  children,\n}: {\n  authPromise: Promise<InitialState> | InitialState;\n  children: React.ReactNode;\n}) {\n  return <PromisifiedAuthContext.Provider value={authPromise}>{children}</PromisifiedAuthContext.Provider>;\n}\n\n/**\n * Returns the current auth state, the user and session ids and the `getToken`\n * that can be used to retrieve the given template or the default Clerk token.\n *\n * Until Clerk loads, `isLoaded` will be set to `false`.\n * Once Clerk loads, `isLoaded` will be set to `true`, and you can\n * safely access the `userId` and `sessionId` variables.\n *\n * For projects using NextJs or Remix, you can have immediate access to this data during SSR\n * simply by using the `ClerkProvider`.\n *\n * @example\n * import { useAuth } from '@clerk/nextjs'\n *\n * function Hello() {\n *   const { isSignedIn, sessionId, userId } = useAuth();\n *   if(isSignedIn) {\n *     return null;\n *   }\n *   console.log(sessionId, userId)\n *   return <div>...</div>\n * }\n *\n * @example\n * This page will be fully rendered during SSR.\n *\n * ```tsx\n * import { useAuth } from '@clerk/nextjs'\n *\n * export HelloPage = () => {\n *   const { isSignedIn, sessionId, userId } = useAuth();\n *   console.log(isSignedIn, sessionId, userId)\n *   return <div>...</div>\n * }\n * ```\n */\nexport function usePromisifiedAuth(options: Parameters<typeof useAuth>[0] = {}) {\n  const isPagesRouter = useRouter();\n  const valueFromContext = React.useContext(PromisifiedAuthContext);\n\n  let resolvedData = valueFromContext;\n  if (valueFromContext && 'then' in valueFromContext) {\n    resolvedData = React.use(valueFromContext);\n  }\n\n  // At this point we should have a usable auth object\n  if (typeof window === 'undefined') {\n    // Pages router should always use useAuth as it is able to grab initial auth state from context during SSR.\n    if (isPagesRouter) {\n      return useAuth(options);\n    }\n\n    // We don't need to deal with Clerk being loaded here\n    return useDerivedAuth({ ...resolvedData, ...options });\n  } else {\n    return useAuth({ ...resolvedData, ...options });\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA,SAAS,eAAe;;AACxB,SAAS,sBAAsB;AAE/B,SAAS,iBAAiB;AAC1B,OAAO,WAAW;;;;;;;AAElB,MAAM,waAAyB,UAAA,CAAM,aAAA,CAA2D,IAAI;AAE7F,SAAS,wBAAwB,EACtC,WAAA,EACA,QAAA,EACF,EAGG;IACD,OAAO,aAAA,GAAA,8YAAA,CAAA,UAAA,CAAA,aAAA,CAAC,uBAAuB,QAAA,EAAvB;QAAgC,OAAO;IAAA,GAAc,QAAS;AACxE;AAsCO,SAAS,mBAAmB,UAAyC,CAAC,CAAA,EAAG;IAC9E,MAAM,sWAAgB,YAAA,CAAU;IAChC,MAAM,kaAAmB,UAAA,CAAM,UAAA,CAAW,sBAAsB;IAEhE,IAAI,eAAe;IACnB,IAAI,oBAAoB,UAAU,kBAAkB;QAClD,6ZAAe,WAAA,CAAM,GAAA,CAAI,gBAAgB;IAC3C;IAGA,IAAI,OAAO,WAAW,aAAa;QAEjC,IAAI,eAAe;YACjB,uVAAO,UAAA,EAAQ,OAAO;QACxB;QAGA,QAAO,gWAAA,EAAe;YAAE,GAAG,YAAA;YAAc,GAAG,OAAA;QAAQ,CAAC;IACvD,OAAO;QACL,uVAAO,UAAA,EAAQ;YAAE,GAAG,YAAA;YAAc,GAAG,OAAA;QAAQ,CAAC;IAChD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/client-boundary/hooks/useSafeLayoutEffect.tsx"], "sourcesContent": ["import React from 'react';\n\n// TODO: Import from shared once [JS-118] is done\nexport const useSafeLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n"], "names": [], "mappings": ";;;AAAA,OAAO,WAAW;;;AAGX,MAAM,sBAAsB,OAAO,WAAW,6ZAAc,UAAA,CAAM,eAAA,kZAAkB,UAAA,CAAM,SAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/client-boundary/NextOptionsContext.tsx"], "sourcesContent": ["import React from 'react';\n\nimport type { NextClerkProviderProps } from '../types';\n\ntype ClerkNextContextValue = Partial<Omit<NextClerkProviderProps, 'children'>>;\n\nconst ClerkNextOptionsCtx = React.createContext<{ value: ClerkNextContextValue } | undefined>(undefined);\nClerkNextOptionsCtx.displayName = 'ClerkNextOptionsCtx';\n\nconst useClerkNextOptions = () => {\n  const ctx = React.useContext(ClerkNextOptionsCtx) as { value: ClerkNextContextValue };\n  return ctx?.value;\n};\n\nconst ClerkNextOptionsProvider = (\n  props: React.PropsWithChildren<{ options: ClerkNextContextValue }>,\n): React.JSX.Element => {\n  const { children, options } = props;\n  return <ClerkNextOptionsCtx.Provider value={{ value: options }}>{children}</ClerkNextOptionsCtx.Provider>;\n};\n\nexport { ClerkNextOptionsProvider, useClerkNextOptions };\n"], "names": [], "mappings": ";;;;AAAA,OAAO,WAAW;;;AAMlB,MAAM,qaAAsB,UAAA,CAAM,aAAA,CAA4D,KAAA,CAAS;AACvG,oBAAoB,WAAA,GAAc;AAElC,MAAM,sBAAsB,MAAM;IAChC,MAAM,qZAAM,UAAA,CAAM,UAAA,CAAW,mBAAmB;IAChD,OAAO,OAAA,OAAA,KAAA,IAAA,IAAK,KAAA;AACd;AAEA,MAAM,2BAA2B,CAC/B,UACsB;IACtB,MAAM,EAAE,QAAA,EAAU,OAAA,CAAQ,CAAA,GAAI;IAC9B,OAAO,aAAA,GAAA,8YAAA,CAAA,UAAA,CAAA,aAAA,CAAC,oBAAoB,QAAA,EAApB;QAA6B,OAAO;YAAE,OAAO;QAAQ;IAAA,GAAI,QAAS;AAC5E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/utils/clerk-js-script.tsx"], "sourcesContent": ["import { useClerk } from '@clerk/clerk-react';\nimport { buildClerkJsScriptAttributes, clerkJsScriptUrl } from '@clerk/clerk-react/internal';\nimport NextScript from 'next/script';\nimport React from 'react';\n\nimport { useClerkNextOptions } from '../client-boundary/NextOptionsContext';\n\ntype ClerkJSScriptProps = {\n  router: 'app' | 'pages';\n};\n\nfunction ClerkJSScript(props: ClerkJSScriptProps) {\n  const { publishable<PERSON>ey, clerkJSU<PERSON>, clerkJ<PERSON><PERSON><PERSON>, clerkJSV<PERSON>t, nonce } = useClerkNextOptions();\n  const { domain, proxyUrl } = useClerk();\n\n  /**\n   * If no publishable key, avoid appending an invalid script in the DOM.\n   */\n  if (!publishableKey) {\n    return null;\n  }\n\n  const options = {\n    domain,\n    proxyUrl,\n    publishable<PERSON><PERSON>,\n    clerkJ<PERSON>rl,\n    clerkJSVersion,\n    clerkJSV<PERSON>t,\n    nonce,\n  };\n  const scriptUrl = clerkJsScriptUrl(options);\n\n  /**\n   * Notes:\n   * `next/script` in 13.x.x when used with App Router will fail to pass any of our `data-*` attributes, resulting in errors\n   * Nextjs App Router will automatically move inline scripts inside `<head/>`\n   * Using the `nextjs/script` for App Router with the `beforeInteractive` strategy will throw an error because our custom script will be mounted outside the `html` tag.\n   */\n  const Script = props.router === 'app' ? 'script' : NextScript;\n\n  return (\n    <Script\n      src={scriptUrl}\n      data-clerk-js-script\n      async\n      // `nextjs/script` will add defer by default and does not get removed when we async is true\n      defer={props.router === 'pages' ? false : undefined}\n      crossOrigin='anonymous'\n      strategy={props.router === 'pages' ? 'beforeInteractive' : undefined}\n      {...buildClerkJsScriptAttributes(options)}\n    />\n  );\n}\n\nexport { ClerkJSScript };\n"], "names": [], "mappings": ";;;AAAA,SAAS,gBAAgB;;AACzB,SAAS,8BAA8B,wBAAwB;;AAC/D,OAAO,gBAAgB;AACvB,OAAO,WAAW;AAElB,SAAS,2BAA2B;;;;;;;AAMpC,SAAS,cAAc,KAAA,EAA2B;IAChD,MAAM,EAAE,cAAA,EAAgB,UAAA,EAAY,cAAA,EAAgB,cAAA,EAAgB,KAAA,CAAM,CAAA,oZAAI,sBAAA,CAAoB;IAClG,MAAM,EAAE,MAAA,EAAQ,QAAA,CAAS,CAAA,IAAI,sTAAA,CAAS;IAKtC,IAAI,CAAC,gBAAgB;QACnB,OAAO;IACT;IAEA,MAAM,UAAU;QACd;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IACA,MAAM,2TAAY,mBAAA,EAAiB,OAAO;IAQ1C,MAAM,SAAS,MAAM,MAAA,KAAW,QAAQ,mVAAW,UAAA;IAEnD,OACE,aAAA,GAAA,8YAAA,CAAA,UAAA,CAAA,aAAA,CAAC,QAAA;QACC,KAAK;QACL,wBAAoB;QACpB,OAAK;QAEL,OAAO,MAAM,MAAA,KAAW,UAAU,QAAQ,KAAA;QAC1C,aAAY;QACZ,UAAU,MAAM,MAAA,KAAW,UAAU,sBAAsB,KAAA;QAC1D,kTAAG,+BAAA,EAA6B,OAAO,CAAA;IAAA;AAG9C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/server/constants.ts"], "sourcesContent": ["import { apiUrlFromPublishableKey } from '@clerk/shared/apiUrlFromPublishableKey';\nimport { isTruthy } from '@clerk/shared/underscore';\n\nexport const CLERK_JS_VERSION = process.env.NEXT_PUBLIC_CLERK_JS_VERSION || '';\nexport const CLERK_JS_URL = process.env.NEXT_PUBLIC_CLERK_JS_URL || '';\nexport const API_VERSION = process.env.CLERK_API_VERSION || 'v1';\nexport const SECRET_KEY = process.env.CLERK_SECRET_KEY || '';\nexport const PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY || '';\nexport const ENCRYPTION_KEY = process.env.CLERK_ENCRYPTION_KEY || '';\nexport const API_URL = process.env.CLERK_API_URL || apiUrlFromPublishableKey(PUBLISHABLE_KEY);\nexport const DOMAIN = process.env.NEXT_PUBLIC_CLERK_DOMAIN || '';\nexport const PROXY_URL = process.env.NEXT_PUBLIC_CLERK_PROXY_URL || '';\nexport const IS_SATELLITE = isTruthy(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE) || false;\nexport const SIGN_IN_URL = process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL || '';\nexport const SIGN_UP_URL = process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL || '';\nexport const SDK_METADATA = {\n  name: PACKAGE_NAME,\n  version: PACKAGE_VERSION,\n  environment: process.env.NODE_ENV,\n};\n\nexport const TELEMETRY_DISABLED = isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED);\nexport const TELEMETRY_DEBUG = isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG);\n\nexport const KEYLESS_DISABLED = isTruthy(process.env.NEXT_PUBLIC_CLERK_KEYLESS_DISABLED) || false;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,SAAS,gCAAgC;;AACzC,SAAS,gBAAgB;;;;;AAElB,MAAM,mBAAmB,QAAQ,GAAA,CAAI,4BAAA,IAAgC;AACrE,MAAM,eAAe,QAAQ,GAAA,CAAI,wBAAA,IAA4B;AAC7D,MAAM,cAAc,QAAQ,GAAA,CAAI,iBAAA,IAAqB;AACrD,MAAM,aAAa,QAAQ,GAAA,CAAI,gBAAA,IAAoB;AACnD,MAAM,kBAAkB,QAAQ,IAAI,uFAAqC;AACzE,MAAM,iBAAiB,QAAQ,GAAA,CAAI,oBAAA,IAAwB;AAC3D,MAAM,UAAU,QAAQ,GAAA,CAAI,aAAA,mTAAiB,2BAAA,EAAyB,eAAe;AACrF,MAAM,SAAS,QAAQ,GAAA,CAAI,wBAAA,IAA4B;AACvD,MAAM,YAAY,QAAQ,GAAA,CAAI,2BAAA,IAA+B;AAC7D,MAAM,gBAAe,yTAAA,EAAS,QAAQ,GAAA,CAAI,8BAA8B,KAAK;AAC7E,MAAM,cAAc,QAAQ,IAAI,oCAAiC;AACjE,MAAM,cAAc,QAAQ,IAAI,oCAAiC;AACjE,MAAM,eAAe;IAC1B,MAAM;IACN,SAAS;IACT,WAAA,EAAa,QAAQ,IAAI;AAC3B;AAEO,MAAM,sBAAqB,yTAAA,CAAS,QAAQ,IAAI,oCAAoC;AACpF,MAAM,iUAAkB,WAAA,EAAS,QAAQ,GAAA,CAAI,iCAAiC;AAE9E,MAAM,kUAAmB,WAAA,EAAS,QAAQ,GAAA,CAAI,kCAAkC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 409, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/utils/sdk-versions.ts"], "sourcesContent": ["import nextPkg from 'next/package.json';\n\nconst isNext13 = nextPkg.version.startsWith('13.');\n\n/**\n * Those versions are affected by a bundling issue that will break the application if `node:fs` is used inside a server function.\n * The affected versions are >=next@13.5.4 and <=next@14.0.4\n */\nconst isNextWithUnstableServerActions = isNext13 || nextPkg.version.startsWith('14.0');\n\nexport { isNext13, isNextWithUnstableServerActions };\n"], "names": [], "mappings": ";;;;AAAA,OAAO,aAAa;;;AAEpB,MAAM,4TAAW,UAAA,CAAQ,OAAA,CAAQ,UAAA,CAAW,KAAK;AAMjD,MAAM,kCAAkC,6TAAY,UAAA,CAAQ,OAAA,CAAQ,UAAA,CAAW,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/utils/feature-flags.ts"], "sourcesContent": ["import { isDevelopmentEnvironment } from '@clerk/shared/utils';\n\nimport { KEYLESS_DISABLED } from '../server/constants';\nimport { isNextWithUnstableServerActions } from './sdk-versions';\n\nconst canUseKeyless =\n  !isNextWithUnstableServerActions &&\n  // Next.js will inline the value of 'development' or 'production' on the client bundle, so this is client-safe.\n  isDevelopmentEnvironment() &&\n  !KEYLESS_DISABLED;\n\nexport { canUseKeyless };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,gCAAgC;AAEzC,SAAS,wBAAwB;AACjC,SAAS,uCAAuC;;;;;AAEhD,MAAM,gBACJ,8XAAC,kCAAA,IAAA,+GAAA;+SAED,2BAAA,CAAyB,MACzB,yXAAC,mBAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/utils/mergeNextClerkPropsWithEnv.ts"], "sourcesContent": ["import { isTruthy } from '@clerk/shared/underscore';\n\nimport { SDK_METADATA } from '../server/constants';\nimport type { NextClerkProviderProps } from '../types';\n\n// @ts-ignore - https://github.com/microsoft/TypeScript/issues/47663\nexport const mergeNextClerkPropsWithEnv = (props: Omit<NextClerkProviderProps, 'children'>): any => {\n  return {\n    ...props,\n    publishableKey: props.publishableKey || process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY || '',\n    clerkJSUrl: props.clerkJSUrl || process.env.NEXT_PUBLIC_CLERK_JS_URL,\n    clerkJSVersion: props.clerkJSVersion || process.env.NEXT_PUBLIC_CLERK_JS_VERSION,\n    proxyUrl: props.proxyUrl || process.env.NEXT_PUBLIC_CLERK_PROXY_URL || '',\n    domain: props.domain || process.env.NEXT_PUBLIC_CLERK_DOMAIN || '',\n    isSatellite: props.isSatellite || isTruthy(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE),\n    signInUrl: props.signInUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL || '',\n    signUpUrl: props.signUpUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL || '',\n    signInForceRedirectUrl:\n      props.signInForceRedirectUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_IN_FORCE_REDIRECT_URL || '',\n    signUpForceRedirectUrl:\n      props.signUpForceRedirectUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_UP_FORCE_REDIRECT_URL || '',\n    signInFallbackRedirectUrl:\n      props.signInFallbackRedirectUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL || '',\n    signUpFallbackRedirectUrl:\n      props.signUpFallbackRedirectUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL || '',\n    afterSignInUrl: props.afterSignInUrl || process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL || '',\n    afterSignUpUrl: props.afterSignUpUrl || process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL || '',\n    newSubscriptionRedirectUrl:\n      props.newSubscriptionRedirectUrl || process.env.NEXT_PUBLIC_CLERK_CHECKOUT_CONTINUE_URL || '',\n    telemetry: props.telemetry ?? {\n      disabled: isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),\n      debug: isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG),\n    },\n    sdkMetadata: SDK_METADATA,\n  };\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,gBAAgB;;AAEzB,SAAS,oBAAoB;;;;AAItB,MAAM,6BAA6B,CAAC,UAAyD;IANpG,IAAA;IAOE,OAAO;QACL,GAAG,KAAA;QACH,gBAAgB,MAAM,cAAA,IAAkB,QAAQ,IAAI,uFAAqC;QACzF,YAAY,MAAM,UAAA,IAAc,QAAQ,GAAA,CAAI,wBAAA;QAC5C,gBAAgB,MAAM,cAAA,IAAkB,QAAQ,GAAA,CAAI,4BAAA;QACpD,UAAU,MAAM,QAAA,IAAY,QAAQ,GAAA,CAAI,2BAAA,IAA+B;QACvE,QAAQ,MAAM,MAAA,IAAU,QAAQ,GAAA,CAAI,wBAAA,IAA4B;QAChE,aAAa,MAAM,WAAA,mTAAe,WAAA,EAAS,QAAQ,GAAA,CAAI,8BAA8B;QACrF,WAAW,MAAM,SAAA,IAAa,QAAQ,IAAI,oCAAiC;QAC3E,WAAW,MAAM,SAAA,IAAa,QAAQ,IAAI,oCAAiC;QAC3E,wBACE,MAAM,sBAAA,IAA0B,QAAQ,GAAA,CAAI,4CAAA,IAAgD;QAC9F,wBACE,MAAM,sBAAA,IAA0B,QAAQ,GAAA,CAAI,4CAAA,IAAgD;QAC9F,2BACE,MAAM,yBAAA,IAA6B,QAAQ,IAAI,sCAAmD;QACpG,2BACE,MAAM,yBAAA,IAA6B,QAAQ,IAAI,uCAAmD;QACpG,gBAAgB,MAAM,cAAA,IAAkB,QAAQ,GAAA,CAAI,mCAAA,IAAuC;QAC3F,gBAAgB,MAAM,cAAA,IAAkB,QAAQ,GAAA,CAAI,mCAAA,IAAuC;QAC3F,4BACE,MAAM,0BAAA,IAA8B,QAAQ,GAAA,CAAI,uCAAA,IAA2C;QAC7F,WAAA,CAAW,KAAA,MAAM,SAAA,KAAN,OAAA,KAAmB;YAC5B,yTAAU,WAAA,CAAS,QAAQ,IAAI,oCAAoC;YACnE,sTAAO,WAAA,EAAS,QAAQ,GAAA,CAAI,iCAAiC;QAC/D;QACA,qYAAa,eAAA;IACf;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/client-boundary/hooks/usePagesRouter.tsx"], "sourcesContent": ["import { useRouter } from 'next/compat/router';\n\nexport const usePagesRouter = () => {\n  // The compat version of useRouter returns null instead of throwing an error\n  // when used inside app router instead of pages router\n  // we use it to detect if the component is used inside pages or app router\n  // so we can use the correct algorithm to get the path\n  return { pagesRouter: useRouter() };\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,iBAAiB;;;AAEnB,MAAM,iBAAiB,MAAM;IAKlC,OAAO;QAAE,mWAAa,YAAA,CAAU;IAAE;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 509, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/utils/router-telemetry.ts"], "sourcesContent": ["import { eventFrameworkMetadata } from '@clerk/shared/telemetry';\n\nimport { useClerk } from '../client-boundary/hooks';\nimport { usePagesRouter } from '../client-boundary/hooks/usePagesRouter';\n\nconst RouterTelemetry = () => {\n  const clerk = useClerk();\n  const { pagesRouter } = usePagesRouter();\n\n  /**\n   * Caching and throttling is handled internally it's safe to execute on every navigation.\n   */\n  clerk.telemetry?.record(\n    eventFrameworkMetadata({\n      router: pagesRouter ? 'pages' : 'app',\n      ...(globalThis?.next?.version ? { nextjsVersion: globalThis.next.version } : {}),\n    }),\n  );\n\n  return null;\n};\n\nexport { RouterTelemetry };\n"], "names": [], "mappings": ";;;AAAA,SAAS,8BAA8B;;AAEvC,SAAS,gBAAgB;AACzB,SAAS,sBAAsB;;;;;AAE/B,MAAM,kBAAkB,MAAM;IAL9B,IAAA,IAAA;IAME,MAAM,oTAAQ,WAAA,CAAS;IACvB,MAAM,EAAE,WAAA,CAAY,CAAA,yZAAI,iBAAA,CAAe;IAKvC,CAAA,KAAA,MAAM,SAAA,KAAN,OAAA,KAAA,IAAA,GAAiB,MAAA,gTACf,yBAAA,EAAuB;QACrB,QAAQ,cAAc,UAAU;QAChC,GAAA,CAAA,CAAI,KAAA,cAAA,OAAA,KAAA,IAAA,WAAY,IAAA,KAAZ,OAAA,KAAA,IAAA,GAAkB,OAAA,IAAU;YAAE,eAAe,WAAW,IAAA,CAAK,OAAA;QAAQ,IAAI,CAAC,CAAA;IAChF,CAAC;IAGH,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 540, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/dist/esm/app-router/server-actions.js"], "sourcesContent": ["\"use server\";\nimport { cookies } from \"next/headers\";\nasync function invalidateCacheAction() {\n  void (await cookies()).delete(`__clerk_invalidate_cache_cookie_${Date.now()}`);\n}\nexport {\n  invalidateCacheAction\n};\n"], "names": [], "mappings": ";;;;;;IAME,wBAAA,WAAA,GAAA,CAAA,GAAA,+ZAAA,CAAA,wBAAA,EAAA,8CAAA,+ZAAA,CAAA,aAAA,EAAA,KAAA,GAAA,+ZAAA,CAAA,mBAAA,EAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/utils/removeBasePath.ts"], "sourcesContent": ["/**\n * Removes the Next.js basePath from the provided destination if set.\n * @param to Destination route to navigate to\n * @returns Destination without basePath, if set\n */\nexport function removeBasePath(to: string): string {\n  let destination = to;\n  const basePath = process.env.__NEXT_ROUTER_BASEPATH;\n  if (basePath && destination.startsWith(basePath)) {\n    destination = destination.slice(basePath.length);\n  }\n\n  return destination;\n}\n"], "names": [], "mappings": ";;;;AAKO,SAAS,eAAe,EAAA,EAAoB;IACjD,IAAI,cAAc;IAClB,MAAM,WAAW,QAAQ,IAAI;IAC7B,IAAI,YAAY,YAAY,UAAA,CAAW,QAAQ,GAAG;;IAElD;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 573, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/app-router/client/useInternalNavFun.ts"], "sourcesContent": ["import type { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';\nimport { usePathname } from 'next/navigation';\nimport { useCallback, useEffect, useTransition } from 'react';\n\nimport { removeBasePath } from '../../utils/removeBasePath';\n\nconst getClerkNavigationObject = (name: string) => {\n  window.__clerk_internal_navigations ??= {};\n  // @ts-ignore\n  window.__clerk_internal_navigations[name] ??= {};\n  return window.__clerk_internal_navigations[name];\n};\n\nexport const useInternalNavFun = (props: {\n  windowNav: typeof window.history.pushState | typeof window.history.replaceState | undefined;\n  routerNav: AppRouterInstance['push'] | AppRouterInstance['replace'];\n  name: string;\n}): NavigationFunction => {\n  const { windowNav, routerNav, name } = props;\n  const pathname = usePathname();\n  const [isPending, startTransition] = useTransition();\n\n  if (windowNav) {\n    getClerkNavigationObject(name).fun = (to, opts) => {\n      return new Promise<void>(res => {\n        // We need to use window to store the reference to the buffer,\n        // as ClerkProvider might be unmounted and remounted during navigations\n        // If we use a ref, it will be reset when ClerkProvider is unmounted\n        getClerkNavigationObject(name).promisesBuffer ??= [];\n        getClerkNavigationObject(name).promisesBuffer?.push(res);\n        startTransition(() => {\n          // If the navigation is internal, we should use the history API to navigate\n          // as this is the way to perform a shallow navigation in Next.js App Router\n          // without unmounting/remounting the page or fetching data from the server.\n          if (opts?.__internal_metadata?.navigationType === 'internal') {\n            // In 14.1.0, useSearchParams becomes reactive to shallow updates,\n            // but only if passing `null` as the history state.\n            // Older versions need to maintain the history state for push/replace to work,\n            // without affecting how the Next router works.\n            const state = ((window as any).next?.version ?? '') < '14.1.0' ? history.state : null;\n            windowNav(state, '', to);\n          } else {\n            // If the navigation is external (usually when navigating away from the component but still within the app),\n            // we should use the Next.js router to navigate as it will handle updating the URL and also\n            // fetching the new page if necessary.\n            routerNav(removeBasePath(to));\n          }\n        });\n      });\n    };\n  }\n\n  const flushPromises = () => {\n    getClerkNavigationObject(name).promisesBuffer?.forEach(resolve => resolve());\n    getClerkNavigationObject(name).promisesBuffer = [];\n  };\n\n  // Flush any pending promises on mount/unmount\n  useEffect(() => {\n    flushPromises();\n    return flushPromises;\n  }, []);\n\n  // Handle flushing the promise buffer when a navigation happens\n  useEffect(() => {\n    if (!isPending) {\n      flushPromises();\n    }\n  }, [pathname, isPending]);\n\n  return useCallback<NavigationFunction>((to, metadata) => {\n    return getClerkNavigationObject(name).fun(to, metadata);\n    // We are not expecting name to change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n};\n"], "names": ["_a", "_b", "_c"], "mappings": ";;;AACA,SAAS,mBAAmB;AAC5B,SAAS,aAAa,WAAW,qBAAqB;AAEtD,SAAS,sBAAsB;;;;;AAE/B,MAAM,2BAA2B,CAAC,SAAiB;IANnD,IAAA,IAAA,IAAA;IAOE,CAAA,KAAA,OAAO,4BAAA,KAAP,OAAA,KAAA,OAAO,4BAAA,GAAiC,CAAC;IAEzC,CAAA,KAAA,CAAA,KAAA,OAAO,4BAAA,CAAA,CAAP,KAAA,KAAA,OAAA,KAAA,EAAA,CAAA,KAAA,GAA8C,CAAC;IAC/C,OAAO,OAAO,4BAAA,CAA6B,IAAI,CAAA;AACjD;AAEO,MAAM,oBAAoB,CAAC,UAIR;IACxB,MAAM,EAAE,SAAA,EAAW,SAAA,EAAW,IAAA,CAAK,CAAA,GAAI;IACvC,MAAM,eAAW,0VAAA,CAAY;IAC7B,MAAM,CAAC,WAAW,eAAe,CAAA,sZAAI,gBAAA,CAAc;IAEnD,IAAI,WAAW;QACb,yBAAyB,IAAI,EAAE,GAAA,GAAM,CAAC,IAAI,SAAS;YACjD,OAAO,IAAI,QAAc,CAAA,QAAO;gBAxBtC,IAAA,IAAA,IAAA;gBA4BQ,CAAA,KAAA,CAAA,KAAA,yBAAyB,IAAI,CAAA,EAAE,cAAA,KAA/B,OAAA,KAAA,GAA+B,cAAA,GAAmB,CAAC,CAAA;gBACnD,CAAA,KAAA,yBAAyB,IAAI,EAAE,cAAA,KAA/B,OAAA,KAAA,IAAA,GAA+C,IAAA,CAAK;gBACpD,gBAAgB,MAAM;oBA9B9B,IAAAA,KAAAC,KAAAC;oBAkCU,IAAA,CAAA,CAAIF,MAAA,QAAA,OAAA,KAAA,IAAA,KAAM,mBAAA,KAAN,OAAA,KAAA,IAAAA,IAA2B,cAAA,MAAmB,YAAY;wBAK5D,MAAM,QAAA,CAAA,CAAUE,MAAAA,CAAAD,MAAA,OAAe,IAAA,KAAf,OAAA,KAAA,IAAAA,IAAqB,OAAA,KAArB,OAAAC,MAAgC,EAAA,IAAM,WAAW,QAAQ,KAAA,GAAQ;wBACjF,UAAU,OAAO,IAAI,EAAE;oBACzB,OAAO;wBAIL,0YAAU,iBAAA,EAAe,EAAE,CAAC;oBAC9B;gBACF,CAAC;YACH,CAAC;QACH;IACF;IAEA,MAAM,gBAAgB,MAAM;QApD9B,IAAA;QAqDI,CAAA,KAAA,yBAAyB,IAAI,EAAE,cAAA,KAA/B,OAAA,KAAA,IAAA,GAA+C,OAAA,CAAQ,CAAA,UAAW,QAAQ;QAC1E,yBAAyB,IAAI,EAAE,cAAA,GAAiB,CAAC,CAAA;IACnD;IAGA,CAAA,GAAA,8YAAA,CAAA,YAAA,EAAU,MAAM;QACd,cAAc;QACd,OAAO;IACT,GAAG,CAAC,CAAC;IAGL,CAAA,GAAA,8YAAA,CAAA,YAAA,EAAU,MAAM;QACd,IAAI,CAAC,WAAW;YACd,cAAc;QAChB;IACF,GAAG;QAAC;QAAU,SAAS;KAAC;IAExB,0ZAAO,cAAA,EAAgC,CAAC,IAAI,aAAa;QACvD,OAAO,yBAAyB,IAAI,EAAE,GAAA,CAAI,IAAI,QAAQ;IAGxD,GAAG,CAAC,CAAC;AACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 640, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/app-router/client/useAwaitablePush.ts"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\n\nimport { useInternalNavFun } from './useInternalNavFun';\n\n/**\n * Creates an \"awaitable\" navigation function that will do its best effort to wait for Next.js to finish its route transition.\n * This is accomplished by wrapping the call to `router.push` in `startTransition()`, which should rely on <PERSON>act to coordinate the pending state. We key off of\n * `isPending` to flush the stored promises and ensure the navigates \"resolve\".\n */\nexport const useAwaitablePush = () => {\n  const router = useRouter();\n\n  return useInternalNavFun({\n    windowNav: typeof window !== 'undefined' ? window.history.pushState.bind(window.history) : undefined,\n    routerNav: router.push.bind(router),\n    name: 'push',\n  });\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,iBAAiB;AAE1B,SAAS,yBAAyB;;;;;AAO3B,MAAM,mBAAmB,MAAM;IACpC,MAAM,yVAAS,YAAA,CAAU;IAEzB,4ZAAO,oBAAA,EAAkB;QACvB,WAAW,OAAO,WAAW,cAAc,OAAO,OAAA,CAAQ,SAAA,CAAU,IAAA,CAAK,OAAO,OAAO,IAAI,KAAA;QAC3F,WAAW,OAAO,IAAA,CAAK,IAAA,CAAK,MAAM;QAClC,MAAM;IACR,CAAC;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 665, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/app-router/client/useAwaitableReplace.ts"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\n\nimport { useInternalNavFun } from './useInternalNavFun';\n\n/**\n * Creates an \"awaitable\" navigation function that will do its best effort to wait for Next.js to finish its route transition.\n * This is accomplished by wrapping the call to `router.replace` in `startTransition()`, which should rely on <PERSON>act to coordinate the pending state. We key off of\n * `isPending` to flush the stored promises and ensure the navigates \"resolve\".\n */\nexport const useAwaitableReplace = () => {\n  const router = useRouter();\n\n  return useInternalNavFun({\n    windowNav: typeof window !== 'undefined' ? window.history.replaceState.bind(window.history) : undefined,\n    routerNav: router.replace.bind(router),\n    name: 'replace',\n  });\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,iBAAiB;AAE1B,SAAS,yBAAyB;;;;;AAO3B,MAAM,sBAAsB,MAAM;IACvC,MAAM,yVAAS,YAAA,CAAU;IAEzB,4ZAAO,oBAAA,EAAkB;QACvB,WAAW,OAAO,WAAW,cAAc,OAAO,OAAA,CAAQ,YAAA,CAAa,IAAA,CAAK,OAAO,OAAO,IAAI,KAAA;QAC9F,WAAW,OAAO,OAAA,CAAQ,IAAA,CAAK,MAAM;QACrC,MAAM;IACR,CAAC;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/app-router/client/ClerkProvider.tsx"], "sourcesContent": ["'use client';\nimport { <PERSON><PERSON><PERSON><PERSON> as ReactClerkProvider } from '@clerk/clerk-react';\nimport { inBrowser } from '@clerk/shared/browser';\nimport { logger } from '@clerk/shared/logger';\nimport dynamic from 'next/dynamic';\nimport { useRouter } from 'next/navigation';\nimport nextPackage from 'next/package.json';\nimport React, { useEffect, useTransition } from 'react';\n\nimport { useSafeLayoutEffect } from '../../client-boundary/hooks/useSafeLayoutEffect';\nimport { ClerkNextOptionsProvider, useClerkNextOptions } from '../../client-boundary/NextOptionsContext';\nimport type { NextClerkProviderProps } from '../../types';\nimport { ClerkJSScript } from '../../utils/clerk-js-script';\nimport { canUseKeyless } from '../../utils/feature-flags';\nimport { mergeNextClerkPropsWithEnv } from '../../utils/mergeNextClerkPropsWithEnv';\nimport { RouterTelemetry } from '../../utils/router-telemetry';\nimport { isNextWithUnstableServerActions } from '../../utils/sdk-versions';\nimport { invalidateCacheAction } from '../server-actions';\nimport { useAwaitablePush } from './useAwaitablePush';\nimport { useAwaitableReplace } from './useAwaitableReplace';\n\n/**\n * LazyCreateKeylessApplication should only be loaded if the conditions below are met.\n * Note: Using lazy() with Suspense instead of dynamic is not possible as React will throw a hydration error when `ClerkProvider` wraps `<html><body>...`\n */\nconst LazyCreateKeylessApplication = dynamic(() =>\n  import('./keyless-creator-reader.js').then(m => m.KeylessCreatorOrReader),\n);\n\nconst NextClientClerkProvider = (props: NextClerkProviderProps) => {\n  if (isNextWithUnstableServerActions) {\n    const deprecationWarning = `Clerk:\\nYour current Next.js version (${nextPackage.version}) will be deprecated in the next major release of \"@clerk/nextjs\". Please upgrade to next@14.1.0 or later.`;\n    if (inBrowser()) {\n      logger.warnOnce(deprecationWarning);\n    } else {\n      logger.logOnce(`\\n\\x1b[43m----------\\n${deprecationWarning}\\n----------\\x1b[0m\\n`);\n    }\n  }\n\n  const { __unstable_invokeMiddlewareOnAuthStateChange = true, children } = props;\n  const router = useRouter();\n  const push = useAwaitablePush();\n  const replace = useAwaitableReplace();\n  const [isPending, startTransition] = useTransition();\n\n  // Avoid rendering nested ClerkProviders by checking for the existence of the ClerkNextOptions context provider\n  const isNested = Boolean(useClerkNextOptions());\n  if (isNested) {\n    return props.children;\n  }\n\n  useEffect(() => {\n    if (!isPending) {\n      window.__clerk_internal_invalidateCachePromise?.();\n    }\n  }, [isPending]);\n\n  useSafeLayoutEffect(() => {\n    window.__unstable__onBeforeSetActive = intent => {\n      /**\n       * We need to invalidate the cache in case the user is navigating to a page that\n       * was previously cached using the auth state that was active at the time.\n       *\n       *  We also need to await for the invalidation to happen before we navigate,\n       * otherwise the navigation will use the cached page.\n       *\n       * For example, if we did not invalidate the flow, the following scenario would be broken:\n       * - The middleware is configured in such a way that it redirects you back to the same page if a certain condition is true (eg, you need to pick an org)\n       * - The user has a <Link href=/> component in the page\n       * - The UB is mounted with afterSignOutUrl=/\n       * - The user clicks the Link. A nav to / happens, a 307 to the current page is returned so a navigation does not take place. The / navigation is now cached as a 307 to the current page\n       * - The user clicks sign out\n       * - We call router.refresh()\n       * - We navigate to / but its cached and instead, we 'redirect' to the current page\n       *\n       *  For more information on cache invalidation, see:\n       * https://nextjs.org/docs/app/building-your-application/caching#invalidation-1\n       */\n      return new Promise(resolve => {\n        window.__clerk_internal_invalidateCachePromise = resolve;\n\n        const nextVersion = window?.next?.version || '';\n\n        // ATTENTION: Avoid using wrapping code with `startTransition` on versions >= 14\n        // otherwise the fetcher of `useReverification()` will be pending indefinitely when called within `startTransition`.\n        if (nextVersion.startsWith('13')) {\n          startTransition(() => {\n            router.refresh();\n          });\n        }\n        // On Next.js v15 calling a server action that returns a 404 error when deployed on Vercel is prohibited, failing with 405 status code.\n        // When a user transitions from \"signed in\" to \"singed out\", we clear the `__session` cookie, then we call `__unstable__onBeforeSetActive`.\n        // If we were to call `invalidateCacheAction` while the user is already signed out (deleted cookie), any page protected by `auth.protect()`\n        // will result to the server action returning a 404 error (this happens because server actions inherit the protection rules of the page they are called from).\n        // SOLUTION:\n        // To mitigate this, since the router cache on version 15 is much less aggressive, we can treat this as a noop and simply resolve the promise.\n        // Once `setActive` performs the navigation, `__unstable__onAfterSetActive` will kick in and perform a router.refresh ensuring shared layouts will also update with the correct authentication context.\n        else if (nextVersion.startsWith('15') && intent === 'sign-out') {\n          resolve(); // noop\n        } else {\n          void invalidateCacheAction().then(() => resolve());\n        }\n      });\n    };\n\n    window.__unstable__onAfterSetActive = () => {\n      if (__unstable_invokeMiddlewareOnAuthStateChange) {\n        return router.refresh();\n      }\n    };\n  }, []);\n\n  const mergedProps = mergeNextClerkPropsWithEnv({\n    ...props,\n    // @ts-expect-error Error because of the stricter types of internal `push`\n    routerPush: push,\n    // @ts-expect-error Error because of the stricter types of internal `replace`\n    routerReplace: replace,\n  });\n\n  return (\n    <ClerkNextOptionsProvider options={mergedProps}>\n      <ReactClerkProvider {...mergedProps}>\n        <RouterTelemetry />\n        <ClerkJSScript router='app' />\n        {children}\n      </ReactClerkProvider>\n    </ClerkNextOptionsProvider>\n  );\n};\n\nexport const ClientClerkProvider = (props: NextClerkProviderProps & { disableKeyless?: boolean }) => {\n  const { children, disableKeyless = false, ...rest } = props;\n  const safePublishableKey = mergeNextClerkPropsWithEnv(rest).publishableKey;\n\n  if (safePublishableKey || !canUseKeyless || disableKeyless) {\n    return <NextClientClerkProvider {...rest}>{children}</NextClientClerkProvider>;\n  }\n\n  return (\n    <LazyCreateKeylessApplication>\n      <NextClientClerkProvider {...rest}>{children}</NextClientClerkProvider>\n    </LazyCreateKeylessApplication>\n  );\n};\n"], "names": [], "mappings": ";;;AACA,SAAS,iBAAiB,0BAA0B;;AACpD,SAAS,iBAAiB;;AAC1B,SAAS,cAAc;;AACvB,OAAO,aAAa;AACpB,SAAS,iBAAiB;AAC1B,OAAO,iBAAiB;AACxB,OAAO,SAAS,WAAW,qBAAqB;AAEhD,SAAS,2BAA2B;AACpC,SAAS,0BAA0B,2BAA2B;AAE9D,SAAS,qBAAqB;AAC9B,SAAS,qBAAqB;AAC9B,SAAS,kCAAkC;AAC3C,SAAS,uBAAuB;AAChC,SAAS,uCAAuC;AAChD,SAAS,6BAA6B;AACtC,SAAS,wBAAwB;AACjC,SAAS,2BAA2B;;;;;;;;;;;;;;;;;;;;AAMpC,MAAM,4YAA+B,UAAA,EAAQ,IAC3C,OAAO,6BAA6B,2RAAE,IAAA,CAAK,CAAA,IAAK,EAAE,sBAAsB;AAG1E,MAAM,0BAA0B,CAAC,UAAkC;IACjE,iYAAI,kCAAA,EAAiC;QACnC,MAAM,qBAAqB,CAAA;8BAAA,mTAAyC,UAAA,CAAY,OAAO,CAAA,0GAAA,CAAA;QACvF,IAAI,2TAAA,CAAU,IAAG;YACf,0SAAA,CAAA,SAAA,CAAO,QAAA,CAAS,kBAAkB;QACpC,OAAO;YACL,0SAAA,CAAA,SAAA,CAAO,OAAA,CAAQ,CAAA;;AAAA,EAAyB,kBAAkB,CAAA;;AAAA,CAAuB;QACnF;IACF;IAEA,MAAM,EAAE,+CAA+C,IAAA,EAAM,QAAA,CAAS,CAAA,GAAI;IAC1E,MAAM,yVAAS,YAAA,CAAU;IACzB,MAAM,2ZAAO,mBAAA,CAAiB;IAC9B,MAAM,iaAAU,sBAAA,CAAoB;IACpC,MAAM,CAAC,WAAW,eAAe,CAAA,sZAAI,gBAAA,CAAc;IAGnD,MAAM,WAAW,QAAQ,uaAAA,CAAoB,CAAC;IAC9C,IAAI,UAAU;QACZ,OAAO,MAAM,QAAA;IACf;IAEA,CAAA,GAAA,8YAAA,CAAA,YAAA,EAAU,MAAM;QAnDlB,IAAA;QAoDI,IAAI,CAAC,WAAW;YACd,CAAA,KAAA,OAAO,uCAAA,KAAP,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA;QACF;IACF,GAAG;QAAC,SAAS;KAAC;IAEd,CAAA,GAAA,sZAAA,CAAA,sBAAA,EAAoB,MAAM;QACxB,OAAO,6BAAA,GAAgC,CAAA,WAAU;YAoB/C,OAAO,IAAI,QAAQ,CAAA,YAAW;gBA9EpC,IAAA;gBA+EQ,OAAO,uCAAA,GAA0C;gBAEjD,MAAM,cAAA,CAAA,CAAc,KAAA,UAAA,OAAA,KAAA,IAAA,OAAQ,IAAA,KAAR,OAAA,KAAA,IAAA,GAAc,OAAA,KAAW;gBAI7C,IAAI,YAAY,UAAA,CAAW,IAAI,GAAG;oBAChC,gBAAgB,MAAM;wBACpB,OAAO,OAAA,CAAQ;oBACjB,CAAC;gBACH,OAAA,IAQS,YAAY,UAAA,CAAW,IAAI,KAAK,WAAW,YAAY;oBAC9D,QAAQ;gBACV,OAAO;oBACL,maAAK,wBAAA,CAAsB,GAAE,IAAA,CAAK,IAAM,QAAQ,CAAC;gBACnD;YACF,CAAC;QACH;QAEA,OAAO,4BAAA,GAA+B,MAAM;YAC1C,IAAI,8CAA8C;gBAChD,OAAO,OAAO,OAAA,CAAQ;YACxB;QACF;IACF,GAAG,CAAC,CAAC;IAEL,MAAM,0ZAAc,6BAAA,EAA2B;QAC7C,GAAG,KAAA;QAAA,0EAAA;QAEH,YAAY;QAAA,6EAAA;QAEZ,eAAe;IACjB,CAAC;IAED,OACE,aAAA,GAAA,8YAAA,CAAA,UAAA,CAAA,aAAA,8YAAC,2BAAA,EAAA;QAAyB,SAAS;IAAA,GACjC,aAAA,GAAA,8YAAA,CAAA,UAAA,CAAA,aAAA,gUAAC,iBAAA,EAAA;QAAoB,GAAG,WAAA;IAAA,GACtB,aAAA,GAAA,8YAAA,CAAA,UAAA,CAAA,aAAA,kYAAC,kBAAA,EAAA,IAAgB,GACjB,aAAA,GAAA,8YAAA,CAAA,UAAA,CAAA,aAAA,CAAC,mZAAA,EAAA;QAAc,QAAO;IAAA,CAAM,GAC3B,QACH,CACF;AAEJ;AAEO,MAAM,sBAAsB,CAAC,UAAiE;IACnG,MAAM,EAAE,QAAA,EAAU,iBAAiB,KAAA,EAAO,GAAG,KAAK,CAAA,GAAI;IACtD,MAAM,qBAAqB,yaAAA,EAA2B,IAAI,EAAE,cAAA;IAE5D,IAAI,sBAAsB,+XAAC,gBAAA,IAAiB,gBAAgB;QAC1D,OAAO,aAAA,GAAA,8YAAA,CAAA,UAAA,CAAA,aAAA,CAAC,yBAAA;YAAyB,GAAG,IAAA;QAAA,GAAO,QAAS;IACtD;IAEA,OACE,aAAA,GAAA,8YAAA,CAAA,UAAA,CAAA,aAAA,CAAC,8BAAA,MACC,aAAA,GAAA,8YAAA,CAAA,UAAA,CAAA,aAAA,CAAC,yBAAA;QAAyB,GAAG,IAAA;IAAA,GAAO,QAAS,CAC/C;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 822, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/app-router/client/keyless-cookie-sync.tsx"], "sourcesContent": ["'use client';\n\nimport type { AccountlessApplication } from '@clerk/backend';\nimport { useSelectedLayoutSegments } from 'next/navigation';\nimport type { PropsWithChildren } from 'react';\nimport { useEffect } from 'react';\n\nimport { canUseKeyless } from '../../utils/feature-flags';\n\nexport function KeylessCookieSync(props: PropsWithChildren<AccountlessApplication>) {\n  const segments = useSelectedLayoutSegments();\n  const isNotFoundRoute = segments[0]?.startsWith('/_not-found') || false;\n\n  useEffect(() => {\n    if (canUseKeyless && !isNotFoundRoute) {\n      void import('../keyless-actions.js').then(m =>\n        m.syncKeylessConfigAction({\n          ...props,\n          // Preserve the current url and return back, once keys are synced in the middleware\n          returnUrl: window.location.href,\n        }),\n      );\n    }\n  }, [isNotFoundRoute]);\n\n  return props.children;\n}\n"], "names": [], "mappings": ";;;AAGA,SAAS,iCAAiC;AAE1C,SAAS,iBAAiB;AAE1B,SAAS,qBAAqB;;;;;;AAEvB,SAAS,kBAAkB,KAAA,EAAkD;IATpF,IAAA;IAUE,MAAM,2VAAW,4BAAA,CAA0B;IAC3C,MAAM,kBAAA,CAAA,CAAkB,KAAA,QAAA,CAAS,CAAC,CAAA,KAAV,OAAA,KAAA,IAAA,GAAa,UAAA,CAAW,cAAA,KAAkB;IAElE,CAAA,GAAA,8YAAA,CAAA,YAAA,EAAU,MAAM;QACd,kYAAI,gBAAA,IAAiB,CAAC,iBAAiB;YACrC,KAAK,OAAO,uBAAuB,mRAAE,IAAA,CAAK,CAAA,IACxC,EAAE,uBAAA,CAAwB;oBACxB,GAAG,KAAA;oBAAA,mFAAA;oBAEH,WAAW,OAAO,QAAA,CAAS,IAAA;gBAC7B,CAAC;QAEL;IACF,GAAG;QAAC,eAAe;KAAC;IAEpB,OAAO,MAAM,QAAA;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 857, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40swc%2Bhelpers%400.5.15/node_modules/%40swc/helpers/cjs/_interop_require_default.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexports._ = _interop_require_default;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,yBAAyB,GAAG;IACjC,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,SAAS;IAAI;AACxD;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 869, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40swc%2Bhelpers%400.5.15/node_modules/%40swc/helpers/cjs/_interop_require_wildcard.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) return obj;\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") return { default: obj };\n\n    var cache = _getRequireWildcardCache(nodeInterop);\n\n    if (cache && cache.has(obj)) return cache.get(obj);\n\n    var newObj = { __proto__: null };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n\n    for (var key in obj) {\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);\n            else newObj[key] = obj[key];\n        }\n    }\n\n    newObj.default = obj;\n\n    if (cache) cache.set(obj, newObj);\n\n    return newObj;\n}\nexports._ = _interop_require_wildcard;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,yBAAyB,WAAW;IACzC,IAAI,OAAO,YAAY,YAAY,OAAO;IAE1C,IAAI,oBAAoB,IAAI;IAC5B,IAAI,mBAAmB,IAAI;IAE3B,OAAO,CAAC,2BAA2B,SAAS,WAAW;QACnD,OAAO,cAAc,mBAAmB;IAC5C,CAAC,EAAE;AACP;AACA,SAAS,0BAA0B,GAAG,EAAE,WAAW;IAC/C,IAAI,CAAC,eAAe,OAAO,IAAI,UAAU,EAAE,OAAO;IAClD,IAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY,OAAO;QAAE,SAAS;IAAI;IAEhG,IAAI,QAAQ,yBAAyB;IAErC,IAAI,SAAS,MAAM,GAAG,CAAC,MAAM,OAAO,MAAM,GAAG,CAAC;IAE9C,IAAI,SAAS;QAAE,WAAW;IAAK;IAC/B,IAAI,wBAAwB,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAEpF,IAAK,IAAI,OAAO,IAAK;QACjB,IAAI,QAAQ,aAAa,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM;YACrE,IAAI,OAAO,wBAAwB,OAAO,wBAAwB,CAAC,KAAK,OAAO;YAC/E,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG,OAAO,cAAc,CAAC,QAAQ,KAAK;iBAClE,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;QAC/B;IACJ;IAEA,OAAO,OAAO,GAAG;IAEjB,IAAI,OAAO,MAAM,GAAG,CAAC,KAAK;IAE1B,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 907, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40tanstack%2Breact-query%405.80.7_react%4019.1.0/node_modules/%40tanstack/react-query/src/QueryClientProvider.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nimport type { QueryClient } from '@tanstack/query-core'\n\nexport const QueryClientContext = React.createContext<QueryClient | undefined>(\n  undefined,\n)\n\nexport const useQueryClient = (queryClient?: QueryClient) => {\n  const client = React.useContext(QueryClientContext)\n\n  if (queryClient) {\n    return queryClient\n  }\n\n  if (!client) {\n    throw new Error('No QueryClient set, use QueryClientProvider to set one')\n  }\n\n  return client\n}\n\nexport type QueryClientProviderProps = {\n  client: QueryClient\n  children?: React.ReactNode\n}\n\nexport const QueryClientProvider = ({\n  client,\n  children,\n}: QueryClientProviderProps): React.JSX.Element => {\n  React.useEffect(() => {\n    client.mount()\n    return () => {\n      client.unmount()\n    }\n  }, [client])\n\n  return (\n    <QueryClientContext.Provider value={client}>\n      {children}\n    </QueryClientContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AACA,YAAY,WAAW;AAuCnB;;;;AAnCG,IAAM,waAA2B,gBAAA,EACtC,KAAA;AAGK,IAAM,iBAAiB,CAAC,gBAA8B;IAC3D,MAAM,UAAe,+ZAAA,EAAW,kBAAkB;IAElD,IAAI,aAAa;QACf,OAAO;IACT;IAEA,IAAI,CAAC,QAAQ;QACX,MAAM,IAAI,MAAM,wDAAwD;IAC1E;IAEA,OAAO;AACT;AAOO,IAAM,sBAAsB,CAAC,EAClC,MAAA,EACA,QAAA,EACF,KAAmD;uZAC3C,YAAA,EAAU,MAAM;QACpB,OAAO,KAAA,CAAM;QACb,OAAO,MAAM;YACX,OAAO,OAAA,CAAQ;QACjB;IACF,GAAG;QAAC,MAAM;KAAC;IAEX,OACE,aAAA,GAAA,CAAA,GAAA,gaAAA,CAAA,MAAA,EAAC,mBAAmB,QAAA,EAAnB;QAA4B,OAAO;QACjC;IAAA,CACH;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 951, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40tanstack%2Breact-query%405.80.7_react%4019.1.0/node_modules/%40tanstack/react-query/src/infiniteQueryOptions.ts"], "sourcesContent": ["import type {\n  DataTag,\n  DefaultError,\n  InfiniteData,\n  InitialDataFunction,\n  NonUndefinedGuard,\n  Omit<PERSON>eyof,\n  Query<PERSON>ey,\n  SkipToken,\n} from '@tanstack/query-core'\nimport type { UseInfiniteQueryOptions } from './types'\n\nexport type UndefinedInitialDataInfiniteOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n> = UseInfiniteQueryOptions<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey,\n  TPageParam\n> & {\n  initialData?:\n    | undefined\n    | NonUndefinedGuard<InfiniteData<TQueryFnData, TPageParam>>\n    | InitialDataFunction<\n        NonUndefinedGuard<InfiniteData<TQueryFnData, TPageParam>>\n      >\n}\n\nexport type UnusedSkipTokenInfiniteOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQ<PERSON>y<PERSON>ey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n> = OmitKeyof<\n  UseInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey, TPageParam>,\n  'queryFn'\n> & {\n  queryFn?: Exclude<\n    UseInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >['queryFn'],\n    SkipToken | undefined\n  >\n}\n\nexport type DefinedInitialDataInfiniteOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n> = UseInfiniteQueryOptions<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey,\n  TPageParam\n> & {\n  initialData:\n    | NonUndefinedGuard<InfiniteData<TQueryFnData, TPageParam>>\n    | (() => NonUndefinedGuard<InfiniteData<TQueryFnData, TPageParam>>)\n    | undefined\n}\n\nexport function infiniteQueryOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: DefinedInitialDataInfiniteOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n): DefinedInitialDataInfiniteOptions<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey,\n  TPageParam\n> & {\n  queryKey: DataTag<TQueryKey, InfiniteData<TQueryFnData>, TError>\n}\n\nexport function infiniteQueryOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: UnusedSkipTokenInfiniteOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n): UnusedSkipTokenInfiniteOptions<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey,\n  TPageParam\n> & {\n  queryKey: DataTag<TQueryKey, InfiniteData<TQueryFnData>, TError>\n}\n\nexport function infiniteQueryOptions<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: UndefinedInitialDataInfiniteOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n): UndefinedInitialDataInfiniteOptions<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey,\n  TPageParam\n> & {\n  queryKey: DataTag<TQueryKey, InfiniteData<TQueryFnData>, TError>\n}\n\nexport function infiniteQueryOptions(options: unknown) {\n  return options\n}\n"], "names": [], "mappings": ";;;;AAkJO,SAAS,qBAAqB,OAAA,EAAkB;IACrD,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 966, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40tanstack%2Breact-query%405.80.7_react%4019.1.0/node_modules/%40tanstack/react-query/src/queryOptions.ts"], "sourcesContent": ["import type {\n  DataTag,\n  DefaultError,\n  InitialDataFunction,\n  NonUndefinedGuard,\n  OmitKeyof,\n  QueryFunction,\n  QueryKey,\n  SkipToken,\n} from '@tanstack/query-core'\nimport type { UseQueryOptions } from './types'\n\nexport type UndefinedInitialDataOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = UseQueryOptions<TQueryFnData, TError, TData, TQueryKey> & {\n  initialData?:\n    | undefined\n    | InitialDataFunction<NonUndefinedGuard<TQueryFnData>>\n    | NonUndefinedGuard<TQueryFnData>\n}\n\nexport type UnusedSkipTokenOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = OmitKeyof<\n  UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  'queryFn'\n> & {\n  queryFn?: Exclude<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>['queryFn'],\n    SkipToken | undefined\n  >\n}\n\nexport type DefinedInitialDataOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = Omit<UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>, 'queryFn'> & {\n  initialData:\n    | NonUndefinedGuard<TQueryFnData>\n    | (() => NonUndefinedGuard<TQueryFnData>)\n  queryFn?: QueryFunction<TQueryFnData, TQueryKey>\n}\n\nexport function queryOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n): DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey> & {\n  queryKey: DataTag<TQueryKey, TQueryFnData, TError>\n}\n\nexport function queryOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UnusedSkipTokenOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UnusedSkipTokenOptions<TQueryFnData, TError, TData, TQueryKey> & {\n  queryKey: DataTag<TQueryKey, TQueryFnData, TError>\n}\n\nexport function queryOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey> & {\n  queryKey: DataTag<TQueryKey, TQueryFnData, TError>\n}\n\nexport function queryOptions(options: unknown) {\n  return options\n}\n"], "names": [], "mappings": ";;;;AAoFO,SAAS,aAAa,OAAA,EAAkB;IAC7C,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 981, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40tanstack%2Breact-query%405.80.7_react%4019.1.0/node_modules/%40tanstack/react-query/src/QueryErrorResetBoundary.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\n// CONTEXT\nexport type QueryErrorResetFunction = () => void\nexport type QueryErrorIsResetFunction = () => boolean\nexport type QueryErrorClearResetFunction = () => void\n\nexport interface QueryErrorResetBoundaryValue {\n  clearReset: QueryErrorClearResetFunction\n  isReset: QueryErrorIsResetFunction\n  reset: QueryErrorResetFunction\n}\n\nfunction createValue(): QueryErrorResetBoundaryValue {\n  let isReset = false\n  return {\n    clearReset: () => {\n      isReset = false\n    },\n    reset: () => {\n      isReset = true\n    },\n    isReset: () => {\n      return isReset\n    },\n  }\n}\n\nconst QueryErrorResetBoundaryContext = React.createContext(createValue())\n\n// HOOK\n\nexport const useQueryErrorResetBoundary = () =>\n  React.useContext(QueryErrorResetBoundaryContext)\n\n// COMPONENT\n\nexport type QueryErrorResetBoundaryFunction = (\n  value: QueryErrorResetBoundaryValue,\n) => React.ReactNode\n\nexport interface QueryErrorResetBoundaryProps {\n  children: QueryErrorResetBoundaryFunction | React.ReactNode\n}\n\nexport const QueryErrorResetBoundary = ({\n  children,\n}: QueryErrorResetBoundaryProps) => {\n  const [value] = React.useState(() => createValue())\n  return (\n    <QueryErrorResetBoundaryContext.Provider value={value}>\n      {typeof children === 'function' ? children(value) : children}\n    </QueryErrorResetBoundaryContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA,YAAY,WAAW;AAkDnB;;;;AArCJ,SAAS,cAA4C;IACnD,IAAI,UAAU;IACd,OAAO;QACL,YAAY,MAAM;YAChB,UAAU;QACZ;QACA,OAAO,MAAM;YACX,UAAU;QACZ;QACA,SAAS,MAAM;YACb,OAAO;QACT;IACF;AACF;AAEA,IAAM,obAAuC,gBAAA,EAAc,YAAY,CAAC;AAIjE,IAAM,6BAA6B,uZAClC,aAAA,EAAW,8BAA8B;AAY1C,IAAM,0BAA0B,CAAC,EACtC,QAAA,EACF,KAAoC;IAClC,MAAM,CAAC,KAAK,CAAA,sZAAU,WAAA,EAAS,IAAM,YAAY,CAAC;IAClD,OACE,aAAA,GAAA,CAAA,GAAA,gaAAA,CAAA,MAAA,EAAC,+BAA+B,QAAA,EAA/B;QAAwC;QACtC,UAAA,OAAO,aAAa,aAAa,SAAS,KAAK,IAAI;IAAA,CACtD;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1022, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40tanstack%2Breact-query%405.80.7_react%4019.1.0/node_modules/%40tanstack/react-query/src/errorBoundaryUtils.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\nimport { shouldThrowError } from '@tanstack/query-core'\nimport type {\n  DefaultedQueryObserverOptions,\n  Query,\n  QueryKey,\n  QueryObserverResult,\n  ThrowOnError,\n} from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\n\nexport const ensurePreventErrorBoundaryRetry = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  options: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  if (\n    options.suspense ||\n    options.throwOnError ||\n    options.experimental_prefetchInRender\n  ) {\n    // Prevent retrying failed query if the error boundary has not been reset yet\n    if (!errorResetBoundary.isReset()) {\n      options.retryOnMount = false\n    }\n  }\n}\n\nexport const useClearResetErrorBoundary = (\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  React.useEffect(() => {\n    errorResetBoundary.clearReset()\n  }, [errorResetBoundary])\n}\n\nexport const getHasError = <\n  TData,\n  TError,\n  TQueryFnData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>({\n  result,\n  errorResetBoundary,\n  throwOnError,\n  query,\n  suspense,\n}: {\n  result: QueryObserverResult<TData, TError>\n  errorResetBoundary: QueryErrorResetBoundaryValue\n  throwOnError: ThrowOnError<TQueryFnData, TError, TQueryData, TQueryKey>\n  query: Query<TQueryFnData, TError, TQueryData, TQueryKey> | undefined\n  suspense: boolean | undefined\n}) => {\n  return (\n    result.isError &&\n    !errorResetBoundary.isReset() &&\n    !result.isFetching &&\n    query &&\n    ((suspense && result.data === undefined) ||\n      shouldThrowError(throwOnError, [result.error, query]))\n  )\n}\n"], "names": [], "mappings": ";;;;;;AACA,YAAY,WAAW;AACvB,SAAS,wBAAwB;;;;AAU1B,IAAM,kCAAkC,CAO7C,SAOA,uBACG;IACH,IACE,QAAQ,QAAA,IACR,QAAQ,YAAA,IACR,QAAQ,6BAAA,EACR;QAEA,IAAI,CAAC,mBAAmB,OAAA,CAAQ,GAAG;YACjC,QAAQ,YAAA,GAAe;QACzB;IACF;AACF;AAEO,IAAM,6BAA6B,CACxC,uBACG;uZACG,YAAA,EAAU,MAAM;QACpB,mBAAmB,UAAA,CAAW;IAChC,GAAG;QAAC,kBAAkB;KAAC;AACzB;AAEO,IAAM,cAAc,CAMzB,EACA,MAAA,EACA,kBAAA,EACA,YAAA,EACA,KAAA,EACA,QAAA,EACF,KAMM;IACJ,OACE,OAAO,OAAA,IACP,CAAC,mBAAmB,OAAA,CAAQ,KAC5B,CAAC,OAAO,UAAA,IACR,SAAA,CACE,YAAY,OAAO,IAAA,KAAS,KAAA,0PAC5B,mBAAA,EAAiB,cAAc;QAAC,OAAO,KAAA;QAAO,KAAK;KAAC,CAAA;AAE1D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1061, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40tanstack%2Breact-query%405.80.7_react%4019.1.0/node_modules/%40tanstack/react-query/src/IsRestoringProvider.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nconst IsRestoringContext = React.createContext(false)\n\nexport const useIsRestoring = () => React.useContext(IsRestoringContext)\nexport const IsRestoringProvider = IsRestoringContext.Provider\n"], "names": [], "mappings": ";;;;;AACA,YAAY,WAAW;;;AAEvB,IAAM,waAA2B,gBAAA,EAAc,KAAK;AAE7C,IAAM,iBAAiB,uZAAY,aAAA,EAAW,kBAAkB;AAChE,IAAM,sBAAsB,mBAAmB,QAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1080, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40tanstack%2Breact-query%405.80.7_react%4019.1.0/node_modules/%40tanstack/react-query/src/suspense.ts"], "sourcesContent": ["import type {\n  DefaultError,\n  DefaultedQueryObserverOptions,\n  Query,\n  QueryKey,\n  QueryObserver,\n  QueryObserverResult,\n} from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\n\nexport const defaultThrowOnError = <\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  _error: TError,\n  query: Query<TQueryFnData, TError, TData, TQueryKey>,\n) => query.state.data === undefined\n\nexport const ensureSuspenseTimers = (\n  defaultedOptions: DefaultedQueryObserverOptions<any, any, any, any, any>,\n) => {\n  if (defaultedOptions.suspense) {\n    // Handle staleTime to ensure minimum 1000ms in Suspense mode\n    // This prevents unnecessary refetching when components remount after suspending\n\n    const clamp = (value: number | 'static' | undefined) =>\n      value === 'static' ? value : Math.max(value ?? 1000, 1000)\n\n    const originalStaleTime = defaultedOptions.staleTime\n    defaultedOptions.staleTime =\n      typeof originalStaleTime === 'function'\n        ? (...args) => clamp(originalStaleTime(...args))\n        : clamp(originalStaleTime)\n\n    if (typeof defaultedOptions.gcTime === 'number') {\n      defaultedOptions.gcTime = Math.max(defaultedOptions.gcTime, 1000)\n    }\n  }\n}\n\nexport const willFetch = (\n  result: QueryObserverResult<any, any>,\n  isRestoring: boolean,\n) => result.isLoading && result.isFetching && !isRestoring\n\nexport const shouldSuspend = (\n  defaultedOptions:\n    | DefaultedQueryObserverOptions<any, any, any, any, any>\n    | undefined,\n  result: QueryObserverResult<any, any>,\n) => defaultedOptions?.suspense && result.isPending\n\nexport const fetchOptimistic = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  defaultedOptions: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) =>\n  observer.fetchOptimistic(defaultedOptions).catch(() => {\n    errorResetBoundary.clearReset()\n  })\n"], "names": [], "mappings": ";;;;;;;;AAUO,IAAM,sBAAsB,CAMjC,QACA,QACG,MAAM,KAAA,CAAM,IAAA,KAAS,KAAA;AAEnB,IAAM,uBAAuB,CAClC,qBACG;IACH,IAAI,iBAAiB,QAAA,EAAU;QAI7B,MAAM,QAAQ,CAAC,QACb,UAAU,WAAW,QAAQ,KAAK,GAAA,CAAI,SAAS,KAAM,GAAI;QAE3D,MAAM,oBAAoB,iBAAiB,SAAA;QAC3C,iBAAiB,SAAA,GACf,OAAO,sBAAsB,aACzB,CAAA,GAAI,OAAS,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAC7C,MAAM,iBAAiB;QAE7B,IAAI,OAAO,iBAAiB,MAAA,KAAW,UAAU;YAC/C,iBAAiB,MAAA,GAAS,KAAK,GAAA,CAAI,iBAAiB,MAAA,EAAQ,GAAI;QAClE;IACF;AACF;AAEO,IAAM,YAAY,CACvB,QACA,cACG,OAAO,SAAA,IAAa,OAAO,UAAA,IAAc,CAAC;AAExC,IAAM,gBAAgB,CAC3B,kBAGA,SACG,kBAAkB,YAAY,OAAO,SAAA;AAEnC,IAAM,kBAAkB,CAO7B,kBAOA,UACA,qBAEA,SAAS,eAAA,CAAgB,gBAAgB,EAAE,KAAA,CAAM,MAAM;QACrD,mBAAmB,UAAA,CAAW;IAChC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1112, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40tanstack%2Breact-query%405.80.7_react%4019.1.0/node_modules/%40tanstack/react-query/src/useBaseQuery.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nimport { isServer, noop, notifyManager } from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary,\n} from './errorBoundaryUtils'\nimport { useIsRestoring } from './IsRestoringProvider'\nimport {\n  ensureSuspenseTimers,\n  fetchOptimistic,\n  shouldSuspend,\n  willFetch,\n} from './suspense'\nimport type {\n  QueryClient,\n  QueryKey,\n  QueryObserver,\n  QueryObserverResult,\n} from '@tanstack/query-core'\nimport type { UseBaseQueryOptions } from './types'\n\nexport function useBaseQuery<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  <PERSON><PERSON><PERSON>y<PERSON>ey extends QueryKey,\n>(\n  options: UseBaseQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  Observer: typeof QueryObserver,\n  queryClient?: QueryClient,\n): QueryObserverResult<TData, TError> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof options !== 'object' || Array.isArray(options)) {\n      throw new Error(\n        'Bad argument type. Starting with v5, only the \"Object\" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object',\n      )\n    }\n  }\n\n  const isRestoring = useIsRestoring()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n  const client = useQueryClient(queryClient)\n  const defaultedOptions = client.defaultQueryOptions(options)\n\n  ;(client.getDefaultOptions().queries as any)?._experimental_beforeQuery?.(\n    defaultedOptions,\n  )\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (!defaultedOptions.queryFn) {\n      console.error(\n        `[${defaultedOptions.queryHash}]: No queryFn was passed as an option, and no default queryFn was found. The queryFn parameter is only optional when using a default queryFn. More info here: https://tanstack.com/query/latest/docs/framework/react/guides/default-query-function`,\n      )\n    }\n  }\n\n  // Make sure results are optimistically set in fetching state before subscribing or updating options\n  defaultedOptions._optimisticResults = isRestoring\n    ? 'isRestoring'\n    : 'optimistic'\n\n  ensureSuspenseTimers(defaultedOptions)\n  ensurePreventErrorBoundaryRetry(defaultedOptions, errorResetBoundary)\n\n  useClearResetErrorBoundary(errorResetBoundary)\n\n  // this needs to be invoked before creating the Observer because that can create a cache entry\n  const isNewCacheEntry = !client\n    .getQueryCache()\n    .get(defaultedOptions.queryHash)\n\n  const [observer] = React.useState(\n    () =>\n      new Observer<TQueryFnData, TError, TData, TQueryData, TQueryKey>(\n        client,\n        defaultedOptions,\n      ),\n  )\n\n  // note: this must be called before useSyncExternalStore\n  const result = observer.getOptimisticResult(defaultedOptions)\n\n  const shouldSubscribe = !isRestoring && options.subscribed !== false\n  React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => {\n        const unsubscribe = shouldSubscribe\n          ? observer.subscribe(notifyManager.batchCalls(onStoreChange))\n          : noop\n\n        // Update result to make sure we did not miss any query updates\n        // between creating the observer and subscribing to it.\n        observer.updateResult()\n\n        return unsubscribe\n      },\n      [observer, shouldSubscribe],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  React.useEffect(() => {\n    observer.setOptions(defaultedOptions)\n  }, [defaultedOptions, observer])\n\n  // Handle suspense\n  if (shouldSuspend(defaultedOptions, result)) {\n    throw fetchOptimistic(defaultedOptions, observer, errorResetBoundary)\n  }\n\n  // Handle error boundary\n  if (\n    getHasError({\n      result,\n      errorResetBoundary,\n      throwOnError: defaultedOptions.throwOnError,\n      query: client\n        .getQueryCache()\n        .get<\n          TQueryFnData,\n          TError,\n          TQueryData,\n          TQueryKey\n        >(defaultedOptions.queryHash),\n      suspense: defaultedOptions.suspense,\n    })\n  ) {\n    throw result.error\n  }\n\n  ;(client.getDefaultOptions().queries as any)?._experimental_afterQuery?.(\n    defaultedOptions,\n    result,\n  )\n\n  if (\n    defaultedOptions.experimental_prefetchInRender &&\n    !isServer &&\n    willFetch(result, isRestoring)\n  ) {\n    const promise = isNewCacheEntry\n      ? // Fetch immediately on render in order to ensure `.promise` is resolved even if the component is unmounted\n        fetchOptimistic(defaultedOptions, observer, errorResetBoundary)\n      : // subscribe to the \"cache promise\" so that we can finalize the currentThenable once data comes in\n        client.getQueryCache().get(defaultedOptions.queryHash)?.promise\n\n    promise?.catch(noop).finally(() => {\n      // `.updateResult()` will trigger `.#currentThenable` to finalize\n      observer.updateResult()\n    })\n  }\n\n  // Handle result property usage tracking\n  return !defaultedOptions.notifyOnChangeProps\n    ? observer.trackResult(result)\n    : result\n}\n"], "names": [], "mappings": ";;;;AACA,YAAY,WAAW;AAEvB,SAAS,UAAU,MAAM,qBAAqB;;AAC9C,SAAS,sBAAsB;AAC/B,SAAS,kCAAkC;AAC3C;AAKA,SAAS,sBAAsB;AAC/B;;;;;;;;;AAcO,SAAS,aAOd,OAAA,EAOA,QAAA,EACA,WAAA,EACoC;IACpC,IAAI,QAAQ,IAAI,aAAa,WAAc;QACzC,IAAI,OAAO,YAAY,YAAY,MAAM,OAAA,CAAQ,OAAO,GAAG;YACzD,MAAM,IAAI,MACR;QAEJ;IACF;IAEA,MAAM,ySAAc,iBAAA,CAAe;IACnC,MAAM,oTAAqB,6BAAA,CAA2B;IACtD,MAAM,oSAAS,iBAAA,EAAe,WAAW;IACzC,MAAM,mBAAmB,OAAO,mBAAA,CAAoB,OAAO;IAEzD,OAAO,iBAAA,CAAkB,EAAE,OAAA,EAAiB,4BAC5C;IAGF,IAAI,QAAQ,IAAI,aAAa,WAAc;QACzC,IAAI,CAAC,iBAAiB,OAAA,EAAS;YAC7B,QAAQ,KAAA,CACN,CAAA,CAAA,EAAI,iBAAiB,SAAS,CAAA,kPAAA,CAAA;QAElC;IACF;IAGA,iBAAiB,kBAAA,GAAqB,cAClC,gBACA;IAEJ,CAAA,GAAA,2QAAA,CAAA,uBAAA,EAAqB,gBAAgB;IACrC,CAAA,GAAA,qRAAA,CAAA,kCAAA,EAAgC,kBAAkB,kBAAkB;IAEpE,CAAA,GAAA,qRAAA,CAAA,6BAAA,EAA2B,kBAAkB;IAG7C,MAAM,kBAAkB,CAAC,OACtB,aAAA,CAAc,EACd,GAAA,CAAI,iBAAiB,SAAS;IAEjC,MAAM,CAAC,QAAQ,CAAA,sZAAU,WAAA,EACvB,IACE,IAAI,SACF,QACA;IAKN,MAAM,SAAS,SAAS,mBAAA,CAAoB,gBAAgB;IAE5D,MAAM,kBAAkB,CAAC,eAAe,QAAQ,UAAA,KAAe;uZACzD,uBAAA,qZACE,cAAA,EACJ,CAAC,kBAAkB;QACjB,MAAM,cAAc,kBAChB,SAAS,SAAA,0PAAU,gBAAA,CAAc,UAAA,CAAW,aAAa,CAAC,qPAC1D,OAAA;QAIJ,SAAS,YAAA,CAAa;QAEtB,OAAO;IACT,GACA;QAAC;QAAU,eAAe;KAAA,GAE5B,IAAM,SAAS,gBAAA,CAAiB,GAChC,IAAM,SAAS,gBAAA,CAAiB;uZAG5B,YAAA,EAAU,MAAM;QACpB,SAAS,UAAA,CAAW,gBAAgB;IACtC,GAAG;QAAC;QAAkB,QAAQ;KAAC;IAG/B,oRAAI,gBAAA,EAAc,kBAAkB,MAAM,GAAG;QAC3C,sRAAM,kBAAA,EAAgB,kBAAkB,UAAU,kBAAkB;IACtE;IAGA,8RACE,cAAA,EAAY;QACV;QACA;QACA,cAAc,iBAAiB,YAAA;QAC/B,OAAO,OACJ,aAAA,CAAc,EACd,GAAA,CAKC,iBAAiB,SAAS;QAC9B,UAAU,iBAAiB,QAAA;IAC7B,CAAC,GACD;QACA,MAAM,OAAO,KAAA;IACf;;IAEE,OAAO,iBAAA,CAAkB,EAAE,OAAA,EAAiB,2BAC5C,kBACA;IAGF,IACE,iBAAiB,6BAAA,IACjB,kPAAC,WAAA,oRACD,YAAA,EAAU,QAAQ,WAAW,GAC7B;QACA,MAAM,UAAU,kBAAA,2GAAA;wRAEZ,kBAAA,EAAgB,kBAAkB,UAAU,kBAAkB,IAAA,kGAAA;QAE9D,OAAO,aAAA,CAAc,EAAE,GAAA,CAAI,iBAAiB,SAAS,GAAG;QAE5D,SAAS,uPAAM,OAAI,EAAE,QAAQ,MAAM;YAEjC,SAAS,YAAA,CAAa;QACxB,CAAC;IACH;IAGA,OAAO,CAAC,iBAAiB,mBAAA,GACrB,SAAS,WAAA,CAAY,MAAM,IAC3B;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1202, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40tanstack%2Breact-query%405.80.7_react%4019.1.0/node_modules/%40tanstack/react-query/src/useInfiniteQuery.ts"], "sourcesContent": ["'use client'\nimport { InfiniteQueryObserver } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport type {\n  DefaultError,\n  InfiniteData,\n  QueryClient,\n  QueryKey,\n  QueryObserver,\n} from '@tanstack/query-core'\nimport type {\n  DefinedUseInfiniteQueryResult,\n  UseInfiniteQueryOptions,\n  UseInfiniteQueryResult,\n} from './types'\nimport type {\n  DefinedInitialDataInfiniteOptions,\n  UndefinedInitialDataInfiniteOptions,\n} from './infiniteQueryOptions'\n\nexport function useInfiniteQuery<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: DefinedInitialDataInfiniteOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n): DefinedUseInfiniteQueryResult<TData, TError>\n\nexport function useInfiniteQuery<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: UndefinedInitialDataInfiniteOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n): UseInfiniteQueryResult<TData, TError>\n\nexport function useInfiniteQuery<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: UseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n): UseInfiniteQueryResult<TData, TError>\n\nexport function useInfiniteQuery(\n  options: UseInfiniteQueryOptions,\n  queryClient?: QueryClient,\n) {\n  return useBaseQuery(\n    options,\n    InfiniteQueryObserver as typeof QueryObserver,\n    queryClient,\n  )\n}\n"], "names": [], "mappings": ";;;;AACA,SAAS,6BAA6B;AACtC,SAAS,oBAAoB;;;;AAqEtB,SAAS,iBACd,OAAA,EACA,WAAA,EACA;IACA,2RAAO,eAAA,EACL,0QACA,wBAAA,EACA;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1222, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40tanstack%2Breact-query%405.80.7_react%4019.1.0/node_modules/%40tanstack/react-query/src/useMutation.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\nimport {\n  MutationObserver,\n  noop,\n  notifyManager,\n  shouldThrowError,\n} from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport type {\n  UseMutateFunction,\n  UseMutationOptions,\n  UseMutationResult,\n} from './types'\nimport type { DefaultError, QueryClient } from '@tanstack/query-core'\n\n// HOOK\n\nexport function useMutation<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n>(\n  options: UseMutationOptions<TData, TError, TVariables, TContext>,\n  queryClient?: QueryClient,\n): UseMutationResult<TData, TError, TVariables, TContext> {\n  const client = useQueryClient(queryClient)\n\n  const [observer] = React.useState(\n    () =>\n      new MutationObserver<TData, TError, TVariables, TContext>(\n        client,\n        options,\n      ),\n  )\n\n  React.useEffect(() => {\n    observer.setOptions(options)\n  }, [observer, options])\n\n  const result = React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        observer.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [observer],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  const mutate = React.useCallback<\n    UseMutateFunction<TData, TError, TVariables, TContext>\n  >(\n    (variables, mutateOptions) => {\n      observer.mutate(variables, mutateOptions).catch(noop)\n    },\n    [observer],\n  )\n\n  if (\n    result.error &&\n    shouldThrowError(observer.options.throwOnError, [result.error])\n  ) {\n    throw result.error\n  }\n\n  return { ...result, mutate, mutateAsync: result.mutate }\n}\n"], "names": [], "mappings": ";;;;AACA,YAAY,WAAW;;;AACvB;AAMA,SAAS,sBAAsB;;;;;AAUxB,SAAS,YAMd,OAAA,EACA,WAAA,EACwD;IACxD,MAAM,oSAAS,iBAAA,EAAe,WAAW;IAEzC,MAAM,CAAC,QAAQ,CAAA,GAAU,8ZAAA,EACvB,IACE,gQAAI,mBAAA,CACF,QACA;uZAIA,YAAA,EAAU,MAAM;QACpB,SAAS,UAAA,CAAW,OAAO;IAC7B,GAAG;QAAC;QAAU,OAAO;KAAC;IAEtB,MAAM,UAAe,yaAAA,qZACb,cAAA,EACJ,CAAC,gBACC,SAAS,SAAA,0PAAU,gBAAA,CAAc,UAAA,CAAW,aAAa,CAAC,GAC5D;QAAC,QAAQ;KAAA,GAEX,IAAM,SAAS,gBAAA,CAAiB,GAChC,IAAM,SAAS,gBAAA,CAAiB;IAGlC,MAAM,4ZAAe,cAAA,EAGnB,CAAC,WAAW,kBAAkB;QAC5B,SAAS,MAAA,CAAO,WAAW,aAAa,EAAE,KAAA,kPAAM,OAAI;IACtD,GACA;QAAC,QAAQ;KAAA;IAGX,IACE,OAAO,KAAA,IACP,wQAAA,EAAiB,SAAS,OAAA,CAAQ,YAAA,EAAc;QAAC,OAAO,KAAK;KAAC,GAC9D;QACA,MAAM,OAAO,KAAA;IACf;IAEA,OAAO;QAAE,GAAG,MAAA;QAAQ;QAAQ,aAAa,OAAO,MAAA;IAAO;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1271, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40tanstack%2Breact-query%405.80.7_react%4019.1.0/node_modules/%40tanstack/react-query/src/usePrefetchInfiniteQuery.tsx"], "sourcesContent": ["import { useQueryClient } from './QueryClientProvider'\nimport type {\n  DefaultError,\n  FetchInfiniteQueryOptions,\n  QueryClient,\n  QueryKey,\n} from '@tanstack/query-core'\n\nexport function usePrefetchInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: FetchInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n) {\n  const client = useQueryClient(queryClient)\n\n  if (!client.getQueryState(options.queryKey)) {\n    client.prefetchInfiniteQuery(options)\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,sBAAsB;;AAQxB,SAAS,yBAOd,OAAA,EAOA,WAAA,EACA;IACA,MAAM,oSAAS,iBAAA,EAAe,WAAW;IAEzC,IAAI,CAAC,OAAO,aAAA,CAAc,QAAQ,QAAQ,GAAG;QAC3C,OAAO,qBAAA,CAAsB,OAAO;IACtC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1291, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40tanstack%2Breact-query%405.80.7_react%4019.1.0/node_modules/%40tanstack/react-query/src/usePrefetchQuery.tsx"], "sourcesContent": ["import { useQueryClient } from './QueryClientProvider'\nimport type {\n  DefaultError,\n  FetchQueryOptions,\n  QueryClient,\n  QueryKey,\n} from '@tanstack/query-core'\n\nexport function usePrefetchQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n) {\n  const client = useQueryClient(queryClient)\n\n  if (!client.getQueryState(options.queryKey)) {\n    client.prefetchQuery(options)\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,sBAAsB;;AAQxB,SAAS,iBAMd,OAAA,EACA,WAAA,EACA;IACA,MAAM,oSAAS,iBAAA,EAAe,WAAW;IAEzC,IAAI,CAAC,OAAO,aAAA,CAAc,QAAQ,QAAQ,GAAG;QAC3C,OAAO,aAAA,CAAc,OAAO;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1311, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40tanstack%2Breact-query%405.80.7_react%4019.1.0/node_modules/%40tanstack/react-query/src/useQueries.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nimport {\n  QueriesObserver,\n  QueryObserver,\n  noop,\n  notifyManager,\n} from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport { useIsRestoring } from './IsRestoringProvider'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary,\n} from './errorBoundaryUtils'\nimport {\n  ensureSuspenseTimers,\n  fetchOptimistic,\n  shouldSuspend,\n  willFetch,\n} from './suspense'\nimport type {\n  DefinedUseQueryResult,\n  UseQueryOptions,\n  UseQueryResult,\n} from './types'\nimport type {\n  DefaultError,\n  OmitKeyof,\n  QueriesObserverOptions,\n  QueriesPlaceholderDataFunction,\n  QueryClient,\n  QueryFunction,\n  QueryKey,\n  QueryObserverOptions,\n  ThrowOnError,\n} from '@tanstack/query-core'\n\n// This defines the `UseQueryOptions` that are accepted in `QueriesOptions` & `GetOptions`.\n// `placeholderData` function always gets undefined passed\ntype UseQueryOptionsForUseQueries<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = OmitKeyof<\n  UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  'placeholderData' | 'subscribed'\n> & {\n  placeholderData?: TQueryFnData | QueriesPlaceholderDataFunction<TQueryFnData>\n}\n\n// Avoid TS depth-limit error in case of large array literal\ntype MAXIMUM_DEPTH = 20\n\n// Widen the type of the symbol to enable type inference even if skipToken is not immutable.\ntype SkipTokenForUseQueries = symbol\n\ntype GetUseQueryOptionsForUseQueries<T> =\n  // Part 1: responsible for applying explicit type parameter to function arguments, if object { queryFnData: TQueryFnData, error: TError, data: TData }\n  T extends {\n    queryFnData: infer TQueryFnData\n    error?: infer TError\n    data: infer TData\n  }\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n      ? UseQueryOptionsForUseQueries<TQueryFnData, TError>\n      : T extends { data: infer TData; error?: infer TError }\n        ? UseQueryOptionsForUseQueries<unknown, TError, TData>\n        : // Part 2: responsible for applying explicit type parameter to function arguments, if tuple [TQueryFnData, TError, TData]\n          T extends [infer TQueryFnData, infer TError, infer TData]\n          ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData>\n          : T extends [infer TQueryFnData, infer TError]\n            ? UseQueryOptionsForUseQueries<TQueryFnData, TError>\n            : T extends [infer TQueryFnData]\n              ? UseQueryOptionsForUseQueries<TQueryFnData>\n              : // Part 3: responsible for inferring and enforcing type if no explicit parameter was provided\n                T extends {\n                    queryFn?:\n                      | QueryFunction<infer TQueryFnData, infer TQueryKey>\n                      | SkipTokenForUseQueries\n                    select?: (data: any) => infer TData\n                    throwOnError?: ThrowOnError<any, infer TError, any, any>\n                  }\n                ? UseQueryOptionsForUseQueries<\n                    TQueryFnData,\n                    unknown extends TError ? DefaultError : TError,\n                    unknown extends TData ? TQueryFnData : TData,\n                    TQueryKey\n                  >\n                : // Fallback\n                  UseQueryOptionsForUseQueries\n\n// A defined initialData setting should return a DefinedUseQueryResult rather than UseQueryResult\ntype GetDefinedOrUndefinedQueryResult<T, TData, TError = unknown> = T extends {\n  initialData?: infer TInitialData\n}\n  ? unknown extends TInitialData\n    ? UseQueryResult<TData, TError>\n    : TInitialData extends TData\n      ? DefinedUseQueryResult<TData, TError>\n      : TInitialData extends () => infer TInitialDataResult\n        ? unknown extends TInitialDataResult\n          ? UseQueryResult<TData, TError>\n          : TInitialDataResult extends TData\n            ? DefinedUseQueryResult<TData, TError>\n            : UseQueryResult<TData, TError>\n        : UseQueryResult<TData, TError>\n  : UseQueryResult<TData, TError>\n\ntype GetUseQueryResult<T> =\n  // Part 1: responsible for mapping explicit type parameter to function result, if object\n  T extends { queryFnData: any; error?: infer TError; data: infer TData }\n    ? GetDefinedOrUndefinedQueryResult<T, TData, TError>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n      ? GetDefinedOrUndefinedQueryResult<T, TQueryFnData, TError>\n      : T extends { data: infer TData; error?: infer TError }\n        ? GetDefinedOrUndefinedQueryResult<T, TData, TError>\n        : // Part 2: responsible for mapping explicit type parameter to function result, if tuple\n          T extends [any, infer TError, infer TData]\n          ? GetDefinedOrUndefinedQueryResult<T, TData, TError>\n          : T extends [infer TQueryFnData, infer TError]\n            ? GetDefinedOrUndefinedQueryResult<T, TQueryFnData, TError>\n            : T extends [infer TQueryFnData]\n              ? GetDefinedOrUndefinedQueryResult<T, TQueryFnData>\n              : // Part 3: responsible for mapping inferred type to results, if no explicit parameter was provided\n                T extends {\n                    queryFn?:\n                      | QueryFunction<infer TQueryFnData, any>\n                      | SkipTokenForUseQueries\n                    select?: (data: any) => infer TData\n                    throwOnError?: ThrowOnError<any, infer TError, any, any>\n                  }\n                ? GetDefinedOrUndefinedQueryResult<\n                    T,\n                    unknown extends TData ? TQueryFnData : TData,\n                    unknown extends TError ? DefaultError : TError\n                  >\n                : // Fallback\n                  UseQueryResult\n\n/**\n * QueriesOptions reducer recursively unwraps function arguments to infer/enforce type param\n */\nexport type QueriesOptions<\n  T extends Array<any>,\n  TResults extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseQueryOptionsForUseQueries>\n  : T extends []\n    ? []\n    : T extends [infer Head]\n      ? [...TResults, GetUseQueryOptionsForUseQueries<Head>]\n      : T extends [infer Head, ...infer Tails]\n        ? QueriesOptions<\n            [...Tails],\n            [...TResults, GetUseQueryOptionsForUseQueries<Head>],\n            [...TDepth, 1]\n          >\n        : ReadonlyArray<unknown> extends T\n          ? T\n          : // If T is *some* array but we couldn't assign unknown[] to it, then it must hold some known/homogenous type!\n            // use this to infer the param types in the case of Array.map() argument\n            T extends Array<\n                UseQueryOptionsForUseQueries<\n                  infer TQueryFnData,\n                  infer TError,\n                  infer TData,\n                  infer TQueryKey\n                >\n              >\n            ? Array<\n                UseQueryOptionsForUseQueries<\n                  TQueryFnData,\n                  TError,\n                  TData,\n                  TQueryKey\n                >\n              >\n            : // Fallback\n              Array<UseQueryOptionsForUseQueries>\n\n/**\n * QueriesResults reducer recursively maps type param to results\n */\nexport type QueriesResults<\n  T extends Array<any>,\n  TResults extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseQueryResult>\n  : T extends []\n    ? []\n    : T extends [infer Head]\n      ? [...TResults, GetUseQueryResult<Head>]\n      : T extends [infer Head, ...infer Tails]\n        ? QueriesResults<\n            [...Tails],\n            [...TResults, GetUseQueryResult<Head>],\n            [...TDepth, 1]\n          >\n        : { [K in keyof T]: GetUseQueryResult<T[K]> }\n\nexport function useQueries<\n  T extends Array<any>,\n  TCombinedResult = QueriesResults<T>,\n>(\n  {\n    queries,\n    ...options\n  }: {\n    queries:\n      | readonly [...QueriesOptions<T>]\n      | readonly [...{ [K in keyof T]: GetUseQueryOptionsForUseQueries<T[K]> }]\n    combine?: (result: QueriesResults<T>) => TCombinedResult\n    subscribed?: boolean\n  },\n  queryClient?: QueryClient,\n): TCombinedResult {\n  const client = useQueryClient(queryClient)\n  const isRestoring = useIsRestoring()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n\n  const defaultedQueries = React.useMemo(\n    () =>\n      queries.map((opts) => {\n        const defaultedOptions = client.defaultQueryOptions(\n          opts as QueryObserverOptions,\n        )\n\n        // Make sure the results are already in fetching state before subscribing or updating options\n        defaultedOptions._optimisticResults = isRestoring\n          ? 'isRestoring'\n          : 'optimistic'\n\n        return defaultedOptions\n      }),\n    [queries, client, isRestoring],\n  )\n\n  defaultedQueries.forEach((query) => {\n    ensureSuspenseTimers(query)\n    ensurePreventErrorBoundaryRetry(query, errorResetBoundary)\n  })\n\n  useClearResetErrorBoundary(errorResetBoundary)\n\n  const [observer] = React.useState(\n    () =>\n      new QueriesObserver<TCombinedResult>(\n        client,\n        defaultedQueries,\n        options as QueriesObserverOptions<TCombinedResult>,\n      ),\n  )\n\n  // note: this must be called before useSyncExternalStore\n  const [optimisticResult, getCombinedResult, trackResult] =\n    observer.getOptimisticResult(\n      defaultedQueries,\n      (options as QueriesObserverOptions<TCombinedResult>).combine,\n    )\n\n  const shouldSubscribe = !isRestoring && options.subscribed !== false\n  React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        shouldSubscribe\n          ? observer.subscribe(notifyManager.batchCalls(onStoreChange))\n          : noop,\n      [observer, shouldSubscribe],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  React.useEffect(() => {\n    observer.setQueries(\n      defaultedQueries,\n      options as QueriesObserverOptions<TCombinedResult>,\n    )\n  }, [defaultedQueries, options, observer])\n\n  const shouldAtLeastOneSuspend = optimisticResult.some((result, index) =>\n    shouldSuspend(defaultedQueries[index], result),\n  )\n\n  const suspensePromises = shouldAtLeastOneSuspend\n    ? optimisticResult.flatMap((result, index) => {\n        const opts = defaultedQueries[index]\n\n        if (opts) {\n          const queryObserver = new QueryObserver(client, opts)\n          if (shouldSuspend(opts, result)) {\n            return fetchOptimistic(opts, queryObserver, errorResetBoundary)\n          } else if (willFetch(result, isRestoring)) {\n            void fetchOptimistic(opts, queryObserver, errorResetBoundary)\n          }\n        }\n        return []\n      })\n    : []\n\n  if (suspensePromises.length > 0) {\n    throw Promise.all(suspensePromises)\n  }\n  const firstSingleResultWhichShouldThrow = optimisticResult.find(\n    (result, index) => {\n      const query = defaultedQueries[index]\n      return (\n        query &&\n        getHasError({\n          result,\n          errorResetBoundary,\n          throwOnError: query.throwOnError,\n          query: client.getQueryCache().get(query.queryHash),\n          suspense: query.suspense,\n        })\n      )\n    },\n  )\n\n  if (firstSingleResultWhichShouldThrow?.error) {\n    throw firstSingleResultWhichShouldThrow.error\n  }\n\n  return getCombinedResult(trackResult())\n}\n"], "names": [], "mappings": ";;;;AACA,YAAY,WAAW;AAEvB;;;;AAMA,SAAS,sBAAsB;AAC/B,SAAS,sBAAsB;AAC/B,SAAS,kCAAkC;AAC3C;AAKA;;;;;;;;;AA8LO,SAAS,WAId,EACE,OAAA,EACA,GAAG,SACL,EAOA,WAAA,EACiB;IACjB,MAAM,oSAAS,iBAAA,EAAe,WAAW;IACzC,MAAM,wSAAc,kBAAA,CAAe;IACnC,MAAM,oTAAqB,6BAAA,CAA2B;IAEtD,MAAM,saAAyB,UAAA,EAC7B,IACE,QAAQ,GAAA,CAAI,CAAC,SAAS;YACpB,MAAM,mBAAmB,OAAO,mBAAA,CAC9B;YAIF,iBAAiB,kBAAA,GAAqB,cAClC,gBACA;YAEJ,OAAO;QACT,CAAC,GACH;QAAC;QAAS;QAAQ,WAAW;KAAA;IAG/B,iBAAiB,OAAA,CAAQ,CAAC,UAAU;QAClC,CAAA,GAAA,2QAAA,CAAA,uBAAA,EAAqB,KAAK;QAC1B,CAAA,GAAA,qRAAA,CAAA,kCAAA,EAAgC,OAAO,kBAAkB;IAC3D,CAAC;IAED,CAAA,GAAA,qRAAA,CAAA,6BAAA,EAA2B,kBAAkB;IAE7C,MAAM,CAAC,QAAQ,CAAA,IAAU,6ZAAA,EACvB,IACE,+PAAI,kBAAA,CACF,QACA,kBACA;IAKN,MAAM,CAAC,kBAAkB,mBAAmB,WAAW,CAAA,GACrD,SAAS,mBAAA,CACP,kBACC,QAAoD,OAAA;IAGzD,MAAM,kBAAkB,CAAC,eAAe,QAAQ,UAAA,KAAe;uZACzD,uBAAA,qZACE,cAAA,EACJ,CAAC,gBACC,kBACI,SAAS,SAAA,0PAAU,gBAAA,CAAc,UAAA,CAAW,aAAa,CAAC,qPAC1D,OAAA,EACN;QAAC;QAAU,eAAe;KAAA,GAE5B,IAAM,SAAS,gBAAA,CAAiB,GAChC,IAAM,SAAS,gBAAA,CAAiB;uZAG5B,YAAA,EAAU,MAAM;QACpB,SAAS,UAAA,CACP,kBACA;IAEJ,GAAG;QAAC;QAAkB;QAAS,QAAQ;KAAC;IAExC,MAAM,0BAA0B,iBAAiB,IAAA,CAAK,CAAC,QAAQ,wRAC7D,gBAAA,EAAc,gBAAA,CAAiB,KAAK,CAAA,EAAG,MAAM;IAG/C,MAAM,mBAAmB,0BACrB,iBAAiB,OAAA,CAAQ,CAAC,QAAQ,UAAU;QAC1C,MAAM,OAAO,gBAAA,CAAiB,KAAK,CAAA;QAEnC,IAAI,MAAM;YACR,MAAM,gBAAgB,6PAAI,gBAAA,CAAc,QAAQ,IAAI;YACpD,KAAI,+RAAA,EAAc,MAAM,MAAM,GAAG;gBAC/B,uRAAO,kBAAA,EAAgB,MAAM,eAAe,kBAAkB;YAChE,OAAA,IAAW,4RAAA,EAAU,QAAQ,WAAW,GAAG;gBACzC,qRAAK,kBAAA,EAAgB,MAAM,eAAe,kBAAkB;YAC9D;QACF;QACA,OAAO,CAAC,CAAA;IACV,CAAC,IACD,CAAC,CAAA;IAEL,IAAI,iBAAiB,MAAA,GAAS,GAAG;QAC/B,MAAM,QAAQ,GAAA,CAAI,gBAAgB;IACpC;IACA,MAAM,oCAAoC,iBAAiB,IAAA,CACzD,CAAC,QAAQ,UAAU;QACjB,MAAM,QAAQ,gBAAA,CAAiB,KAAK,CAAA;QACpC,OACE,mSACA,cAAA,EAAY;YACV;YACA;YACA,cAAc,MAAM,YAAA;YACpB,OAAO,OAAO,aAAA,CAAc,EAAE,GAAA,CAAI,MAAM,SAAS;YACjD,UAAU,MAAM,QAAA;QAClB,CAAC;IAEL;IAGF,IAAI,mCAAmC,OAAO;QAC5C,MAAM,kCAAkC,KAAA;IAC1C;IAEA,OAAO,kBAAkB,YAAY,CAAC;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1404, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40tanstack%2Breact-query%405.80.7_react%4019.1.0/node_modules/%40tanstack/react-query/src/useQuery.ts"], "sourcesContent": ["'use client'\nimport { QueryObserver } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport type {\n  DefaultError,\n  NoInfer,\n  QueryClient,\n  QueryKey,\n} from '@tanstack/query-core'\nimport type {\n  DefinedUseQueryResult,\n  UseQueryOptions,\n  UseQueryResult,\n} from './types'\nimport type {\n  DefinedInitialDataOptions,\n  UndefinedInitialDataOptions,\n} from './queryOptions'\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): DefinedUseQueryResult<NoInfer<TData>, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  T<PERSON><PERSON><PERSON><PERSON><PERSON> extends QueryKey = QueryKey,\n>(\n  options: UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): UseQueryResult<NoInfer<TData>, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): UseQueryResult<NoInfer<TData>, TError>\n\nexport function useQuery(options: UseQueryOptions, queryClient?: QueryClient) {\n  return useBaseQuery(options, QueryObserver, queryClient)\n}\n"], "names": [], "mappings": ";;;;AACA,SAAS,qBAAqB;AAC9B,SAAS,oBAAoB;;;;AA+CtB,SAAS,SAAS,OAAA,EAA0B,WAAA,EAA2B;IAC5E,2RAAO,eAAA,EAAa,kQAAS,gBAAA,EAAe,WAAW;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1424, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40tanstack%2Breact-query%405.80.7_react%4019.1.0/node_modules/%40tanstack/react-query/src/useSuspenseInfiniteQuery.ts"], "sourcesContent": ["'use client'\nimport { InfiniteQueryObserver, skipToken } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport { defaultThrowOnError } from './suspense'\nimport type {\n  DefaultError,\n  InfiniteData,\n  InfiniteQueryObserverSuccessResult,\n  QueryClient,\n  QueryKey,\n  QueryObserver,\n} from '@tanstack/query-core'\nimport type {\n  UseSuspenseInfiniteQueryOptions,\n  UseSuspenseInfiniteQueryResult,\n} from './types'\n\nexport function useSuspenseInfiniteQuery<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: UseSuspenseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n): UseSuspenseInfiniteQueryResult<TData, TError> {\n  if (process.env.NODE_ENV !== 'production') {\n    if ((options.queryFn as any) === skipToken) {\n      console.error('skipToken is not allowed for useSuspenseInfiniteQuery')\n    }\n  }\n\n  return useBaseQuery(\n    {\n      ...options,\n      enabled: true,\n      suspense: true,\n      throwOnError: defaultThrowOnError,\n    },\n    InfiniteQueryObserver as typeof QueryObserver,\n    queryClient,\n  ) as InfiniteQueryObserverSuccessResult<TData, TError>\n}\n"], "names": [], "mappings": ";;;;;AACA,SAAS,uBAAuB,iBAAiB;AACjD,SAAS,oBAAoB;AAC7B,SAAS,2BAA2B;;;;;AAc7B,SAAS,yBAOd,OAAA,EAOA,WAAA,EAC+C;IAC/C,IAAI,QAAQ,IAAI,aAAa,WAAc;QACzC,IAAK,QAAQ,OAAA,sPAAoB,YAAA,EAAW;YAC1C,QAAQ,KAAA,CAAM,uDAAuD;QACvE;IACF;IAEA,2RAAO,eAAA,EACL;QACE,GAAG,OAAA;QACH,SAAS;QACT,UAAU;QACV,0RAAc,sBAAA;IAChB,oQACA,wBAAA,EACA;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1457, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40tanstack%2Breact-query%405.80.7_react%4019.1.0/node_modules/%40tanstack/react-query/src/useSuspenseQueries.ts"], "sourcesContent": ["'use client'\nimport { skipToken } from '@tanstack/query-core'\nimport { useQueries } from './useQueries'\nimport { defaultThrowOnError } from './suspense'\nimport type { UseSuspenseQueryOptions, UseSuspenseQueryResult } from './types'\nimport type {\n  DefaultError,\n  QueryClient,\n  QueryFunction,\n  ThrowOnError,\n} from '@tanstack/query-core'\n\n// Avoid TS depth-limit error in case of large array literal\ntype MAXIMUM_DEPTH = 20\n\n// Widen the type of the symbol to enable type inference even if skipToken is not immutable.\ntype SkipTokenForUseQueries = symbol\n\ntype GetUseSuspenseQueryOptions<T> =\n  // Part 1: responsible for applying explicit type parameter to function arguments, if object { queryFnData: TQueryFnData, error: TError, data: TData }\n  T extends {\n    queryFnData: infer TQueryFnData\n    error?: infer TError\n    data: infer TData\n  }\n    ? UseSuspenseQueryOptions<TQueryFnData, TError, TData>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n      ? UseSuspenseQueryOptions<TQueryFnData, TError>\n      : T extends { data: infer TData; error?: infer TError }\n        ? UseSuspenseQueryOptions<unknown, TError, TData>\n        : // Part 2: responsible for applying explicit type parameter to function arguments, if tuple [TQueryFnData, TError, TData]\n          T extends [infer TQueryFnData, infer TError, infer TData]\n          ? UseSuspenseQueryOptions<TQueryFnData, TError, TData>\n          : T extends [infer TQueryFnData, infer TError]\n            ? UseSuspenseQueryOptions<TQueryFnData, TError>\n            : T extends [infer TQueryFnData]\n              ? UseSuspenseQueryOptions<TQueryFnData>\n              : // Part 3: responsible for inferring and enforcing type if no explicit parameter was provided\n                T extends {\n                    queryFn?:\n                      | QueryFunction<infer TQueryFnData, infer TQueryKey>\n                      | SkipTokenForUseQueries\n                    select?: (data: any) => infer TData\n                    throwOnError?: ThrowOnError<any, infer TError, any, any>\n                  }\n                ? UseSuspenseQueryOptions<\n                    TQueryFnData,\n                    TError,\n                    TData,\n                    TQueryKey\n                  >\n                : T extends {\n                      queryFn?:\n                        | QueryFunction<infer TQueryFnData, infer TQueryKey>\n                        | SkipTokenForUseQueries\n                      throwOnError?: ThrowOnError<any, infer TError, any, any>\n                    }\n                  ? UseSuspenseQueryOptions<\n                      TQueryFnData,\n                      TError,\n                      TQueryFnData,\n                      TQueryKey\n                    >\n                  : // Fallback\n                    UseSuspenseQueryOptions\n\ntype GetUseSuspenseQueryResult<T> =\n  // Part 1: responsible for mapping explicit type parameter to function result, if object\n  T extends { queryFnData: any; error?: infer TError; data: infer TData }\n    ? UseSuspenseQueryResult<TData, TError>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n      ? UseSuspenseQueryResult<TQueryFnData, TError>\n      : T extends { data: infer TData; error?: infer TError }\n        ? UseSuspenseQueryResult<TData, TError>\n        : // Part 2: responsible for mapping explicit type parameter to function result, if tuple\n          T extends [any, infer TError, infer TData]\n          ? UseSuspenseQueryResult<TData, TError>\n          : T extends [infer TQueryFnData, infer TError]\n            ? UseSuspenseQueryResult<TQueryFnData, TError>\n            : T extends [infer TQueryFnData]\n              ? UseSuspenseQueryResult<TQueryFnData>\n              : // Part 3: responsible for mapping inferred type to results, if no explicit parameter was provided\n                T extends {\n                    queryFn?:\n                      | QueryFunction<infer TQueryFnData, any>\n                      | SkipTokenForUseQueries\n                    select?: (data: any) => infer TData\n                    throwOnError?: ThrowOnError<any, infer TError, any, any>\n                  }\n                ? UseSuspenseQueryResult<\n                    unknown extends TData ? TQueryFnData : TData,\n                    unknown extends TError ? DefaultError : TError\n                  >\n                : T extends {\n                      queryFn?:\n                        | QueryFunction<infer TQueryFnData, any>\n                        | SkipTokenForUseQueries\n                      throwOnError?: ThrowOnError<any, infer TError, any, any>\n                    }\n                  ? UseSuspenseQueryResult<\n                      TQueryFnData,\n                      unknown extends TError ? DefaultError : TError\n                    >\n                  : // Fallback\n                    UseSuspenseQueryResult\n\n/**\n * SuspenseQueriesOptions reducer recursively unwraps function arguments to infer/enforce type param\n */\nexport type SuspenseQueriesOptions<\n  T extends Array<any>,\n  TResults extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseSuspenseQueryOptions>\n  : T extends []\n    ? []\n    : T extends [infer Head]\n      ? [...TResults, GetUseSuspenseQueryOptions<Head>]\n      : T extends [infer Head, ...infer Tails]\n        ? SuspenseQueriesOptions<\n            [...Tails],\n            [...TResults, GetUseSuspenseQueryOptions<Head>],\n            [...TDepth, 1]\n          >\n        : Array<unknown> extends T\n          ? T\n          : // If T is *some* array but we couldn't assign unknown[] to it, then it must hold some known/homogenous type!\n            // use this to infer the param types in the case of Array.map() argument\n            T extends Array<\n                UseSuspenseQueryOptions<\n                  infer TQueryFnData,\n                  infer TError,\n                  infer TData,\n                  infer TQueryKey\n                >\n              >\n            ? Array<\n                UseSuspenseQueryOptions<TQueryFnData, TError, TData, TQueryKey>\n              >\n            : // Fallback\n              Array<UseSuspenseQueryOptions>\n\n/**\n * SuspenseQueriesResults reducer recursively maps type param to results\n */\nexport type SuspenseQueriesResults<\n  T extends Array<any>,\n  TResults extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseSuspenseQueryResult>\n  : T extends []\n    ? []\n    : T extends [infer Head]\n      ? [...TResults, GetUseSuspenseQueryResult<Head>]\n      : T extends [infer Head, ...infer Tails]\n        ? SuspenseQueriesResults<\n            [...Tails],\n            [...TResults, GetUseSuspenseQueryResult<Head>],\n            [...TDepth, 1]\n          >\n        : { [K in keyof T]: GetUseSuspenseQueryResult<T[K]> }\n\nexport function useSuspenseQueries<\n  T extends Array<any>,\n  TCombinedResult = SuspenseQueriesResults<T>,\n>(\n  options: {\n    queries:\n      | readonly [...SuspenseQueriesOptions<T>]\n      | readonly [...{ [K in keyof T]: GetUseSuspenseQueryOptions<T[K]> }]\n    combine?: (result: SuspenseQueriesResults<T>) => TCombinedResult\n  },\n  queryClient?: QueryClient,\n): TCombinedResult\n\nexport function useSuspenseQueries<\n  T extends Array<any>,\n  TCombinedResult = SuspenseQueriesResults<T>,\n>(\n  options: {\n    queries: readonly [...SuspenseQueriesOptions<T>]\n    combine?: (result: SuspenseQueriesResults<T>) => TCombinedResult\n  },\n  queryClient?: QueryClient,\n): TCombinedResult\n\nexport function useSuspenseQueries(options: any, queryClient?: QueryClient) {\n  return useQueries(\n    {\n      ...options,\n      queries: options.queries.map((query: any) => {\n        if (process.env.NODE_ENV !== 'production') {\n          if (query.queryFn === skipToken) {\n            console.error('skipToken is not allowed for useSuspenseQueries')\n          }\n        }\n\n        return {\n          ...query,\n          suspense: true,\n          throwOnError: defaultThrowOnError,\n          enabled: true,\n          placeholderData: undefined,\n        }\n      }),\n    },\n    queryClient,\n  )\n}\n"], "names": [], "mappings": ";;;;AACA,SAAS,iBAAiB;AAC1B,SAAS,kBAAkB;AAC3B,SAAS,2BAA2B;;;;;AAyL7B,SAAS,mBAAmB,OAAA,EAAc,WAAA,EAA2B;IAC1E,QAAO,8RAAA,EACL;QACE,GAAG,OAAA;QACH,SAAS,QAAQ,OAAA,CAAQ,GAAA,CAAI,CAAC,UAAe;YAC3C,IAAI,QAAQ,IAAI,aAAa,WAAc;gBACzC,IAAI,MAAM,OAAA,qPAAY,aAAA,EAAW;oBAC/B,QAAQ,KAAA,CAAM,iDAAiD;gBACjE;YACF;YAEA,OAAO;gBACL,GAAG,KAAA;gBACH,UAAU;gBACV,0RAAc,sBAAA;gBACd,SAAS;gBACT,iBAAiB,KAAA;YACnB;QACF,CAAC;IACH,GACA;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1495, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40tanstack%2Breact-query%405.80.7_react%4019.1.0/node_modules/%40tanstack/react-query/src/useSuspenseQuery.ts"], "sourcesContent": ["'use client'\nimport { QueryObserver, skipToken } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport { defaultThrowOnError } from './suspense'\nimport type { UseSuspenseQueryOptions, UseSuspenseQueryResult } from './types'\nimport type { DefaultError, QueryClient, QueryKey } from '@tanstack/query-core'\n\nexport function useSuspenseQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseSuspenseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): UseSuspenseQueryResult<TData, TError> {\n  if (process.env.NODE_ENV !== 'production') {\n    if ((options.queryFn as any) === skipToken) {\n      console.error('skipToken is not allowed for useSuspenseQuery')\n    }\n  }\n\n  return useBaseQuery(\n    {\n      ...options,\n      enabled: true,\n      suspense: true,\n      throwOnError: defaultThrowOnError,\n      placeholderData: undefined,\n    },\n    QueryObserver,\n    queryClient,\n  ) as UseSuspenseQueryResult<TData, TError>\n}\n"], "names": [], "mappings": ";;;;;AACA,SAAS,eAAe,iBAAiB;AACzC,SAAS,oBAAoB;AAC7B,SAAS,2BAA2B;;;;;AAI7B,SAAS,iBAMd,OAAA,EACA,WAAA,EACuC;IACvC,IAAI,QAAQ,IAAI,aAAa,WAAc;QACzC,IAAK,QAAQ,OAAA,sPAAoB,YAAA,EAAW;YAC1C,QAAQ,KAAA,CAAM,+CAA+C;QAC/D;IACF;IAEA,2RAAO,eAAA,EACL;QACE,GAAG,OAAA;QACH,SAAS;QACT,UAAU;QACV,0RAAc,sBAAA;QACd,iBAAiB,KAAA;IACnB,4PACA,gBAAA,EACA;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1529, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40tanstack%2Breact-query-devtools%405.80.7_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__react%4019.1.0/node_modules/%40tanstack/react-query-devtools/src/ReactQueryDevtools.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\nimport { onlineManager, useQueryClient } from '@tanstack/react-query'\nimport { TanstackQueryDevtools } from '@tanstack/query-devtools'\nimport type {\n  DevtoolsButtonPosition,\n  DevtoolsErrorType,\n  DevtoolsPosition,\n} from '@tanstack/query-devtools'\nimport type { QueryClient } from '@tanstack/react-query'\n\nexport interface DevtoolsOptions {\n  /**\n   * Set this true if you want the dev tools to default to being open\n   */\n  initialIsOpen?: boolean\n  /**\n   * The position of the React Query logo to open and close the devtools panel.\n   * 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'\n   * Defaults to 'bottom-right'.\n   */\n  buttonPosition?: DevtoolsButtonPosition\n  /**\n   * The position of the React Query devtools panel.\n   * 'top' | 'bottom' | 'left' | 'right'\n   * Defaults to 'bottom'.\n   */\n  position?: DevtoolsPosition\n  /**\n   * Custom instance of QueryClient\n   */\n  client?: QueryClient\n  /**\n   * Use this so you can define custom errors that can be shown in the devtools.\n   */\n  errorTypes?: Array<DevtoolsErrorType>\n  /**\n   * Use this to pass a nonce to the style tag that is added to the document head. This is useful if you are using a Content Security Policy (CSP) nonce to allow inline styles.\n   */\n  styleNonce?: string\n  /**\n   * Use this so you can attach the devtool's styles to specific element in the DOM.\n   */\n  shadowDOMTarget?: ShadowRoot\n}\n\nexport function ReactQueryDevtools(\n  props: DevtoolsOptions,\n): React.ReactElement | null {\n  const queryClient = useQueryClient(props.client)\n  const ref = React.useRef<HTMLDivElement>(null)\n  const {\n    buttonPosition,\n    position,\n    initialIsOpen,\n    errorTypes,\n    styleNonce,\n    shadowDOMTarget,\n  } = props\n  const [devtools] = React.useState(\n    new TanstackQueryDevtools({\n      client: queryClient,\n      queryFlavor: 'React Query',\n      version: '5',\n      onlineManager,\n      buttonPosition,\n      position,\n      initialIsOpen,\n      errorTypes,\n      styleNonce,\n      shadowDOMTarget,\n    }),\n  )\n\n  React.useEffect(() => {\n    devtools.setClient(queryClient)\n  }, [queryClient, devtools])\n\n  React.useEffect(() => {\n    if (buttonPosition) {\n      devtools.setButtonPosition(buttonPosition)\n    }\n  }, [buttonPosition, devtools])\n\n  React.useEffect(() => {\n    if (position) {\n      devtools.setPosition(position)\n    }\n  }, [position, devtools])\n\n  React.useEffect(() => {\n    devtools.setInitialIsOpen(initialIsOpen || false)\n  }, [initialIsOpen, devtools])\n\n  React.useEffect(() => {\n    devtools.setErrorTypes(errorTypes || [])\n  }, [errorTypes, devtools])\n\n  React.useEffect(() => {\n    if (ref.current) {\n      devtools.mount(ref.current)\n    }\n\n    return () => {\n      devtools.unmount()\n    }\n  }, [devtools])\n\n  return <div dir=\"ltr\" className=\"tsqd-parent-container\" ref={ref}></div>\n}\n"], "names": [], "mappings": ";;;;AACA,YAAY,WAAW;AACvB,SAAS,eAAe,sBAAsB;;AAC9C,SAAS,6BAA6B;AAyG7B;;;;;;AA9DF,SAAS,mBACd,KAAA,EAC2B;IAC3B,MAAM,ySAAc,iBAAA,EAAe,MAAM,MAAM;IAC/C,MAAM,MAAY,4ZAAA,EAAuB,IAAI;IAC7C,MAAM,EACJ,cAAA,EACA,QAAA,EACA,aAAA,EACA,UAAA,EACA,UAAA,EACA,eAAA,EACF,GAAI;IACJ,MAAM,CAAC,QAAQ,CAAA,sZAAU,WAAA,EACvB,iPAAI,wBAAA,CAAsB;QACxB,QAAQ;QACR,aAAa;QACb,SAAS;uBACT,yQAAA;QACA;QACA;QACA;QACA;QACA;QACA;IACF,CAAC;uZAGG,YAAA,EAAU,MAAM;QACpB,SAAS,SAAA,CAAU,WAAW;IAChC,GAAG;QAAC;QAAa,QAAQ;KAAC;uZAEpB,YAAA,EAAU,MAAM;QACpB,IAAI,gBAAgB;YAClB,SAAS,iBAAA,CAAkB,cAAc;QAC3C;IACF,GAAG;QAAC;QAAgB,QAAQ;KAAC;uZAEvB,YAAA,EAAU,MAAM;QACpB,IAAI,UAAU;YACZ,SAAS,WAAA,CAAY,QAAQ;QAC/B;IACF,GAAG;QAAC;QAAU,QAAQ;KAAC;uZAEjB,YAAA,EAAU,MAAM;QACpB,SAAS,gBAAA,CAAiB,iBAAiB,KAAK;IAClD,GAAG;QAAC;QAAe,QAAQ;KAAC;uZAEtB,YAAA,EAAU,MAAM;QACpB,SAAS,aAAA,CAAc,cAAc,CAAC,CAAC;IACzC,GAAG;QAAC;QAAY,QAAQ;KAAC;KAEnB,8ZAAA,EAAU,MAAM;QACpB,IAAI,IAAI,OAAA,EAAS;YACf,SAAS,KAAA,CAAM,IAAI,OAAO;QAC5B;QAEA,OAAO,MAAM;YACX,SAAS,OAAA,CAAQ;QACnB;IACF,GAAG;QAAC,QAAQ;KAAC;IAEb,OAAO,aAAA,GAAA,CAAA,GAAA,gaAAA,CAAA,MAAA,EAAC,OAAA;QAAI,KAAI;QAAM,WAAU;QAAwB;IAAA,CAAU;AACpE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1617, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40tanstack%2Breact-query-devtools%405.80.7_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__react%4019.1.0/node_modules/%40tanstack/react-query-devtools/src/ReactQueryDevtoolsPanel.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\nimport { onlineManager, useQueryClient } from '@tanstack/react-query'\nimport { TanstackQueryDevtoolsPanel } from '@tanstack/query-devtools'\nimport type { DevtoolsErrorType } from '@tanstack/query-devtools'\nimport type { QueryClient } from '@tanstack/react-query'\n\nexport interface DevtoolsPanelOptions {\n  /**\n   * Custom instance of QueryClient\n   */\n  client?: QueryClient\n  /**\n   * Use this so you can define custom errors that can be shown in the devtools.\n   */\n  errorTypes?: Array<DevtoolsErrorType>\n  /**\n   * Use this to pass a nonce to the style tag that is added to the document head. This is useful if you are using a Content Security Policy (CSP) nonce to allow inline styles.\n   */\n  styleNonce?: string\n  /**\n   * Use this so you can attach the devtool's styles to specific element in the DOM.\n   */\n  shadowDOMTarget?: ShadowRoot\n\n  /**\n   * Custom styles for the devtools panel\n   * @default { height: '500px' }\n   * @example { height: '100%' }\n   * @example { height: '100%', width: '100%' }\n   */\n  style?: React.CSSProperties\n\n  /**\n   * Callback function that is called when the devtools panel is closed\n   */\n  onClose?: () => unknown\n}\n\nexport function ReactQueryDevtoolsPanel(\n  props: DevtoolsPanelOptions,\n): React.ReactElement | null {\n  const queryClient = useQueryClient(props.client)\n  const ref = React.useRef<HTMLDivElement>(null)\n  const { errorTypes, styleNonce, shadowDOMTarget } = props\n  const [devtools] = React.useState(\n    new TanstackQueryDevtoolsPanel({\n      client: queryClient,\n      queryFlavor: 'React Query',\n      version: '5',\n      onlineManager,\n      buttonPosition: 'bottom-left',\n      position: 'bottom',\n      initialIsOpen: true,\n      errorTypes,\n      styleNonce,\n      shadowDOMTarget,\n      onClose: props.onClose,\n    }),\n  )\n\n  React.useEffect(() => {\n    devtools.setClient(queryClient)\n  }, [queryClient, devtools])\n\n  React.useEffect(() => {\n    devtools.setOnClose(props.onClose ?? (() => {}))\n  }, [props.onClose, devtools])\n\n  React.useEffect(() => {\n    devtools.setErrorTypes(errorTypes || [])\n  }, [errorTypes, devtools])\n\n  React.useEffect(() => {\n    if (ref.current) {\n      devtools.mount(ref.current)\n    }\n\n    return () => {\n      devtools.unmount()\n    }\n  }, [devtools])\n\n  return (\n    <div\n      style={{ height: '500px', ...props.style }}\n      className=\"tsqd-parent-container\"\n      ref={ref}\n    ></div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA,YAAY,WAAW;;AACvB,SAAS,eAAe,sBAAsB;AAC9C,SAAS,kCAAkC;AAiFvC;;;;;;AA7CG,SAAS,wBACd,KAAA,EAC2B;IAC3B,MAAM,ySAAc,iBAAA,EAAe,MAAM,MAAM;IAC/C,MAAM,UAAY,wZAAA,EAAuB,IAAI;IAC7C,MAAM,EAAE,UAAA,EAAY,UAAA,EAAY,eAAA,CAAgB,CAAA,GAAI;IACpD,MAAM,CAAC,QAAQ,CAAA,sZAAU,WAAA,EACvB,iPAAI,6BAAA,CAA2B;QAC7B,QAAQ;QACR,aAAa;QACb,SAAS;gRACT,gBAAA;QACA,gBAAgB;QAChB,UAAU;QACV,eAAe;QACf;QACA;QACA;QACA,SAAS,MAAM,OAAA;IACjB,CAAC;IAGG,+ZAAA,EAAU,MAAM;QACpB,SAAS,SAAA,CAAU,WAAW;IAChC,GAAG;QAAC;QAAa,QAAQ;KAAC;uZAEpB,YAAA,EAAU,MAAM;QACpB,SAAS,UAAA,CAAW,MAAM,OAAA,IAAA,CAAY,KAAO,CAAA,AAAD,CAAG;IACjD,GAAG;QAAC,MAAM,OAAA;QAAS,QAAQ;KAAC;uZAEtB,YAAA,EAAU,MAAM;QACpB,SAAS,aAAA,CAAc,cAAc,CAAC,CAAC;IACzC,GAAG;QAAC;QAAY,QAAQ;KAAC;uZAEnB,YAAA,EAAU,MAAM;QACpB,IAAI,IAAI,OAAA,EAAS;YACf,SAAS,KAAA,CAAM,IAAI,OAAO;QAC5B;QAEA,OAAO,MAAM;YACX,SAAS,OAAA,CAAQ;QACnB;IACF,GAAG;QAAC,QAAQ;KAAC;IAEb,OACE,aAAA,GAAA,CAAA,GAAA,gaAAA,CAAA,MAAA,EAAC,OAAA;QACC,OAAO;YAAE,QAAQ;YAAS,GAAG,MAAM,KAAA;QAAM;QACzC,WAAU;QACV;IAAA;AAGN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1693, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40tanstack%2Breact-query-devtools%405.80.7_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__react%4019.1.0/node_modules/%40tanstack/react-query-devtools/src/index.ts"], "sourcesContent": ["'use client'\n\nimport * as Devtools from './ReactQueryDevtools'\nimport * as DevtoolsPanel from './ReactQueryDevtoolsPanel'\n\nexport const ReactQueryDevtools: (typeof Devtools)['ReactQueryDevtools'] =\n  process.env.NODE_ENV !== 'development'\n    ? function () {\n        return null\n      }\n    : Devtools.ReactQueryDevtools\n\nexport const ReactQueryDevtoolsPanel: (typeof DevtoolsPanel)['ReactQueryDevtoolsPanel'] =\n  process.env.NODE_ENV !== 'development'\n    ? function () {\n        return null\n      }\n    : DevtoolsPanel.ReactQueryDevtoolsPanel\n"], "names": ["ReactQueryDevtools", "ReactQueryDevtoolsPanel"], "mappings": ";;;;;AAEA,YAAY,cAAc;AAC1B,YAAY,mBAAmB;;;;AAExB,IAAMA,sBACX,QAAQ,IAAI,aAAa,gBACrB,WAAY,gZAGH,qBAAA;AAER,IAAMC,2BACX,QAAQ,IAAI,aAAa,gBACrB,WAAY,qZAGE,0BAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1713, "column": 0}, "map": {"version": 3, "file": "getQueryKey-BY58RNzP.mjs", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Breact-query%4011.4.2_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__%40trpc%2Bclient%4011.4.2_d478052bf51ccf7e080ad331641b9119/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/objectWithoutPropertiesLoose.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Breact-query%4011.4.2_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__%40trpc%2Bclient%4011.4.2_d478052bf51ccf7e080ad331641b9119/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/objectWithoutProperties.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Breact-query%4011.4.2_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__%40trpc%2Bclient%4011.4.2_d478052bf51ccf7e080ad331641b9119/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/typeof.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Breact-query%4011.4.2_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__%40trpc%2Bclient%4011.4.2_d478052bf51ccf7e080ad331641b9119/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/toPrimitive.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Breact-query%4011.4.2_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__%40trpc%2Bclient%4011.4.2_d478052bf51ccf7e080ad331641b9119/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/toPropertyKey.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Breact-query%4011.4.2_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__%40trpc%2Bclient%4011.4.2_d478052bf51ccf7e080ad331641b9119/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/defineProperty.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Breact-query%4011.4.2_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__%40trpc%2Bclient%4011.4.2_d478052bf51ccf7e080ad331641b9119/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/objectSpread2.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Breact-query%4011.4.2_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__%40trpc%2Bclient%4011.4.2_d478052bf51ccf7e080ad331641b9119/node_modules/%40trpc/react-query/src/internals/getQueryKey.ts"], "sourcesContent": ["function _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (e.includes(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nmodule.exports = _objectWithoutPropertiesLoose, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var objectWithoutPropertiesLoose = require(\"./objectWithoutPropertiesLoose.js\");\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var s = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < s.length; r++) o = s[r], t.includes(o) || {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nmodule.exports = _objectWithoutProperties, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _typeof(o);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nmodule.exports = toPrimitive, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nvar toPrimitive = require(\"./toPrimitive.js\");\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nmodule.exports = toPropertyKey, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var defineProperty = require(\"./defineProperty.js\");\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nmodule.exports = _objectSpread2, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import { skipToken } from '@tanstack/react-query';\nimport {\n  isObject,\n  type DeepPartial,\n} from '@trpc/server/unstable-core-do-not-import';\nimport type { DecoratedMutation, DecoratedQuery } from '../createTRPCReact';\nimport type { DecorateRouterRecord } from '../shared';\n\nexport type QueryType = 'any' | 'infinite' | 'query';\n\nexport type TRPCQueryKey = [\n  readonly string[],\n  { input?: unknown; type?: Exclude<QueryType, 'any'> }?,\n];\n\nexport type TRPCMutationKey = [readonly string[]]; // = [TRPCQueryKey[0]]\n\ntype ProcedureOrRouter =\n  | DecoratedMutation<any>\n  | DecoratedQuery<any>\n  | DecorateRouterRecord<any, any>;\n\n/**\n * To allow easy interactions with groups of related queries, such as\n * invalidating all queries of a router, we use an array as the path when\n * storing in tanstack query.\n **/\nexport function getQueryKeyInternal(\n  path: readonly string[],\n  input: unknown,\n  type: QueryType,\n): TRPCQueryKey {\n  // Construct a query key that is easy to destructure and flexible for\n  // partial selecting etc.\n  // https://github.com/trpc/trpc/issues/3128\n\n  // some parts of the path may be dot-separated, split them up\n  const splitPath = path.flatMap((part) => part.split('.'));\n\n  if (!input && (!type || type === 'any')) {\n    // this matches also all mutations (see `getMutationKeyInternal`)\n\n    // for `utils.invalidate()` to match all queries (including vanilla react-query)\n    // we don't want nested array if path is empty, i.e. `[]` instead of `[[]]`\n    return splitPath.length ? [splitPath] : ([] as unknown as TRPCQueryKey);\n  }\n\n  if (\n    type === 'infinite' &&\n    isObject(input) &&\n    ('direction' in input || 'cursor' in input)\n  ) {\n    const {\n      cursor: _,\n      direction: __,\n      ...inputWithoutCursorAndDirection\n    } = input;\n    return [\n      splitPath,\n      {\n        input: inputWithoutCursorAndDirection,\n        type: 'infinite',\n      },\n    ];\n  }\n  return [\n    splitPath,\n    {\n      ...(typeof input !== 'undefined' &&\n        input !== skipToken && { input: input }),\n      ...(type && type !== 'any' && { type: type }),\n    },\n  ];\n}\n\nexport function getMutationKeyInternal(path: readonly string[]) {\n  return getQueryKeyInternal(path, undefined, 'any') as TRPCMutationKey;\n}\n\ntype GetInfiniteQueryInput<\n  TProcedureInput,\n  TInputWithoutCursorAndDirection = Omit<\n    TProcedureInput,\n    'cursor' | 'direction'\n  >,\n> = keyof TInputWithoutCursorAndDirection extends never\n  ? undefined\n  : DeepPartial<TInputWithoutCursorAndDirection> | undefined;\n\n/** @internal */\nexport type GetQueryProcedureInput<TProcedureInput> = TProcedureInput extends {\n  cursor?: any;\n}\n  ? GetInfiniteQueryInput<TProcedureInput>\n  : DeepPartial<TProcedureInput> | undefined;\n\ntype GetParams<TProcedureOrRouter extends ProcedureOrRouter> =\n  TProcedureOrRouter extends DecoratedQuery<infer $Def>\n    ? [input?: GetQueryProcedureInput<$Def['input']>, type?: QueryType]\n    : [];\n\n/**\n * Method to extract the query key for a procedure\n * @param procedureOrRouter - procedure or AnyRouter\n * @param input - input to procedureOrRouter\n * @param type - defaults to `any`\n * @see https://trpc.io/docs/v11/getQueryKey\n */\nexport function getQueryKey<TProcedureOrRouter extends ProcedureOrRouter>(\n  procedureOrRouter: TProcedureOrRouter,\n  ..._params: GetParams<TProcedureOrRouter>\n) {\n  const [input, type] = _params;\n\n  // @ts-expect-error - we don't expose _def on the type layer\n  const path = procedureOrRouter._def().path as string[];\n  const queryKey = getQueryKeyInternal(path, input, type ?? 'any');\n  return queryKey;\n}\n\n// TODO: look over if we can't use a single type\nexport type QueryKeyKnown<TInput, TType extends Exclude<QueryType, 'any'>> = [\n  string[],\n  { input?: GetQueryProcedureInput<TInput>; type: TType }?,\n];\n\n/**\n * Method to extract the mutation key for a procedure\n * @param procedure - procedure\n * @see https://trpc.io/docs/v11/getQueryKey#mutations\n */\nexport function getMutationKey<TProcedure extends DecoratedMutation<any>>(\n  procedure: TProcedure,\n) {\n  // @ts-expect-error - we don't expose _def on the type layer\n  const path = procedure._def().path as string[];\n  return getMutationKeyInternal(path);\n}\n"], "names": ["_objectWithoutProperties", "_typeof", "o", "_typeof", "toPrimitive", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "r", "path: readonly string[]", "input: unknown", "type: QueryType", "procedureOrRouter: TProcedureOrRouter", "procedure: TProcedure"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAAA,SAAS,8BAA8B,CAAA,EAAG,CAAA,EAAG;YAC3C,IAAI,QAAQ,EAAG,CAAA,OAAO,CAAE;YACxB,IAAI,IAAI,CAAE;YACV,IAAK,IAAI,KAAK,EAAG,KAAI,EAAE,EAAC,cAAA,CAAe,IAAA,CAAK,GAAG,EAAE,EAAE;gBACjD,IAAI,EAAE,QAAA,CAAS,EAAE,CAAE,CAAA;gBACnB,CAAA,CAAE,EAAA,GAAK,CAAA,CAAE,EAAA;YACV;YACD,OAAO;QACR;QACD,OAAO,OAAA,GAAU,+BAA+B,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCTrH,IAAI,+BAAA;QACJ,SAASA,2BAAyB,CAAA,EAAG,CAAA,EAAG;YACtC,IAAI,QAAQ,EAAG,CAAA,OAAO,CAAE;YACxB,IAAI,GACF,GACA,IAAI,6BAA6B,GAAG,EAAE;YACxC,IAAI,OAAO,qBAAA,EAAuB;gBAChC,IAAI,IAAI,OAAO,qBAAA,CAAsB,EAAE;gBACvC,IAAK,IAAI,GAAG,IAAI,EAAE,MAAA,EAAQ,IAAK,IAAI,CAAA,CAAE,EAAA,EAAI,EAAE,QAAA,CAAS,EAAE,KAAI,EAAE,EAAC,oBAAA,CAAqB,IAAA,CAAK,GAAG,EAAE,IAAA,CAAK,CAAA,CAAE,EAAA,GAAK,CAAA,CAAE,EAAA;YAC3G;YACD,OAAO;QACR;QACD,OAAO,OAAA,GAAUA,4BAA0B,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCZhH,SAASC,UAAQ,CAAA,EAAG;YAClB;YAEA,OAAO,OAAO,OAAA,GAAUA,YAAU,cAAA,OAAqB,UAAU,YAAA,OAAmB,OAAO,QAAA,GAAW,SAAUC,GAAAA,EAAG;gBACjH,OAAA,OAAcA;YACf,IAAG,SAAUA,GAAAA,EAAG;gBACf,OAAOA,OAAK,cAAA,OAAqB,UAAUA,IAAE,WAAA,KAAgB,UAAUA,QAAM,OAAO,SAAA,GAAY,WAAA,OAAkBA;YACnH,GAAE,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA,EAAS,UAAQ,EAAE;QAC5F;QACD,OAAO,OAAA,GAAUD,WAAS,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCT/F,IAAIE,YAAAA,gBAAAA,CAAiC,UAAA;QACrC,SAASC,cAAY,CAAA,EAAG,CAAA,EAAG;YACzB,IAAI,YAAY,UAAQ,EAAE,IAAA,CAAK,EAAG,CAAA,OAAO;YACzC,IAAI,IAAI,CAAA,CAAE,OAAO,WAAA,CAAA;YACjB,IAAA,KAAS,MAAM,GAAG;gBAChB,IAAI,IAAI,EAAE,IAAA,CAAK,GAAG,KAAK,UAAU;gBACjC,IAAI,YAAY,UAAQ,EAAE,CAAE,CAAA,OAAO;gBACnC,MAAM,IAAI,UAAU;YACrB;YACD,OAAO,CAAC,aAAa,IAAI,SAAS,MAAA,EAAQ,EAAE;QAC7C;QACD,OAAO,OAAA,GAAUA,eAAa,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCXnG,IAAI,UAAA,gBAAA,CAAiC,UAAA;QACrC,IAAI,cAAA;QACJ,SAASC,gBAAc,CAAA,EAAG;YACxB,IAAI,IAAI,YAAY,GAAG,SAAS;YAChC,OAAO,YAAY,QAAQ,EAAE,GAAG,IAAI,IAAI;QACzC;QACD,OAAO,OAAA,GAAUA,iBAAe,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCNrG,IAAI,gBAAA;QACJ,SAAS,gBAAgB,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;YAChC,OAAA,CAAQ,IAAI,cAAc,EAAE,KAAK,IAAI,OAAO,cAAA,CAAe,GAAG,GAAG;gBAC/D,OAAO;gBACP,YAAA,CAAa;gBACb,cAAA,CAAe;gBACf,UAAA,CAAW;YACZ,EAAC,GAAG,CAAA,CAAE,EAAA,GAAK,GAAG;QAChB;QACD,OAAO,OAAA,GAAU,iBAAiB,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCTvG,IAAI,iBAAA;QACJ,SAAS,QAAQ,CAAA,EAAG,CAAA,EAAG;YACrB,IAAI,IAAI,OAAO,IAAA,CAAK,EAAE;YACtB,IAAI,OAAO,qBAAA,EAAuB;gBAChC,IAAI,IAAI,OAAO,qBAAA,CAAsB,EAAE;gBACvC,KAAA,CAAM,IAAI,EAAE,MAAA,CAAO,SAAUC,GAAAA,EAAG;oBAC9B,OAAO,OAAO,wBAAA,CAAyB,GAAGA,IAAE,CAAC,UAAA;gBAC9C,EAAC,GAAG,EAAE,IAAA,CAAK,KAAA,CAAM,GAAG,EAAE;YACxB;YACD,OAAO;QACR;QACD,SAAS,eAAe,CAAA,EAAG;YACzB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAA,EAAQ,IAAK;gBACzC,IAAI,IAAI,QAAQ,SAAA,CAAU,EAAA,GAAK,SAAA,CAAU,EAAA,GAAK,CAAE;gBAChD,IAAI,IAAI,QAAQ,OAAO,EAAE,EAAA,CAAG,EAAE,CAAC,OAAA,CAAQ,SAAUA,GAAAA,EAAG;oBAClD,eAAe,GAAGA,KAAG,CAAA,CAAEA,IAAAA,CAAG;gBAC3B,EAAC,GAAG,OAAO,yBAAA,GAA4B,OAAO,gBAAA,CAAiB,GAAG,OAAO,yBAAA,CAA0B,EAAE,CAAC,GAAG,QAAQ,OAAO,EAAE,CAAC,CAAC,OAAA,CAAQ,SAAUA,GAAAA,EAAG;oBAChJ,OAAO,cAAA,CAAe,GAAGA,KAAG,OAAO,wBAAA,CAAyB,GAAGA,IAAE,CAAC;gBACnE,EAAC;YACH;YACD,OAAO;QACR;QACD,OAAO,OAAA,GAAU,gBAAgB,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;;IC+BhG;IACA;CAAA;;;;;IA3BN,SAAgB,oBACdC,IAAAA,EACAC,KAAAA,EACAC,IAAAA,EACc;IAMd,MAAM,YAAY,KAAK,OAAA,CAAQ,CAAC,OAAS,KAAK,KAAA,CAAM,IAAI,CAAC;IAEzD,IAAA,CAAK,SAAA,CAAA,CAAW,QAAQ,SAAS,KAAA,EAK/B,CAAA,OAAO,UAAU,MAAA,GAAS;QAAC,SAAU;KAAA,GAAI,CAAE,CAAA;IAG7C,IACE,SAAS,eACT,qQAAA,EAAS,MAAM,IAAA,CACd,eAAe,SAAS,YAAY,KAAA,GACrC;QACA,MAAM,EACJ,QAAQ,CAAA,EACR,WAAW,EAAA,EAEZ,GAAA,OADI,iCAAA,CAAA,GAAA,+BAAA,OAAA,EACD,OAAA;QACJ,OAAO;YACL;YACA;gBACE,OAAO;gBACP,MAAM;YACP,CACF;SAAA;IACF;IACD,OAAO;QACL;QAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,OAEa,UAAU,eACnB,2PAAU,YAAA,IAAa;YAAS;QAAO,IACrC,QAAQ,SAAS,SAAS;YAAQ;QAAM,EAE/C;KAAA;AACF;AAED,SAAgB,uBAAuBF,IAAAA,EAAyB;IAC9D,OAAO,oBAAoB,MAAA,KAAA,GAAiB,MAAM;AACnD;;;;;;;GA+BD,SAAgB,YACdG,iBAAAA,EACA,GAAG,OAAA,EACH;IACA,MAAM,CAAC,OAAO,KAAK,GAAG;IAGtB,MAAM,OAAO,kBAAkB,IAAA,EAAM,CAAC,IAAA;IACtC,MAAM,WAAW,oBAAoB,MAAM,OAAO,SAAA,QAAA,SAAA,KAAA,IAAA,OAAQ,MAAM;IAChE,OAAO;AACR;;;;;GAaD,SAAgB,eACdC,SAAAA,EACA;IAEA,MAAM,OAAO,UAAU,IAAA,EAAM,CAAC,IAAA;IAC9B,OAAO,uBAAuB,KAAK;AACpC", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7], "debugId": null}}, {"offset": {"line": 1944, "column": 0}, "map": {"version": 3, "file": "shared-JtnEvJvB.mjs", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Breact-query%4011.4.2_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__%40trpc%2Bclient%4011.4.2_d478052bf51ccf7e080ad331641b9119/node_modules/%40trpc/react-query/src/shared/proxy/decorationProxy.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Breact-query%4011.4.2_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__%40trpc%2Bclient%4011.4.2_d478052bf51ccf7e080ad331641b9119/node_modules/%40trpc/react-query/src/internals/context.tsx", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Breact-query%4011.4.2_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__%40trpc%2Bclient%4011.4.2_d478052bf51ccf7e080ad331641b9119/node_modules/%40trpc/react-query/src/shared/proxy/utilsProxy.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Breact-query%4011.4.2_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__%40trpc%2Bclient%4011.4.2_d478052bf51ccf7e080ad331641b9119/node_modules/%40trpc/react-query/src/shared/proxy/useQueriesProxy.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Breact-query%4011.4.2_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__%40trpc%2Bclient%4011.4.2_d478052bf51ccf7e080ad331641b9119/node_modules/%40trpc/react-query/src/internals/getClientArgs.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Breact-query%4011.4.2_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__%40trpc%2Bclient%4011.4.2_d478052bf51ccf7e080ad331641b9119/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/asyncIterator.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Breact-query%4011.4.2_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__%40trpc%2Bclient%4011.4.2_d478052bf51ccf7e080ad331641b9119/node_modules/%40trpc/react-query/src/internals/trpcResult.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Breact-query%4011.4.2_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__%40trpc%2Bclient%4011.4.2_d478052bf51ccf7e080ad331641b9119/node_modules/%40trpc/react-query/src/utils/createUtilityFunctions.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Breact-query%4011.4.2_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__%40trpc%2Bclient%4011.4.2_d478052bf51ccf7e080ad331641b9119/node_modules/%40trpc/react-query/src/shared/hooks/createHooksInternal.tsx", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Breact-query%4011.4.2_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__%40trpc%2Bclient%4011.4.2_d478052bf51ccf7e080ad331641b9119/node_modules/%40trpc/react-query/src/shared/queryClient.ts"], "sourcesContent": ["import type { AnyRouter } from '@trpc/server/unstable-core-do-not-import';\nimport { createRecursiveProxy } from '@trpc/server/unstable-core-do-not-import';\nimport type { CreateReactQueryHooks } from '../hooks/createHooksInternal';\n\n/**\n * Create proxy for decorating procedures\n * @internal\n */\nexport function createReactDecoration<\n  TRouter extends AnyRouter,\n  TSSRContext = unknown,\n>(hooks: CreateReactQueryHooks<TRouter, TSSRContext>) {\n  return createRecursiveProxy(({ path, args }) => {\n    const pathCopy = [...path];\n\n    // The last arg is for instance `.useMutation` or `.useQuery()`\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const lastArg = pathCopy.pop()!;\n\n    if (lastArg === 'useMutation') {\n      return (hooks as any)[lastArg](pathCopy, ...args);\n    }\n\n    if (lastArg === '_def') {\n      return {\n        path: pathCopy,\n      };\n    }\n\n    const [input, ...rest] = args;\n    const opts = rest[0] ?? {};\n\n    return (hooks as any)[lastArg](pathCopy, input, opts);\n  });\n}\n", "import type {\n  CancelOptions,\n  FetchInfiniteQueryOptions,\n  FetchQueryOptions,\n  InfiniteData,\n  InvalidateOptions,\n  InvalidateQueryFilters,\n  MutationOptions,\n  QueryClient,\n  QueryFilters,\n  QueryKey,\n  RefetchOptions,\n  RefetchQueryFilters,\n  ResetOptions,\n  SetDataOptions,\n  Updater,\n} from '@tanstack/react-query';\nimport type {\n  TRPCClient,\n  TRPCClientError,\n  TRPCRequestOptions,\n  TRPCUntypedClient,\n} from '@trpc/client';\nimport type {\n  AnyClientTypes,\n  AnyRouter,\n  DistributiveOmit,\n} from '@trpc/server/unstable-core-do-not-import';\nimport * as React from 'react';\nimport type {\n  DefinedTRPCInfiniteQueryOptionsIn,\n  DefinedTRPCInfiniteQueryOptionsOut,\n  DefinedTRPCQueryOptionsIn,\n  DefinedTRPCQueryOptionsOut,\n  ExtractCursorType,\n  UndefinedTRPCInfiniteQueryOptionsIn,\n  UndefinedTRPCInfiniteQueryOptionsOut,\n  UndefinedTRPCQueryOptionsIn,\n  UndefinedTRPCQueryOptionsOut,\n} from '../shared';\nimport type { TRPCMutationKey, TRPCQueryKey } from './getQueryKey';\n\ninterface TRPCUseUtilsOptions {\n  /**\n   * tRPC-related options\n   */\n  trpc?: TRPCRequestOptions;\n}\nexport interface TRPCFetchQueryOptions<TOutput, TError>\n  extends DistributiveOmit<FetchQueryOptions<TOutput, TError>, 'queryKey'>,\n    TRPCUseUtilsOptions {\n  //\n}\n\nexport type TRPCFetchInfiniteQueryOptions<TInput, TOutput, TError> =\n  DistributiveOmit<\n    FetchInfiniteQueryOptions<\n      TOutput,\n      TError,\n      TOutput,\n      TRPCQueryKey,\n      ExtractCursorType<TInput>\n    >,\n    'queryKey' | 'initialPageParam'\n  > &\n    TRPCUseUtilsOptions & {\n      initialCursor?: ExtractCursorType<TInput>;\n    };\n\n/** @internal */\nexport type SSRState = 'mounted' | 'mounting' | 'prepass' | false;\n\nexport interface TRPCContextPropsBase<TRouter extends AnyRouter, TSSRContext> {\n  /**\n   * The `TRPCClient`\n   */\n  client: TRPCUntypedClient<TRouter>;\n  /**\n   * The SSR context when server-side rendering\n   * @default null\n   */\n  ssrContext?: TSSRContext | null;\n  /**\n   * State of SSR hydration.\n   * - `false` if not using SSR.\n   * - `prepass` when doing a prepass to fetch queries' data\n   * - `mounting` before TRPCProvider has been rendered on the client\n   * - `mounted` when the TRPCProvider has been rendered on the client\n   * @default false\n   */\n  ssrState?: SSRState;\n  /**\n   * @deprecated pass abortOnUnmount to `createTRPCReact` instead\n   * Abort loading query calls when unmounting a component - usually when navigating to a new page\n   * @default false\n   */\n  abortOnUnmount?: boolean;\n}\n\n/**\n * @internal\n */\nexport type DecoratedTRPCContextProps<\n  TRouter extends AnyRouter,\n  TSSRContext,\n> = TRPCContextPropsBase<TRouter, TSSRContext> & {\n  client: TRPCClient<TRouter>;\n};\n\nexport interface TRPCContextProps<TRouter extends AnyRouter, TSSRContext>\n  extends TRPCContextPropsBase<TRouter, TSSRContext> {\n  /**\n   * The react-query `QueryClient`\n   */\n  queryClient: QueryClient;\n}\n\nexport const contextProps: (keyof TRPCContextPropsBase<any, any>)[] = [\n  'client',\n  'ssrContext',\n  'ssrState',\n  'abortOnUnmount',\n];\n\n/**\n * @internal\n */\nexport interface TRPCContextState<\n  TRouter extends AnyRouter,\n  TSSRContext = undefined,\n> extends Required<TRPCContextProps<TRouter, TSSRContext>>,\n    TRPCQueryUtils<TRouter> {}\n\n/**\n * @internal\n */\nexport interface TRPCQueryUtils<TRouter extends AnyRouter> {\n  /**\n   * @see https://tanstack.com/query/latest/docs/framework/react/reference/queryOptions#queryoptions\n   */\n  queryOptions(\n    path: readonly string[], // <-- look into if needed\n    queryKey: TRPCQueryKey,\n    opts?: UndefinedTRPCQueryOptionsIn<\n      unknown,\n      unknown,\n      TRPCClientError<AnyClientTypes>\n    >,\n  ): UndefinedTRPCQueryOptionsOut<\n    unknown,\n    unknown,\n    TRPCClientError<AnyClientTypes>\n  >;\n  queryOptions(\n    path: readonly string[], // <-- look into if needed\n    queryKey: TRPCQueryKey,\n    opts: DefinedTRPCQueryOptionsIn<\n      unknown,\n      unknown,\n      TRPCClientError<AnyClientTypes>\n    >,\n  ): DefinedTRPCQueryOptionsOut<\n    unknown,\n    unknown,\n    TRPCClientError<AnyClientTypes>\n  >;\n\n  /**\n   * @see https://tanstack.com/query/latest/docs/framework/react/reference/infiniteQueryOptions#infinitequeryoptions\n   */\n  infiniteQueryOptions(\n    path: readonly string[], // <-- look into if needed\n    queryKey: TRPCQueryKey,\n    opts: UndefinedTRPCInfiniteQueryOptionsIn<\n      unknown,\n      unknown,\n      unknown,\n      TRPCClientError<AnyClientTypes>\n    >,\n  ): UndefinedTRPCInfiniteQueryOptionsOut<\n    unknown,\n    unknown,\n    unknown,\n    TRPCClientError<AnyClientTypes>\n  >;\n  infiniteQueryOptions(\n    path: readonly string[], // <-- look into if needed\n    queryKey: TRPCQueryKey,\n    opts: DefinedTRPCInfiniteQueryOptionsIn<\n      unknown,\n      unknown,\n      unknown,\n      TRPCClientError<AnyClientTypes>\n    >,\n  ): DefinedTRPCInfiniteQueryOptionsOut<\n    unknown,\n    unknown,\n    unknown,\n    TRPCClientError<AnyClientTypes>\n  >;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientfetchquery\n   */\n  fetchQuery: (\n    queryKey: TRPCQueryKey,\n    opts?: TRPCFetchQueryOptions<unknown, TRPCClientError<TRouter>>,\n  ) => Promise<unknown>;\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientfetchinfinitequery\n   */\n  fetchInfiniteQuery: (\n    queryKey: TRPCQueryKey,\n    opts?: TRPCFetchInfiniteQueryOptions<\n      unknown,\n      unknown,\n      TRPCClientError<TRouter>\n    >,\n  ) => Promise<InfiniteData<unknown, unknown>>;\n  /**\n   * @see https://tanstack.com/query/v5/docs/framework/react/guides/prefetching\n   */\n  prefetchQuery: (\n    queryKey: TRPCQueryKey,\n    opts?: TRPCFetchQueryOptions<unknown, TRPCClientError<TRouter>>,\n  ) => Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientprefetchinfinitequery\n   */\n  prefetchInfiniteQuery: (\n    queryKey: TRPCQueryKey,\n    opts?: TRPCFetchInfiniteQueryOptions<\n      unknown,\n      unknown,\n      TRPCClientError<TRouter>\n    >,\n  ) => Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientensurequerydata\n   */\n  ensureQueryData: (\n    queryKey: TRPCQueryKey,\n    opts?: TRPCFetchQueryOptions<unknown, TRPCClientError<TRouter>>,\n  ) => Promise<unknown>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/framework/react/guides/query-invalidation\n   */\n  invalidateQueries: (\n    queryKey: TRPCQueryKey,\n    filters?: InvalidateQueryFilters<TRPCQueryKey>,\n    options?: InvalidateOptions,\n  ) => Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientresetqueries\n   */\n  resetQueries: (\n    queryKey: TRPCQueryKey,\n    filters?: QueryFilters<TRPCQueryKey>,\n    options?: ResetOptions,\n  ) => Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientrefetchqueries\n   */\n  refetchQueries: (\n    queryKey: TRPCQueryKey,\n    filters?: RefetchQueryFilters<TRPCQueryKey>,\n    options?: RefetchOptions,\n  ) => Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/framework/react/guides/query-cancellation\n   */\n  cancelQuery: (\n    queryKey: TRPCQueryKey,\n    options?: CancelOptions,\n  ) => Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientsetquerydata\n   */\n  setQueryData: (\n    queryKey: TRPCQueryKey,\n    updater: Updater<unknown, unknown>,\n    options?: SetDataOptions,\n  ) => void;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientsetqueriesdata\n   */\n  setQueriesData: (\n    queryKey: TRPCQueryKey,\n    filters: QueryFilters,\n    updater: Updater<unknown, unknown>,\n    options?: SetDataOptions,\n  ) => [QueryKey, unknown][];\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientgetquerydata\n   */\n  getQueryData: (queryKey: TRPCQueryKey) => unknown;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientsetquerydata\n   */\n  setInfiniteQueryData: (\n    queryKey: TRPCQueryKey,\n    updater: Updater<\n      InfiniteData<unknown> | undefined,\n      InfiniteData<unknown> | undefined\n    >,\n    options?: SetDataOptions,\n  ) => void;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientgetquerydata\n   */\n  getInfiniteQueryData: (\n    queryKey: TRPCQueryKey,\n  ) => InfiniteData<unknown> | undefined;\n\n  /**\n   * @see https://tanstack.com/query/latest/docs/reference/QueryClient/#queryclientsetmutationdefaults\n   */\n  setMutationDefaults: (\n    mutationKey: TRPCMutationKey,\n    options:\n      | MutationOptions\n      | ((args: {\n          canonicalMutationFn: (input: unknown) => Promise<unknown>;\n        }) => MutationOptions),\n  ) => void;\n\n  /**\n   * @see https://tanstack.com/query/latest/docs/reference/QueryClient#queryclientgetmutationdefaults\n   */\n  getMutationDefaults: (\n    mutationKey: TRPCMutationKey,\n  ) => MutationOptions | undefined;\n\n  /**\n   * @see https://tanstack.com/query/latest/docs/reference/QueryClient#queryclientismutating\n   */\n  isMutating: (filters: { mutationKey: TRPCMutationKey }) => number;\n}\nexport const TRPCContext = React.createContext?.(null as any);\n", "import type {\n  CancelOptions,\n  InfiniteData,\n  InvalidateOptions,\n  InvalidateQueryFilters,\n  Query,\n  QueryFilters,\n  QueryKey,\n  RefetchOptions,\n  RefetchQueryFilters,\n  ResetOptions,\n  SetDataOptions,\n  SkipToken,\n  Updater,\n} from '@tanstack/react-query';\nimport type { TRPCClientError } from '@trpc/client';\nimport { createTRPCClientProxy } from '@trpc/client';\nimport type {\n  AnyMutationProcedure,\n  AnyQueryProcedure,\n  AnyRootTypes,\n  AnyRouter,\n  DeepPartial,\n  inferProcedureInput,\n  inferProcedureOutput,\n  inferTransformedProcedureOutput,\n  ProtectedIntersection,\n  RouterRecord,\n} from '@trpc/server/unstable-core-do-not-import';\nimport {\n  createFlatProxy,\n  createRecursiveProxy,\n} from '@trpc/server/unstable-core-do-not-import';\nimport type {\n  DecoratedTRPCContextProps,\n  TRPCContextState,\n  TRPCFetchInfiniteQueryOptions,\n  TRPCFetchQueryOptions,\n  TRPCQueryUtils,\n} from '../../internals/context';\nimport { contextProps } from '../../internals/context';\nimport type { QueryKeyKnown, QueryType } from '../../internals/getQueryKey';\nimport {\n  getMutationKeyInternal,\n  getQueryKeyInternal,\n} from '../../internals/getQueryKey';\nimport type { InferMutationOptions } from '../../utils/inferReactQueryProcedure';\nimport type { ExtractCursorType } from '../hooks/types';\nimport type {\n  DefinedTRPCInfiniteQueryOptionsIn,\n  DefinedTRPCInfiniteQueryOptionsOut,\n  DefinedTRPCQueryOptionsIn,\n  DefinedTRPCQueryOptionsOut,\n  UndefinedTRPCInfiniteQueryOptionsIn,\n  UndefinedTRPCInfiniteQueryOptionsOut,\n  UndefinedTRPCQueryOptionsIn,\n  UndefinedTRPCQueryOptionsOut,\n  UnusedSkipTokenTRPCInfiniteQueryOptionsIn,\n  UnusedSkipTokenTRPCInfiniteQueryOptionsOut,\n  UnusedSkipTokenTRPCQueryOptionsIn,\n  UnusedSkipTokenTRPCQueryOptionsOut,\n} from '../types';\n\nexport type DecorateQueryProcedure<\n  TRoot extends AnyRootTypes,\n  TProcedure extends AnyQueryProcedure,\n> = {\n  /**\n   * @see https://tanstack.com/query/latest/docs/framework/react/reference/queryOptions#queryoptions\n   */\n  queryOptions<\n    TQueryFnData extends inferTransformedProcedureOutput<TRoot, TProcedure>,\n    TData = TQueryFnData,\n  >(\n    input: inferProcedureInput<TProcedure> | SkipToken,\n    opts: DefinedTRPCQueryOptionsIn<\n      TQueryFnData,\n      TData,\n      TRPCClientError<TRoot>\n    >,\n  ): DefinedTRPCQueryOptionsOut<TQueryFnData, TData, TRPCClientError<TRoot>>;\n  /**\n   * @see https://tanstack.com/query/latest/docs/framework/react/reference/queryOptions#queryoptions\n   */\n  queryOptions<\n    TQueryFnData extends inferTransformedProcedureOutput<TRoot, TProcedure>,\n    TData = TQueryFnData,\n  >(\n    input: inferProcedureInput<TProcedure> | SkipToken,\n    opts?: UnusedSkipTokenTRPCQueryOptionsIn<\n      TQueryFnData,\n      TData,\n      TRPCClientError<TRoot>\n    >,\n  ): UnusedSkipTokenTRPCQueryOptionsOut<\n    TQueryFnData,\n    TData,\n    TRPCClientError<TRoot>\n  >;\n  /**\n   * @see https://tanstack.com/query/latest/docs/framework/react/reference/queryOptions#queryoptions\n   */\n  queryOptions<\n    TQueryFnData extends inferTransformedProcedureOutput<TRoot, TProcedure>,\n    TData = TQueryFnData,\n  >(\n    input: inferProcedureInput<TProcedure> | SkipToken,\n    opts?: UndefinedTRPCQueryOptionsIn<\n      TQueryFnData,\n      TData,\n      TRPCClientError<TRoot>\n    >,\n  ): UndefinedTRPCQueryOptionsOut<TQueryFnData, TData, TRPCClientError<TRoot>>;\n\n  /**\n   * @see https://tanstack.com/query/latest/docs/framework/react/reference/infiniteQueryOptions#infinitequeryoptions\n   */\n  infiniteQueryOptions<\n    TQueryFnData extends inferTransformedProcedureOutput<TRoot, TProcedure>,\n    TData = TQueryFnData,\n  >(\n    input: inferProcedureInput<TProcedure> | SkipToken,\n    opts: DefinedTRPCInfiniteQueryOptionsIn<\n      inferProcedureInput<TProcedure>,\n      TQueryFnData,\n      TData,\n      TRPCClientError<TRoot>\n    >,\n  ): DefinedTRPCInfiniteQueryOptionsOut<\n    inferProcedureInput<TProcedure>,\n    TQueryFnData,\n    TData,\n    TRPCClientError<TRoot>\n  >;\n  /**\n   * @see https://tanstack.com/query/latest/docs/framework/react/reference/infiniteQueryOptions#infinitequeryoptions\n   */\n  infiniteQueryOptions<\n    TQueryFnData extends inferTransformedProcedureOutput<TRoot, TProcedure>,\n    TData = TQueryFnData,\n  >(\n    input: inferProcedureInput<TProcedure>,\n    opts: UnusedSkipTokenTRPCInfiniteQueryOptionsIn<\n      inferProcedureInput<TProcedure>,\n      TQueryFnData,\n      TData,\n      TRPCClientError<TRoot>\n    >,\n  ): UnusedSkipTokenTRPCInfiniteQueryOptionsOut<\n    inferProcedureInput<TProcedure>,\n    TQueryFnData,\n    TData,\n    TRPCClientError<TRoot>\n  >;\n  /**\n   * @see https://tanstack.com/query/latest/docs/framework/react/reference/infiniteQueryOptions#infinitequeryoptions\n   */\n  infiniteQueryOptions<\n    TQueryFnData extends inferTransformedProcedureOutput<TRoot, TProcedure>,\n    TData = TQueryFnData,\n  >(\n    input: inferProcedureInput<TProcedure> | SkipToken,\n    opts?: UndefinedTRPCInfiniteQueryOptionsIn<\n      inferProcedureInput<TProcedure>,\n      TQueryFnData,\n      TData,\n      TRPCClientError<TRoot>\n    >,\n  ): UndefinedTRPCInfiniteQueryOptionsOut<\n    inferProcedureInput<TProcedure>,\n    TQueryFnData,\n    TData,\n    TRPCClientError<TRoot>\n  >;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientfetchquery\n   */\n  fetch(\n    input: inferProcedureInput<TProcedure>,\n    opts?: TRPCFetchQueryOptions<\n      inferTransformedProcedureOutput<TRoot, TProcedure>,\n      TRPCClientError<TRoot>\n    >,\n  ): Promise<inferTransformedProcedureOutput<TRoot, TProcedure>>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientfetchinfinitequery\n   */\n  fetchInfinite(\n    input: inferProcedureInput<TProcedure>,\n    opts?: TRPCFetchInfiniteQueryOptions<\n      inferProcedureInput<TProcedure>,\n      inferTransformedProcedureOutput<TRoot, TProcedure>,\n      TRPCClientError<TRoot>\n    >,\n  ): Promise<\n    InfiniteData<\n      inferTransformedProcedureOutput<TRoot, TProcedure>,\n      NonNullable<ExtractCursorType<inferProcedureInput<TProcedure>>> | null\n    >\n  >;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientprefetchquery\n   */\n  prefetch(\n    input: inferProcedureInput<TProcedure>,\n    opts?: TRPCFetchQueryOptions<\n      inferTransformedProcedureOutput<TRoot, TProcedure>,\n      TRPCClientError<TRoot>\n    >,\n  ): Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientprefetchinfinitequery\n   */\n  prefetchInfinite(\n    input: inferProcedureInput<TProcedure>,\n    opts?: TRPCFetchInfiniteQueryOptions<\n      inferProcedureInput<TProcedure>,\n      inferTransformedProcedureOutput<TRoot, TProcedure>,\n      TRPCClientError<TRoot>\n    >,\n  ): Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientensurequerydata\n   */\n  ensureData(\n    input: inferProcedureInput<TProcedure>,\n    opts?: TRPCFetchQueryOptions<\n      inferTransformedProcedureOutput<TRoot, TProcedure>,\n      TRPCClientError<TRoot>\n    >,\n  ): Promise<inferTransformedProcedureOutput<TRoot, TProcedure>>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientinvalidatequeries\n   */\n  invalidate(\n    input?: DeepPartial<inferProcedureInput<TProcedure>>,\n    filters?: Omit<InvalidateQueryFilters, 'predicate'> & {\n      predicate?: (\n        query: Query<\n          inferProcedureOutput<TProcedure>,\n          TRPCClientError<TRoot>,\n          inferTransformedProcedureOutput<TRoot, TProcedure>,\n          QueryKeyKnown<\n            inferProcedureInput<TProcedure>,\n            inferProcedureInput<TProcedure> extends { cursor?: any } | void\n              ? 'infinite'\n              : 'query'\n          >\n        >,\n      ) => boolean;\n    },\n    options?: InvalidateOptions,\n  ): Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientrefetchqueries\n   */\n  refetch(\n    input?: inferProcedureInput<TProcedure>,\n    filters?: RefetchQueryFilters,\n    options?: RefetchOptions,\n  ): Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientcancelqueries\n   */\n  cancel(\n    input?: inferProcedureInput<TProcedure>,\n    options?: CancelOptions,\n  ): Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientresetqueries\n   */\n  reset(\n    input?: inferProcedureInput<TProcedure>,\n    options?: ResetOptions,\n  ): Promise<void>;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientsetquerydata\n   */\n  setData(\n    /**\n     * The input of the procedure\n     */\n    input: inferProcedureInput<TProcedure>,\n    updater: Updater<\n      inferTransformedProcedureOutput<TRoot, TProcedure> | undefined,\n      inferTransformedProcedureOutput<TRoot, TProcedure> | undefined\n    >,\n    options?: SetDataOptions,\n  ): void;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientsetquerydata\n   */\n  setQueriesData(\n    /**\n     * The input of the procedure\n     */\n    input: inferProcedureInput<TProcedure>,\n    filters: QueryFilters,\n    updater: Updater<\n      inferTransformedProcedureOutput<TRoot, TProcedure> | undefined,\n      inferTransformedProcedureOutput<TRoot, TProcedure> | undefined\n    >,\n    options?: SetDataOptions,\n  ): [QueryKey, inferTransformedProcedureOutput<TRoot, TProcedure>];\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientsetquerydata\n   */\n  setInfiniteData(\n    input: inferProcedureInput<TProcedure>,\n    updater: Updater<\n      | InfiniteData<\n          inferTransformedProcedureOutput<TRoot, TProcedure>,\n          NonNullable<ExtractCursorType<inferProcedureInput<TProcedure>>> | null\n        >\n      | undefined,\n      | InfiniteData<\n          inferTransformedProcedureOutput<TRoot, TProcedure>,\n          NonNullable<ExtractCursorType<inferProcedureInput<TProcedure>>> | null\n        >\n      | undefined\n    >,\n    options?: SetDataOptions,\n  ): void;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientgetquerydata\n   */\n  getData(\n    input?: inferProcedureInput<TProcedure>,\n  ): inferTransformedProcedureOutput<TRoot, TProcedure> | undefined;\n\n  /**\n   * @see https://tanstack.com/query/v5/docs/reference/QueryClient#queryclientgetquerydata\n   */\n  getInfiniteData(\n    input?: inferProcedureInput<TProcedure>,\n  ):\n    | InfiniteData<\n        inferTransformedProcedureOutput<TRoot, TProcedure>,\n        NonNullable<ExtractCursorType<inferProcedureInput<TProcedure>>> | null\n      >\n    | undefined;\n};\n\ntype DecorateMutationProcedure<\n  TRoot extends AnyRootTypes,\n  TProcedure extends AnyMutationProcedure,\n> = {\n  setMutationDefaults<TMeta = unknown>(\n    options:\n      | InferMutationOptions<TRoot, TProcedure, TMeta>\n      | ((args: {\n          canonicalMutationFn: NonNullable<\n            InferMutationOptions<TRoot, TProcedure>['mutationFn']\n          >;\n        }) => InferMutationOptions<TRoot, TProcedure, TMeta>),\n  ): void;\n\n  getMutationDefaults(): InferMutationOptions<TRoot, TProcedure> | undefined;\n\n  isMutating(): number;\n};\n\n/**\n * this is the type that is used to add in procedures that can be used on\n * an entire router\n */\ntype DecorateRouter = {\n  /**\n   * Invalidate the full router\n   * @see https://trpc.io/docs/v10/useContext#query-invalidation\n   * @see https://tanstack.com/query/v5/docs/framework/react/guides/query-invalidation\n   */\n  invalidate(\n    input?: undefined,\n    filters?: InvalidateQueryFilters,\n    options?: InvalidateOptions,\n  ): Promise<void>;\n};\n\n/**\n * @internal\n */\nexport type DecoratedProcedureUtilsRecord<\n  TRoot extends AnyRootTypes,\n  TRecord extends RouterRecord,\n> = DecorateRouter & {\n  [TKey in keyof TRecord]: TRecord[TKey] extends infer $Value\n    ? $Value extends AnyQueryProcedure\n      ? DecorateQueryProcedure<TRoot, $Value>\n      : $Value extends AnyMutationProcedure\n        ? DecorateMutationProcedure<TRoot, $Value>\n        : $Value extends RouterRecord\n          ? DecoratedProcedureUtilsRecord<TRoot, $Value> & DecorateRouter\n          : never\n    : never;\n}; // Add functions that should be available at utils root\n\ntype AnyDecoratedProcedure = DecorateQueryProcedure<any, any> &\n  DecorateMutationProcedure<any, any>;\n\nexport type CreateReactUtils<\n  TRouter extends AnyRouter,\n  TSSRContext,\n> = ProtectedIntersection<\n  DecoratedTRPCContextProps<TRouter, TSSRContext>,\n  DecoratedProcedureUtilsRecord<\n    TRouter['_def']['_config']['$types'],\n    TRouter['_def']['record']\n  >\n>;\n\nexport type CreateQueryUtils<TRouter extends AnyRouter> =\n  DecoratedProcedureUtilsRecord<\n    TRouter['_def']['_config']['$types'],\n    TRouter['_def']['record']\n  >;\n\nexport const getQueryType = (\n  utilName: keyof AnyDecoratedProcedure,\n): QueryType => {\n  switch (utilName) {\n    case 'queryOptions':\n    case 'fetch':\n    case 'ensureData':\n    case 'prefetch':\n    case 'getData':\n    case 'setData':\n    case 'setQueriesData':\n      return 'query';\n\n    case 'infiniteQueryOptions':\n    case 'fetchInfinite':\n    case 'prefetchInfinite':\n    case 'getInfiniteData':\n    case 'setInfiniteData':\n      return 'infinite';\n\n    case 'setMutationDefaults':\n    case 'getMutationDefaults':\n    case 'isMutating':\n    case 'cancel':\n    case 'invalidate':\n    case 'refetch':\n    case 'reset':\n      return 'any';\n  }\n};\n\n/**\n * @internal\n */\nfunction createRecursiveUtilsProxy<TRouter extends AnyRouter>(\n  context: TRPCQueryUtils<TRouter>,\n) {\n  return createRecursiveProxy<CreateQueryUtils<TRouter>>((opts) => {\n    const path = [...opts.path];\n    const utilName = path.pop() as keyof AnyDecoratedProcedure;\n    const args = [...opts.args] as Parameters<\n      AnyDecoratedProcedure[typeof utilName]\n    >;\n    const input = args.shift(); // args can now be spread when input removed\n    const queryType = getQueryType(utilName);\n    const queryKey = getQueryKeyInternal(path, input, queryType);\n\n    const contextMap: Record<keyof AnyDecoratedProcedure, () => unknown> = {\n      infiniteQueryOptions: () =>\n        context.infiniteQueryOptions(path, queryKey, args[0]),\n      queryOptions: () => context.queryOptions(path, queryKey, ...args),\n      /**\n       * DecorateQueryProcedure\n       */\n      fetch: () => context.fetchQuery(queryKey, ...args),\n      fetchInfinite: () => context.fetchInfiniteQuery(queryKey, args[0]),\n      prefetch: () => context.prefetchQuery(queryKey, ...args),\n      prefetchInfinite: () => context.prefetchInfiniteQuery(queryKey, args[0]),\n      ensureData: () => context.ensureQueryData(queryKey, ...args),\n      invalidate: () => context.invalidateQueries(queryKey, ...args),\n      reset: () => context.resetQueries(queryKey, ...args),\n      refetch: () => context.refetchQueries(queryKey, ...args),\n      cancel: () => context.cancelQuery(queryKey, ...args),\n      setData: () => {\n        context.setQueryData(queryKey, args[0], args[1]);\n      },\n      setQueriesData: () =>\n        context.setQueriesData(queryKey, args[0], args[1], args[2]),\n      setInfiniteData: () => {\n        context.setInfiniteQueryData(queryKey, args[0], args[1]);\n      },\n      getData: () => context.getQueryData(queryKey),\n      getInfiniteData: () => context.getInfiniteQueryData(queryKey),\n      /**\n       * DecorateMutationProcedure\n       */\n      setMutationDefaults: () =>\n        context.setMutationDefaults(getMutationKeyInternal(path), input),\n      getMutationDefaults: () =>\n        context.getMutationDefaults(getMutationKeyInternal(path)),\n      isMutating: () =>\n        context.isMutating({ mutationKey: getMutationKeyInternal(path) }),\n    };\n\n    return contextMap[utilName]();\n  });\n}\n\n/**\n * @internal\n */\nexport function createReactQueryUtils<TRouter extends AnyRouter, TSSRContext>(\n  context: TRPCContextState<AnyRouter, TSSRContext>,\n) {\n  type CreateReactUtilsReturnType = CreateReactUtils<TRouter, TSSRContext>;\n\n  const clientProxy = createTRPCClientProxy(context.client);\n\n  const proxy = createRecursiveUtilsProxy(\n    context,\n  ) as CreateReactUtilsReturnType;\n\n  return createFlatProxy<CreateReactUtilsReturnType>((key) => {\n    const contextName = key as (typeof contextProps)[number];\n    if (contextName === 'client') {\n      return clientProxy;\n    }\n    if (contextProps.includes(contextName)) {\n      return context[contextName];\n    }\n\n    return proxy[key];\n  });\n}\n\n/**\n * @internal\n */\nexport function createQueryUtilsProxy<TRouter extends AnyRouter>(\n  context: TRPCQueryUtils<TRouter>,\n): CreateQueryUtils<TRouter> {\n  return createRecursiveUtilsProxy(context);\n}\n", "import type { QueryOptions } from '@tanstack/react-query';\nimport type { TRPCClient } from '@trpc/client';\nimport {\n  getUntypedClient,\n  TRPCUntypedClient,\n  type TRPCClientError,\n} from '@trpc/client';\nimport type {\n  AnyProcedure,\n  AnyQueryProcedure,\n  AnyRootTypes,\n  AnyRouter,\n  inferProcedureInput,\n  inferTransformedProcedureOutput,\n  RouterRecord,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { createRecursiveProxy } from '@trpc/server/unstable-core-do-not-import';\nimport { getQueryKeyInternal } from '../../internals/getQueryKey';\nimport type {\n  TrpcQueryOptionsForUseQueries,\n  TrpcQueryOptionsForUseSuspenseQueries,\n} from '../../internals/useQueries';\nimport type { TRPCUseQueryBaseOptions } from '../hooks/types';\n\ntype GetQueryOptions<\n  TRoot extends AnyRootTypes,\n  TProcedure extends AnyProcedure,\n> = <TData = inferTransformedProcedureOutput<TRoot, TProcedure>>(\n  input: inferProcedureInput<TProcedure>,\n  opts?: TrpcQueryOptionsForUseQueries<\n    inferTransformedProcedureOutput<TRoot, TProcedure>,\n    TData,\n    TRPCClientError<TRoot>\n  >,\n) => TrpcQueryOptionsForUseQueries<\n  inferTransformedProcedureOutput<TRoot, TProcedure>,\n  TData,\n  TRPCClientError<TRoot>\n>;\n\n/**\n * @internal\n */\nexport type UseQueriesProcedureRecord<\n  TRoot extends AnyRootTypes,\n  TRecord extends RouterRecord,\n> = {\n  [TKey in keyof TRecord]: TRecord[TKey] extends infer $Value\n    ? $Value extends AnyQueryProcedure\n      ? GetQueryOptions<TRoot, $Value>\n      : $Value extends RouterRecord\n        ? UseQueriesProcedureRecord<TRoot, $Value>\n        : never\n    : never;\n};\n\ntype GetSuspenseQueryOptions<\n  TRoot extends AnyRootTypes,\n  TProcedure extends AnyQueryProcedure,\n> = <TData = inferTransformedProcedureOutput<TRoot, TProcedure>>(\n  input: inferProcedureInput<TProcedure>,\n  opts?: TrpcQueryOptionsForUseSuspenseQueries<\n    inferTransformedProcedureOutput<TRoot, TProcedure>,\n    TData,\n    TRPCClientError<TRoot>\n  >,\n) => TrpcQueryOptionsForUseSuspenseQueries<\n  inferTransformedProcedureOutput<TRoot, TProcedure>,\n  TData,\n  TRPCClientError<TRoot>\n>;\n\n/**\n * @internal\n */\nexport type UseSuspenseQueriesProcedureRecord<\n  TRoot extends AnyRootTypes,\n  TRecord extends RouterRecord,\n> = {\n  [TKey in keyof TRecord]: TRecord[TKey] extends infer $Value\n    ? $Value extends AnyQueryProcedure\n      ? GetSuspenseQueryOptions<TRoot, $Value>\n      : $Value extends RouterRecord\n        ? UseSuspenseQueriesProcedureRecord<TRoot, $Value>\n        : never\n    : never;\n};\n\n/**\n * Create proxy for `useQueries` options\n * @internal\n */\nexport function createUseQueries<TRouter extends AnyRouter>(\n  client: TRPCUntypedClient<TRouter> | TRPCClient<TRouter>,\n) {\n  const untypedClient: TRPCUntypedClient<TRouter> =\n    client instanceof TRPCUntypedClient ? client : getUntypedClient(client);\n\n  return createRecursiveProxy<\n    UseQueriesProcedureRecord<\n      TRouter['_def']['_config']['$types'],\n      TRouter['_def']['record']\n    >\n  >((opts) => {\n    const arrayPath = opts.path;\n    const dotPath = arrayPath.join('.');\n    const [input, _opts] = opts.args as [\n      unknown,\n      Partial<QueryOptions> & TRPCUseQueryBaseOptions,\n    ];\n\n    const options: QueryOptions = {\n      queryKey: getQueryKeyInternal(arrayPath, input, 'query'),\n      queryFn: () => {\n        return untypedClient.query(dotPath, input, _opts?.trpc);\n      },\n      ..._opts,\n    };\n\n    return options;\n  });\n}\n", "import type { TRPCQuery<PERSON>ey } from './getQueryKey';\n\n/**\n * @internal\n */\nexport function getClientArgs<TOptions>(\n  queryKey: TRPCQuery<PERSON>ey,\n  opts: TOptions,\n  infiniteParams?: {\n    pageParam: any;\n    direction: 'forward' | 'backward';\n  },\n) {\n  const path = queryKey[0];\n  let input = queryKey[1]?.input;\n  if (infiniteParams) {\n    input = {\n      ...(input ?? {}),\n      ...(infiniteParams.pageParam ? { cursor: infiniteParams.pageParam } : {}),\n      direction: infiniteParams.direction,\n    };\n  }\n  return [path.join('.'), input, (opts as any)?.trpc] as const;\n}\n", "function _asyncIterator(r) {\n  var n,\n    t,\n    o,\n    e = 2;\n  for (\"undefined\" != typeof Symbol && (t = Symbol.asyncIterator, o = Symbol.iterator); e--;) {\n    if (t && null != (n = r[t])) return n.call(r);\n    if (o && null != (n = r[o])) return new AsyncFromSyncIterator(n.call(r));\n    t = \"@@asyncIterator\", o = \"@@iterator\";\n  }\n  throw new TypeError(\"Object is not async iterable\");\n}\nfunction AsyncFromSyncIterator(r) {\n  function AsyncFromSyncIteratorContinuation(r) {\n    if (Object(r) !== r) return Promise.reject(new TypeError(r + \" is not an object.\"));\n    var n = r.done;\n    return Promise.resolve(r.value).then(function (r) {\n      return {\n        value: r,\n        done: n\n      };\n    });\n  }\n  return AsyncFromSyncIterator = function AsyncFromSyncIterator(r) {\n    this.s = r, this.n = r.next;\n  }, AsyncFromSyncIterator.prototype = {\n    s: null,\n    n: null,\n    next: function next() {\n      return AsyncFromSyncIteratorContinuation(this.n.apply(this.s, arguments));\n    },\n    \"return\": function _return(r) {\n      var n = this.s[\"return\"];\n      return void 0 === n ? Promise.resolve({\n        value: r,\n        done: !0\n      }) : AsyncFromSyncIteratorContinuation(n.apply(this.s, arguments));\n    },\n    \"throw\": function _throw(r) {\n      var n = this.s[\"return\"];\n      return void 0 === n ? Promise.reject(r) : AsyncFromSyncIteratorContinuation(n.apply(this.s, arguments));\n    }\n  }, new AsyncFromSyncIterator(r);\n}\nmodule.exports = _asyncIterator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import type { QueryClient } from '@tanstack/react-query';\nimport * as React from 'react';\nimport type { TRPCQueryOptionsResult } from '../shared';\nimport type { TRPCHookResult } from '../shared/hooks/types';\nimport type { TRPCQueryKey } from './getQueryKey';\n\nexport function createTRPCOptionsResult(value: {\n  path: readonly string[];\n}): TRPCQueryOptionsResult['trpc'] {\n  const path = value.path.join('.');\n\n  return {\n    path,\n  };\n}\n\n/**\n * Makes a stable reference of the `trpc` prop\n */\nexport function useHookResult(value: {\n  path: readonly string[];\n}): TRPCHookResult['trpc'] {\n  const result = createTRPCOptionsResult(value);\n  return React.useMemo(() => result, [result]);\n}\n\n/**\n * @internal\n */\nexport async function buildQueryFromAsyncIterable(\n  asyncIterable: AsyncIterable<unknown>,\n  queryClient: QueryClient,\n  queryKey: TRPCQuery<PERSON>ey,\n) {\n  const queryCache = queryClient.getQueryCache();\n\n  const query = queryCache.build(queryClient, {\n    queryKey,\n  });\n\n  query.setState({\n    data: [],\n    status: 'success',\n  });\n\n  const aggregate: unknown[] = [];\n  for await (const value of asyncIterable) {\n    aggregate.push(value);\n\n    query.setState({\n      data: [...aggregate],\n    });\n  }\n  return aggregate;\n}\n", "import type { QueryFunctionContext } from '@tanstack/react-query';\nimport {\n  infiniteQueryOptions,\n  queryOptions,\n  skipToken,\n  type QueryClient,\n} from '@tanstack/react-query';\nimport type { TRPCClient, TRPCClientError } from '@trpc/client';\nimport { getUntypedClient, TRPCUntypedClient } from '@trpc/client';\nimport type { AnyRouter } from '@trpc/server/unstable-core-do-not-import';\nimport { isAsyncIterable } from '@trpc/server/unstable-core-do-not-import';\nimport type { AnyClientTypes } from '@trpc/server/unstable-core-do-not-import/clientish/inferrable';\nimport { getClientArgs } from '../internals/getClientArgs';\nimport type { TRPCQueryKey } from '../internals/getQueryKey';\nimport {\n  buildQueryFromAsyncIterable,\n  createTRPCOptionsResult,\n} from '../internals/trpcResult';\nimport type { DefinedTRPCQueryOptionsOut } from '../shared';\nimport { type TRPCQueryUtils } from '../shared';\n\nexport interface CreateQueryUtilsOptions<TRouter extends AnyRouter> {\n  /**\n   * The `TRPCClient`\n   */\n  client: TRPCClient<TRouter> | TRPCUntypedClient<TRouter>;\n  /**\n   * The `QueryClient` from `react-query`\n   */\n  queryClient: QueryClient;\n}\n\n/**\n * Creates a set of utility functions that can be used to interact with `react-query`\n * @param opts the `TRPCClient` and `QueryClient` to use\n * @returns a set of utility functions that can be used to interact with `react-query`\n * @internal\n */\nexport function createUtilityFunctions<TRouter extends AnyRouter>(\n  opts: CreateQueryUtilsOptions<TRouter>,\n): TRPCQueryUtils<TRouter> {\n  const { client, queryClient } = opts;\n  const untypedClient =\n    client instanceof TRPCUntypedClient ? client : getUntypedClient(client);\n\n  return {\n    infiniteQueryOptions: (path, queryKey, opts) => {\n      const inputIsSkipToken = queryKey[1]?.input === skipToken;\n\n      const queryFn = async (\n        queryFnContext: QueryFunctionContext<TRPCQueryKey, unknown>,\n      ): Promise<unknown> => {\n        const actualOpts = {\n          ...opts,\n          trpc: {\n            ...opts?.trpc,\n            ...(opts?.trpc?.abortOnUnmount\n              ? { signal: queryFnContext.signal }\n              : { signal: null }),\n          },\n        };\n\n        const result = await untypedClient.query(\n          ...getClientArgs(queryKey, actualOpts, {\n            direction: queryFnContext.direction,\n            pageParam: queryFnContext.pageParam,\n          }),\n        );\n\n        return result;\n      };\n\n      return Object.assign(\n        infiniteQueryOptions({\n          ...opts,\n          initialData: opts?.initialData as any,\n          queryKey,\n          queryFn: inputIsSkipToken ? skipToken : queryFn,\n          initialPageParam: (opts?.initialCursor as any) ?? null,\n        }),\n        { trpc: createTRPCOptionsResult({ path }) },\n      );\n    },\n\n    queryOptions: (path, queryKey, opts) => {\n      const inputIsSkipToken = queryKey[1]?.input === skipToken;\n\n      const queryFn = async (\n        queryFnContext: QueryFunctionContext<TRPCQueryKey>,\n      ): Promise<unknown> => {\n        const actualOpts = {\n          ...opts,\n          trpc: {\n            ...opts?.trpc,\n            ...(opts?.trpc?.abortOnUnmount\n              ? { signal: queryFnContext.signal }\n              : { signal: null }),\n          },\n        };\n\n        const result = await untypedClient.query(\n          ...getClientArgs(queryKey, actualOpts),\n        );\n\n        if (isAsyncIterable(result)) {\n          return buildQueryFromAsyncIterable(result, queryClient, queryKey);\n        }\n\n        return result;\n      };\n\n      return Object.assign(\n        queryOptions({\n          ...opts,\n          initialData: opts?.initialData,\n          queryKey,\n          queryFn: inputIsSkipToken ? skipToken : queryFn,\n        }),\n        { trpc: createTRPCOptionsResult({ path }) },\n      ) as DefinedTRPCQueryOptionsOut<\n        unknown,\n        unknown,\n        TRPCClientError<AnyClientTypes>\n      >;\n    },\n\n    fetchQuery: (queryKey, opts) => {\n      return queryClient.fetchQuery({\n        ...opts,\n        queryKey,\n        queryFn: () => untypedClient.query(...getClientArgs(queryKey, opts)),\n      });\n    },\n\n    fetchInfiniteQuery: (queryKey, opts) => {\n      return queryClient.fetchInfiniteQuery({\n        ...opts,\n        queryKey,\n        queryFn: ({ pageParam, direction }) => {\n          return untypedClient.query(\n            ...getClientArgs(queryKey, opts, { pageParam, direction }),\n          );\n        },\n        initialPageParam: opts?.initialCursor ?? null,\n      });\n    },\n\n    prefetchQuery: (queryKey, opts) => {\n      return queryClient.prefetchQuery({\n        ...opts,\n        queryKey,\n        queryFn: () => untypedClient.query(...getClientArgs(queryKey, opts)),\n      });\n    },\n\n    prefetchInfiniteQuery: (queryKey, opts) => {\n      return queryClient.prefetchInfiniteQuery({\n        ...opts,\n        queryKey,\n        queryFn: ({ pageParam, direction }) => {\n          return untypedClient.query(\n            ...getClientArgs(queryKey, opts, { pageParam, direction }),\n          );\n        },\n        initialPageParam: opts?.initialCursor ?? null,\n      });\n    },\n\n    ensureQueryData: (queryKey, opts) => {\n      return queryClient.ensureQueryData({\n        ...opts,\n        queryKey,\n        queryFn: () => untypedClient.query(...getClientArgs(queryKey, opts)),\n      });\n    },\n\n    invalidateQueries: (queryKey, filters, options) => {\n      return queryClient.invalidateQueries(\n        {\n          ...filters,\n          queryKey,\n        },\n        options,\n      );\n    },\n    resetQueries: (queryKey, filters, options) => {\n      return queryClient.resetQueries(\n        {\n          ...filters,\n          queryKey,\n        },\n        options,\n      );\n    },\n\n    refetchQueries: (queryKey, filters, options) => {\n      return queryClient.refetchQueries(\n        {\n          ...filters,\n          queryKey,\n        },\n        options,\n      );\n    },\n\n    cancelQuery: (queryKey, options) => {\n      return queryClient.cancelQueries(\n        {\n          queryKey,\n        },\n        options,\n      );\n    },\n\n    setQueryData: (queryKey, updater, options) => {\n      return queryClient.setQueryData(queryKey, updater as any, options);\n    },\n\n    // eslint-disable-next-line max-params\n    setQueriesData: (queryKey, filters, updater, options) => {\n      return queryClient.setQueriesData(\n        {\n          ...filters,\n          queryKey,\n        },\n        updater,\n        options,\n      );\n    },\n\n    getQueryData: (queryKey) => {\n      return queryClient.getQueryData(queryKey);\n    },\n\n    setInfiniteQueryData: (queryKey, updater, options) => {\n      return queryClient.setQueryData(queryKey, updater as any, options);\n    },\n\n    getInfiniteQueryData: (queryKey) => {\n      return queryClient.getQueryData(queryKey);\n    },\n\n    setMutationDefaults: (mutationKey, options) => {\n      const path = mutationKey[0];\n      const canonicalMutationFn = (input: unknown) => {\n        return untypedClient.mutation(\n          ...getClientArgs([path, { input }], opts),\n        );\n      };\n      return queryClient.setMutationDefaults(\n        mutationKey,\n        typeof options === 'function'\n          ? options({ canonicalMutationFn })\n          : options,\n      );\n    },\n\n    getMutationDefaults: (mutationKey) => {\n      return queryClient.getMutationDefaults(mutationKey);\n    },\n\n    isMutating: (filters) => {\n      return queryClient.isMutating({\n        ...filters,\n        exact: true,\n      });\n    },\n  };\n}\n", "// TODO: Look into fixing react-compiler support\n/* eslint-disable react-hooks/react-compiler */\nimport {\n  useInfiniteQuery as __useInfiniteQuery,\n  useMutation as __useMutation,\n  usePrefetchInfiniteQuery as __usePrefetchInfiniteQuery,\n  useQueries as __useQueries,\n  useQuery as __useQuery,\n  useSuspenseInfiniteQuery as __useSuspenseInfiniteQuery,\n  useSuspenseQueries as __useSuspenseQueries,\n  useSuspenseQuery as __useSuspenseQuery,\n  usePrefetchQuery as _usePrefetchQuery,\n  hashKey,\n  skipToken,\n} from '@tanstack/react-query';\nimport type { TRPCClientErrorLike } from '@trpc/client';\nimport {\n  createTRPCClient,\n  getUntypedClient,\n  TRPCUntypedClient,\n} from '@trpc/client';\nimport type { Unsubscribable } from '@trpc/server/observable';\nimport type { AnyRouter } from '@trpc/server/unstable-core-do-not-import';\nimport { isAsyncIterable } from '@trpc/server/unstable-core-do-not-import';\nimport * as React from 'react';\nimport type { SSRState, TRPCContextState } from '../../internals/context';\nimport { TRPCContext } from '../../internals/context';\nimport { getClientArgs } from '../../internals/getClientArgs';\nimport type { TRPCQueryKey } from '../../internals/getQueryKey';\nimport {\n  getMutationKeyInternal,\n  getQueryKeyInternal,\n} from '../../internals/getQueryKey';\nimport {\n  buildQueryFromAsyncIterable,\n  useHookResult,\n} from '../../internals/trpcResult';\nimport type {\n  TRPCUseQueries,\n  TRPCUseSuspenseQueries,\n} from '../../internals/useQueries';\nimport { createUtilityFunctions } from '../../utils/createUtilityFunctions';\nimport { createUseQueries } from '../proxy/useQueriesProxy';\nimport type { CreateTRPCReactOptions, UseMutationOverride } from '../types';\nimport type {\n  TRPCProvider,\n  TRPCQueryOptions,\n  TRPCSubscriptionConnectingResult,\n  TRPCSubscriptionIdleResult,\n  TRPCSubscriptionResult,\n  UseTRPCInfiniteQueryOptions,\n  UseTRPCInfiniteQueryResult,\n  UseTRPCMutationOptions,\n  UseTRPCMutationResult,\n  UseTRPCPrefetchInfiniteQueryOptions,\n  UseTRPCPrefetchQueryOptions,\n  UseTRPCQueryOptions,\n  UseTRPCQueryResult,\n  UseTRPCSubscriptionOptions,\n  UseTRPCSuspenseInfiniteQueryOptions,\n  UseTRPCSuspenseInfiniteQueryResult,\n  UseTRPCSuspenseQueryOptions,\n  UseTRPCSuspenseQueryResult,\n} from './types';\n\nconst trackResult = <T extends object>(\n  result: T,\n  onTrackResult: (key: keyof T) => void,\n): T => {\n  const trackedResult = new Proxy(result, {\n    get(target, prop) {\n      onTrackResult(prop as keyof T);\n      return target[prop as keyof T];\n    },\n  });\n\n  return trackedResult;\n};\n\n/**\n * @internal\n */\nexport function createRootHooks<\n  TRouter extends AnyRouter,\n  TSSRContext = unknown,\n>(config?: CreateTRPCReactOptions<TRouter>) {\n  const mutationSuccessOverride: UseMutationOverride['onSuccess'] =\n    config?.overrides?.useMutation?.onSuccess ??\n    ((options) => options.originalFn());\n\n  type TError = TRPCClientErrorLike<TRouter>;\n\n  type ProviderContext = TRPCContextState<TRouter, TSSRContext>;\n\n  const Context = (config?.context ??\n    TRPCContext) as React.Context<ProviderContext>;\n\n  const createClient = createTRPCClient<TRouter>;\n\n  const TRPCProvider: TRPCProvider<TRouter, TSSRContext> = (props) => {\n    const { abortOnUnmount = false, queryClient, ssrContext } = props;\n    const [ssrState, setSSRState] = React.useState<SSRState>(\n      props.ssrState ?? false,\n    );\n\n    const client: TRPCUntypedClient<TRouter> =\n      props.client instanceof TRPCUntypedClient\n        ? props.client\n        : getUntypedClient(props.client);\n\n    const fns = React.useMemo(\n      () =>\n        createUtilityFunctions({\n          client,\n          queryClient,\n        }),\n      [client, queryClient],\n    );\n\n    const contextValue = React.useMemo<ProviderContext>(\n      () => ({\n        abortOnUnmount,\n        queryClient,\n        client,\n        ssrContext: ssrContext ?? null,\n        ssrState,\n        ...fns,\n      }),\n      [abortOnUnmount, client, fns, queryClient, ssrContext, ssrState],\n    );\n\n    React.useEffect(() => {\n      // Only updating state to `mounted` if we are using SSR.\n      // This makes it so we don't have an unnecessary re-render when opting out of SSR.\n      setSSRState((state) => (state ? 'mounted' : false));\n    }, []);\n    return (\n      <Context.Provider value={contextValue}>{props.children}</Context.Provider>\n    );\n  };\n\n  function useContext() {\n    const context = React.useContext(Context);\n\n    if (!context) {\n      throw new Error(\n        'Unable to find tRPC Context. Did you forget to wrap your App inside `withTRPC` HoC?',\n      );\n    }\n    return context;\n  }\n\n  /**\n   * Hack to make sure errors return `status`='error` when doing SSR\n   * @see https://github.com/trpc/trpc/pull/1645\n   */\n  function useSSRQueryOptionsIfNeeded<\n    TOptions extends { retryOnMount?: boolean } | undefined,\n  >(queryKey: TRPCQueryKey, opts: TOptions): TOptions {\n    const { queryClient, ssrState } = useContext();\n    return ssrState &&\n      ssrState !== 'mounted' &&\n      queryClient.getQueryCache().find({ queryKey })?.state.status === 'error'\n      ? {\n          retryOnMount: false,\n          ...opts,\n        }\n      : opts;\n  }\n\n  function useQuery(\n    path: readonly string[],\n    input: unknown,\n    opts?: UseTRPCQueryOptions<unknown, unknown, TError>,\n  ): UseTRPCQueryResult<unknown, TError> {\n    const context = useContext();\n    const { abortOnUnmount, client, ssrState, queryClient, prefetchQuery } =\n      context;\n    const queryKey = getQueryKeyInternal(path, input, 'query');\n\n    const defaultOpts = queryClient.getQueryDefaults(queryKey);\n\n    const isInputSkipToken = input === skipToken;\n\n    if (\n      typeof window === 'undefined' &&\n      ssrState === 'prepass' &&\n      opts?.trpc?.ssr !== false &&\n      (opts?.enabled ?? defaultOpts?.enabled) !== false &&\n      !isInputSkipToken &&\n      !queryClient.getQueryCache().find({ queryKey })\n    ) {\n      void prefetchQuery(queryKey, opts as any);\n    }\n    const ssrOpts = useSSRQueryOptionsIfNeeded(queryKey, {\n      ...defaultOpts,\n      ...opts,\n    });\n\n    const shouldAbortOnUnmount =\n      opts?.trpc?.abortOnUnmount ?? config?.abortOnUnmount ?? abortOnUnmount;\n\n    const hook = __useQuery(\n      {\n        ...ssrOpts,\n        queryKey: queryKey as any,\n        queryFn: isInputSkipToken\n          ? input\n          : async (queryFunctionContext) => {\n              const actualOpts = {\n                ...ssrOpts,\n                trpc: {\n                  ...ssrOpts?.trpc,\n                  ...(shouldAbortOnUnmount\n                    ? { signal: queryFunctionContext.signal }\n                    : { signal: null }),\n                },\n              };\n\n              const result = await client.query(\n                ...getClientArgs(queryKey, actualOpts),\n              );\n\n              if (isAsyncIterable(result)) {\n                return buildQueryFromAsyncIterable(\n                  result,\n                  queryClient,\n                  queryKey,\n                );\n              }\n              return result;\n            },\n      },\n      queryClient,\n    ) as UseTRPCQueryResult<unknown, TError>;\n\n    hook.trpc = useHookResult({\n      path,\n    });\n\n    return hook;\n  }\n\n  function usePrefetchQuery(\n    path: string[],\n    input: unknown,\n    opts?: UseTRPCPrefetchQueryOptions<unknown, unknown, TError>,\n  ): void {\n    const context = useContext();\n    const queryKey = getQueryKeyInternal(path, input, 'query');\n\n    const isInputSkipToken = input === skipToken;\n\n    const shouldAbortOnUnmount =\n      opts?.trpc?.abortOnUnmount ??\n      config?.abortOnUnmount ??\n      context.abortOnUnmount;\n\n    _usePrefetchQuery({\n      ...opts,\n      queryKey: queryKey as any,\n      queryFn: isInputSkipToken\n        ? input\n        : (queryFunctionContext) => {\n            const actualOpts = {\n              trpc: {\n                ...opts?.trpc,\n                ...(shouldAbortOnUnmount\n                  ? { signal: queryFunctionContext.signal }\n                  : {}),\n              },\n            };\n\n            return context.client.query(...getClientArgs(queryKey, actualOpts));\n          },\n    });\n  }\n\n  function useSuspenseQuery(\n    path: readonly string[],\n    input: unknown,\n    opts?: UseTRPCSuspenseQueryOptions<unknown, unknown, TError>,\n  ): UseTRPCSuspenseQueryResult<unknown, TError> {\n    const context = useContext();\n    const queryKey = getQueryKeyInternal(path, input, 'query');\n\n    const shouldAbortOnUnmount =\n      opts?.trpc?.abortOnUnmount ??\n      config?.abortOnUnmount ??\n      context.abortOnUnmount;\n\n    const hook = __useSuspenseQuery(\n      {\n        ...opts,\n        queryKey: queryKey as any,\n        queryFn: (queryFunctionContext) => {\n          const actualOpts = {\n            ...opts,\n            trpc: {\n              ...opts?.trpc,\n              ...(shouldAbortOnUnmount\n                ? { signal: queryFunctionContext.signal }\n                : { signal: null }),\n            },\n          };\n\n          return context.client.query(...getClientArgs(queryKey, actualOpts));\n        },\n      },\n      context.queryClient,\n    ) as UseTRPCQueryResult<unknown, TError>;\n\n    hook.trpc = useHookResult({\n      path,\n    });\n\n    return [hook.data, hook as any];\n  }\n\n  function useMutation(\n    path: readonly string[],\n    opts?: UseTRPCMutationOptions<unknown, TError, unknown, unknown>,\n  ): UseTRPCMutationResult<unknown, TError, unknown, unknown> {\n    const { client, queryClient } = useContext();\n\n    const mutationKey = getMutationKeyInternal(path);\n\n    const defaultOpts = queryClient.defaultMutationOptions(\n      queryClient.getMutationDefaults(mutationKey),\n    );\n\n    const hook = __useMutation(\n      {\n        ...opts,\n        mutationKey: mutationKey,\n        mutationFn: (input) => {\n          return client.mutation(...getClientArgs([path, { input }], opts));\n        },\n        onSuccess(...args) {\n          const originalFn = () =>\n            opts?.onSuccess?.(...args) ?? defaultOpts?.onSuccess?.(...args);\n\n          return mutationSuccessOverride({\n            originalFn,\n            queryClient,\n            meta: opts?.meta ?? defaultOpts?.meta ?? {},\n          });\n        },\n      },\n      queryClient,\n    ) as UseTRPCMutationResult<unknown, TError, unknown, unknown>;\n\n    hook.trpc = useHookResult({\n      path,\n    });\n\n    return hook;\n  }\n  const initialStateIdle: Omit<TRPCSubscriptionIdleResult<unknown>, 'reset'> = {\n    data: undefined,\n    error: null,\n    status: 'idle',\n  };\n\n  const initialStateConnecting: Omit<\n    TRPCSubscriptionConnectingResult<unknown, TError>,\n    'reset'\n  > = {\n    data: undefined,\n    error: null,\n    status: 'connecting',\n  };\n\n  /* istanbul ignore next -- @preserve */\n  function useSubscription(\n    path: readonly string[],\n    input: unknown,\n    opts: UseTRPCSubscriptionOptions<unknown, TError>,\n  ) {\n    const enabled = opts?.enabled ?? input !== skipToken;\n    const queryKey = hashKey(getQueryKeyInternal(path, input, 'any'));\n    const { client } = useContext();\n\n    const optsRef = React.useRef<typeof opts>(opts);\n    React.useEffect(() => {\n      optsRef.current = opts;\n    });\n\n    type $Result = TRPCSubscriptionResult<unknown, TError>;\n\n    const [trackedProps] = React.useState(new Set<keyof $Result>([]));\n\n    const addTrackedProp = React.useCallback(\n      (key: keyof $Result) => {\n        trackedProps.add(key);\n      },\n      [trackedProps],\n    );\n\n    const currentSubscriptionRef = React.useRef<Unsubscribable>(null);\n\n    const updateState = React.useCallback(\n      (callback: (prevState: $Result) => $Result) => {\n        const prev = resultRef.current;\n        const next = (resultRef.current = callback(prev));\n\n        let shouldUpdate = false;\n        for (const key of trackedProps) {\n          if (prev[key] !== next[key]) {\n            shouldUpdate = true;\n            break;\n          }\n        }\n        if (shouldUpdate) {\n          setState(trackResult(next, addTrackedProp));\n        }\n      },\n      [addTrackedProp, trackedProps],\n    );\n\n    const reset = React.useCallback((): void => {\n      // unsubscribe from the previous subscription\n      currentSubscriptionRef.current?.unsubscribe();\n\n      if (!enabled) {\n        updateState(() => ({ ...initialStateIdle, reset }));\n        return;\n      }\n      updateState(() => ({ ...initialStateConnecting, reset }));\n      const subscription = client.subscription(\n        path.join('.'),\n        input ?? undefined,\n        {\n          onStarted: () => {\n            optsRef.current.onStarted?.();\n            updateState((prev) => ({\n              ...prev,\n              status: 'pending',\n              error: null,\n            }));\n          },\n          onData: (data) => {\n            optsRef.current.onData?.(data);\n            updateState((prev) => ({\n              ...prev,\n              status: 'pending',\n              data,\n              error: null,\n            }));\n          },\n          onError: (error) => {\n            optsRef.current.onError?.(error);\n            updateState((prev) => ({\n              ...prev,\n              status: 'error',\n              error,\n            }));\n          },\n          onConnectionStateChange: (result) => {\n            updateState((prev) => {\n              switch (result.state) {\n                case 'idle':\n                  return {\n                    ...prev,\n                    status: result.state,\n                    error: null,\n                    data: undefined,\n                  };\n                case 'connecting':\n                  return {\n                    ...prev,\n                    error: result.error,\n                    status: result.state,\n                  };\n\n                case 'pending':\n                  // handled when data is / onStarted\n                  return prev;\n              }\n            });\n          },\n          onComplete: () => {\n            optsRef.current.onComplete?.();\n\n            // In the case of WebSockets, the connection might not be idle so `onConnectionStateChange` will not be called until the connection is closed.\n            // In this case, we need to set the state to idle manually.\n            updateState((prev) => ({\n              ...prev,\n              status: 'idle',\n              error: null,\n              data: undefined,\n            }));\n\n            // (We might want to add a `connectionState` to the state to track the connection state separately)\n          },\n        },\n      );\n\n      currentSubscriptionRef.current = subscription;\n\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [client, queryKey, enabled, updateState]);\n    React.useEffect(() => {\n      reset();\n\n      return () => {\n        currentSubscriptionRef.current?.unsubscribe();\n      };\n    }, [reset]);\n\n    const resultRef = React.useRef<$Result>(\n      enabled\n        ? { ...initialStateConnecting, reset }\n        : { ...initialStateIdle, reset },\n    );\n\n    const [state, setState] = React.useState<$Result>(\n      trackResult(resultRef.current, addTrackedProp),\n    );\n\n    return state;\n  }\n\n  function useInfiniteQuery(\n    path: readonly string[],\n    input: unknown,\n    opts: UseTRPCInfiniteQueryOptions<unknown, unknown, TError>,\n  ): UseTRPCInfiniteQueryResult<unknown, TError, unknown> {\n    const {\n      client,\n      ssrState,\n      prefetchInfiniteQuery,\n      queryClient,\n      abortOnUnmount,\n    } = useContext();\n    const queryKey = getQueryKeyInternal(path, input, 'infinite');\n\n    const defaultOpts = queryClient.getQueryDefaults(queryKey);\n\n    const isInputSkipToken = input === skipToken;\n\n    if (\n      typeof window === 'undefined' &&\n      ssrState === 'prepass' &&\n      opts?.trpc?.ssr !== false &&\n      (opts?.enabled ?? defaultOpts?.enabled) !== false &&\n      !isInputSkipToken &&\n      !queryClient.getQueryCache().find({ queryKey })\n    ) {\n      void prefetchInfiniteQuery(queryKey, { ...defaultOpts, ...opts } as any);\n    }\n\n    const ssrOpts = useSSRQueryOptionsIfNeeded(queryKey, {\n      ...defaultOpts,\n      ...opts,\n    });\n\n    // request option should take priority over global\n    const shouldAbortOnUnmount = opts?.trpc?.abortOnUnmount ?? abortOnUnmount;\n\n    const hook = __useInfiniteQuery(\n      {\n        ...ssrOpts,\n        initialPageParam: opts.initialCursor ?? null,\n        persister: opts.persister,\n        queryKey: queryKey as any,\n        queryFn: isInputSkipToken\n          ? input\n          : (queryFunctionContext) => {\n              const actualOpts = {\n                ...ssrOpts,\n                trpc: {\n                  ...ssrOpts?.trpc,\n                  ...(shouldAbortOnUnmount\n                    ? { signal: queryFunctionContext.signal }\n                    : { signal: null }),\n                },\n              };\n\n              return client.query(\n                ...getClientArgs(queryKey, actualOpts, {\n                  pageParam:\n                    queryFunctionContext.pageParam ?? opts.initialCursor,\n                  direction: queryFunctionContext.direction,\n                }),\n              );\n            },\n      },\n      queryClient,\n    ) as UseTRPCInfiniteQueryResult<unknown, TError, unknown>;\n\n    hook.trpc = useHookResult({\n      path,\n    });\n    return hook;\n  }\n\n  function usePrefetchInfiniteQuery(\n    path: string[],\n    input: unknown,\n    opts: UseTRPCPrefetchInfiniteQueryOptions<unknown, unknown, TError>,\n  ): void {\n    const context = useContext();\n    const queryKey = getQueryKeyInternal(path, input, 'infinite');\n\n    const defaultOpts = context.queryClient.getQueryDefaults(queryKey);\n\n    const isInputSkipToken = input === skipToken;\n\n    const ssrOpts = useSSRQueryOptionsIfNeeded(queryKey, {\n      ...defaultOpts,\n      ...opts,\n    });\n\n    // request option should take priority over global\n    const shouldAbortOnUnmount =\n      opts?.trpc?.abortOnUnmount ?? context.abortOnUnmount;\n\n    __usePrefetchInfiniteQuery({\n      ...opts,\n      initialPageParam: opts.initialCursor ?? null,\n      queryKey,\n      queryFn: isInputSkipToken\n        ? input\n        : (queryFunctionContext) => {\n            const actualOpts = {\n              ...ssrOpts,\n              trpc: {\n                ...ssrOpts?.trpc,\n                ...(shouldAbortOnUnmount\n                  ? { signal: queryFunctionContext.signal }\n                  : {}),\n              },\n            };\n\n            return context.client.query(\n              ...getClientArgs(queryKey, actualOpts, {\n                pageParam: queryFunctionContext.pageParam ?? opts.initialCursor,\n                direction: queryFunctionContext.direction,\n              }),\n            );\n          },\n    });\n  }\n\n  function useSuspenseInfiniteQuery(\n    path: readonly string[],\n    input: unknown,\n    opts: UseTRPCSuspenseInfiniteQueryOptions<unknown, unknown, TError>,\n  ): UseTRPCSuspenseInfiniteQueryResult<unknown, TError, unknown> {\n    const context = useContext();\n    const queryKey = getQueryKeyInternal(path, input, 'infinite');\n\n    const defaultOpts = context.queryClient.getQueryDefaults(queryKey);\n\n    const ssrOpts = useSSRQueryOptionsIfNeeded(queryKey, {\n      ...defaultOpts,\n      ...opts,\n    });\n\n    // request option should take priority over global\n    const shouldAbortOnUnmount =\n      opts?.trpc?.abortOnUnmount ?? context.abortOnUnmount;\n\n    const hook = __useSuspenseInfiniteQuery(\n      {\n        ...opts,\n        initialPageParam: opts.initialCursor ?? null,\n        queryKey,\n        queryFn: (queryFunctionContext) => {\n          const actualOpts = {\n            ...ssrOpts,\n            trpc: {\n              ...ssrOpts?.trpc,\n              ...(shouldAbortOnUnmount\n                ? { signal: queryFunctionContext.signal }\n                : {}),\n            },\n          };\n\n          return context.client.query(\n            ...getClientArgs(queryKey, actualOpts, {\n              pageParam: queryFunctionContext.pageParam ?? opts.initialCursor,\n              direction: queryFunctionContext.direction,\n            }),\n          );\n        },\n      },\n      context.queryClient,\n    ) as UseTRPCInfiniteQueryResult<unknown, TError, unknown>;\n\n    hook.trpc = useHookResult({\n      path,\n    });\n\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    return [hook.data!, hook as any];\n  }\n\n  const useQueries: TRPCUseQueries<TRouter> = (queriesCallback, options) => {\n    const { ssrState, queryClient, prefetchQuery, client } = useContext();\n\n    const proxy = createUseQueries(client);\n\n    const queries = queriesCallback(proxy);\n\n    if (typeof window === 'undefined' && ssrState === 'prepass') {\n      for (const query of queries) {\n        const queryOption = query as TRPCQueryOptions<any, any>;\n        if (\n          queryOption.trpc?.ssr !== false &&\n          !queryClient.getQueryCache().find({ queryKey: queryOption.queryKey })\n        ) {\n          void prefetchQuery(queryOption.queryKey, queryOption as any);\n        }\n      }\n    }\n\n    return __useQueries(\n      {\n        queries: queries.map((query) => ({\n          ...query,\n          queryKey: (query as TRPCQueryOptions<any, any>).queryKey,\n        })),\n        combine: options?.combine as any,\n      },\n      queryClient,\n    );\n  };\n\n  const useSuspenseQueries: TRPCUseSuspenseQueries<TRouter> = (\n    queriesCallback,\n  ) => {\n    const { queryClient, client } = useContext();\n\n    const proxy = createUseQueries(client);\n\n    const queries = queriesCallback(proxy);\n\n    const hook = __useSuspenseQueries(\n      {\n        queries: queries.map((query) => ({\n          ...query,\n          queryFn: query.queryFn,\n          queryKey: (query as TRPCQueryOptions<any, any>).queryKey,\n        })),\n      },\n      queryClient,\n    );\n\n    return [hook.map((h) => h.data), hook] as any;\n  };\n\n  return {\n    Provider: TRPCProvider,\n    createClient,\n    useContext,\n    useUtils: useContext,\n    useQuery,\n    usePrefetchQuery,\n    useSuspenseQuery,\n    useQueries,\n    useSuspenseQueries,\n    useMutation,\n    useSubscription,\n    useInfiniteQuery,\n    usePrefetchInfiniteQuery,\n    useSuspenseInfiniteQuery,\n  };\n}\n\n/**\n * Infer the type of a `createReactQueryHooks` function\n * @internal\n */\nexport type CreateReactQueryHooks<\n  TRouter extends AnyRouter,\n  TSSRContext = unknown,\n> = ReturnType<typeof createRootHooks<TRouter, TSSRContext>>;\n", "import type { QueryClientConfig } from '@tanstack/react-query';\nimport { QueryClient } from '@tanstack/react-query';\n\n/**\n * @internal\n */\nexport type CreateTRPCReactQueryClientConfig =\n  | {\n      queryClient?: QueryClient;\n      queryClientConfig?: never;\n    }\n  | {\n      queryClientConfig?: QueryClientConfig;\n      queryClient?: never;\n    };\n\n/**\n * @internal\n */\nexport const getQueryClient = (config: CreateTRPCReactQueryClientConfig) =>\n  config.queryClient ?? new QueryClient(config.queryClientConfig);\n"], "names": ["hooks: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><TR<PERSON><PERSON>, T<PERSON>RContext>", "contextProps: (keyof TRPCContextPropsBase<any, any>)[]", "React", "utilName: keyof AnyDecoratedProcedure", "context: TRPCQueryUtils<TRouter>", "contextMap: Record<keyof AnyDecoratedProcedure, () => unknown>", "context: TRPCContextState<AnyRouter, TSSRContext>", "client: TRPCUntypedClient<TRouter> | TRPCClient<TRouter>", "untypedClient: TRPCUntypedClient<TRouter>", "options: QueryOptions", "queryKey: TRPCQueryKey", "opts: TOptions", "infiniteParams?: {\n    pageParam: any;\n    direction: 'forward' | 'backward';\n  }", "_asyncIterator", "r", "AsyncFromSyncIterator", "value: {\n  path: readonly string[];\n}", "asyncIterable: AsyncIterable<unknown>", "queryClient: QueryClient", "queryKey: TRPCQueryKey", "aggregate: unknown[]", "opts: Create<PERSON>ueryUtilsOptions<TRouter>", "opts", "queryFnContext: QueryFunctionContext<TRPCQueryKey, unknown>", "queryFnContext: QueryFunctionContext<TRPCQueryKey>", "input: unknown", "result: T", "onTrackResult: (key: keyof T) => void", "config?: CreateTRPCReactOptions<TRouter>", "mutationSuccessOverride: UseMutationOverride['onSuccess']", "TRPCProvider: T<PERSON><PERSON>rov<PERSON><TR<PERSON><PERSON>, TSSRContext>", "client: TRPCUntypedClient<TRouter>", "queryKey: TRPCQueryKey", "opts: TOptions", "useQuery", "path: readonly string[]", "input: unknown", "opts?: UseTRPCQueryOptions<unknown, unknown, TError>", "usePrefetchQuery", "path: string[]", "opts?: UseTRPCPrefetchQueryOptions<unknown, unknown, TError>", "useSuspenseQuery", "opts?: UseTRPCSuspenseQueryOptions<unknown, unknown, TError>", "useMutation", "opts?: UseTRPCMutationOptions<unknown, TError, unknown, unknown>", "initialStateIdle: Omit<TRPCSubscriptionIdleResult<unknown>, 'reset'>", "initialStateConnecting: Omit<\n    TRPCSubscriptionConnectingResult<unknown, TError>,\n    'reset'\n  >", "opts: UseTRPCSubscriptionOptions<unknown, TError>", "key: keyof $Result", "callback: (prevState: $Result) => $Result", "useInfiniteQuery", "opts: UseTRPCInfiniteQueryOptions<unknown, unknown, TError>", "usePrefetchInfiniteQuery", "opts: UseTRPCPrefetchInfiniteQueryOptions<unknown, unknown, TError>", "useSuspenseInfiniteQuery", "opts: UseTRPCSuspenseInfiniteQueryOptions<unknown, unknown, TError>", "useQueries: TRPCUseQueries<TRouter>", "useSuspenseQueries: TRPCUseSuspenseQueries<TRouter>", "config: CreateTRPCReactQueryClientConfig"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAQA,SAAgB,sBAGdA,KAAAA,EAAoD;IACpD,0QAAO,uBAAA,EAAqB,CAAC,EAAE,IAAA,EAAM,IAAA,EAAM,KAAK;;QAC9C,MAAM,WAAW,CAAC;eAAG,IAAK;SAAA;QAI1B,MAAM,UAAU,SAAS,GAAA,EAAK;QAE9B,IAAI,YAAY,cACd,CAAA,OAAQ,KAAA,CAAc,QAAA,CAAS,UAAU,GAAG,KAAK;QAGnD,IAAI,YAAY,OACd,CAAA,OAAO;YACL,MAAM;QACP;QAGH,MAAM,CAAC,OAAO,GAAG,KAAK,GAAG;QACzB,MAAM,OAAA,CAAA,SAAO,IAAA,CAAK,EAAA,MAAA,QAAA,WAAA,KAAA,IAAA,SAAM,CAAE;QAE1B,OAAQ,KAAA,CAAc,QAAA,CAAS,UAAU,OAAO,KAAK;IACtD,EAAC;AACH;;;;ACmFD,MAAaC,eAAyD;IACpE;IACA;IACA;IACA;CACD;AAmOD,MAAa,cAAA,CAAA,uBAAcC,+YAAM,aAAA,MAAA,QAAA,yBAAA,KAAA,IAAA,KAAA,IAAN,qBAAA,IAAA,CAAA,gZAAsB,KAAY;;;ACiF7D,MAAa,eAAe,CAC1BC,aACc;IACd,OAAQ,UAAR;QACE,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK,iBACH;YAAA,OAAO;QAET,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK,kBACH;YAAA,OAAO;QAET,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK,QACH;YAAA,OAAO;IACV;AACF;;;GAKD,SAAS,0BACPC,OAAAA,EACA;IACA,0QAAO,uBAAA,EAAgD,CAAC,SAAS;QAC/D,MAAM,OAAO,CAAC;eAAG,KAAK,IAAK;SAAA;QAC3B,MAAM,WAAW,KAAK,GAAA,EAAK;QAC3B,MAAM,OAAO,CAAC;eAAG,KAAK,IAAK;SAAA;QAG3B,MAAM,QAAQ,KAAK,KAAA,EAAO;QAC1B,MAAM,YAAY,aAAa,SAAS;QACxC,MAAM,6YAAW,sBAAA,EAAoB,MAAM,OAAO,UAAU;QAE5D,MAAMC,aAAiE;YACrE,sBAAsB,IACpB,QAAQ,oBAAA,CAAqB,MAAM,UAAU,IAAA,CAAK,EAAA,CAAG;YACvD,cAAc,IAAM,QAAQ,YAAA,CAAa,MAAM,UAAU,GAAG,KAAK;YAIjE,OAAO,IAAM,QAAQ,UAAA,CAAW,UAAU,GAAG,KAAK;YAClD,eAAe,IAAM,QAAQ,kBAAA,CAAmB,UAAU,IAAA,CAAK,EAAA,CAAG;YAClE,UAAU,IAAM,QAAQ,aAAA,CAAc,UAAU,GAAG,KAAK;YACxD,kBAAkB,IAAM,QAAQ,qBAAA,CAAsB,UAAU,IAAA,CAAK,EAAA,CAAG;YACxE,YAAY,IAAM,QAAQ,eAAA,CAAgB,UAAU,GAAG,KAAK;YAC5D,YAAY,IAAM,QAAQ,iBAAA,CAAkB,UAAU,GAAG,KAAK;YAC9D,OAAO,IAAM,QAAQ,YAAA,CAAa,UAAU,GAAG,KAAK;YACpD,SAAS,IAAM,QAAQ,cAAA,CAAe,UAAU,GAAG,KAAK;YACxD,QAAQ,IAAM,QAAQ,WAAA,CAAY,UAAU,GAAG,KAAK;YACpD,SAAS,MAAM;gBACb,QAAQ,YAAA,CAAa,UAAU,IAAA,CAAK,EAAA,EAAI,IAAA,CAAK,EAAA,CAAG;YACjD;YACD,gBAAgB,IACd,QAAQ,cAAA,CAAe,UAAU,IAAA,CAAK,EAAA,EAAI,IAAA,CAAK,EAAA,EAAI,IAAA,CAAK,EAAA,CAAG;YAC7D,iBAAiB,MAAM;gBACrB,QAAQ,oBAAA,CAAqB,UAAU,IAAA,CAAK,EAAA,EAAI,IAAA,CAAK,EAAA,CAAG;YACzD;YACD,SAAS,IAAM,QAAQ,YAAA,CAAa,SAAS;YAC7C,iBAAiB,IAAM,QAAQ,oBAAA,CAAqB,SAAS;YAI7D,qBAAqB,IACnB,QAAQ,mBAAA,CAAoB,2ZAAA,EAAuB,KAAK,EAAE,MAAM;YAClE,qBAAqB,IACnB,QAAQ,mBAAA,mYAAoB,yBAAA,EAAuB,KAAK,CAAC;YAC3D,YAAY,IACV,QAAQ,UAAA,CAAW;oBAAE,+YAAa,yBAAA,EAAuB,KAAK;gBAAE,EAAC;QACpE;QAED,OAAO,UAAA,CAAW,SAAA,EAAW;IAC9B,EAAC;AACH;;;GAKD,SAAgB,sBACdC,OAAAA,EACA;IAGA,MAAM,8UAAc,wBAAA,EAAsB,QAAQ,MAAA,CAAO;IAEzD,MAAM,QAAQ,0BACZ,QACD;IAED,0QAAO,kBAAA,EAA4C,CAAC,QAAQ;QAC1D,MAAM,cAAc;QACpB,IAAI,gBAAgB,SAClB,CAAA,OAAO;QAET,IAAI,aAAa,QAAA,CAAS,YAAY,CACpC,CAAA,OAAO,OAAA,CAAQ,YAAA;QAGjB,OAAO,KAAA,CAAM,IAAA;IACd,EAAC;AACH;;;GAKD,SAAgB,sBACdF,OAAAA,EAC2B;IAC3B,OAAO,0BAA0B,QAAQ;AAC1C;;;;;;;GC5cD,SAAgB,iBACdG,MAAAA,EACA;IACA,MAAMC,gBACJ,8UAAkB,oBAAA,GAAoB,yUAAS,mBAAA,EAAiB,OAAO;IAEzE,0QAAO,uBAAA,EAKL,CAAC,SAAS;QACV,MAAM,YAAY,KAAK,IAAA;QACvB,MAAM,UAAU,UAAU,IAAA,CAAK,IAAI;QACnC,MAAM,CAAC,OAAO,MAAM,GAAG,KAAK,IAAA;QAK5B,MAAMC,UAAAA,CAAAA,GAAAA,uBAAAA,OAAAA,EAAAA;YACJ,4YAAU,sBAAA,EAAoB,WAAW,OAAO,QAAQ;YACxD,SAAS,MAAM;gBACb,OAAO,cAAc,KAAA,CAAM,SAAS,OAAA,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAO,MAAO,IAAA,CAAK;YACxD;WACE;QAGL,OAAO;IACR,EAAC;AACH;;;;;;GCpHD,SAAgB,cACdC,QAAAA,EACAC,IAAAA,EACAC,cAAAA,EAIA;;IACA,MAAM,OAAO,QAAA,CAAS,EAAA;IACtB,IAAI,QAAA,CAAA,aAAQ,QAAA,CAAS,EAAA,MAAA,QAAA,eAAA,KAAA,IAAA,KAAA,IAAA,WAAI,KAAA;IACzB,IAAI,gBAAgB;;QAClB,QAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,CAAA,SACM,KAAA,MAAA,QAAA,WAAA,KAAA,IAAA,SAAS,CAAE,IACX,eAAe,SAAA,GAAY;YAAE,QAAQ,eAAe,SAAA;QAAW,IAAG,CAAE,IAAA,CAAA,GAAA;YACxE,WAAW,eAAe,SAAA;QAAA;IAE7B;IACD,OAAO;QAAC,KAAK,IAAA,CAAK,IAAI;QAAE;oDAAQ,KAAc,IAAA;KAAK;AACpD;;;;;QCvBD,SAASC,iBAAe,CAAA,EAAG;YACzB,IAAI,GACF,GACA,GACA,IAAI;YACN,IAAK,eAAA,OAAsB,UAAA,CAAW,IAAI,OAAO,aAAA,EAAe,IAAI,OAAO,QAAA,GAAW,KAAM;gBAC1F,IAAI,KAAK,QAAA,CAAS,IAAI,CAAA,CAAE,EAAA,EAAK,CAAA,OAAO,EAAE,IAAA,CAAK,EAAE;gBAC7C,IAAI,KAAK,QAAA,CAAS,IAAI,CAAA,CAAE,EAAA,EAAK,CAAA,OAAO,IAAI,sBAAsB,EAAE,IAAA,CAAK,EAAE;gBACvE,IAAI,mBAAmB,IAAI;YAC5B;YACD,MAAM,IAAI,UAAU;QACrB;QACD,SAAS,sBAAsB,CAAA,EAAG;YAChC,SAAS,kCAAkCC,GAAAA,EAAG;gBAC5C,IAAI,OAAOA,IAAE,KAAKA,IAAG,CAAA,OAAO,QAAQ,MAAA,CAAO,IAAI,UAAUA,MAAI,sBAAsB;gBACnF,IAAI,IAAIA,IAAE,IAAA;gBACV,OAAO,QAAQ,OAAA,CAAQA,IAAE,KAAA,CAAM,CAAC,IAAA,CAAK,SAAUA,GAAAA,EAAG;oBAChD,OAAO;wBACL,OAAOA;wBACP,MAAM;oBACP;gBACF,EAAC;YACH;YACD,OAAO,wBAAwB,SAASC,wBAAsBD,GAAAA,EAAG;gBAC/D,IAAA,CAAK,CAAA,GAAIA,KAAG,IAAA,CAAK,CAAA,GAAIA,IAAE,IAAA;YACxB,GAAE,sBAAsB,SAAA,GAAY;gBACnC,GAAG;gBACH,GAAG;gBACH,MAAM,SAAS,OAAO;oBACpB,OAAO,kCAAkC,IAAA,CAAK,CAAA,CAAE,KAAA,CAAM,IAAA,CAAK,CAAA,EAAG,UAAU,CAAC;gBAC1E;gBACD,UAAU,SAAS,QAAQA,GAAAA,EAAG;oBAC5B,IAAI,IAAI,IAAA,CAAK,CAAA,CAAE,SAAA;oBACf,OAAA,KAAY,MAAM,IAAI,QAAQ,OAAA,CAAQ;wBACpC,OAAOA;wBACP,MAAA,CAAO;oBACR,EAAC,GAAG,kCAAkC,EAAE,KAAA,CAAM,IAAA,CAAK,CAAA,EAAG,UAAU,CAAC;gBACnE;gBACD,SAAS,SAAS,OAAOA,GAAAA,EAAG;oBAC1B,IAAI,IAAI,IAAA,CAAK,CAAA,CAAE,SAAA;oBACf,OAAA,KAAY,MAAM,IAAI,QAAQ,MAAA,CAAOA,IAAE,GAAG,kCAAkC,EAAE,KAAA,CAAM,IAAA,CAAK,CAAA,EAAG,UAAU,CAAC;gBACxG;YACF,GAAE,IAAI,sBAAsB;QAC9B;QACD,OAAO,OAAA,GAAUD,kBAAgB,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;ACtCtG,SAAgB,wBAAwBG,KAAAA,EAEL;IACjC,MAAM,OAAO,MAAM,IAAA,CAAK,IAAA,CAAK,IAAI;IAEjC,OAAO;QACL;IACD;AACF;;;GAKD,SAAgB,cAAcA,KAAAA,EAEH;IACzB,MAAM,SAAS,wBAAwB,MAAM;IAC7C,OAAO,+YAAM,OAAA,CAAQ,IAAM,QAAQ;QAAC,MAAO;KAAA,CAAC;AAC7C;;;GAKD,eAAsB,4BACpBC,aAAAA,EACAC,WAAAA,EACAC,QAAAA,EACA;IACA,MAAM,aAAa,YAAY,aAAA,EAAe;IAE9C,MAAM,QAAQ,WAAW,KAAA,CAAM,aAAa;QAC1C;IACD,EAAC;IAEF,MAAM,QAAA,CAAS;QACb,MAAM,CAAE,CAAA;QACR,QAAQ;IACT,EAAC;IAEF,MAAMC,YAAuB,CAAE,CAAA;;;;;8DACL,gBAAA,OAAA,4BAAA,CAAA,CAAA,QAAA,MAAA,UAAA,IAAA,EAAA,EAAA,IAAA,EAAA,4BAAA,MAAA;kBAAT,QAAA,MAAA,KAAA;YAAwB;gBACvC,UAAU,IAAA,CAAK,MAAM;gBAErB,MAAM,QAAA,CAAS;oBACb,MAAM,CAAC;2BAAG,SAAU;qBAAA;gBACrB,EAAC;YACH;;;;;;;;;;;;IACD,OAAO;AACR;;;;;;;;;GChBD,SAAgB,uBACdC,IAAAA,EACyB;IACzB,MAAM,EAAE,MAAA,EAAQ,WAAA,EAAa,GAAG;IAChC,MAAM,gBACJ,8UAAkB,oBAAA,GAAoB,SAAS,mVAAA,EAAiB,OAAO;IAEzE,OAAO;QACL,sBAAsB,CAAC,MAAM,UAAUC,WAAS;;YAC9C,MAAM,mBAAA,CAAA,CAAA,aAAmB,QAAA,CAAS,EAAA,MAAA,QAAA,eAAA,KAAA,IAAA,KAAA,IAAA,WAAI,KAAA,uPAAU,YAAA;YAEhD,MAAM,UAAU,OACdC,mBACqB;;gBACrB,MAAM,aAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACDD,SAAAA,CAAAA,GAAAA;oBACH,MAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IACKA,OAAM,IAAA,GAAA,CAAA,WAAA,QAAA,WAAA,KAAA,KAAA,CAAA,aACLA,OAAM,IAAA,MAAA,QAAA,eAAA,KAAA,IAAA,KAAA,IAAA,WAAM,cAAA,IACZ;wBAAE,QAAQ,eAAe,MAAA;oBAAQ,IACjC;wBAAE,QAAQ;oBAAM;gBAAA;gBAIxB,MAAM,SAAS,MAAM,cAAc,KAAA,CACjC,GAAG,cAAc,UAAU,YAAY;oBACrC,WAAW,eAAe,SAAA;oBAC1B,WAAW,eAAe,SAAA;gBAC3B,EAAC,CACH;gBAED,OAAO;YACR;YAED,OAAO,OAAO,MAAA,6RACZ,uBAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACKA,SAAAA,CAAAA,GAAAA;gBACH,aAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAaA,OAAM,WAAA;gBACnB;gBACA,SAAS,oQAAmB,YAAA,GAAY;gBACxC,kBAAA,CAAA,OAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAmBA,OAAM,aAAA,MAAA,QAAA,SAAA,KAAA,IAAA,OAAyB;eAClD,EACF;gBAAE,MAAM,wBAAwB;oBAAE;gBAAM,EAAC;YAAE,EAC5C;QACF;QAED,cAAc,CAAC,MAAM,UAAUA,WAAS;;YACtC,MAAM,mBAAA,CAAA,CAAA,cAAmB,QAAA,CAAS,EAAA,MAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAI,KAAA,MAAU,6PAAA;YAEhD,MAAM,UAAU,OACdE,mBACqB;;gBACrB,MAAM,aAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACDF,SAAAA,CAAAA,GAAAA;oBACH,MAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IACKA,OAAM,IAAA,GAAA,CAAA,WAAA,QAAA,WAAA,KAAA,KAAA,CAAA,cACLA,OAAM,IAAA,MAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAM,cAAA,IACZ;wBAAE,QAAQ,eAAe,MAAA;oBAAQ,IACjC;wBAAE,QAAQ;oBAAM;gBAAA;gBAIxB,MAAM,SAAS,MAAM,cAAc,KAAA,CACjC,GAAG,cAAc,UAAU,WAAW,CACvC;gBAED,+PAAI,kBAAA,EAAgB,OAAO,CACzB,CAAA,OAAO,4BAA4B,QAAQ,aAAa,SAAS;gBAGnE,OAAO;YACR;YAED,OAAO,OAAO,MAAA,qRACZ,eAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACKA,SAAAA,CAAAA,GAAAA;gBACH,aAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAaA,OAAM,WAAA;gBACnB;gBACA,SAAS,oQAAmB,YAAA,GAAY;eACxC,EACF;gBAAE,MAAM,wBAAwB;oBAAE;gBAAM,EAAC;YAAE,EAC5C;QAKF;QAED,YAAY,CAAC,UAAUA,WAAS;YAC9B,OAAO,YAAY,UAAA,CAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACdA,SAAAA,CAAAA,GAAAA;gBACH;gBACA,SAAS,IAAM,cAAc,KAAA,CAAM,GAAG,cAAc,UAAUA,OAAK,CAAC;eACpE;QACH;QAED,oBAAoB,CAAC,UAAUA,WAAS;;YACtC,OAAO,YAAY,kBAAA,CAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACdA,SAAAA,CAAAA,GAAAA;gBACH;gBACA,SAAS,CAAC,EAAE,SAAA,EAAW,SAAA,EAAW,KAAK;oBACrC,OAAO,cAAc,KAAA,CACnB,GAAG,cAAc,UAAUA,QAAM;wBAAE;wBAAW;oBAAW,EAAC,CAC3D;gBACF;gBACD,kBAAA,CAAA,sBAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAkBA,OAAM,aAAA,MAAA,QAAA,wBAAA,KAAA,IAAA,sBAAiB;eACzC;QACH;QAED,eAAe,CAAC,UAAUA,WAAS;YACjC,OAAO,YAAY,aAAA,CAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACdA,SAAAA,CAAAA,GAAAA;gBACH;gBACA,SAAS,IAAM,cAAc,KAAA,CAAM,GAAG,cAAc,UAAUA,OAAK,CAAC;eACpE;QACH;QAED,uBAAuB,CAAC,UAAUA,WAAS;;YACzC,OAAO,YAAY,qBAAA,CAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACdA,SAAAA,CAAAA,GAAAA;gBACH;gBACA,SAAS,CAAC,EAAE,SAAA,EAAW,SAAA,EAAW,KAAK;oBACrC,OAAO,cAAc,KAAA,CACnB,GAAG,cAAc,UAAUA,QAAM;wBAAE;wBAAW;oBAAW,EAAC,CAC3D;gBACF;gBACD,kBAAA,CAAA,uBAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAkBA,OAAM,aAAA,MAAA,QAAA,yBAAA,KAAA,IAAA,uBAAiB;eACzC;QACH;QAED,iBAAiB,CAAC,UAAUA,WAAS;YACnC,OAAO,YAAY,eAAA,CAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACdA,SAAAA,CAAAA,GAAAA;gBACH;gBACA,SAAS,IAAM,cAAc,KAAA,CAAM,GAAG,cAAc,UAAUA,OAAK,CAAC;eACpE;QACH;QAED,mBAAmB,CAAC,UAAU,SAAS,YAAY;YACjD,OAAO,YAAY,iBAAA,CAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAEZ,UAAA,CAAA,GAAA;gBACH;YAAA,IAEF,QACD;QACF;QACD,cAAc,CAAC,UAAU,SAAS,YAAY;YAC5C,OAAO,YAAY,YAAA,CAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAEZ,UAAA,CAAA,GAAA;gBACH;YAAA,IAEF,QACD;QACF;QAED,gBAAgB,CAAC,UAAU,SAAS,YAAY;YAC9C,OAAO,YAAY,cAAA,CAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAEZ,UAAA,CAAA,GAAA;gBACH;YAAA,IAEF,QACD;QACF;QAED,aAAa,CAAC,UAAU,YAAY;YAClC,OAAO,YAAY,aAAA,CACjB;gBACE;YACD,GACD,QACD;QACF;QAED,cAAc,CAAC,UAAU,SAAS,YAAY;YAC5C,OAAO,YAAY,YAAA,CAAa,UAAU,SAAgB,QAAQ;QACnE;QAGD,gBAAgB,CAAC,UAAU,SAAS,SAAS,YAAY;YACvD,OAAO,YAAY,cAAA,CAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAEZ,UAAA,CAAA,GAAA;gBACH;YAAA,IAEF,SACA,QACD;QACF;QAED,cAAc,CAAC,aAAa;YAC1B,OAAO,YAAY,YAAA,CAAa,SAAS;QAC1C;QAED,sBAAsB,CAAC,UAAU,SAAS,YAAY;YACpD,OAAO,YAAY,YAAA,CAAa,UAAU,SAAgB,QAAQ;QACnE;QAED,sBAAsB,CAAC,aAAa;YAClC,OAAO,YAAY,YAAA,CAAa,SAAS;QAC1C;QAED,qBAAqB,CAAC,aAAa,YAAY;YAC7C,MAAM,OAAO,WAAA,CAAY,EAAA;YACzB,MAAM,sBAAsB,CAACG,UAAmB;gBAC9C,OAAO,cAAc,QAAA,CACnB,GAAG,cAAc;oBAAC;oBAAM;wBAAE;oBAAO,CAAC;iBAAA,EAAE,KAAK,CAC1C;YACF;YACD,OAAO,YAAY,mBAAA,CACjB,aAAA,OACO,YAAY,aACf,QAAQ;gBAAE;YAAqB,EAAC,GAChC,QACL;QACF;QAED,qBAAqB,CAAC,gBAAgB;YACpC,OAAO,YAAY,mBAAA,CAAoB,YAAY;QACpD;QAED,YAAY,CAAC,YAAY;YACvB,OAAO,YAAY,UAAA,CAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACd,UAAA,CAAA,GAAA;gBACH,OAAO;YAAA,GACP;QACH;IACF;AACF;;;;AC3MD,MAAM,cAAc,CAClBC,QACAC,kBACM;IACN,MAAM,gBAAgB,IAAI,MAAM,QAAQ;QACtC,KAAI,MAAA,EAAQ,IAAA,EAAM;YAChB,cAAc,KAAgB;YAC9B,OAAO,MAAA,CAAO,KAAA;QACf;IACF;IAED,OAAO;AACR;;;GAKD,SAAgB,gBAGdC,MAAAA,EAA0C;;IAC1C,MAAMC,0BAAAA,CAAAA,wBAAAA,WAAAA,QAAAA,WAAAA,KAAAA,KAAAA,CAAAA,oBACJ,OAAQ,SAAA,MAAA,QAAA,sBAAA,KAAA,KAAA,CAAA,oBAAA,kBAAW,WAAA,MAAA,QAAA,sBAAA,KAAA,IAAA,KAAA,IAAA,kBAAa,SAAA,MAAA,QAAA,0BAAA,KAAA,IAAA,wBAC/B,CAAC,UAAY,QAAQ,UAAA,EAAY;IAMpC,MAAM,UAAA,CAAA,kBAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAW,OAAQ,OAAA,MAAA,QAAA,oBAAA,KAAA,IAAA,kBACvB;IAEF,MAAM,2UAAe,mBAAA;IAErB,MAAMC,eAAmD,CAAC,UAAU;;QAClE,MAAM,EAAE,iBAAiB,KAAA,EAAO,WAAA,EAAa,UAAA,EAAY,GAAG;QAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,+YAAM,QAAA,CAAA,CAAA,kBACpC,MAAM,QAAA,MAAA,QAAA,oBAAA,KAAA,IAAA,kBAAY,MACnB;QAED,MAAMC,SACJ,MAAM,MAAA,wUAAkB,oBAAA,GACpB,MAAM,MAAA,GACN,mVAAA,EAAiB,MAAM,MAAA,CAAO;QAEpC,MAAM,MAAM,+YAAM,OAAA,CAChB,IACE,uBAAuB;gBACrB;gBACA;YACD,EAAC,EACJ;YAAC;YAAQ,WAAY;SAAA,CACtB;QAED,MAAM,eAAe,+YAAM,OAAA,CACzB,IAAA,CAAA,GAAA,qBAAA,OAAA,EAAA;gBACE;gBACA;gBACA;gBACA,YAAY,eAAA,QAAA,eAAA,KAAA,IAAA,aAAc;gBAC1B;eACG,MAEL;YAAC;YAAgB;YAAQ;YAAK;YAAa;YAAY;SAAS,CACjE;QAED,+YAAM,SAAA,CAAU,MAAM;YAGpB,YAAY,CAAC,QAAW,QAAQ,YAAY,MAAO;QACpD,GAAE,CAAE,CAAA,CAAC;QACN,OAAA,aAAA,uaACE,OAAA,EAAC,QAAQ,QAAA,EAAA;YAAS,OAAO;sBAAe,MAAM,QAAA;UAA4B;IAE7E;IAED,SAAS,aAAa;QACpB,MAAM,UAAU,+YAAM,UAAA,CAAW,QAAQ;QAEzC,IAAA,CAAK,QACH,CAAA,MAAM,IAAI,MACR;QAGJ,OAAO;IACR;;;;IAMD,SAAS,2BAEPC,QAAAA,EAAwBC,IAAAA,EAA0B;;QAClD,MAAM,EAAE,WAAA,EAAa,QAAA,EAAU,GAAG,YAAY;QAC9C,OAAO,YACL,aAAa,aAAA,CAAA,CAAA,wBACb,YAAY,aAAA,EAAe,CAAC,IAAA,CAAK;YAAE;QAAU,EAAC,MAAA,QAAA,0BAAA,KAAA,IAAA,KAAA,IAAA,sBAAE,KAAA,CAAM,MAAA,MAAW,UAAA,CAAA,GAAA,qBAAA,OAAA,EAAA;YAE7D,cAAc;QAAA,GACX,QAEL;IACL;IAED,SAASC,WACPC,IAAAA,EACAC,KAAAA,EACAC,IAAAA,EACqC;;QACrC,MAAM,UAAU,YAAY;QAC5B,MAAM,EAAE,cAAA,EAAgB,MAAA,EAAQ,QAAA,EAAU,WAAA,EAAa,aAAA,EAAe,GACpE;QACF,MAAM,6YAAW,sBAAA,EAAoB,MAAM,OAAO,QAAQ;QAE1D,MAAM,cAAc,YAAY,gBAAA,CAAiB,SAAS;QAE1D,MAAM,mBAAmB,2PAAU,YAAA;QAEnC,IAAA,OACS,WAAW,eAClB,aAAa,aAAA,CAAA,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,aACb,KAAM,IAAA,MAAA,QAAA,eAAA,KAAA,IAAA,KAAA,IAAA,WAAM,GAAA,MAAQ,SAAA,CAAA,CAAA,gBAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IACnB,KAAM,OAAA,MAAA,QAAA,kBAAA,KAAA,IAAA,gBAAA,gBAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAW,YAAa,OAAA,MAAa,SAAA,CAC3C,oBAAA,CACA,YAAY,aAAA,EAAe,CAAC,IAAA,CAAK;YAAE;QAAU,EAAC,CAE/C,CAAK,cAAc,UAAU,KAAY;QAE3C,MAAM,UAAU,2BAA2B,UAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACtC,cACA,MACH;QAEF,MAAM,uBAAA,CAAA,OAAA,CAAA,wBAAA,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,cACJ,KAAM,IAAA,MAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAM,cAAA,MAAA,QAAA,0BAAA,KAAA,IAAA,wBAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAkB,OAAQ,cAAA,MAAA,QAAA,SAAA,KAAA,IAAA,OAAkB;QAE1D,MAAM,uRAAO,WAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAEN,UAAA,CAAA,GAAA;YACO;YACV,SAAS,mBACL,QACA,OAAO,yBAAyB;gBAC9B,MAAM,aAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACD,UAAA,CAAA,GAAA;oBACH,MAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IACK,QAAS,IAAA,GACR,uBACA;wBAAE,QAAQ,qBAAqB,MAAA;oBAAQ,IACvC;wBAAE,QAAQ;oBAAM;gBAAA;gBAIxB,MAAM,SAAS,MAAM,OAAO,KAAA,CAC1B,GAAG,cAAc,UAAU,WAAW,CACvC;gBAED,+PAAI,kBAAA,EAAgB,OAAO,CACzB,CAAA,OAAO,4BACL,QACA,aACA,SACD;gBAEH,OAAO;YACR;YAEP,YACD;QAED,KAAK,IAAA,GAAO,cAAc;YACxB;QACD,EAAC;QAEF,OAAO;IACR;IAED,SAASC,mBACPC,IAAAA,EACAH,KAAAA,EACAI,IAAAA,EACM;;QACN,MAAM,UAAU,YAAY;QAC5B,MAAM,6YAAW,sBAAA,EAAoB,MAAM,OAAO,QAAQ;QAE1D,MAAM,mBAAmB,2PAAU,YAAA;QAEnC,MAAM,uBAAA,CAAA,QAAA,CAAA,yBAAA,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,cACJ,KAAM,IAAA,MAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAM,cAAA,MAAA,QAAA,2BAAA,KAAA,IAAA,yBAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IACZ,OAAQ,cAAA,MAAA,QAAA,UAAA,KAAA,IAAA,QACR,QAAQ,cAAA;QAEV,CAAA,GAAA,mRAAA,CAAA,mBAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACK,OAAA,CAAA,GAAA;YACO;YACV,SAAS,mBACL,QACA,CAAC,yBAAyB;gBACxB,MAAM,aAAa;oBACjB,MAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IACK,KAAM,IAAA,GACL,uBACA;wBAAE,QAAQ,qBAAqB,MAAA;oBAAQ,IACvC,CAAE;gBAET;gBAED,OAAO,QAAQ,MAAA,CAAO,KAAA,CAAM,GAAG,cAAc,UAAU,WAAW,CAAC;YACpE;WACL;IACH;IAED,SAASC,mBACPN,IAAAA,EACAC,KAAAA,EACAM,IAAAA,EAC6C;;QAC7C,MAAM,UAAU,YAAY;QAC5B,MAAM,4YAAW,uBAAA,EAAoB,MAAM,OAAO,QAAQ;QAE1D,MAAM,uBAAA,CAAA,QAAA,CAAA,yBAAA,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,cACJ,KAAM,IAAA,MAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAM,cAAA,MAAA,QAAA,2BAAA,KAAA,IAAA,yBAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IACZ,OAAQ,cAAA,MAAA,QAAA,UAAA,KAAA,IAAA,QACR,QAAQ,cAAA;QAEV,MAAM,QAAO,0SAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAEN,OAAA,CAAA,GAAA;YACO;YACV,SAAS,CAAC,yBAAyB;gBACjC,MAAM,aAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACD,OAAA,CAAA,GAAA;oBACH,MAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IACK,KAAM,IAAA,GACL,uBACA;wBAAE,QAAQ,qBAAqB,MAAA;oBAAQ,IACvC;wBAAE,QAAQ;oBAAM;gBAAA;gBAIxB,OAAO,QAAQ,MAAA,CAAO,KAAA,CAAM,GAAG,cAAc,UAAU,WAAW,CAAC;YACpE;YAEH,QAAQ,WAAA,CACT;QAED,KAAK,IAAA,GAAO,cAAc;YACxB;QACD,EAAC;QAEF,OAAO;YAAC,KAAK,IAAA;YAAM,IAAY;SAAA;IAChC;IAED,SAASC,cACPR,IAAAA,EACAS,IAAAA,EAC0D;QAC1D,MAAM,EAAE,MAAA,EAAQ,WAAA,EAAa,GAAG,YAAY;QAE5C,MAAM,gZAAc,yBAAA,EAAuB,KAAK;QAEhD,MAAM,cAAc,YAAY,sBAAA,CAC9B,YAAY,mBAAA,CAAoB,YAAY,CAC7C;QAED,MAAM,0RAAO,cAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAEN,OAAA,CAAA,GAAA;YACU;YACb,YAAY,CAAC,UAAU;gBACrB,OAAO,OAAO,QAAA,CAAS,GAAG,cAAc;oBAAC;oBAAM;wBAAE;oBAAO,CAAC;iBAAA,EAAE,KAAK,CAAC;YAClE;YACD,WAAU,GAAG,IAAA,EAAM;;gBACjB,MAAM,aAAa,MACjB;;2GAAM,SAAA,MAAA,QAAA,qBAAA,KAAA,IAAA,KAAA,IAAN,iBAAA,IAAA,CAAA,MAAkB,GAAG,KAAK,MAAA,QAAA,oBAAA,KAAA,IAAA,kBAAA,gBAAA,QAAA,gBAAA,KAAA,KAAA,CAAA,wBAAI,YAAa,SAAA,MAAA,QAAA,0BAAA,KAAA,IAAA,KAAA,IAAb,sBAAA,IAAA,CAAA,aAAyB,GAAG,KAAK;;gBAEjE,OAAO,wBAAwB;oBAC7B;oBACA;oBACA,MAAA,CAAA,QAAA,CAAA,aAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAM,KAAM,IAAA,MAAA,QAAA,eAAA,KAAA,IAAA,aAAA,gBAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAQ,YAAa,IAAA,MAAA,QAAA,UAAA,KAAA,IAAA,QAAQ,CAAE;gBAC5C,EAAC;YACH;YAEH,YACD;QAED,KAAK,IAAA,GAAO,cAAc;YACxB;QACD,EAAC;QAEF,OAAO;IACR;IACD,MAAMC,mBAAuE;QAC3E,MAAA,KAAA;QACA,OAAO;QACP,QAAQ;IACT;IAED,MAAMC,yBAGF;QACF,MAAA,KAAA;QACA,OAAO;QACP,QAAQ;IACT;4CAGD,SAAS,gBACPX,IAAAA,EACAC,KAAAA,EACAW,IAAAA,EACA;;QACA,MAAM,UAAA,CAAA,iBAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAU,KAAM,OAAA,MAAA,QAAA,mBAAA,KAAA,IAAA,iBAAW,2PAAU,YAAA;QAC3C,MAAM,gQAAW,UAAA,oYAAQ,sBAAA,EAAoB,MAAM,OAAO,MAAM,CAAC;QACjE,MAAM,EAAE,MAAA,EAAQ,GAAG,YAAY;QAE/B,MAAM,UAAU,+YAAM,MAAA,CAAoB,KAAK;QAC/C,+YAAM,SAAA,CAAU,MAAM;YACpB,QAAQ,OAAA,GAAU;QACnB,EAAC;QAIF,MAAM,CAAC,aAAa,GAAG,+YAAM,QAAA,CAAS,IAAI,IAAmB,CAAE,CAAA,EAAE;QAEjE,MAAM,iBAAiB,+YAAM,WAAA,CAC3B,CAACC,QAAuB;YACtB,aAAa,GAAA,CAAI,IAAI;QACtB,GACD;YAAC,YAAa;SAAA,CACf;QAED,MAAM,yBAAyB,+YAAM,MAAA,CAAuB,KAAK;QAEjE,MAAM,cAAc,+YAAM,WAAA,CACxB,CAACC,aAA8C;YAC7C,MAAM,OAAO,UAAU,OAAA;YACvB,MAAM,OAAQ,UAAU,OAAA,GAAU,SAAS,KAAK;YAEhD,IAAI,eAAe;YACnB,KAAK,MAAM,OAAO,aAChB,IAAI,IAAA,CAAK,IAAA,KAAS,IAAA,CAAK,IAAA,EAAM;gBAC3B,eAAe;gBACf;YACD;YAEH,IAAI,aACF,CAAA,SAAS,YAAY,MAAM,eAAe,CAAC;QAE9C,GACD;YAAC;YAAgB,YAAa;SAAA,CAC/B;QAED,MAAM,QAAQ,+YAAM,WAAA,CAAY,MAAY;;YAE1C,CAAA,wBAAA,uBAAuB,OAAA,MAAA,QAAA,0BAAA,KAAA,KAAvB,sBAAgC,WAAA,EAAa;YAE7C,IAAA,CAAK,SAAS;gBACZ,YAAY,IAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAY,mBAAA,CAAA,GAAA;wBAAkB;oBAAA,GAAS;gBACnD;YACD;YACD,YAAY,IAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAY,yBAAA,CAAA,GAAA;oBAAwB;gBAAA,GAAS;YACzD,MAAM,eAAe,OAAO,YAAA,CAC1B,KAAK,IAAA,CAAK,IAAI,EACd,UAAA,QAAA,UAAA,KAAA,IAAA,QAAA,KAAA,GACA;gBACE,WAAW,MAAM;;oBACf,CAAA,wBAAA,CAAA,mBAAA,QAAQ,OAAA,EAAQ,SAAA,MAAA,QAAA,0BAAA,KAAA,KAAhB,sBAAA,IAAA,CAAA,iBAA6B;oBAC7B,YAAY,CAAC,OAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACR,OAAA,CAAA,GAAA;4BACH,QAAQ;4BACR,OAAO;2BACN;gBACJ;gBACD,QAAQ,CAAC,SAAS;;oBAChB,CAAA,wBAAA,CAAA,oBAAA,QAAQ,OAAA,EAAQ,MAAA,MAAA,QAAA,0BAAA,KAAA,KAAhB,sBAAA,IAAA,CAAA,mBAAyB,KAAK;oBAC9B,YAAY,CAAC,OAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACR,OAAA,CAAA,GAAA;4BACH,QAAQ;4BACR;4BACA,OAAO;2BACN;gBACJ;gBACD,SAAS,CAAC,UAAU;;oBAClB,CAAA,wBAAA,CAAA,oBAAA,QAAQ,OAAA,EAAQ,OAAA,MAAA,QAAA,0BAAA,KAAA,KAAhB,sBAAA,IAAA,CAAA,mBAA0B,MAAM;oBAChC,YAAY,CAAC,OAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACR,OAAA,CAAA,GAAA;4BACH,QAAQ;4BACR;2BACC;gBACJ;gBACD,yBAAyB,CAAC,WAAW;oBACnC,YAAY,CAAC,SAAS;wBACpB,OAAQ,OAAO,KAAA,EAAf;4BACE,KAAK,OACH;gCAAA,OAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACK,OAAA,CAAA,GAAA;oCACH,QAAQ,OAAO,KAAA;oCACf,OAAO;oCACP,MAAA,KAAA;;4BAEJ,KAAK,aACH;gCAAA,OAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACK,OAAA,CAAA,GAAA;oCACH,OAAO,OAAO,KAAA;oCACd,QAAQ,OAAO,KAAA;;4BAGnB,KAAK,UAEH;gCAAA,OAAO;wBACV;oBACF,EAAC;gBACH;gBACD,YAAY,MAAM;;oBAChB,CAAA,wBAAA,CAAA,oBAAA,QAAQ,OAAA,EAAQ,UAAA,MAAA,QAAA,0BAAA,KAAA,KAAhB,sBAAA,IAAA,CAAA,kBAA8B;oBAI9B,YAAY,CAAC,OAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACR,OAAA,CAAA,GAAA;4BACH,QAAQ;4BACR,OAAO;4BACP,MAAA,KAAA;2BACC;gBAGJ;YACF,EACF;YAED,uBAAuB,OAAA,GAAU;QAGlC,GAAE;YAAC;YAAQ;YAAU;YAAS;SAAY,CAAC;QAC5C,+YAAM,SAAA,CAAU,MAAM;YACpB,OAAO;YAEP,OAAO,MAAM;;gBACX,CAAA,yBAAA,uBAAuB,OAAA,MAAA,QAAA,2BAAA,KAAA,KAAvB,uBAAgC,WAAA,EAAa;YAC9C;QACF,GAAE;YAAC,KAAM;SAAA,CAAC;QAEX,MAAM,YAAY,+YAAM,MAAA,CACtB,UAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACS,yBAAA,CAAA,GAAA;YAAwB;QAAA,KAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACxB,mBAAA,CAAA,GAAA;YAAkB;QAAA,GAC5B;QAED,MAAM,CAAC,OAAO,SAAS,GAAG,+YAAM,QAAA,CAC9B,YAAY,UAAU,OAAA,EAAS,eAAe,CAC/C;QAED,OAAO;IACR;IAED,SAASC,mBACPf,IAAAA,EACAC,KAAAA,EACAe,IAAAA,EACsD;;QACtD,MAAM,EACJ,MAAA,EACA,QAAA,EACA,qBAAA,EACA,WAAA,EACA,cAAA,EACD,GAAG,YAAY;QAChB,MAAM,YAAW,uZAAA,EAAoB,MAAM,OAAO,WAAW;QAE7D,MAAM,cAAc,YAAY,gBAAA,CAAiB,SAAS;QAE1D,MAAM,mBAAmB,2PAAU,YAAA;QAEnC,IAAA,OACS,WAAW,eAClB,aAAa,aAAA,CAAA,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,cACb,KAAM,IAAA,MAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAM,GAAA,MAAQ,SAAA,CAAA,CAAA,iBAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IACnB,KAAM,OAAA,MAAA,QAAA,mBAAA,KAAA,IAAA,iBAAA,gBAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAW,YAAa,OAAA,MAAa,SAAA,CAC3C,oBAAA,CACA,YAAY,aAAA,EAAe,CAAC,IAAA,CAAK;YAAE;QAAU,EAAC,CAE/C,CAAK,sBAAsB,UAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAe,cAAgB,MAAc;QAG1E,MAAM,UAAU,2BAA2B,UAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACtC,cACA,MACH;QAGF,MAAM,uBAAA,CAAA,yBAAA,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,cAAuB,KAAM,IAAA,MAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAM,cAAA,MAAA,QAAA,2BAAA,KAAA,IAAA,yBAAkB;QAE3D,MAAM,+RAAO,mBAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAEN,UAAA,CAAA,GAAA;YACH,kBAAA,CAAA,sBAAkB,KAAK,aAAA,MAAA,QAAA,wBAAA,KAAA,IAAA,sBAAiB;YACxC,WAAW,KAAK,SAAA;YACN;YACV,SAAS,mBACL,QACA,CAAC,yBAAyB;;gBACxB,MAAM,aAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACD,UAAA,CAAA,GAAA;oBACH,MAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IACK,QAAS,IAAA,GACR,uBACA;wBAAE,QAAQ,qBAAqB,MAAA;oBAAQ,IACvC;wBAAE,QAAQ;oBAAM;gBAAA;gBAIxB,OAAO,OAAO,KAAA,CACZ,GAAG,cAAc,UAAU,YAAY;oBACrC,WAAA,CAAA,wBACE,qBAAqB,SAAA,MAAA,QAAA,0BAAA,KAAA,IAAA,wBAAa,KAAK,aAAA;oBACzC,WAAW,qBAAqB,SAAA;gBACjC,EAAC,CACH;YACF;YAEP,YACD;QAED,KAAK,IAAA,GAAO,cAAc;YACxB;QACD,EAAC;QACF,OAAO;IACR;IAED,SAASC,2BACPb,IAAAA,EACAH,KAAAA,EACAiB,IAAAA,EACM;;QACN,MAAM,UAAU,YAAY;QAC5B,MAAM,6YAAW,sBAAA,EAAoB,MAAM,OAAO,WAAW;QAE7D,MAAM,cAAc,QAAQ,WAAA,CAAY,gBAAA,CAAiB,SAAS;QAElE,MAAM,mBAAmB,2PAAU,YAAA;QAEnC,MAAM,UAAU,2BAA2B,UAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACtC,cACA,MACH;QAGF,MAAM,uBAAA,CAAA,yBAAA,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,cACJ,KAAM,IAAA,MAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAM,cAAA,MAAA,QAAA,2BAAA,KAAA,IAAA,yBAAkB,QAAQ,cAAA;QAExC,CAAA,GAAA,2RAAA,CAAA,2BAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACK,OAAA,CAAA,GAAA;YACH,kBAAA,CAAA,uBAAkB,KAAK,aAAA,MAAA,QAAA,yBAAA,KAAA,IAAA,uBAAiB;YACxC;YACA,SAAS,mBACL,QACA,CAAC,yBAAyB;;gBACxB,MAAM,aAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACD,UAAA,CAAA,GAAA;oBACH,MAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IACK,QAAS,IAAA,GACR,uBACA;wBAAE,QAAQ,qBAAqB,MAAA;oBAAQ,IACvC,CAAE;gBAAA;gBAIV,OAAO,QAAQ,MAAA,CAAO,KAAA,CACpB,GAAG,cAAc,UAAU,YAAY;oBACrC,WAAA,CAAA,yBAAW,qBAAqB,SAAA,MAAA,QAAA,2BAAA,KAAA,IAAA,yBAAa,KAAK,aAAA;oBAClD,WAAW,qBAAqB,SAAA;gBACjC,EAAC,CACH;YACF;WACL;IACH;IAED,SAASC,2BACPnB,IAAAA,EACAC,KAAAA,EACAmB,IAAAA,EAC8D;;QAC9D,MAAM,UAAU,YAAY;QAC5B,MAAM,6YAAW,sBAAA,EAAoB,MAAM,OAAO,WAAW;QAE7D,MAAM,cAAc,QAAQ,WAAA,CAAY,gBAAA,CAAiB,SAAS;QAElE,MAAM,UAAU,2BAA2B,UAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACtC,cACA,MACH;QAGF,MAAM,uBAAA,CAAA,yBAAA,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,cACJ,KAAM,IAAA,MAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAM,cAAA,MAAA,QAAA,2BAAA,KAAA,IAAA,yBAAkB,QAAQ,cAAA;QAExC,MAAM,QAAO,0TAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAEN,OAAA,CAAA,GAAA;YACH,kBAAA,CAAA,uBAAkB,KAAK,aAAA,MAAA,QAAA,yBAAA,KAAA,IAAA,uBAAiB;YACxC;YACA,SAAS,CAAC,yBAAyB;;gBACjC,MAAM,aAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACD,UAAA,CAAA,GAAA;oBACH,MAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IACK,QAAS,IAAA,GACR,uBACA;wBAAE,QAAQ,qBAAqB,MAAA;oBAAQ,IACvC,CAAE;gBAAA;gBAIV,OAAO,QAAQ,MAAA,CAAO,KAAA,CACpB,GAAG,cAAc,UAAU,YAAY;oBACrC,WAAA,CAAA,yBAAW,qBAAqB,SAAA,MAAA,QAAA,2BAAA,KAAA,IAAA,yBAAa,KAAK,aAAA;oBAClD,WAAW,qBAAqB,SAAA;gBACjC,EAAC,CACH;YACF;YAEH,QAAQ,WAAA,CACT;QAED,KAAK,IAAA,GAAO,cAAc;YACxB;QACD,EAAC;QAGF,OAAO;YAAC,KAAK,IAAA;YAAO,IAAY;SAAA;IACjC;IAED,MAAMC,eAAsC,CAAC,iBAAiB,YAAY;QACxE,MAAM,EAAE,QAAA,EAAU,WAAA,EAAa,aAAA,EAAe,MAAA,EAAQ,GAAG,YAAY;QAErE,MAAM,QAAQ,iBAAiB,OAAO;QAEtC,MAAM,UAAU,gBAAgB,MAAM;QAEtC,IAAA,OAAW,WAAW,eAAe,aAAa,UAChD,CAAA,KAAK,MAAM,SAAS,QAAS;;YAC3B,MAAM,cAAc;YACpB,IAAA,CAAA,CAAA,oBACE,YAAY,IAAA,MAAA,QAAA,sBAAA,KAAA,IAAA,KAAA,IAAA,kBAAM,GAAA,MAAQ,SAAA,CACzB,YAAY,aAAA,EAAe,CAAC,IAAA,CAAK;gBAAE,UAAU,YAAY,QAAA;YAAU,EAAC,CAErE,CAAK,cAAc,YAAY,QAAA,EAAU,YAAmB;QAE/D;QAGH,yRAAO,aAAA,EACL;YACE,SAAS,QAAQ,GAAA,CAAI,CAAC,QAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACjB,QAAA,CAAA,GAAA;oBACH,UAAW,MAAqC,QAAA;gBAAA,GAC/C;YACH,SAAA,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAS,QAAS,OAAA;QACnB,GACD,YACD;IACF;IAED,MAAMC,uBAAsD,CAC1D,oBACG;QACH,MAAM,EAAE,WAAA,EAAa,MAAA,EAAQ,GAAG,YAAY;QAE5C,MAAM,QAAQ,iBAAiB,OAAO;QAEtC,MAAM,UAAU,gBAAgB,MAAM;QAEtC,MAAM,iSAAO,qBAAA,EACX;YACE,SAAS,QAAQ,GAAA,CAAI,CAAC,QAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACjB,QAAA,CAAA,GAAA;oBACH,SAAS,MAAM,OAAA;oBACf,UAAW,MAAqC,QAAA;mBAC/C;QACJ,GACD,YACD;QAED,OAAO;YAAC,KAAK,GAAA,CAAI,CAAC,IAAM,EAAE,IAAA,CAAK;YAAE,IAAK;SAAA;IACvC;IAED,OAAO;QACL,UAAU;QACV;QACA;QACA,UAAU;QACV,UAAA;QACA,kBAAA;QACA,kBAAA;QACA,YAAA;QACA,oBAAA;QACA,aAAA;QACA;QACA,kBAAA;QACA,0BAAA;QACA,0BAAA;IACD;AACF;;;;;GC9uBD,MAAa,iBAAiB,CAACC,WAC7B;;yCAAO,WAAA,MAAA,QAAA,wBAAA,KAAA,IAAA,sBAAe,2PAAI,cAAA,CAAY,OAAO,iBAAA;AAAkB", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "debugId": null}}, {"offset": {"line": 2889, "column": 0}, "map": {"version": 3, "file": "index.mjs", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Breact-query%4011.4.2_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__%40trpc%2Bclient%4011.4.2_d478052bf51ccf7e080ad331641b9119/node_modules/%40trpc/react-query/src/createTRPCReact.tsx", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Breact-query%4011.4.2_%40tanstack%2Breact-query%405.80.7_react%4019.1.0__%40trpc%2Bclient%4011.4.2_d478052bf51ccf7e080ad331641b9119/node_modules/%40trpc/react-query/src/createTRPCQueryUtils.tsx"], "sourcesContent": ["import type {\n  DefinedInitialDataInfiniteOptions,\n  DefinedUseInfiniteQueryResult,\n  InfiniteData,\n  SkipToken,\n  UndefinedInitialDataInfiniteOptions,\n  UseInfiniteQueryOptions,\n  UseInfiniteQueryResult,\n  UseSuspenseInfiniteQueryOptions,\n  UseSuspenseInfiniteQueryResult,\n  UseSuspenseQueryResult,\n} from '@tanstack/react-query';\nimport type { createTRPCClient, TRPCClientErrorLike } from '@trpc/client';\nimport type {\n  AnyProcedure,\n  AnyRootTypes,\n  AnyRouter,\n  inferAsyncIterableYield,\n  inferProcedureInput,\n  inferTransformedProcedureOutput,\n  ProcedureType,\n  ProtectedIntersection,\n  RouterRecord,\n  Simplify,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { createFlatProxy } from '@trpc/server/unstable-core-do-not-import';\nimport * as React from 'react';\nimport type {\n  TRPCUseQueries,\n  TRPCUseSuspenseQueries,\n} from './internals/useQueries';\nimport type {\n  CreateReactUtils,\n  TRPCFetchInfiniteQueryOptions,\n  TRPCFetchQueryOptions,\n} from './shared';\nimport { createReactDecoration, createReactQueryUtils } from './shared';\nimport type { CreateReactQueryHooks } from './shared/hooks/createHooksInternal';\nimport { createRootHooks } from './shared/hooks/createHooksInternal';\nimport type {\n  DefinedUseTRPCQueryOptions,\n  DefinedUseTRPCQueryResult,\n  TRPCHookResult,\n  TRPCProvider,\n  TRPCSubscriptionResult,\n  TRPCUseQueryBaseOptions,\n  UseTRPCMutationOptions,\n  UseTRPCMutationResult,\n  UseTRPCQueryOptions,\n  UseTRPCQueryResult,\n  UseTRPCSubscriptionOptions,\n  UseTRPCSuspenseQueryOptions,\n} from './shared/hooks/types';\nimport type { CreateTRPCReactOptions } from './shared/types';\n\ntype ResolverDef = {\n  input: any;\n  output: any;\n  transformer: boolean;\n  errorShape: any;\n};\n/**\n * @internal\n */\nexport interface ProcedureUseQuery<TDef extends ResolverDef> {\n  <TQueryFnData extends TDef['output'] = TDef['output'], TData = TQueryFnData>(\n    input: TDef['input'] | SkipToken,\n    opts: DefinedUseTRPCQueryOptions<\n      TQueryFnData,\n      TData,\n      TRPCClientErrorLike<{\n        errorShape: TDef['errorShape'];\n        transformer: TDef['transformer'];\n      }>,\n      TDef['output']\n    >,\n  ): DefinedUseTRPCQueryResult<\n    TData,\n    TRPCClientErrorLike<{\n      errorShape: TDef['errorShape'];\n      transformer: TDef['transformer'];\n    }>\n  >;\n\n  <TQueryFnData extends TDef['output'] = TDef['output'], TData = TQueryFnData>(\n    input: TDef['input'] | SkipToken,\n    opts?: UseTRPCQueryOptions<\n      TQueryFnData,\n      TData,\n      TRPCClientErrorLike<TDef>,\n      TDef['output']\n    >,\n  ): UseTRPCQueryResult<TData, TRPCClientErrorLike<TDef>>;\n}\n\n/**\n * @internal\n */\nexport type ProcedureUsePrefetchQuery<TDef extends ResolverDef> = (\n  input: TDef['input'] | SkipToken,\n  opts?: TRPCFetchQueryOptions<TDef['output'], TRPCClientErrorLike<TDef>>,\n) => void;\n\n/**\n * @remark `void` is here due to https://github.com/trpc/trpc/pull/4374\n */\ntype CursorInput = {\n  cursor?: any;\n} | void;\n\ntype ReservedInfiniteQueryKeys = 'cursor' | 'direction';\ntype InfiniteInput<TInput> =\n  | Omit<TInput, ReservedInfiniteQueryKeys>\n  | SkipToken;\n\ntype inferCursorType<TInput> = TInput extends { cursor?: any }\n  ? TInput['cursor']\n  : unknown;\n\ntype makeInfiniteQueryOptions<TCursor, TOptions> = Omit<\n  TOptions,\n  'queryKey' | 'initialPageParam' | 'queryFn' | 'queryHash' | 'queryHashFn'\n> &\n  TRPCUseQueryBaseOptions & {\n    initialCursor?: TCursor;\n  };\n\ntype trpcInfiniteData<TDef extends ResolverDef> = Simplify<\n  InfiniteData<TDef['output'], inferCursorType<TDef['input']>>\n>;\n// references from react-query\n// 1st\n// declare function useInfiniteQuery<\n//   TQueryFnData,\n//   TError = DefaultError,\n//   TData = InfiniteData<TQueryFnData>,\n//   TQueryKey extends QueryKey = QueryKey,\n//   TPageParam = unknown,\n// >(\n//   options: DefinedInitialDataInfiniteOptions<\n//     TQueryFnData,\n//     TError,\n//     TData,\n//     TQueryKey,\n//     TPageParam\n//   >,\n//   queryClient?: QueryClient,\n// ): DefinedUseInfiniteQueryResult<TData, TError>;\n// 2nd\n// declare function useInfiniteQuery<\n//   TQueryFnData,\n//   TError = DefaultError,\n//   TData = InfiniteData<TQueryFnData>,\n//   TQueryKey extends QueryKey = QueryKey,\n//   TPageParam = unknown,\n// >(\n//   options: UndefinedInitialDataInfiniteOptions<\n//     TQueryFnData,\n//     TError,\n//     TData,\n//     TQueryKey,\n//     TPageParam\n//   >,\n//   queryClient?: QueryClient,\n// ): UseInfiniteQueryResult<TData, TError>;\n// 3rd\n// declare function useInfiniteQuery<\n//   TQueryFnData,\n//   TError = DefaultError,\n//   TData = InfiniteData<TQueryFnData>,\n//   TQueryKey extends QueryKey = QueryKey,\n//   TPageParam = unknown,\n// >(\n//   options: UseInfiniteQueryOptions<\n//     TQueryFnData,\n//     TError,\n//     TData,\n//     TQueryFnData,\n//     TQueryKey,\n//     TPageParam\n//   >,\n//   queryClient?: QueryClient,\n// ): UseInfiniteQueryResult<TData, TError>;\n\nexport interface useTRPCInfiniteQuery<TDef extends ResolverDef> {\n  // 1st\n  <TData = trpcInfiniteData<TDef>>(\n    input: InfiniteInput<TDef['input']>,\n    opts: makeInfiniteQueryOptions<\n      inferCursorType<TDef['input']>,\n      DefinedInitialDataInfiniteOptions<\n        //     TQueryFnData,\n        TDef['output'],\n        //     TError,\n        TRPCClientErrorLike<TDef>,\n        //     TData,\n        TData,\n        //     TQueryKey,\n        any,\n        //     TPageParam\n        inferCursorType<TDef['input']>\n      >\n    >,\n  ): TRPCHookResult &\n    DefinedUseInfiniteQueryResult<TData, TRPCClientErrorLike<TDef>>;\n\n  // 2nd\n  <TData = trpcInfiniteData<TDef>>(\n    input: InfiniteInput<TDef['input']>,\n    opts?: makeInfiniteQueryOptions<\n      inferCursorType<TDef['input']>,\n      UndefinedInitialDataInfiniteOptions<\n        //     TQueryFnData,\n        TDef['output'],\n        //     TError,\n        TRPCClientErrorLike<TDef>,\n        //     TData,\n        TData,\n        //     TQueryKey,\n        any,\n        //     TPageParam\n        inferCursorType<TDef['input']>\n      >\n    >,\n  ): TRPCHookResult & UseInfiniteQueryResult<TData, TRPCClientErrorLike<TDef>>;\n\n  // 3rd:\n  <TData = trpcInfiniteData<TDef>>(\n    input: InfiniteInput<TDef['input']>,\n    opts?: makeInfiniteQueryOptions<\n      inferCursorType<TDef['input']>,\n      UseInfiniteQueryOptions<\n        //     TQueryFnData,\n        TDef['output'],\n        //     TError,\n        TRPCClientErrorLike<TDef>,\n        //     TData,\n        TData,\n        //     TQueryKey,\n        any,\n        //     TPageParam\n        inferCursorType<TDef['input']>\n      >\n    >,\n  ): TRPCHookResult & UseInfiniteQueryResult<TData, TRPCClientErrorLike<TDef>>;\n}\n\n// references from react-query\n// declare function useSuspenseInfiniteQuery<\n//   TQueryFnData,\n//   TError = DefaultError,\n//   TData = InfiniteData<TQueryFnData>,\n//   TQueryKey extends QueryKey = QueryKey,\n//   TPageParam = unknown,\n// >(\n//   options: UseSuspenseInfiniteQueryOptions<\n//     TQueryFnData,\n//     TError,\n//     TData,\n//     TQueryFnData,\n//     TQueryKey,\n//     TPageParam\n//   >,\n//   queryClient?: QueryClient,\n// ): UseSuspenseInfiniteQueryResult<TData, TError>;\n\nexport type useTRPCSuspenseInfiniteQuery<TDef extends ResolverDef> = (\n  input: InfiniteInput<TDef['input']>,\n  opts: makeInfiniteQueryOptions<\n    inferCursorType<TDef['input']>,\n    UseSuspenseInfiniteQueryOptions<\n      //     TQueryFnData,\n      TDef['output'],\n      //     TError,\n      TRPCClientErrorLike<TDef>,\n      //     TData,\n      trpcInfiniteData<TDef>,\n      //     TQueryKey,\n      any,\n      //     TPageParam\n      inferCursorType<TDef['input']>\n    >\n  >,\n) => [\n  trpcInfiniteData<TDef>,\n  TRPCHookResult &\n    UseSuspenseInfiniteQueryResult<\n      trpcInfiniteData<TDef>,\n      TRPCClientErrorLike<TDef>\n    >,\n];\n\n/**\n * @internal\n */\nexport type MaybeDecoratedInfiniteQuery<TDef extends ResolverDef> =\n  TDef['input'] extends CursorInput\n    ? {\n        /**\n         * @see https://trpc.io/docs/v11/client/react/useInfiniteQuery\n         */\n        useInfiniteQuery: useTRPCInfiniteQuery<TDef>;\n        /**\n         * @see https://trpc.io/docs/client/react/suspense#usesuspenseinfinitequery\n         */\n        useSuspenseInfiniteQuery: useTRPCSuspenseInfiniteQuery<TDef>;\n\n        usePrefetchInfiniteQuery: (\n          input: Omit<TDef['input'], ReservedInfiniteQueryKeys> | SkipToken,\n          opts: TRPCFetchInfiniteQueryOptions<\n            TDef['input'],\n            TDef['output'],\n            TRPCClientErrorLike<TDef>\n          >,\n        ) => void;\n      }\n    : object;\n\n/**\n * @internal\n */\nexport type DecoratedQueryMethods<TDef extends ResolverDef> = {\n  /**\n   * @see https://trpc.io/docs/v11/client/react/useQuery\n   */\n  useQuery: ProcedureUseQuery<TDef>;\n  usePrefetchQuery: ProcedureUsePrefetchQuery<TDef>;\n  /**\n   * @see https://trpc.io/docs/v11/client/react/suspense#usesuspensequery\n   */\n  useSuspenseQuery: <\n    TQueryFnData extends TDef['output'] = TDef['output'],\n    TData = TQueryFnData,\n  >(\n    input: TDef['input'],\n    opts?: UseTRPCSuspenseQueryOptions<\n      TQueryFnData,\n      TData,\n      TRPCClientErrorLike<TDef>\n    >,\n  ) => [\n    TData,\n    UseSuspenseQueryResult<TData, TRPCClientErrorLike<TDef>> & TRPCHookResult,\n  ];\n};\n\n/**\n * @internal\n */\nexport type DecoratedQuery<TDef extends ResolverDef> =\n  MaybeDecoratedInfiniteQuery<TDef> & DecoratedQueryMethods<TDef>;\n\nexport type DecoratedMutation<TDef extends ResolverDef> = {\n  /**\n   * @see https://trpc.io/docs/v11/client/react/useMutation\n   */\n  useMutation: <TContext = unknown>(\n    opts?: UseTRPCMutationOptions<\n      TDef['input'],\n      TRPCClientErrorLike<TDef>,\n      TDef['output'],\n      TContext\n    >,\n  ) => UseTRPCMutationResult<\n    TDef['output'],\n    TRPCClientErrorLike<TDef>,\n    TDef['input'],\n    TContext\n  >;\n};\n\ninterface ProcedureUseSubscription<TDef extends ResolverDef> {\n  // Without skip token\n  (\n    input: TDef['input'],\n    opts?: UseTRPCSubscriptionOptions<\n      inferAsyncIterableYield<TDef['output']>,\n      TRPCClientErrorLike<TDef>\n    >,\n  ): TRPCSubscriptionResult<\n    inferAsyncIterableYield<TDef['output']>,\n    TRPCClientErrorLike<TDef>\n  >;\n\n  // With skip token\n  (\n    input: TDef['input'] | SkipToken,\n    opts?: Omit<\n      UseTRPCSubscriptionOptions<\n        inferAsyncIterableYield<TDef['output']>,\n        TRPCClientErrorLike<TDef>\n      >,\n      'enabled'\n    >,\n  ): TRPCSubscriptionResult<\n    inferAsyncIterableYield<TDef['output']>,\n    TRPCClientErrorLike<TDef>\n  >;\n}\n/**\n * @internal\n */\nexport type DecorateProcedure<\n  TType extends ProcedureType,\n  TDef extends ResolverDef,\n> = TType extends 'query'\n  ? DecoratedQuery<TDef>\n  : TType extends 'mutation'\n    ? DecoratedMutation<TDef>\n    : TType extends 'subscription'\n      ? {\n          /**\n           * @see https://trpc.io/docs/v11/subscriptions\n           */\n          useSubscription: ProcedureUseSubscription<TDef>;\n        }\n      : never;\n\n/**\n * @internal\n */\nexport type DecorateRouterRecord<\n  TRoot extends AnyRootTypes,\n  TRecord extends RouterRecord,\n> = {\n  [TKey in keyof TRecord]: TRecord[TKey] extends infer $Value\n    ? $Value extends AnyProcedure\n      ? DecorateProcedure<\n          $Value['_def']['type'],\n          {\n            input: inferProcedureInput<$Value>;\n            output: inferTransformedProcedureOutput<TRoot, $Value>;\n            transformer: TRoot['transformer'];\n            errorShape: TRoot['errorShape'];\n          }\n        >\n      : $Value extends RouterRecord\n        ? DecorateRouterRecord<TRoot, $Value>\n        : never\n    : never;\n};\n\n/**\n * @internal\n */\nexport type CreateTRPCReactBase<TRouter extends AnyRouter, TSSRContext> = {\n  /**\n   * @deprecated renamed to `useUtils` and will be removed in a future tRPC version\n   *\n   * @see https://trpc.io/docs/v11/client/react/useUtils\n   */\n  useContext(): CreateReactUtils<TRouter, TSSRContext>;\n  /**\n   * @see https://trpc.io/docs/v11/client/react/useUtils\n   */\n  useUtils(): CreateReactUtils<TRouter, TSSRContext>;\n  Provider: TRPCProvider<TRouter, TSSRContext>;\n  createClient: typeof createTRPCClient<TRouter>;\n  useQueries: TRPCUseQueries<TRouter>;\n  useSuspenseQueries: TRPCUseSuspenseQueries<TRouter>;\n};\n\nexport type CreateTRPCReact<\n  TRouter extends AnyRouter,\n  TSSRContext,\n> = ProtectedIntersection<\n  CreateTRPCReactBase<TRouter, TSSRContext>,\n  DecorateRouterRecord<\n    TRouter['_def']['_config']['$types'],\n    TRouter['_def']['record']\n  >\n>;\n\n/**\n * @internal\n */\nexport function createHooksInternal<\n  TRouter extends AnyRouter,\n  TSSRContext = unknown,\n>(trpc: CreateReactQueryHooks<TRouter, TSSRContext>) {\n  type CreateHooksInternal = CreateTRPCReact<TRouter, TSSRContext>;\n\n  const proxy = createReactDecoration<TRouter, TSSRContext>(\n    trpc,\n  ) as DecorateRouterRecord<\n    TRouter['_def']['_config']['$types'],\n    TRouter['_def']['record']\n  >;\n  return createFlatProxy<CreateHooksInternal>((key) => {\n    if (key === 'useContext' || key === 'useUtils') {\n      return () => {\n        const context = trpc.useUtils();\n        // create a stable reference of the utils context\n        return React.useMemo(() => {\n          return (createReactQueryUtils as any)(context);\n        }, [context]);\n      };\n    }\n\n    if (trpc.hasOwnProperty(key)) {\n      return (trpc as any)[key];\n    }\n\n    return proxy[key];\n  });\n}\n\nexport function createTRPCReact<\n  TRouter extends AnyRouter,\n  TSSRContext = unknown,\n>(\n  opts?: CreateTRPCReactOptions<TRouter>,\n): CreateTRPCReact<TRouter, TSSRContext> {\n  const hooks = createRootHooks<TRouter, TSSRContext>(opts);\n  const proxy = createHooksInternal<TRouter, TSSRContext>(hooks);\n\n  return proxy as any;\n}\n", "import type { AnyRouter } from '@trpc/server/unstable-core-do-not-import';\nimport { createQueryUtilsProxy } from './shared';\nimport type { CreateQueryUtilsOptions } from './utils/createUtilityFunctions';\nimport { createUtilityFunctions } from './utils/createUtilityFunctions';\n\nexport function createTRPCQueryUtils<TRouter extends AnyRouter>(\n  opts: CreateQueryUtilsOptions<TRouter>,\n) {\n  const utils = createUtilityFunctions(opts);\n  return createQueryUtilsProxy<TRouter>(utils);\n}\n"], "names": ["trpc: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><TR<PERSON><PERSON>, TSSRContext>", "opts?: CreateTRPCReactOptions<TRouter>", "opts: Create<PERSON>ueryUtilsOptions<TRouter>"], "mappings": ";;;;;;;;;;;;;;;;;GA4dA,SAAgB,oBAGdA,IAAAA,EAAmD;IAGnD,MAAM,qYAAQ,wBAAA,EACZ,KACD;IAID,OAAO,qRAAA,EAAqC,CAAC,QAAQ;QACnD,IAAI,QAAQ,gBAAgB,QAAQ,WAClC,CAAA,OAAO,MAAM;YACX,MAAM,UAAU,KAAK,QAAA,EAAU;YAE/B,0ZAAO,MAAM,IAAA,EAAQ,MAAM;gBACzB,QAAQ,oZAAA,EAA8B,QAAQ;YAC/C,GAAE;gBAAC,OAAQ;aAAA,CAAC;QACd;QAGH,IAAI,KAAK,cAAA,CAAe,IAAI,CAC1B,CAAA,OAAQ,IAAA,CAAa,IAAA;QAGvB,OAAO,KAAA,CAAM,IAAA;IACd,EAAC;AACH;AAED,SAAgB,gBAIdC,IAAAA,EACuC;IACvC,MAAM,qYAAQ,kBAAA,EAAsC,KAAK;IACzD,MAAM,QAAQ,oBAA0C,MAAM;IAE9D,OAAO;AACR;;;AChgBD,SAAgB,qBACdC,IAAAA,EACA;IACA,MAAM,qYAAQ,yBAAA,EAAuB,KAAK;IAC1C,oYAAO,wBAAA,EAA+B,MAAM;AAC7C", "ignoreList": [0, 1], "debugId": null}}, {"offset": {"line": 2952, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/sonner%402.0.5_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/sonner/dist/index.mjs"], "sourcesContent": ["'use client';\nfunction __insertCSS(code) {\n  if (!code || typeof document == 'undefined') return\n  let head = document.head || document.getElementsByTagName('head')[0]\n  let style = document.createElement('style')\n  style.type = 'text/css'\n  head.appendChild(style)\n  ;style.styleSheet ? (style.styleSheet.cssText = code) : style.appendChild(document.createTextNode(code))\n}\n\nimport React from 'react';\nimport ReactDOM from 'react-dom';\n\nconst getAsset = (type)=>{\n    switch(type){\n        case 'success':\n            return SuccessIcon;\n        case 'info':\n            return InfoIcon;\n        case 'warning':\n            return WarningIcon;\n        case 'error':\n            return ErrorIcon;\n        default:\n            return null;\n    }\n};\nconst bars = Array(12).fill(0);\nconst Loader = ({ visible, className })=>{\n    return /*#__PURE__*/ React.createElement(\"div\", {\n        className: [\n            'sonner-loading-wrapper',\n            className\n        ].filter(Boolean).join(' '),\n        \"data-visible\": visible\n    }, /*#__PURE__*/ React.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, bars.map((_, i)=>/*#__PURE__*/ React.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: `spinner-bar-${i}`\n        }))));\n};\nconst SuccessIcon = /*#__PURE__*/ React.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n}));\nconst WarningIcon = /*#__PURE__*/ React.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n}));\nconst InfoIcon = /*#__PURE__*/ React.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n}));\nconst ErrorIcon = /*#__PURE__*/ React.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n}));\nconst CloseIcon = /*#__PURE__*/ React.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n}, /*#__PURE__*/ React.createElement(\"line\", {\n    x1: \"18\",\n    y1: \"6\",\n    x2: \"6\",\n    y2: \"18\"\n}), /*#__PURE__*/ React.createElement(\"line\", {\n    x1: \"6\",\n    y1: \"6\",\n    x2: \"18\",\n    y2: \"18\"\n}));\n\nconst useIsDocumentHidden = ()=>{\n    const [isDocumentHidden, setIsDocumentHidden] = React.useState(document.hidden);\n    React.useEffect(()=>{\n        const callback = ()=>{\n            setIsDocumentHidden(document.hidden);\n        };\n        document.addEventListener('visibilitychange', callback);\n        return ()=>window.removeEventListener('visibilitychange', callback);\n    }, []);\n    return isDocumentHidden;\n};\n\nlet toastsCounter = 1;\nclass Observer {\n    constructor(){\n        // We use arrow functions to maintain the correct `this` reference\n        this.subscribe = (subscriber)=>{\n            this.subscribers.push(subscriber);\n            return ()=>{\n                const index = this.subscribers.indexOf(subscriber);\n                this.subscribers.splice(index, 1);\n            };\n        };\n        this.publish = (data)=>{\n            this.subscribers.forEach((subscriber)=>subscriber(data));\n        };\n        this.addToast = (data)=>{\n            this.publish(data);\n            this.toasts = [\n                ...this.toasts,\n                data\n            ];\n        };\n        this.create = (data)=>{\n            var _data_id;\n            const { message, ...rest } = data;\n            const id = typeof (data == null ? void 0 : data.id) === 'number' || ((_data_id = data.id) == null ? void 0 : _data_id.length) > 0 ? data.id : toastsCounter++;\n            const alreadyExists = this.toasts.find((toast)=>{\n                return toast.id === id;\n            });\n            const dismissible = data.dismissible === undefined ? true : data.dismissible;\n            if (this.dismissedToasts.has(id)) {\n                this.dismissedToasts.delete(id);\n            }\n            if (alreadyExists) {\n                this.toasts = this.toasts.map((toast)=>{\n                    if (toast.id === id) {\n                        this.publish({\n                            ...toast,\n                            ...data,\n                            id,\n                            title: message\n                        });\n                        return {\n                            ...toast,\n                            ...data,\n                            id,\n                            dismissible,\n                            title: message\n                        };\n                    }\n                    return toast;\n                });\n            } else {\n                this.addToast({\n                    title: message,\n                    ...rest,\n                    dismissible,\n                    id\n                });\n            }\n            return id;\n        };\n        this.dismiss = (id)=>{\n            if (id) {\n                this.dismissedToasts.add(id);\n                requestAnimationFrame(()=>this.subscribers.forEach((subscriber)=>subscriber({\n                            id,\n                            dismiss: true\n                        })));\n            } else {\n                this.toasts.forEach((toast)=>{\n                    this.subscribers.forEach((subscriber)=>subscriber({\n                            id: toast.id,\n                            dismiss: true\n                        }));\n                });\n            }\n            return id;\n        };\n        this.message = (message, data)=>{\n            return this.create({\n                ...data,\n                message\n            });\n        };\n        this.error = (message, data)=>{\n            return this.create({\n                ...data,\n                message,\n                type: 'error'\n            });\n        };\n        this.success = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'success',\n                message\n            });\n        };\n        this.info = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'info',\n                message\n            });\n        };\n        this.warning = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'warning',\n                message\n            });\n        };\n        this.loading = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'loading',\n                message\n            });\n        };\n        this.promise = (promise, data)=>{\n            if (!data) {\n                // Nothing to show\n                return;\n            }\n            let id = undefined;\n            if (data.loading !== undefined) {\n                id = this.create({\n                    ...data,\n                    promise,\n                    type: 'loading',\n                    message: data.loading,\n                    description: typeof data.description !== 'function' ? data.description : undefined\n                });\n            }\n            const p = Promise.resolve(promise instanceof Function ? promise() : promise);\n            let shouldDismiss = id !== undefined;\n            let result;\n            const originalPromise = p.then(async (response)=>{\n                result = [\n                    'resolve',\n                    response\n                ];\n                const isReactElementResponse = React.isValidElement(response);\n                if (isReactElementResponse) {\n                    shouldDismiss = false;\n                    this.create({\n                        id,\n                        type: 'default',\n                        message: response\n                    });\n                } else if (isHttpResponse(response) && !response.ok) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(`HTTP error! status: ${response.status}`) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(`HTTP error! status: ${response.status}`) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !React.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (response instanceof Error) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(response) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !React.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (data.success !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.success === 'function' ? await data.success(response) : data.success;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !React.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'success',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).catch(async (error)=>{\n                result = [\n                    'reject',\n                    error\n                ];\n                if (data.error !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(error) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(error) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !React.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).finally(()=>{\n                if (shouldDismiss) {\n                    // Toast is still in load state (and will be indefinitely — dismiss it)\n                    this.dismiss(id);\n                    id = undefined;\n                }\n                data.finally == null ? void 0 : data.finally.call(data);\n            });\n            const unwrap = ()=>new Promise((resolve, reject)=>originalPromise.then(()=>result[0] === 'reject' ? reject(result[1]) : resolve(result[1])).catch(reject));\n            if (typeof id !== 'string' && typeof id !== 'number') {\n                // cannot Object.assign on undefined\n                return {\n                    unwrap\n                };\n            } else {\n                return Object.assign(id, {\n                    unwrap\n                });\n            }\n        };\n        this.custom = (jsx, data)=>{\n            const id = (data == null ? void 0 : data.id) || toastsCounter++;\n            this.create({\n                jsx: jsx(id),\n                id,\n                ...data\n            });\n            return id;\n        };\n        this.getActiveToasts = ()=>{\n            return this.toasts.filter((toast)=>!this.dismissedToasts.has(toast.id));\n        };\n        this.subscribers = [];\n        this.toasts = [];\n        this.dismissedToasts = new Set();\n    }\n}\nconst ToastState = new Observer();\n// bind this to the toast function\nconst toastFunction = (message, data)=>{\n    const id = (data == null ? void 0 : data.id) || toastsCounter++;\n    ToastState.addToast({\n        title: message,\n        ...data,\n        id\n    });\n    return id;\n};\nconst isHttpResponse = (data)=>{\n    return data && typeof data === 'object' && 'ok' in data && typeof data.ok === 'boolean' && 'status' in data && typeof data.status === 'number';\n};\nconst basicToast = toastFunction;\nconst getHistory = ()=>ToastState.toasts;\nconst getToasts = ()=>ToastState.getActiveToasts();\n// We use `Object.assign` to maintain the correct types as we would lose them otherwise\nconst toast = Object.assign(basicToast, {\n    success: ToastState.success,\n    info: ToastState.info,\n    warning: ToastState.warning,\n    error: ToastState.error,\n    custom: ToastState.custom,\n    message: ToastState.message,\n    promise: ToastState.promise,\n    dismiss: ToastState.dismiss,\n    loading: ToastState.loading\n}, {\n    getHistory,\n    getToasts\n});\n\n__insertCSS(\"[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\");\n\nfunction isAction(action) {\n    return action.label !== undefined;\n}\n\n// Visible toasts amount\nconst VISIBLE_TOASTS_AMOUNT = 3;\n// Viewport padding\nconst VIEWPORT_OFFSET = '24px';\n// Mobile viewport padding\nconst MOBILE_VIEWPORT_OFFSET = '16px';\n// Default lifetime of a toasts (in ms)\nconst TOAST_LIFETIME = 4000;\n// Default toast width\nconst TOAST_WIDTH = 356;\n// Default gap between toasts\nconst GAP = 14;\n// Threshold to dismiss a toast\nconst SWIPE_THRESHOLD = 45;\n// Equal to exit animation duration\nconst TIME_BEFORE_UNMOUNT = 200;\nfunction cn(...classes) {\n    return classes.filter(Boolean).join(' ');\n}\nfunction getDefaultSwipeDirections(position) {\n    const [y, x] = position.split('-');\n    const directions = [];\n    if (y) {\n        directions.push(y);\n    }\n    if (x) {\n        directions.push(x);\n    }\n    return directions;\n}\nconst Toast = (props)=>{\n    var _toast_classNames, _toast_classNames1, _toast_classNames2, _toast_classNames3, _toast_classNames4, _toast_classNames5, _toast_classNames6, _toast_classNames7, _toast_classNames8;\n    const { invert: ToasterInvert, toast, unstyled, interacting, setHeights, visibleToasts, heights, index, toasts, expanded, removeToast, defaultRichColors, closeButton: closeButtonFromToaster, style, cancelButtonStyle, actionButtonStyle, className = '', descriptionClassName = '', duration: durationFromToaster, position, gap, expandByDefault, classNames, icons, closeButtonAriaLabel = 'Close toast' } = props;\n    const [swipeDirection, setSwipeDirection] = React.useState(null);\n    const [swipeOutDirection, setSwipeOutDirection] = React.useState(null);\n    const [mounted, setMounted] = React.useState(false);\n    const [removed, setRemoved] = React.useState(false);\n    const [swiping, setSwiping] = React.useState(false);\n    const [swipeOut, setSwipeOut] = React.useState(false);\n    const [isSwiped, setIsSwiped] = React.useState(false);\n    const [offsetBeforeRemove, setOffsetBeforeRemove] = React.useState(0);\n    const [initialHeight, setInitialHeight] = React.useState(0);\n    const remainingTime = React.useRef(toast.duration || durationFromToaster || TOAST_LIFETIME);\n    const dragStartTime = React.useRef(null);\n    const toastRef = React.useRef(null);\n    const isFront = index === 0;\n    const isVisible = index + 1 <= visibleToasts;\n    const toastType = toast.type;\n    const dismissible = toast.dismissible !== false;\n    const toastClassname = toast.className || '';\n    const toastDescriptionClassname = toast.descriptionClassName || '';\n    // Height index is used to calculate the offset as it gets updated before the toast array, which means we can calculate the new layout faster.\n    const heightIndex = React.useMemo(()=>heights.findIndex((height)=>height.toastId === toast.id) || 0, [\n        heights,\n        toast.id\n    ]);\n    const closeButton = React.useMemo(()=>{\n        var _toast_closeButton;\n        return (_toast_closeButton = toast.closeButton) != null ? _toast_closeButton : closeButtonFromToaster;\n    }, [\n        toast.closeButton,\n        closeButtonFromToaster\n    ]);\n    const duration = React.useMemo(()=>toast.duration || durationFromToaster || TOAST_LIFETIME, [\n        toast.duration,\n        durationFromToaster\n    ]);\n    const closeTimerStartTimeRef = React.useRef(0);\n    const offset = React.useRef(0);\n    const lastCloseTimerStartTimeRef = React.useRef(0);\n    const pointerStartRef = React.useRef(null);\n    const [y, x] = position.split('-');\n    const toastsHeightBefore = React.useMemo(()=>{\n        return heights.reduce((prev, curr, reducerIndex)=>{\n            // Calculate offset up until current toast\n            if (reducerIndex >= heightIndex) {\n                return prev;\n            }\n            return prev + curr.height;\n        }, 0);\n    }, [\n        heights,\n        heightIndex\n    ]);\n    const isDocumentHidden = useIsDocumentHidden();\n    const invert = toast.invert || ToasterInvert;\n    const disabled = toastType === 'loading';\n    offset.current = React.useMemo(()=>heightIndex * gap + toastsHeightBefore, [\n        heightIndex,\n        toastsHeightBefore\n    ]);\n    React.useEffect(()=>{\n        remainingTime.current = duration;\n    }, [\n        duration\n    ]);\n    React.useEffect(()=>{\n        // Trigger enter animation without using CSS animation\n        setMounted(true);\n    }, []);\n    React.useEffect(()=>{\n        const toastNode = toastRef.current;\n        if (toastNode) {\n            const height = toastNode.getBoundingClientRect().height;\n            // Add toast height to heights array after the toast is mounted\n            setInitialHeight(height);\n            setHeights((h)=>[\n                    {\n                        toastId: toast.id,\n                        height,\n                        position: toast.position\n                    },\n                    ...h\n                ]);\n            return ()=>setHeights((h)=>h.filter((height)=>height.toastId !== toast.id));\n        }\n    }, [\n        setHeights,\n        toast.id\n    ]);\n    React.useLayoutEffect(()=>{\n        // Keep height up to date with the content in case it updates\n        if (!mounted) return;\n        const toastNode = toastRef.current;\n        const originalHeight = toastNode.style.height;\n        toastNode.style.height = 'auto';\n        const newHeight = toastNode.getBoundingClientRect().height;\n        toastNode.style.height = originalHeight;\n        setInitialHeight(newHeight);\n        setHeights((heights)=>{\n            const alreadyExists = heights.find((height)=>height.toastId === toast.id);\n            if (!alreadyExists) {\n                return [\n                    {\n                        toastId: toast.id,\n                        height: newHeight,\n                        position: toast.position\n                    },\n                    ...heights\n                ];\n            } else {\n                return heights.map((height)=>height.toastId === toast.id ? {\n                        ...height,\n                        height: newHeight\n                    } : height);\n            }\n        });\n    }, [\n        mounted,\n        toast.title,\n        toast.description,\n        setHeights,\n        toast.id,\n        toast.jsx,\n        toast.action,\n        toast.cancel\n    ]);\n    const deleteToast = React.useCallback(()=>{\n        // Save the offset for the exit swipe animation\n        setRemoved(true);\n        setOffsetBeforeRemove(offset.current);\n        setHeights((h)=>h.filter((height)=>height.toastId !== toast.id));\n        setTimeout(()=>{\n            removeToast(toast);\n        }, TIME_BEFORE_UNMOUNT);\n    }, [\n        toast,\n        removeToast,\n        setHeights,\n        offset\n    ]);\n    React.useEffect(()=>{\n        if (toast.promise && toastType === 'loading' || toast.duration === Infinity || toast.type === 'loading') return;\n        let timeoutId;\n        // Pause the timer on each hover\n        const pauseTimer = ()=>{\n            if (lastCloseTimerStartTimeRef.current < closeTimerStartTimeRef.current) {\n                // Get the elapsed time since the timer started\n                const elapsedTime = new Date().getTime() - closeTimerStartTimeRef.current;\n                remainingTime.current = remainingTime.current - elapsedTime;\n            }\n            lastCloseTimerStartTimeRef.current = new Date().getTime();\n        };\n        const startTimer = ()=>{\n            // setTimeout(, Infinity) behaves as if the delay is 0.\n            // As a result, the toast would be closed immediately, giving the appearance that it was never rendered.\n            // See: https://github.com/denysdovhan/wtfjs?tab=readme-ov-file#an-infinite-timeout\n            if (remainingTime.current === Infinity) return;\n            closeTimerStartTimeRef.current = new Date().getTime();\n            // Let the toast know it has started\n            timeoutId = setTimeout(()=>{\n                toast.onAutoClose == null ? void 0 : toast.onAutoClose.call(toast, toast);\n                deleteToast();\n            }, remainingTime.current);\n        };\n        if (expanded || interacting || isDocumentHidden) {\n            pauseTimer();\n        } else {\n            startTimer();\n        }\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        expanded,\n        interacting,\n        toast,\n        toastType,\n        isDocumentHidden,\n        deleteToast\n    ]);\n    React.useEffect(()=>{\n        if (toast.delete) {\n            deleteToast();\n            toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n        }\n    }, [\n        deleteToast,\n        toast.delete\n    ]);\n    function getLoadingIcon() {\n        var _toast_classNames;\n        if (icons == null ? void 0 : icons.loading) {\n            var _toast_classNames1;\n            return /*#__PURE__*/ React.createElement(\"div\", {\n                className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1.loader, 'sonner-loader'),\n                \"data-visible\": toastType === 'loading'\n            }, icons.loading);\n        }\n        return /*#__PURE__*/ React.createElement(Loader, {\n            className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.loader),\n            visible: toastType === 'loading'\n        });\n    }\n    const icon = toast.icon || (icons == null ? void 0 : icons[toastType]) || getAsset(toastType);\n    var _toast_richColors, _icons_close;\n    return /*#__PURE__*/ React.createElement(\"li\", {\n        tabIndex: 0,\n        ref: toastRef,\n        className: cn(className, toastClassname, classNames == null ? void 0 : classNames.toast, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.toast, classNames == null ? void 0 : classNames.default, classNames == null ? void 0 : classNames[toastType], toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1[toastType]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (_toast_richColors = toast.richColors) != null ? _toast_richColors : defaultRichColors,\n        \"data-styled\": !Boolean(toast.jsx || toast.unstyled || unstyled),\n        \"data-mounted\": mounted,\n        \"data-promise\": Boolean(toast.promise),\n        \"data-swiped\": isSwiped,\n        \"data-removed\": removed,\n        \"data-visible\": isVisible,\n        \"data-y-position\": y,\n        \"data-x-position\": x,\n        \"data-index\": index,\n        \"data-front\": isFront,\n        \"data-swiping\": swiping,\n        \"data-dismissible\": dismissible,\n        \"data-type\": toastType,\n        \"data-invert\": invert,\n        \"data-swipe-out\": swipeOut,\n        \"data-swipe-direction\": swipeOutDirection,\n        \"data-expanded\": Boolean(expanded || expandByDefault && mounted),\n        style: {\n            '--index': index,\n            '--toasts-before': index,\n            '--z-index': toasts.length - index,\n            '--offset': `${removed ? offsetBeforeRemove : offset.current}px`,\n            '--initial-height': expandByDefault ? 'auto' : `${initialHeight}px`,\n            ...style,\n            ...toast.style\n        },\n        onDragEnd: ()=>{\n            setSwiping(false);\n            setSwipeDirection(null);\n            pointerStartRef.current = null;\n        },\n        onPointerDown: (event)=>{\n            if (disabled || !dismissible) return;\n            dragStartTime.current = new Date();\n            setOffsetBeforeRemove(offset.current);\n            // Ensure we maintain correct pointer capture even when going outside of the toast (e.g. when swiping)\n            event.target.setPointerCapture(event.pointerId);\n            if (event.target.tagName === 'BUTTON') return;\n            setSwiping(true);\n            pointerStartRef.current = {\n                x: event.clientX,\n                y: event.clientY\n            };\n        },\n        onPointerUp: ()=>{\n            var _toastRef_current, _toastRef_current1, _dragStartTime_current;\n            if (swipeOut || !dismissible) return;\n            pointerStartRef.current = null;\n            const swipeAmountX = Number(((_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.getPropertyValue('--swipe-amount-x').replace('px', '')) || 0);\n            const swipeAmountY = Number(((_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.getPropertyValue('--swipe-amount-y').replace('px', '')) || 0);\n            const timeTaken = new Date().getTime() - ((_dragStartTime_current = dragStartTime.current) == null ? void 0 : _dragStartTime_current.getTime());\n            const swipeAmount = swipeDirection === 'x' ? swipeAmountX : swipeAmountY;\n            const velocity = Math.abs(swipeAmount) / timeTaken;\n            if (Math.abs(swipeAmount) >= SWIPE_THRESHOLD || velocity > 0.11) {\n                setOffsetBeforeRemove(offset.current);\n                toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n                if (swipeDirection === 'x') {\n                    setSwipeOutDirection(swipeAmountX > 0 ? 'right' : 'left');\n                } else {\n                    setSwipeOutDirection(swipeAmountY > 0 ? 'down' : 'up');\n                }\n                deleteToast();\n                setSwipeOut(true);\n                return;\n            } else {\n                var _toastRef_current2, _toastRef_current3;\n                (_toastRef_current2 = toastRef.current) == null ? void 0 : _toastRef_current2.style.setProperty('--swipe-amount-x', `0px`);\n                (_toastRef_current3 = toastRef.current) == null ? void 0 : _toastRef_current3.style.setProperty('--swipe-amount-y', `0px`);\n            }\n            setIsSwiped(false);\n            setSwiping(false);\n            setSwipeDirection(null);\n        },\n        onPointerMove: (event)=>{\n            var _window_getSelection, // Apply transform using both x and y values\n            _toastRef_current, _toastRef_current1;\n            if (!pointerStartRef.current || !dismissible) return;\n            const isHighlighted = ((_window_getSelection = window.getSelection()) == null ? void 0 : _window_getSelection.toString().length) > 0;\n            if (isHighlighted) return;\n            const yDelta = event.clientY - pointerStartRef.current.y;\n            const xDelta = event.clientX - pointerStartRef.current.x;\n            var _props_swipeDirections;\n            const swipeDirections = (_props_swipeDirections = props.swipeDirections) != null ? _props_swipeDirections : getDefaultSwipeDirections(position);\n            // Determine swipe direction if not already locked\n            if (!swipeDirection && (Math.abs(xDelta) > 1 || Math.abs(yDelta) > 1)) {\n                setSwipeDirection(Math.abs(xDelta) > Math.abs(yDelta) ? 'x' : 'y');\n            }\n            let swipeAmount = {\n                x: 0,\n                y: 0\n            };\n            const getDampening = (delta)=>{\n                const factor = Math.abs(delta) / 20;\n                return 1 / (1.5 + factor);\n            };\n            // Only apply swipe in the locked direction\n            if (swipeDirection === 'y') {\n                // Handle vertical swipes\n                if (swipeDirections.includes('top') || swipeDirections.includes('bottom')) {\n                    if (swipeDirections.includes('top') && yDelta < 0 || swipeDirections.includes('bottom') && yDelta > 0) {\n                        swipeAmount.y = yDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = yDelta * getDampening(yDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.y = Math.abs(dampenedDelta) < Math.abs(yDelta) ? dampenedDelta : yDelta;\n                    }\n                }\n            } else if (swipeDirection === 'x') {\n                // Handle horizontal swipes\n                if (swipeDirections.includes('left') || swipeDirections.includes('right')) {\n                    if (swipeDirections.includes('left') && xDelta < 0 || swipeDirections.includes('right') && xDelta > 0) {\n                        swipeAmount.x = xDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = xDelta * getDampening(xDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.x = Math.abs(dampenedDelta) < Math.abs(xDelta) ? dampenedDelta : xDelta;\n                    }\n                }\n            }\n            if (Math.abs(swipeAmount.x) > 0 || Math.abs(swipeAmount.y) > 0) {\n                setIsSwiped(true);\n            }\n            (_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.setProperty('--swipe-amount-x', `${swipeAmount.x}px`);\n            (_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.setProperty('--swipe-amount-y', `${swipeAmount.y}px`);\n        }\n    }, closeButton && !toast.jsx && toastType !== 'loading' ? /*#__PURE__*/ React.createElement(\"button\", {\n        \"aria-label\": closeButtonAriaLabel,\n        \"data-disabled\": disabled,\n        \"data-close-button\": true,\n        onClick: disabled || !dismissible ? ()=>{} : ()=>{\n            deleteToast();\n            toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n        },\n        className: cn(classNames == null ? void 0 : classNames.closeButton, toast == null ? void 0 : (_toast_classNames2 = toast.classNames) == null ? void 0 : _toast_classNames2.closeButton)\n    }, (_icons_close = icons == null ? void 0 : icons.close) != null ? _icons_close : CloseIcon) : null, (toastType || toast.icon || toast.promise) && toast.icon !== null && ((icons == null ? void 0 : icons[toastType]) !== null || toast.icon) ? /*#__PURE__*/ React.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.icon, toast == null ? void 0 : (_toast_classNames3 = toast.classNames) == null ? void 0 : _toast_classNames3.icon)\n    }, toast.promise || toast.type === 'loading' && !toast.icon ? toast.icon || getLoadingIcon() : null, toast.type !== 'loading' ? icon : null) : null, /*#__PURE__*/ React.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.content, toast == null ? void 0 : (_toast_classNames4 = toast.classNames) == null ? void 0 : _toast_classNames4.content)\n    }, /*#__PURE__*/ React.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.title, toast == null ? void 0 : (_toast_classNames5 = toast.classNames) == null ? void 0 : _toast_classNames5.title)\n    }, toast.jsx ? toast.jsx : typeof toast.title === 'function' ? toast.title() : toast.title), toast.description ? /*#__PURE__*/ React.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: cn(descriptionClassName, toastDescriptionClassname, classNames == null ? void 0 : classNames.description, toast == null ? void 0 : (_toast_classNames6 = toast.classNames) == null ? void 0 : _toast_classNames6.description)\n    }, typeof toast.description === 'function' ? toast.description() : toast.description) : null), /*#__PURE__*/ React.isValidElement(toast.cancel) ? toast.cancel : toast.cancel && isAction(toast.cancel) ? /*#__PURE__*/ React.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-cancel\": true,\n        style: toast.cancelButtonStyle || cancelButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.cancel)) return;\n            if (!dismissible) return;\n            toast.cancel.onClick == null ? void 0 : toast.cancel.onClick.call(toast.cancel, event);\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.cancelButton, toast == null ? void 0 : (_toast_classNames7 = toast.classNames) == null ? void 0 : _toast_classNames7.cancelButton)\n    }, toast.cancel.label) : null, /*#__PURE__*/ React.isValidElement(toast.action) ? toast.action : toast.action && isAction(toast.action) ? /*#__PURE__*/ React.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-action\": true,\n        style: toast.actionButtonStyle || actionButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.action)) return;\n            toast.action.onClick == null ? void 0 : toast.action.onClick.call(toast.action, event);\n            if (event.defaultPrevented) return;\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.actionButton, toast == null ? void 0 : (_toast_classNames8 = toast.classNames) == null ? void 0 : _toast_classNames8.actionButton)\n    }, toast.action.label) : null);\n};\nfunction getDocumentDirection() {\n    if (typeof window === 'undefined') return 'ltr';\n    if (typeof document === 'undefined') return 'ltr'; // For Fresh purpose\n    const dirAttribute = document.documentElement.getAttribute('dir');\n    if (dirAttribute === 'auto' || !dirAttribute) {\n        return window.getComputedStyle(document.documentElement).direction;\n    }\n    return dirAttribute;\n}\nfunction assignOffset(defaultOffset, mobileOffset) {\n    const styles = {};\n    [\n        defaultOffset,\n        mobileOffset\n    ].forEach((offset, index)=>{\n        const isMobile = index === 1;\n        const prefix = isMobile ? '--mobile-offset' : '--offset';\n        const defaultValue = isMobile ? MOBILE_VIEWPORT_OFFSET : VIEWPORT_OFFSET;\n        function assignAll(offset) {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                styles[`${prefix}-${key}`] = typeof offset === 'number' ? `${offset}px` : offset;\n            });\n        }\n        if (typeof offset === 'number' || typeof offset === 'string') {\n            assignAll(offset);\n        } else if (typeof offset === 'object') {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                if (offset[key] === undefined) {\n                    styles[`${prefix}-${key}`] = defaultValue;\n                } else {\n                    styles[`${prefix}-${key}`] = typeof offset[key] === 'number' ? `${offset[key]}px` : offset[key];\n                }\n            });\n        } else {\n            assignAll(defaultValue);\n        }\n    });\n    return styles;\n}\nfunction useSonner() {\n    const [activeToasts, setActiveToasts] = React.useState([]);\n    React.useEffect(()=>{\n        return ToastState.subscribe((toast)=>{\n            if (toast.dismiss) {\n                setTimeout(()=>{\n                    ReactDOM.flushSync(()=>{\n                        setActiveToasts((toasts)=>toasts.filter((t)=>t.id !== toast.id));\n                    });\n                });\n                return;\n            }\n            // Prevent batching, temp solution.\n            setTimeout(()=>{\n                ReactDOM.flushSync(()=>{\n                    setActiveToasts((toasts)=>{\n                        const indexOfExistingToast = toasts.findIndex((t)=>t.id === toast.id);\n                        // Update the toast if it already exists\n                        if (indexOfExistingToast !== -1) {\n                            return [\n                                ...toasts.slice(0, indexOfExistingToast),\n                                {\n                                    ...toasts[indexOfExistingToast],\n                                    ...toast\n                                },\n                                ...toasts.slice(indexOfExistingToast + 1)\n                            ];\n                        }\n                        return [\n                            toast,\n                            ...toasts\n                        ];\n                    });\n                });\n            });\n        });\n    }, []);\n    return {\n        toasts: activeToasts\n    };\n}\nconst Toaster = /*#__PURE__*/ React.forwardRef(function Toaster(props, ref) {\n    const { invert, position = 'bottom-right', hotkey = [\n        'altKey',\n        'KeyT'\n    ], expand, closeButton, className, offset, mobileOffset, theme = 'light', richColors, duration, style, visibleToasts = VISIBLE_TOASTS_AMOUNT, toastOptions, dir = getDocumentDirection(), gap = GAP, icons, containerAriaLabel = 'Notifications' } = props;\n    const [toasts, setToasts] = React.useState([]);\n    const possiblePositions = React.useMemo(()=>{\n        return Array.from(new Set([\n            position\n        ].concat(toasts.filter((toast)=>toast.position).map((toast)=>toast.position))));\n    }, [\n        toasts,\n        position\n    ]);\n    const [heights, setHeights] = React.useState([]);\n    const [expanded, setExpanded] = React.useState(false);\n    const [interacting, setInteracting] = React.useState(false);\n    const [actualTheme, setActualTheme] = React.useState(theme !== 'system' ? theme : typeof window !== 'undefined' ? window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light' : 'light');\n    const listRef = React.useRef(null);\n    const hotkeyLabel = hotkey.join('+').replace(/Key/g, '').replace(/Digit/g, '');\n    const lastFocusedElementRef = React.useRef(null);\n    const isFocusWithinRef = React.useRef(false);\n    const removeToast = React.useCallback((toastToRemove)=>{\n        setToasts((toasts)=>{\n            var _toasts_find;\n            if (!((_toasts_find = toasts.find((toast)=>toast.id === toastToRemove.id)) == null ? void 0 : _toasts_find.delete)) {\n                ToastState.dismiss(toastToRemove.id);\n            }\n            return toasts.filter(({ id })=>id !== toastToRemove.id);\n        });\n    }, []);\n    React.useEffect(()=>{\n        return ToastState.subscribe((toast)=>{\n            if (toast.dismiss) {\n                // Prevent batching of other state updates\n                requestAnimationFrame(()=>{\n                    setToasts((toasts)=>toasts.map((t)=>t.id === toast.id ? {\n                                ...t,\n                                delete: true\n                            } : t));\n                });\n                return;\n            }\n            // Prevent batching, temp solution.\n            setTimeout(()=>{\n                ReactDOM.flushSync(()=>{\n                    setToasts((toasts)=>{\n                        const indexOfExistingToast = toasts.findIndex((t)=>t.id === toast.id);\n                        // Update the toast if it already exists\n                        if (indexOfExistingToast !== -1) {\n                            return [\n                                ...toasts.slice(0, indexOfExistingToast),\n                                {\n                                    ...toasts[indexOfExistingToast],\n                                    ...toast\n                                },\n                                ...toasts.slice(indexOfExistingToast + 1)\n                            ];\n                        }\n                        return [\n                            toast,\n                            ...toasts\n                        ];\n                    });\n                });\n            });\n        });\n    }, [\n        toasts\n    ]);\n    React.useEffect(()=>{\n        if (theme !== 'system') {\n            setActualTheme(theme);\n            return;\n        }\n        if (theme === 'system') {\n            // check if current preference is dark\n            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n                // it's currently dark\n                setActualTheme('dark');\n            } else {\n                // it's not dark\n                setActualTheme('light');\n            }\n        }\n        if (typeof window === 'undefined') return;\n        const darkMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n        try {\n            // Chrome & Firefox\n            darkMediaQuery.addEventListener('change', ({ matches })=>{\n                if (matches) {\n                    setActualTheme('dark');\n                } else {\n                    setActualTheme('light');\n                }\n            });\n        } catch (error) {\n            // Safari < 14\n            darkMediaQuery.addListener(({ matches })=>{\n                try {\n                    if (matches) {\n                        setActualTheme('dark');\n                    } else {\n                        setActualTheme('light');\n                    }\n                } catch (e) {\n                    console.error(e);\n                }\n            });\n        }\n    }, [\n        theme\n    ]);\n    React.useEffect(()=>{\n        // Ensure expanded is always false when no toasts are present / only one left\n        if (toasts.length <= 1) {\n            setExpanded(false);\n        }\n    }, [\n        toasts\n    ]);\n    React.useEffect(()=>{\n        const handleKeyDown = (event)=>{\n            var _listRef_current;\n            const isHotkeyPressed = hotkey.every((key)=>event[key] || event.code === key);\n            if (isHotkeyPressed) {\n                var _listRef_current1;\n                setExpanded(true);\n                (_listRef_current1 = listRef.current) == null ? void 0 : _listRef_current1.focus();\n            }\n            if (event.code === 'Escape' && (document.activeElement === listRef.current || ((_listRef_current = listRef.current) == null ? void 0 : _listRef_current.contains(document.activeElement)))) {\n                setExpanded(false);\n            }\n        };\n        document.addEventListener('keydown', handleKeyDown);\n        return ()=>document.removeEventListener('keydown', handleKeyDown);\n    }, [\n        hotkey\n    ]);\n    React.useEffect(()=>{\n        if (listRef.current) {\n            return ()=>{\n                if (lastFocusedElementRef.current) {\n                    lastFocusedElementRef.current.focus({\n                        preventScroll: true\n                    });\n                    lastFocusedElementRef.current = null;\n                    isFocusWithinRef.current = false;\n                }\n            };\n        }\n    }, [\n        listRef.current\n    ]);\n    return(// Remove item from normal navigation flow, only available via hotkey\n    /*#__PURE__*/ React.createElement(\"section\", {\n        ref: ref,\n        \"aria-label\": `${containerAriaLabel} ${hotkeyLabel}`,\n        tabIndex: -1,\n        \"aria-live\": \"polite\",\n        \"aria-relevant\": \"additions text\",\n        \"aria-atomic\": \"false\",\n        suppressHydrationWarning: true\n    }, possiblePositions.map((position, index)=>{\n        var _heights_;\n        const [y, x] = position.split('-');\n        if (!toasts.length) return null;\n        return /*#__PURE__*/ React.createElement(\"ol\", {\n            key: position,\n            dir: dir === 'auto' ? getDocumentDirection() : dir,\n            tabIndex: -1,\n            ref: listRef,\n            className: className,\n            \"data-sonner-toaster\": true,\n            \"data-sonner-theme\": actualTheme,\n            \"data-y-position\": y,\n            \"data-x-position\": x,\n            style: {\n                '--front-toast-height': `${((_heights_ = heights[0]) == null ? void 0 : _heights_.height) || 0}px`,\n                '--width': `${TOAST_WIDTH}px`,\n                '--gap': `${gap}px`,\n                ...style,\n                ...assignOffset(offset, mobileOffset)\n            },\n            onBlur: (event)=>{\n                if (isFocusWithinRef.current && !event.currentTarget.contains(event.relatedTarget)) {\n                    isFocusWithinRef.current = false;\n                    if (lastFocusedElementRef.current) {\n                        lastFocusedElementRef.current.focus({\n                            preventScroll: true\n                        });\n                        lastFocusedElementRef.current = null;\n                    }\n                }\n            },\n            onFocus: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                if (!isFocusWithinRef.current) {\n                    isFocusWithinRef.current = true;\n                    lastFocusedElementRef.current = event.relatedTarget;\n                }\n            },\n            onMouseEnter: ()=>setExpanded(true),\n            onMouseMove: ()=>setExpanded(true),\n            onMouseLeave: ()=>{\n                // Avoid setting expanded to false when interacting with a toast, e.g. swiping\n                if (!interacting) {\n                    setExpanded(false);\n                }\n            },\n            onDragEnd: ()=>setExpanded(false),\n            onPointerDown: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                setInteracting(true);\n            },\n            onPointerUp: ()=>setInteracting(false)\n        }, toasts.filter((toast)=>!toast.position && index === 0 || toast.position === position).map((toast, index)=>{\n            var _toastOptions_duration, _toastOptions_closeButton;\n            return /*#__PURE__*/ React.createElement(Toast, {\n                key: toast.id,\n                icons: icons,\n                index: index,\n                toast: toast,\n                defaultRichColors: richColors,\n                duration: (_toastOptions_duration = toastOptions == null ? void 0 : toastOptions.duration) != null ? _toastOptions_duration : duration,\n                className: toastOptions == null ? void 0 : toastOptions.className,\n                descriptionClassName: toastOptions == null ? void 0 : toastOptions.descriptionClassName,\n                invert: invert,\n                visibleToasts: visibleToasts,\n                closeButton: (_toastOptions_closeButton = toastOptions == null ? void 0 : toastOptions.closeButton) != null ? _toastOptions_closeButton : closeButton,\n                interacting: interacting,\n                position: position,\n                style: toastOptions == null ? void 0 : toastOptions.style,\n                unstyled: toastOptions == null ? void 0 : toastOptions.unstyled,\n                classNames: toastOptions == null ? void 0 : toastOptions.classNames,\n                cancelButtonStyle: toastOptions == null ? void 0 : toastOptions.cancelButtonStyle,\n                actionButtonStyle: toastOptions == null ? void 0 : toastOptions.actionButtonStyle,\n                closeButtonAriaLabel: toastOptions == null ? void 0 : toastOptions.closeButtonAriaLabel,\n                removeToast: removeToast,\n                toasts: toasts.filter((t)=>t.position == toast.position),\n                heights: heights.filter((h)=>h.position == toast.position),\n                setHeights: setHeights,\n                expandByDefault: expand,\n                gap: gap,\n                expanded: expanded,\n                swipeDirections: props.swipeDirections\n            });\n        }));\n    })));\n});\n\nexport { Toaster, toast, useSonner };\n"], "names": [], "mappings": ";;;;;AAUA;AACA;AAXA;AACA,SAAS,YAAY,IAAI;IACvB,IAAI,CAAC,QAAQ,OAAO,YAAY,aAAa;IAC7C,IAAI,OAAO,SAAS,IAAI,IAAI,SAAS,oBAAoB,CAAC,OAAO,CAAC,EAAE;IACpE,IAAI,QAAQ,SAAS,aAAa,CAAC;IACnC,MAAM,IAAI,GAAG;IACb,KAAK,WAAW,CAAC;IAChB,MAAM,UAAU,GAAI,MAAM,UAAU,CAAC,OAAO,GAAG,OAAQ,MAAM,WAAW,CAAC,SAAS,cAAc,CAAC;AACpG;;;AAKA,MAAM,WAAW,CAAC;IACd,OAAO;QACH,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,OAAO;IACf;AACJ;AACA,MAAM,OAAO,MAAM,IAAI,IAAI,CAAC;AAC5B,MAAM,SAAS,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE;IAClC,OAAO,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAC5C,WAAW;YACP;YACA;SACH,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;QACvB,gBAAgB;IACpB,GAAG,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACxC,WAAW;IACf,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;YACrD,WAAW;YACX,KAAK,CAAC,YAAY,EAAE,GAAG;QAC3B;AACR;AACA,MAAM,cAAc,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;IACzD,OAAO;IACP,SAAS;IACT,MAAM;IACN,QAAQ;IACR,OAAO;AACX,GAAG,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;IACzC,UAAU;IACV,GAAG;IACH,UAAU;AACd;AACA,MAAM,cAAc,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;IACzD,OAAO;IACP,SAAS;IACT,MAAM;IACN,QAAQ;IACR,OAAO;AACX,GAAG,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;IACzC,UAAU;IACV,GAAG;IACH,UAAU;AACd;AACA,MAAM,WAAW,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;IACtD,OAAO;IACP,SAAS;IACT,MAAM;IACN,QAAQ;IACR,OAAO;AACX,GAAG,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;IACzC,UAAU;IACV,GAAG;IACH,UAAU;AACd;AACA,MAAM,YAAY,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;IACvD,OAAO;IACP,SAAS;IACT,MAAM;IACN,QAAQ;IACR,OAAO;AACX,GAAG,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;IACzC,UAAU;IACV,GAAG;IACH,UAAU;AACd;AACA,MAAM,YAAY,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;IACvD,OAAO;IACP,OAAO;IACP,QAAQ;IACR,SAAS;IACT,MAAM;IACN,QAAQ;IACR,aAAa;IACb,eAAe;IACf,gBAAgB;AACpB,GAAG,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;IACzC,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACR,IAAI,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;IAC1C,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACR;AAEA,MAAM,sBAAsB;IACxB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,8YAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,SAAS,MAAM;IAC9E,8YAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,MAAM,WAAW;YACb,oBAAoB,SAAS,MAAM;QACvC;QACA,SAAS,gBAAgB,CAAC,oBAAoB;QAC9C,OAAO,IAAI,OAAO,mBAAmB,CAAC,oBAAoB;IAC9D,GAAG,EAAE;IACL,OAAO;AACX;AAEA,IAAI,gBAAgB;AACpB,MAAM;IACF,aAAa;QACT,kEAAkE;QAClE,IAAI,CAAC,SAAS,GAAG,CAAC;YACd,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YACtB,OAAO;gBACH,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;gBACvC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO;YACnC;QACJ;QACA,IAAI,CAAC,OAAO,GAAG,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,aAAa,WAAW;QACtD;QACA,IAAI,CAAC,QAAQ,GAAG,CAAC;YACb,IAAI,CAAC,OAAO,CAAC;YACb,IAAI,CAAC,MAAM,GAAG;mBACP,IAAI,CAAC,MAAM;gBACd;aACH;QACL;QACA,IAAI,CAAC,MAAM,GAAG,CAAC;YACX,IAAI;YACJ,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,GAAG;YAC7B,MAAM,KAAK,OAAO,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE,MAAM,YAAY,CAAC,CAAC,WAAW,KAAK,EAAE,KAAK,OAAO,KAAK,IAAI,SAAS,MAAM,IAAI,IAAI,KAAK,EAAE,GAAG;YAC9I,MAAM,gBAAgB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACpC,OAAO,MAAM,EAAE,KAAK;YACxB;YACA,MAAM,cAAc,KAAK,WAAW,KAAK,YAAY,OAAO,KAAK,WAAW;YAC5E,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK;gBAC9B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;YAChC;YACA,IAAI,eAAe;gBACf,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;oBAC3B,IAAI,MAAM,EAAE,KAAK,IAAI;wBACjB,IAAI,CAAC,OAAO,CAAC;4BACT,GAAG,KAAK;4BACR,GAAG,IAAI;4BACP;4BACA,OAAO;wBACX;wBACA,OAAO;4BACH,GAAG,KAAK;4BACR,GAAG,IAAI;4BACP;4BACA;4BACA,OAAO;wBACX;oBACJ;oBACA,OAAO;gBACX;YACJ,OAAO;gBACH,IAAI,CAAC,QAAQ,CAAC;oBACV,OAAO;oBACP,GAAG,IAAI;oBACP;oBACA;gBACJ;YACJ;YACA,OAAO;QACX;QACA,IAAI,CAAC,OAAO,GAAG,CAAC;YACZ,IAAI,IAAI;gBACJ,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;gBACzB,sBAAsB,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,aAAa,WAAW;4BAChE;4BACA,SAAS;wBACb;YACZ,OAAO;gBACH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBACjB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,aAAa,WAAW;4BAC1C,IAAI,MAAM,EAAE;4BACZ,SAAS;wBACb;gBACR;YACJ;YACA,OAAO;QACX;QACA,IAAI,CAAC,OAAO,GAAG,CAAC,SAAS;YACrB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACf,GAAG,IAAI;gBACP;YACJ;QACJ;QACA,IAAI,CAAC,KAAK,GAAG,CAAC,SAAS;YACnB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACf,GAAG,IAAI;gBACP;gBACA,MAAM;YACV;QACJ;QACA,IAAI,CAAC,OAAO,GAAG,CAAC,SAAS;YACrB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACf,GAAG,IAAI;gBACP,MAAM;gBACN;YACJ;QACJ;QACA,IAAI,CAAC,IAAI,GAAG,CAAC,SAAS;YAClB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACf,GAAG,IAAI;gBACP,MAAM;gBACN;YACJ;QACJ;QACA,IAAI,CAAC,OAAO,GAAG,CAAC,SAAS;YACrB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACf,GAAG,IAAI;gBACP,MAAM;gBACN;YACJ;QACJ;QACA,IAAI,CAAC,OAAO,GAAG,CAAC,SAAS;YACrB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACf,GAAG,IAAI;gBACP,MAAM;gBACN;YACJ;QACJ;QACA,IAAI,CAAC,OAAO,GAAG,CAAC,SAAS;YACrB,IAAI,CAAC,MAAM;gBACP,kBAAkB;gBAClB;YACJ;YACA,IAAI,KAAK;YACT,IAAI,KAAK,OAAO,KAAK,WAAW;gBAC5B,KAAK,IAAI,CAAC,MAAM,CAAC;oBACb,GAAG,IAAI;oBACP;oBACA,MAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,aAAa,OAAO,KAAK,WAAW,KAAK,aAAa,KAAK,WAAW,GAAG;gBAC7E;YACJ;YACA,MAAM,IAAI,QAAQ,OAAO,CAAC,mBAAmB,WAAW,YAAY;YACpE,IAAI,gBAAgB,OAAO;YAC3B,IAAI;YACJ,MAAM,kBAAkB,EAAE,IAAI,CAAC,OAAO;gBAClC,SAAS;oBACL;oBACA;iBACH;gBACD,MAAM,yBAAyB,8YAAA,CAAA,UAAK,CAAC,cAAc,CAAC;gBACpD,IAAI,wBAAwB;oBACxB,gBAAgB;oBAChB,IAAI,CAAC,MAAM,CAAC;wBACR;wBACA,MAAM;wBACN,SAAS;oBACb;gBACJ,OAAO,IAAI,eAAe,aAAa,CAAC,SAAS,EAAE,EAAE;oBACjD,gBAAgB;oBAChB,MAAM,cAAc,OAAO,KAAK,KAAK,KAAK,aAAa,MAAM,KAAK,KAAK,CAAC,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE,IAAI,KAAK,KAAK;oBAC9H,MAAM,cAAc,OAAO,KAAK,WAAW,KAAK,aAAa,MAAM,KAAK,WAAW,CAAC,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE,IAAI,KAAK,WAAW;oBAChJ,MAAM,mBAAmB,OAAO,gBAAgB,YAAY,CAAC,8YAAA,CAAA,UAAK,CAAC,cAAc,CAAC;oBAClF,MAAM,gBAAgB,mBAAmB,cAAc;wBACnD,SAAS;oBACb;oBACA,IAAI,CAAC,MAAM,CAAC;wBACR;wBACA,MAAM;wBACN;wBACA,GAAG,aAAa;oBACpB;gBACJ,OAAO,IAAI,oBAAoB,OAAO;oBAClC,gBAAgB;oBAChB,MAAM,cAAc,OAAO,KAAK,KAAK,KAAK,aAAa,MAAM,KAAK,KAAK,CAAC,YAAY,KAAK,KAAK;oBAC9F,MAAM,cAAc,OAAO,KAAK,WAAW,KAAK,aAAa,MAAM,KAAK,WAAW,CAAC,YAAY,KAAK,WAAW;oBAChH,MAAM,mBAAmB,OAAO,gBAAgB,YAAY,CAAC,8YAAA,CAAA,UAAK,CAAC,cAAc,CAAC;oBAClF,MAAM,gBAAgB,mBAAmB,cAAc;wBACnD,SAAS;oBACb;oBACA,IAAI,CAAC,MAAM,CAAC;wBACR;wBACA,MAAM;wBACN;wBACA,GAAG,aAAa;oBACpB;gBACJ,OAAO,IAAI,KAAK,OAAO,KAAK,WAAW;oBACnC,gBAAgB;oBAChB,MAAM,cAAc,OAAO,KAAK,OAAO,KAAK,aAAa,MAAM,KAAK,OAAO,CAAC,YAAY,KAAK,OAAO;oBACpG,MAAM,cAAc,OAAO,KAAK,WAAW,KAAK,aAAa,MAAM,KAAK,WAAW,CAAC,YAAY,KAAK,WAAW;oBAChH,MAAM,mBAAmB,OAAO,gBAAgB,YAAY,CAAC,8YAAA,CAAA,UAAK,CAAC,cAAc,CAAC;oBAClF,MAAM,gBAAgB,mBAAmB,cAAc;wBACnD,SAAS;oBACb;oBACA,IAAI,CAAC,MAAM,CAAC;wBACR;wBACA,MAAM;wBACN;wBACA,GAAG,aAAa;oBACpB;gBACJ;YACJ,GAAG,KAAK,CAAC,OAAO;gBACZ,SAAS;oBACL;oBACA;iBACH;gBACD,IAAI,KAAK,KAAK,KAAK,WAAW;oBAC1B,gBAAgB;oBAChB,MAAM,cAAc,OAAO,KAAK,KAAK,KAAK,aAAa,MAAM,KAAK,KAAK,CAAC,SAAS,KAAK,KAAK;oBAC3F,MAAM,cAAc,OAAO,KAAK,WAAW,KAAK,aAAa,MAAM,KAAK,WAAW,CAAC,SAAS,KAAK,WAAW;oBAC7G,MAAM,mBAAmB,OAAO,gBAAgB,YAAY,CAAC,8YAAA,CAAA,UAAK,CAAC,cAAc,CAAC;oBAClF,MAAM,gBAAgB,mBAAmB,cAAc;wBACnD,SAAS;oBACb;oBACA,IAAI,CAAC,MAAM,CAAC;wBACR;wBACA,MAAM;wBACN;wBACA,GAAG,aAAa;oBACpB;gBACJ;YACJ,GAAG,OAAO,CAAC;gBACP,IAAI,eAAe;oBACf,uEAAuE;oBACvE,IAAI,CAAC,OAAO,CAAC;oBACb,KAAK;gBACT;gBACA,KAAK,OAAO,IAAI,OAAO,KAAK,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC;YACtD;YACA,MAAM,SAAS,IAAI,IAAI,QAAQ,CAAC,SAAS,SAAS,gBAAgB,IAAI,CAAC,IAAI,MAAM,CAAC,EAAE,KAAK,WAAW,OAAO,MAAM,CAAC,EAAE,IAAI,QAAQ,MAAM,CAAC,EAAE,GAAG,KAAK,CAAC;YAClJ,IAAI,OAAO,OAAO,YAAY,OAAO,OAAO,UAAU;gBAClD,oCAAoC;gBACpC,OAAO;oBACH;gBACJ;YACJ,OAAO;gBACH,OAAO,OAAO,MAAM,CAAC,IAAI;oBACrB;gBACJ;YACJ;QACJ;QACA,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK;YAChB,MAAM,KAAK,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE,KAAK;YAChD,IAAI,CAAC,MAAM,CAAC;gBACR,KAAK,IAAI;gBACT;gBACA,GAAG,IAAI;YACX;YACA,OAAO;QACX;QACA,IAAI,CAAC,eAAe,GAAG;YACnB,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE;QACzE;QACA,IAAI,CAAC,WAAW,GAAG,EAAE;QACrB,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,eAAe,GAAG,IAAI;IAC/B;AACJ;AACA,MAAM,aAAa,IAAI;AACvB,kCAAkC;AAClC,MAAM,gBAAgB,CAAC,SAAS;IAC5B,MAAM,KAAK,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE,KAAK;IAChD,WAAW,QAAQ,CAAC;QAChB,OAAO;QACP,GAAG,IAAI;QACP;IACJ;IACA,OAAO;AACX;AACA,MAAM,iBAAiB,CAAC;IACpB,OAAO,QAAQ,OAAO,SAAS,YAAY,QAAQ,QAAQ,OAAO,KAAK,EAAE,KAAK,aAAa,YAAY,QAAQ,OAAO,KAAK,MAAM,KAAK;AAC1I;AACA,MAAM,aAAa;AACnB,MAAM,aAAa,IAAI,WAAW,MAAM;AACxC,MAAM,YAAY,IAAI,WAAW,eAAe;AAChD,uFAAuF;AACvF,MAAM,QAAQ,OAAO,MAAM,CAAC,YAAY;IACpC,SAAS,WAAW,OAAO;IAC3B,MAAM,WAAW,IAAI;IACrB,SAAS,WAAW,OAAO;IAC3B,OAAO,WAAW,KAAK;IACvB,QAAQ,WAAW,MAAM;IACzB,SAAS,WAAW,OAAO;IAC3B,SAAS,WAAW,OAAO;IAC3B,SAAS,WAAW,OAAO;IAC3B,SAAS,WAAW,OAAO;AAC/B,GAAG;IACC;IACA;AACJ;AAEA,YAAY;AAEZ,SAAS,SAAS,MAAM;IACpB,OAAO,OAAO,KAAK,KAAK;AAC5B;AAEA,wBAAwB;AACxB,MAAM,wBAAwB;AAC9B,mBAAmB;AACnB,MAAM,kBAAkB;AACxB,0BAA0B;AAC1B,MAAM,yBAAyB;AAC/B,uCAAuC;AACvC,MAAM,iBAAiB;AACvB,sBAAsB;AACtB,MAAM,cAAc;AACpB,6BAA6B;AAC7B,MAAM,MAAM;AACZ,+BAA+B;AAC/B,MAAM,kBAAkB;AACxB,mCAAmC;AACnC,MAAM,sBAAsB;AAC5B,SAAS,GAAG,GAAG,OAAO;IAClB,OAAO,QAAQ,MAAM,CAAC,SAAS,IAAI,CAAC;AACxC;AACA,SAAS,0BAA0B,QAAQ;IACvC,MAAM,CAAC,GAAG,EAAE,GAAG,SAAS,KAAK,CAAC;IAC9B,MAAM,aAAa,EAAE;IACrB,IAAI,GAAG;QACH,WAAW,IAAI,CAAC;IACpB;IACA,IAAI,GAAG;QACH,WAAW,IAAI,CAAC;IACpB;IACA,OAAO;AACX;AACA,MAAM,QAAQ,CAAC;IACX,IAAI,mBAAmB,oBAAoB,oBAAoB,oBAAoB,oBAAoB,oBAAoB,oBAAoB,oBAAoB;IACnK,MAAM,EAAE,QAAQ,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,iBAAiB,EAAE,aAAa,sBAAsB,EAAE,KAAK,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,YAAY,EAAE,EAAE,uBAAuB,EAAE,EAAE,UAAU,mBAAmB,EAAE,QAAQ,EAAE,GAAG,EAAE,eAAe,EAAE,UAAU,EAAE,KAAK,EAAE,uBAAuB,aAAa,EAAE,GAAG;IAClZ,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,8YAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,8YAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,8YAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,8YAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,8YAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,8YAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACzD,MAAM,gBAAgB,8YAAA,CAAA,UAAK,CAAC,MAAM,CAAC,MAAM,QAAQ,IAAI,uBAAuB;IAC5E,MAAM,gBAAgB,8YAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACnC,MAAM,WAAW,8YAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC9B,MAAM,UAAU,UAAU;IAC1B,MAAM,YAAY,QAAQ,KAAK;IAC/B,MAAM,YAAY,MAAM,IAAI;IAC5B,MAAM,cAAc,MAAM,WAAW,KAAK;IAC1C,MAAM,iBAAiB,MAAM,SAAS,IAAI;IAC1C,MAAM,4BAA4B,MAAM,oBAAoB,IAAI;IAChE,8IAA8I;IAC9I,MAAM,cAAc,8YAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAI,QAAQ,SAAS,CAAC,CAAC,SAAS,OAAO,OAAO,KAAK,MAAM,EAAE,KAAK,GAAG;QACjG;QACA,MAAM,EAAE;KACX;IACD,MAAM,cAAc,8YAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAC9B,IAAI;QACJ,OAAO,CAAC,qBAAqB,MAAM,WAAW,KAAK,OAAO,qBAAqB;IACnF,GAAG;QACC,MAAM,WAAW;QACjB;KACH;IACD,MAAM,WAAW,8YAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAI,MAAM,QAAQ,IAAI,uBAAuB,gBAAgB;QACxF,MAAM,QAAQ;QACd;KACH;IACD,MAAM,yBAAyB,8YAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC5C,MAAM,SAAS,8YAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC5B,MAAM,6BAA6B,8YAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAChD,MAAM,kBAAkB,8YAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACrC,MAAM,CAAC,GAAG,EAAE,GAAG,SAAS,KAAK,CAAC;IAC9B,MAAM,qBAAqB,8YAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QACrC,OAAO,QAAQ,MAAM,CAAC,CAAC,MAAM,MAAM;YAC/B,0CAA0C;YAC1C,IAAI,gBAAgB,aAAa;gBAC7B,OAAO;YACX;YACA,OAAO,OAAO,KAAK,MAAM;QAC7B,GAAG;IACP,GAAG;QACC;QACA;KACH;IACD,MAAM,mBAAmB;IACzB,MAAM,SAAS,MAAM,MAAM,IAAI;IAC/B,MAAM,WAAW,cAAc;IAC/B,OAAO,OAAO,GAAG,8YAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAI,cAAc,MAAM,oBAAoB;QACvE;QACA;KACH;IACD,8YAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,cAAc,OAAO,GAAG;IAC5B,GAAG;QACC;KACH;IACD,8YAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,sDAAsD;QACtD,WAAW;IACf,GAAG,EAAE;IACL,8YAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,MAAM,YAAY,SAAS,OAAO;QAClC,IAAI,WAAW;YACX,MAAM,SAAS,UAAU,qBAAqB,GAAG,MAAM;YACvD,+DAA+D;YAC/D,iBAAiB;YACjB,WAAW,CAAC,IAAI;oBACR;wBACI,SAAS,MAAM,EAAE;wBACjB;wBACA,UAAU,MAAM,QAAQ;oBAC5B;uBACG;iBACN;YACL,OAAO,IAAI,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,SAAS,OAAO,OAAO,KAAK,MAAM,EAAE;QAC7E;IACJ,GAAG;QACC;QACA,MAAM,EAAE;KACX;IACD,8YAAA,CAAA,UAAK,CAAC,eAAe,CAAC;QAClB,6DAA6D;QAC7D,IAAI,CAAC,SAAS;QACd,MAAM,YAAY,SAAS,OAAO;QAClC,MAAM,iBAAiB,UAAU,KAAK,CAAC,MAAM;QAC7C,UAAU,KAAK,CAAC,MAAM,GAAG;QACzB,MAAM,YAAY,UAAU,qBAAqB,GAAG,MAAM;QAC1D,UAAU,KAAK,CAAC,MAAM,GAAG;QACzB,iBAAiB;QACjB,WAAW,CAAC;YACR,MAAM,gBAAgB,QAAQ,IAAI,CAAC,CAAC,SAAS,OAAO,OAAO,KAAK,MAAM,EAAE;YACxE,IAAI,CAAC,eAAe;gBAChB,OAAO;oBACH;wBACI,SAAS,MAAM,EAAE;wBACjB,QAAQ;wBACR,UAAU,MAAM,QAAQ;oBAC5B;uBACG;iBACN;YACL,OAAO;gBACH,OAAO,QAAQ,GAAG,CAAC,CAAC,SAAS,OAAO,OAAO,KAAK,MAAM,EAAE,GAAG;wBACnD,GAAG,MAAM;wBACT,QAAQ;oBACZ,IAAI;YACZ;QACJ;IACJ,GAAG;QACC;QACA,MAAM,KAAK;QACX,MAAM,WAAW;QACjB;QACA,MAAM,EAAE;QACR,MAAM,GAAG;QACT,MAAM,MAAM;QACZ,MAAM,MAAM;KACf;IACD,MAAM,cAAc,8YAAA,CAAA,UAAK,CAAC,WAAW,CAAC;QAClC,+CAA+C;QAC/C,WAAW;QACX,sBAAsB,OAAO,OAAO;QACpC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,SAAS,OAAO,OAAO,KAAK,MAAM,EAAE;QAC9D,WAAW;YACP,YAAY;QAChB,GAAG;IACP,GAAG;QACC;QACA;QACA;QACA;KACH;IACD,8YAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,IAAI,MAAM,OAAO,IAAI,cAAc,aAAa,MAAM,QAAQ,KAAK,YAAY,MAAM,IAAI,KAAK,WAAW;QACzG,IAAI;QACJ,gCAAgC;QAChC,MAAM,aAAa;YACf,IAAI,2BAA2B,OAAO,GAAG,uBAAuB,OAAO,EAAE;gBACrE,+CAA+C;gBAC/C,MAAM,cAAc,IAAI,OAAO,OAAO,KAAK,uBAAuB,OAAO;gBACzE,cAAc,OAAO,GAAG,cAAc,OAAO,GAAG;YACpD;YACA,2BAA2B,OAAO,GAAG,IAAI,OAAO,OAAO;QAC3D;QACA,MAAM,aAAa;YACf,uDAAuD;YACvD,wGAAwG;YACxG,mFAAmF;YACnF,IAAI,cAAc,OAAO,KAAK,UAAU;YACxC,uBAAuB,OAAO,GAAG,IAAI,OAAO,OAAO;YACnD,oCAAoC;YACpC,YAAY,WAAW;gBACnB,MAAM,WAAW,IAAI,OAAO,KAAK,IAAI,MAAM,WAAW,CAAC,IAAI,CAAC,OAAO;gBACnE;YACJ,GAAG,cAAc,OAAO;QAC5B;QACA,IAAI,YAAY,eAAe,kBAAkB;YAC7C;QACJ,OAAO;YACH;QACJ;QACA,OAAO,IAAI,aAAa;IAC5B,GAAG;QACC;QACA;QACA;QACA;QACA;QACA;KACH;IACD,8YAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,IAAI,MAAM,MAAM,EAAE;YACd;YACA,MAAM,SAAS,IAAI,OAAO,KAAK,IAAI,MAAM,SAAS,CAAC,IAAI,CAAC,OAAO;QACnE;IACJ,GAAG;QACC;QACA,MAAM,MAAM;KACf;IACD,SAAS;QACL,IAAI;QACJ,IAAI,SAAS,OAAO,KAAK,IAAI,MAAM,OAAO,EAAE;YACxC,IAAI;YACJ,OAAO,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;gBAC5C,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,MAAM,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,MAAM,EAAE;gBAC9K,gBAAgB,cAAc;YAClC,GAAG,MAAM,OAAO;QACpB;QACA,OAAO,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;YAC7C,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,MAAM,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,oBAAoB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,kBAAkB,MAAM;YAC1K,SAAS,cAAc;QAC3B;IACJ;IACA,MAAM,OAAO,MAAM,IAAI,IAAI,CAAC,SAAS,OAAO,KAAK,IAAI,KAAK,CAAC,UAAU,KAAK,SAAS;IACnF,IAAI,mBAAmB;IACvB,OAAO,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,MAAM;QAC3C,UAAU;QACV,KAAK;QACL,WAAW,GAAG,WAAW,gBAAgB,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,oBAAoB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,kBAAkB,KAAK,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO,EAAE,cAAc,OAAO,KAAK,IAAI,UAAU,CAAC,UAAU,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,kBAAkB,CAAC,UAAU;QAC7Z,qBAAqB;QACrB,oBAAoB,CAAC,oBAAoB,MAAM,UAAU,KAAK,OAAO,oBAAoB;QACzF,eAAe,CAAC,QAAQ,MAAM,GAAG,IAAI,MAAM,QAAQ,IAAI;QACvD,gBAAgB;QAChB,gBAAgB,QAAQ,MAAM,OAAO;QACrC,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,mBAAmB;QACnB,mBAAmB;QACnB,cAAc;QACd,cAAc;QACd,gBAAgB;QAChB,oBAAoB;QACpB,aAAa;QACb,eAAe;QACf,kBAAkB;QAClB,wBAAwB;QACxB,iBAAiB,QAAQ,YAAY,mBAAmB;QACxD,OAAO;YACH,WAAW;YACX,mBAAmB;YACnB,aAAa,OAAO,MAAM,GAAG;YAC7B,YAAY,GAAG,UAAU,qBAAqB,OAAO,OAAO,CAAC,EAAE,CAAC;YAChE,oBAAoB,kBAAkB,SAAS,GAAG,cAAc,EAAE,CAAC;YACnE,GAAG,KAAK;YACR,GAAG,MAAM,KAAK;QAClB;QACA,WAAW;YACP,WAAW;YACX,kBAAkB;YAClB,gBAAgB,OAAO,GAAG;QAC9B;QACA,eAAe,CAAC;YACZ,IAAI,YAAY,CAAC,aAAa;YAC9B,cAAc,OAAO,GAAG,IAAI;YAC5B,sBAAsB,OAAO,OAAO;YACpC,sGAAsG;YACtG,MAAM,MAAM,CAAC,iBAAiB,CAAC,MAAM,SAAS;YAC9C,IAAI,MAAM,MAAM,CAAC,OAAO,KAAK,UAAU;YACvC,WAAW;YACX,gBAAgB,OAAO,GAAG;gBACtB,GAAG,MAAM,OAAO;gBAChB,GAAG,MAAM,OAAO;YACpB;QACJ;QACA,aAAa;YACT,IAAI,mBAAmB,oBAAoB;YAC3C,IAAI,YAAY,CAAC,aAAa;YAC9B,gBAAgB,OAAO,GAAG;YAC1B,MAAM,eAAe,OAAO,CAAC,CAAC,oBAAoB,SAAS,OAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,KAAK,CAAC,gBAAgB,CAAC,oBAAoB,OAAO,CAAC,MAAM,GAAG,KAAK;YAC1K,MAAM,eAAe,OAAO,CAAC,CAAC,qBAAqB,SAAS,OAAO,KAAK,OAAO,KAAK,IAAI,mBAAmB,KAAK,CAAC,gBAAgB,CAAC,oBAAoB,OAAO,CAAC,MAAM,GAAG,KAAK;YAC5K,MAAM,YAAY,IAAI,OAAO,OAAO,KAAK,CAAC,CAAC,yBAAyB,cAAc,OAAO,KAAK,OAAO,KAAK,IAAI,uBAAuB,OAAO,EAAE;YAC9I,MAAM,cAAc,mBAAmB,MAAM,eAAe;YAC5D,MAAM,WAAW,KAAK,GAAG,CAAC,eAAe;YACzC,IAAI,KAAK,GAAG,CAAC,gBAAgB,mBAAmB,WAAW,MAAM;gBAC7D,sBAAsB,OAAO,OAAO;gBACpC,MAAM,SAAS,IAAI,OAAO,KAAK,IAAI,MAAM,SAAS,CAAC,IAAI,CAAC,OAAO;gBAC/D,IAAI,mBAAmB,KAAK;oBACxB,qBAAqB,eAAe,IAAI,UAAU;gBACtD,OAAO;oBACH,qBAAqB,eAAe,IAAI,SAAS;gBACrD;gBACA;gBACA,YAAY;gBACZ;YACJ,OAAO;gBACH,IAAI,oBAAoB;gBACxB,CAAC,qBAAqB,SAAS,OAAO,KAAK,OAAO,KAAK,IAAI,mBAAmB,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,GAAG,CAAC;gBACzH,CAAC,qBAAqB,SAAS,OAAO,KAAK,OAAO,KAAK,IAAI,mBAAmB,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,GAAG,CAAC;YAC7H;YACA,YAAY;YACZ,WAAW;YACX,kBAAkB;QACtB;QACA,eAAe,CAAC;YACZ,IAAI,sBACJ,mBAAmB;YACnB,IAAI,CAAC,gBAAgB,OAAO,IAAI,CAAC,aAAa;YAC9C,MAAM,gBAAgB,CAAC,CAAC,uBAAuB,OAAO,YAAY,EAAE,KAAK,OAAO,KAAK,IAAI,qBAAqB,QAAQ,GAAG,MAAM,IAAI;YACnI,IAAI,eAAe;YACnB,MAAM,SAAS,MAAM,OAAO,GAAG,gBAAgB,OAAO,CAAC,CAAC;YACxD,MAAM,SAAS,MAAM,OAAO,GAAG,gBAAgB,OAAO,CAAC,CAAC;YACxD,IAAI;YACJ,MAAM,kBAAkB,CAAC,yBAAyB,MAAM,eAAe,KAAK,OAAO,yBAAyB,0BAA0B;YACtI,kDAAkD;YAClD,IAAI,CAAC,kBAAkB,CAAC,KAAK,GAAG,CAAC,UAAU,KAAK,KAAK,GAAG,CAAC,UAAU,CAAC,GAAG;gBACnE,kBAAkB,KAAK,GAAG,CAAC,UAAU,KAAK,GAAG,CAAC,UAAU,MAAM;YAClE;YACA,IAAI,cAAc;gBACd,GAAG;gBACH,GAAG;YACP;YACA,MAAM,eAAe,CAAC;gBAClB,MAAM,SAAS,KAAK,GAAG,CAAC,SAAS;gBACjC,OAAO,IAAI,CAAC,MAAM,MAAM;YAC5B;YACA,2CAA2C;YAC3C,IAAI,mBAAmB,KAAK;gBACxB,yBAAyB;gBACzB,IAAI,gBAAgB,QAAQ,CAAC,UAAU,gBAAgB,QAAQ,CAAC,WAAW;oBACvE,IAAI,gBAAgB,QAAQ,CAAC,UAAU,SAAS,KAAK,gBAAgB,QAAQ,CAAC,aAAa,SAAS,GAAG;wBACnG,YAAY,CAAC,GAAG;oBACpB,OAAO;wBACH,2CAA2C;wBAC3C,MAAM,gBAAgB,SAAS,aAAa;wBAC5C,+DAA+D;wBAC/D,YAAY,CAAC,GAAG,KAAK,GAAG,CAAC,iBAAiB,KAAK,GAAG,CAAC,UAAU,gBAAgB;oBACjF;gBACJ;YACJ,OAAO,IAAI,mBAAmB,KAAK;gBAC/B,2BAA2B;gBAC3B,IAAI,gBAAgB,QAAQ,CAAC,WAAW,gBAAgB,QAAQ,CAAC,UAAU;oBACvE,IAAI,gBAAgB,QAAQ,CAAC,WAAW,SAAS,KAAK,gBAAgB,QAAQ,CAAC,YAAY,SAAS,GAAG;wBACnG,YAAY,CAAC,GAAG;oBACpB,OAAO;wBACH,2CAA2C;wBAC3C,MAAM,gBAAgB,SAAS,aAAa;wBAC5C,+DAA+D;wBAC/D,YAAY,CAAC,GAAG,KAAK,GAAG,CAAC,iBAAiB,KAAK,GAAG,CAAC,UAAU,gBAAgB;oBACjF;gBACJ;YACJ;YACA,IAAI,KAAK,GAAG,CAAC,YAAY,CAAC,IAAI,KAAK,KAAK,GAAG,CAAC,YAAY,CAAC,IAAI,GAAG;gBAC5D,YAAY;YAChB;YACA,CAAC,oBAAoB,SAAS,OAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,KAAK,CAAC,WAAW,CAAC,oBAAoB,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC;YACtI,CAAC,qBAAqB,SAAS,OAAO,KAAK,OAAO,KAAK,IAAI,mBAAmB,KAAK,CAAC,WAAW,CAAC,oBAAoB,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC;QAC5I;IACJ,GAAG,eAAe,CAAC,MAAM,GAAG,IAAI,cAAc,YAAY,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAClG,cAAc;QACd,iBAAiB;QACjB,qBAAqB;QACrB,SAAS,YAAY,CAAC,cAAc,KAAK,IAAI;YACzC;YACA,MAAM,SAAS,IAAI,OAAO,KAAK,IAAI,MAAM,SAAS,CAAC,IAAI,CAAC,OAAO;QACnE;QACA,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,WAAW,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,WAAW;IAC1L,GAAG,CAAC,eAAe,SAAS,OAAO,KAAK,IAAI,MAAM,KAAK,KAAK,OAAO,eAAe,aAAa,MAAM,CAAC,aAAa,MAAM,IAAI,IAAI,MAAM,OAAO,KAAK,MAAM,IAAI,KAAK,QAAQ,CAAC,CAAC,SAAS,OAAO,KAAK,IAAI,KAAK,CAAC,UAAU,MAAM,QAAQ,MAAM,IAAI,IAAI,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACtR,aAAa;QACb,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,IAAI;IAC5K,GAAG,MAAM,OAAO,IAAI,MAAM,IAAI,KAAK,aAAa,CAAC,MAAM,IAAI,GAAG,MAAM,IAAI,IAAI,mBAAmB,MAAM,MAAM,IAAI,KAAK,YAAY,OAAO,QAAQ,MAAM,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAC1L,gBAAgB;QAChB,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,OAAO;IAClL,GAAG,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACxC,cAAc;QACd,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,KAAK;IAC9K,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,OAAO,MAAM,KAAK,KAAK,aAAa,MAAM,KAAK,KAAK,MAAM,KAAK,GAAG,MAAM,WAAW,GAAG,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACtJ,oBAAoB;QACpB,WAAW,GAAG,sBAAsB,2BAA2B,cAAc,OAAO,KAAK,IAAI,WAAW,WAAW,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,WAAW;IAC3O,GAAG,OAAO,MAAM,WAAW,KAAK,aAAa,MAAM,WAAW,KAAK,MAAM,WAAW,IAAI,OAAO,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,cAAc,CAAC,MAAM,MAAM,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,IAAI,SAAS,MAAM,MAAM,IAAI,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAClP,eAAe;QACf,eAAe;QACf,OAAO,MAAM,iBAAiB,IAAI;QAClC,SAAS,CAAC;YACN,4CAA4C;YAC5C,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG;YAC7B,IAAI,CAAC,aAAa;YAClB,MAAM,MAAM,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,MAAM,EAAE;YAChF;QACJ;QACA,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,YAAY;IAC5L,GAAG,MAAM,MAAM,CAAC,KAAK,IAAI,MAAM,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,cAAc,CAAC,MAAM,MAAM,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,IAAI,SAAS,MAAM,MAAM,IAAI,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAClL,eAAe;QACf,eAAe;QACf,OAAO,MAAM,iBAAiB,IAAI;QAClC,SAAS,CAAC;YACN,4CAA4C;YAC5C,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG;YAC7B,MAAM,MAAM,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,MAAM,EAAE;YAChF,IAAI,MAAM,gBAAgB,EAAE;YAC5B;QACJ;QACA,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,YAAY;IAC5L,GAAG,MAAM,MAAM,CAAC,KAAK,IAAI;AAC7B;AACA,SAAS;IACL,IAAI,OAAO,WAAW,aAAa,OAAO;IAC1C,IAAI,OAAO,aAAa,aAAa,OAAO,OAAO,oBAAoB;IACvE,MAAM,eAAe,SAAS,eAAe,CAAC,YAAY,CAAC;IAC3D,IAAI,iBAAiB,UAAU,CAAC,cAAc;QAC1C,OAAO,OAAO,gBAAgB,CAAC,SAAS,eAAe,EAAE,SAAS;IACtE;IACA,OAAO;AACX;AACA,SAAS,aAAa,aAAa,EAAE,YAAY;IAC7C,MAAM,SAAS,CAAC;IAChB;QACI;QACA;KACH,CAAC,OAAO,CAAC,CAAC,QAAQ;QACf,MAAM,WAAW,UAAU;QAC3B,MAAM,SAAS,WAAW,oBAAoB;QAC9C,MAAM,eAAe,WAAW,yBAAyB;QACzD,SAAS,UAAU,MAAM;YACrB;gBACI;gBACA;gBACA;gBACA;aACH,CAAC,OAAO,CAAC,CAAC;gBACP,MAAM,CAAC,GAAG,OAAO,CAAC,EAAE,KAAK,CAAC,GAAG,OAAO,WAAW,WAAW,GAAG,OAAO,EAAE,CAAC,GAAG;YAC9E;QACJ;QACA,IAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;YAC1D,UAAU;QACd,OAAO,IAAI,OAAO,WAAW,UAAU;YACnC;gBACI;gBACA;gBACA;gBACA;aACH,CAAC,OAAO,CAAC,CAAC;gBACP,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW;oBAC3B,MAAM,CAAC,GAAG,OAAO,CAAC,EAAE,KAAK,CAAC,GAAG;gBACjC,OAAO;oBACH,MAAM,CAAC,GAAG,OAAO,CAAC,EAAE,KAAK,CAAC,GAAG,OAAO,MAAM,CAAC,IAAI,KAAK,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,IAAI;gBACnG;YACJ;QACJ,OAAO;YACH,UAAU;QACd;IACJ;IACA,OAAO;AACX;AACA,SAAS;IACL,MAAM,CAAC,cAAc,gBAAgB,GAAG,8YAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,EAAE;IACzD,8YAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,OAAO,WAAW,SAAS,CAAC,CAAC;YACzB,IAAI,MAAM,OAAO,EAAE;gBACf,WAAW;oBACP,qZAAA,CAAA,UAAQ,CAAC,SAAS,CAAC;wBACf,gBAAgB,CAAC,SAAS,OAAO,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,KAAK,MAAM,EAAE;oBAClE;gBACJ;gBACA;YACJ;YACA,mCAAmC;YACnC,WAAW;gBACP,qZAAA,CAAA,UAAQ,CAAC,SAAS,CAAC;oBACf,gBAAgB,CAAC;wBACb,MAAM,uBAAuB,OAAO,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,KAAK,MAAM,EAAE;wBACpE,wCAAwC;wBACxC,IAAI,yBAAyB,CAAC,GAAG;4BAC7B,OAAO;mCACA,OAAO,KAAK,CAAC,GAAG;gCACnB;oCACI,GAAG,MAAM,CAAC,qBAAqB;oCAC/B,GAAG,KAAK;gCACZ;mCACG,OAAO,KAAK,CAAC,uBAAuB;6BAC1C;wBACL;wBACA,OAAO;4BACH;+BACG;yBACN;oBACL;gBACJ;YACJ;QACJ;IACJ,GAAG,EAAE;IACL,OAAO;QACH,QAAQ;IACZ;AACJ;AACA,MAAM,UAAU,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,UAAU,CAAC,SAAS,QAAQ,KAAK,EAAE,GAAG;IACtE,MAAM,EAAE,MAAM,EAAE,WAAW,cAAc,EAAE,SAAS;QAChD;QACA;KACH,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,gBAAgB,qBAAqB,EAAE,YAAY,EAAE,MAAM,sBAAsB,EAAE,MAAM,GAAG,EAAE,KAAK,EAAE,qBAAqB,eAAe,EAAE,GAAG;IACrP,MAAM,CAAC,QAAQ,UAAU,GAAG,8YAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,EAAE;IAC7C,MAAM,oBAAoB,8YAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QACpC,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI;YACtB;SACH,CAAC,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,QAAQ,MAAM,QAAQ,EAAE,GAAG,CAAC,CAAC,QAAQ,MAAM,QAAQ;IAC/E,GAAG;QACC;QACA;KACH;IACD,MAAM,CAAC,SAAS,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,8YAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,8YAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,8YAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,UAAU,WAAW,QAAQ,OAAO,WAAW,cAAc,OAAO,UAAU,IAAI,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS,UAAU;IACtN,MAAM,UAAU,8YAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,MAAM,cAAc,OAAO,IAAI,CAAC,KAAK,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,UAAU;IAC3E,MAAM,wBAAwB,8YAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC3C,MAAM,mBAAmB,8YAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACtC,MAAM,cAAc,8YAAA,CAAA,UAAK,CAAC,WAAW,CAAC,CAAC;QACnC,UAAU,CAAC;YACP,IAAI;YACJ,IAAI,CAAC,CAAC,CAAC,eAAe,OAAO,IAAI,CAAC,CAAC,QAAQ,MAAM,EAAE,KAAK,cAAc,EAAE,CAAC,KAAK,OAAO,KAAK,IAAI,aAAa,MAAM,GAAG;gBAChH,WAAW,OAAO,CAAC,cAAc,EAAE;YACvC;YACA,OAAO,OAAO,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,OAAO,cAAc,EAAE;QAC1D;IACJ,GAAG,EAAE;IACL,8YAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,OAAO,WAAW,SAAS,CAAC,CAAC;YACzB,IAAI,MAAM,OAAO,EAAE;gBACf,0CAA0C;gBAC1C,sBAAsB;oBAClB,UAAU,CAAC,SAAS,OAAO,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,KAAK,MAAM,EAAE,GAAG;gCAC5C,GAAG,CAAC;gCACJ,QAAQ;4BACZ,IAAI;gBAChB;gBACA;YACJ;YACA,mCAAmC;YACnC,WAAW;gBACP,qZAAA,CAAA,UAAQ,CAAC,SAAS,CAAC;oBACf,UAAU,CAAC;wBACP,MAAM,uBAAuB,OAAO,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,KAAK,MAAM,EAAE;wBACpE,wCAAwC;wBACxC,IAAI,yBAAyB,CAAC,GAAG;4BAC7B,OAAO;mCACA,OAAO,KAAK,CAAC,GAAG;gCACnB;oCACI,GAAG,MAAM,CAAC,qBAAqB;oCAC/B,GAAG,KAAK;gCACZ;mCACG,OAAO,KAAK,CAAC,uBAAuB;6BAC1C;wBACL;wBACA,OAAO;4BACH;+BACG;yBACN;oBACL;gBACJ;YACJ;QACJ;IACJ,GAAG;QACC;KACH;IACD,8YAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,IAAI,UAAU,UAAU;YACpB,eAAe;YACf;QACJ;QACA,IAAI,UAAU,UAAU;YACpB,sCAAsC;YACtC,IAAI,OAAO,UAAU,IAAI,OAAO,UAAU,CAAC,gCAAgC,OAAO,EAAE;gBAChF,sBAAsB;gBACtB,eAAe;YACnB,OAAO;gBACH,gBAAgB;gBAChB,eAAe;YACnB;QACJ;QACA,IAAI,OAAO,WAAW,aAAa;QACnC,MAAM,iBAAiB,OAAO,UAAU,CAAC;QACzC,IAAI;YACA,mBAAmB;YACnB,eAAe,gBAAgB,CAAC,UAAU,CAAC,EAAE,OAAO,EAAE;gBAClD,IAAI,SAAS;oBACT,eAAe;gBACnB,OAAO;oBACH,eAAe;gBACnB;YACJ;QACJ,EAAE,OAAO,OAAO;YACZ,cAAc;YACd,eAAe,WAAW,CAAC,CAAC,EAAE,OAAO,EAAE;gBACnC,IAAI;oBACA,IAAI,SAAS;wBACT,eAAe;oBACnB,OAAO;wBACH,eAAe;oBACnB;gBACJ,EAAE,OAAO,GAAG;oBACR,QAAQ,KAAK,CAAC;gBAClB;YACJ;QACJ;IACJ,GAAG;QACC;KACH;IACD,8YAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,6EAA6E;QAC7E,IAAI,OAAO,MAAM,IAAI,GAAG;YACpB,YAAY;QAChB;IACJ,GAAG;QACC;KACH;IACD,8YAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,MAAM,gBAAgB,CAAC;YACnB,IAAI;YACJ,MAAM,kBAAkB,OAAO,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,MAAM,IAAI,KAAK;YACzE,IAAI,iBAAiB;gBACjB,IAAI;gBACJ,YAAY;gBACZ,CAAC,oBAAoB,QAAQ,OAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,KAAK;YACpF;YACA,IAAI,MAAM,IAAI,KAAK,YAAY,CAAC,SAAS,aAAa,KAAK,QAAQ,OAAO,IAAI,CAAC,CAAC,mBAAmB,QAAQ,OAAO,KAAK,OAAO,KAAK,IAAI,iBAAiB,QAAQ,CAAC,SAAS,aAAa,CAAC,CAAC,GAAG;gBACxL,YAAY;YAChB;QACJ;QACA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAI,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QACC;KACH;IACD,8YAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,IAAI,QAAQ,OAAO,EAAE;YACjB,OAAO;gBACH,IAAI,sBAAsB,OAAO,EAAE;oBAC/B,sBAAsB,OAAO,CAAC,KAAK,CAAC;wBAChC,eAAe;oBACnB;oBACA,sBAAsB,OAAO,GAAG;oBAChC,iBAAiB,OAAO,GAAG;gBAC/B;YACJ;QACJ;IACJ,GAAG;QACC,QAAQ,OAAO;KAClB;IACD,OACA,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,WAAW;QACzC,KAAK;QACL,cAAc,GAAG,mBAAmB,CAAC,EAAE,aAAa;QACpD,UAAU,CAAC;QACX,aAAa;QACb,iBAAiB;QACjB,eAAe;QACf,0BAA0B;IAC9B,GAAG,kBAAkB,GAAG,CAAC,CAAC,UAAU;QAChC,IAAI;QACJ,MAAM,CAAC,GAAG,EAAE,GAAG,SAAS,KAAK,CAAC;QAC9B,IAAI,CAAC,OAAO,MAAM,EAAE,OAAO;QAC3B,OAAO,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,MAAM;YAC3C,KAAK;YACL,KAAK,QAAQ,SAAS,yBAAyB;YAC/C,UAAU,CAAC;YACX,KAAK;YACL,WAAW;YACX,uBAAuB;YACvB,qBAAqB;YACrB,mBAAmB;YACnB,mBAAmB;YACnB,OAAO;gBACH,wBAAwB,GAAG,CAAC,CAAC,YAAY,OAAO,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,UAAU,MAAM,KAAK,EAAE,EAAE,CAAC;gBAClG,WAAW,GAAG,YAAY,EAAE,CAAC;gBAC7B,SAAS,GAAG,IAAI,EAAE,CAAC;gBACnB,GAAG,KAAK;gBACR,GAAG,aAAa,QAAQ,aAAa;YACzC;YACA,QAAQ,CAAC;gBACL,IAAI,iBAAiB,OAAO,IAAI,CAAC,MAAM,aAAa,CAAC,QAAQ,CAAC,MAAM,aAAa,GAAG;oBAChF,iBAAiB,OAAO,GAAG;oBAC3B,IAAI,sBAAsB,OAAO,EAAE;wBAC/B,sBAAsB,OAAO,CAAC,KAAK,CAAC;4BAChC,eAAe;wBACnB;wBACA,sBAAsB,OAAO,GAAG;oBACpC;gBACJ;YACJ;YACA,SAAS,CAAC;gBACN,MAAM,mBAAmB,MAAM,MAAM,YAAY,eAAe,MAAM,MAAM,CAAC,OAAO,CAAC,WAAW,KAAK;gBACrG,IAAI,kBAAkB;gBACtB,IAAI,CAAC,iBAAiB,OAAO,EAAE;oBAC3B,iBAAiB,OAAO,GAAG;oBAC3B,sBAAsB,OAAO,GAAG,MAAM,aAAa;gBACvD;YACJ;YACA,cAAc,IAAI,YAAY;YAC9B,aAAa,IAAI,YAAY;YAC7B,cAAc;gBACV,8EAA8E;gBAC9E,IAAI,CAAC,aAAa;oBACd,YAAY;gBAChB;YACJ;YACA,WAAW,IAAI,YAAY;YAC3B,eAAe,CAAC;gBACZ,MAAM,mBAAmB,MAAM,MAAM,YAAY,eAAe,MAAM,MAAM,CAAC,OAAO,CAAC,WAAW,KAAK;gBACrG,IAAI,kBAAkB;gBACtB,eAAe;YACnB;YACA,aAAa,IAAI,eAAe;QACpC,GAAG,OAAO,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,QAAQ,IAAI,UAAU,KAAK,MAAM,QAAQ,KAAK,UAAU,GAAG,CAAC,CAAC,OAAO;YACjG,IAAI,wBAAwB;YAC5B,OAAO,WAAW,GAAG,8YAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;gBAC5C,KAAK,MAAM,EAAE;gBACb,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,mBAAmB;gBACnB,UAAU,CAAC,yBAAyB,gBAAgB,OAAO,KAAK,IAAI,aAAa,QAAQ,KAAK,OAAO,yBAAyB;gBAC9H,WAAW,gBAAgB,OAAO,KAAK,IAAI,aAAa,SAAS;gBACjE,sBAAsB,gBAAgB,OAAO,KAAK,IAAI,aAAa,oBAAoB;gBACvF,QAAQ;gBACR,eAAe;gBACf,aAAa,CAAC,4BAA4B,gBAAgB,OAAO,KAAK,IAAI,aAAa,WAAW,KAAK,OAAO,4BAA4B;gBAC1I,aAAa;gBACb,UAAU;gBACV,OAAO,gBAAgB,OAAO,KAAK,IAAI,aAAa,KAAK;gBACzD,UAAU,gBAAgB,OAAO,KAAK,IAAI,aAAa,QAAQ;gBAC/D,YAAY,gBAAgB,OAAO,KAAK,IAAI,aAAa,UAAU;gBACnE,mBAAmB,gBAAgB,OAAO,KAAK,IAAI,aAAa,iBAAiB;gBACjF,mBAAmB,gBAAgB,OAAO,KAAK,IAAI,aAAa,iBAAiB;gBACjF,sBAAsB,gBAAgB,OAAO,KAAK,IAAI,aAAa,oBAAoB;gBACvF,aAAa;gBACb,QAAQ,OAAO,MAAM,CAAC,CAAC,IAAI,EAAE,QAAQ,IAAI,MAAM,QAAQ;gBACvD,SAAS,QAAQ,MAAM,CAAC,CAAC,IAAI,EAAE,QAAQ,IAAI,MAAM,QAAQ;gBACzD,YAAY;gBACZ,iBAAiB;gBACjB,KAAK;gBACL,UAAU;gBACV,iBAAiB,MAAM,eAAe;YAC1C;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4122, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next-themes%400.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next-themes/dist/index.mjs"], "sourcesContent": ["\"use client\";import*as t from\"react\";var M=(e,i,s,u,m,a,l,h)=>{let d=document.documentElement,w=[\"light\",\"dark\"];function p(n){(Array.isArray(e)?e:[e]).forEach(y=>{let k=y===\"class\",S=k&&a?m.map(f=>a[f]||f):m;k?(d.classList.remove(...S),d.classList.add(a&&a[n]?a[n]:n)):d.setAttribute(y,n)}),R(n)}function R(n){h&&w.includes(n)&&(d.style.colorScheme=n)}function c(){return window.matchMedia(\"(prefers-color-scheme: dark)\").matches?\"dark\":\"light\"}if(u)p(u);else try{let n=localStorage.getItem(i)||s,y=l&&n===\"system\"?c():n;p(y)}catch(n){}};var b=[\"light\",\"dark\"],I=\"(prefers-color-scheme: dark)\",O=typeof window==\"undefined\",x=t.createContext(void 0),U={setTheme:e=>{},themes:[]},z=()=>{var e;return(e=t.useContext(x))!=null?e:U},J=e=>t.useContext(x)?t.createElement(t.Fragment,null,e.children):t.createElement(V,{...e}),N=[\"light\",\"dark\"],V=({forcedTheme:e,disableTransitionOnChange:i=!1,enableSystem:s=!0,enableColorScheme:u=!0,storageKey:m=\"theme\",themes:a=N,defaultTheme:l=s?\"system\":\"light\",attribute:h=\"data-theme\",value:d,children:w,nonce:p,scriptProps:R})=>{let[c,n]=t.useState(()=>H(m,l)),[T,y]=t.useState(()=>c===\"system\"?E():c),k=d?Object.values(d):a,S=t.useCallback(o=>{let r=o;if(!r)return;o===\"system\"&&s&&(r=E());let v=d?d[r]:r,C=i?W(p):null,P=document.documentElement,L=g=>{g===\"class\"?(P.classList.remove(...k),v&&P.classList.add(v)):g.startsWith(\"data-\")&&(v?P.setAttribute(g,v):P.removeAttribute(g))};if(Array.isArray(h)?h.forEach(L):L(h),u){let g=b.includes(l)?l:null,D=b.includes(r)?r:g;P.style.colorScheme=D}C==null||C()},[p]),f=t.useCallback(o=>{let r=typeof o==\"function\"?o(c):o;n(r);try{localStorage.setItem(m,r)}catch(v){}},[c]),A=t.useCallback(o=>{let r=E(o);y(r),c===\"system\"&&s&&!e&&S(\"system\")},[c,e]);t.useEffect(()=>{let o=window.matchMedia(I);return o.addListener(A),A(o),()=>o.removeListener(A)},[A]),t.useEffect(()=>{let o=r=>{r.key===m&&(r.newValue?n(r.newValue):f(l))};return window.addEventListener(\"storage\",o),()=>window.removeEventListener(\"storage\",o)},[f]),t.useEffect(()=>{S(e!=null?e:c)},[e,c]);let Q=t.useMemo(()=>({theme:c,setTheme:f,forcedTheme:e,resolvedTheme:c===\"system\"?T:c,themes:s?[...a,\"system\"]:a,systemTheme:s?T:void 0}),[c,f,e,T,s,a]);return t.createElement(x.Provider,{value:Q},t.createElement(_,{forcedTheme:e,storageKey:m,attribute:h,enableSystem:s,enableColorScheme:u,defaultTheme:l,value:d,themes:a,nonce:p,scriptProps:R}),w)},_=t.memo(({forcedTheme:e,storageKey:i,attribute:s,enableSystem:u,enableColorScheme:m,defaultTheme:a,value:l,themes:h,nonce:d,scriptProps:w})=>{let p=JSON.stringify([s,i,a,e,h,l,u,m]).slice(1,-1);return t.createElement(\"script\",{...w,suppressHydrationWarning:!0,nonce:typeof window==\"undefined\"?d:\"\",dangerouslySetInnerHTML:{__html:`(${M.toString()})(${p})`}})}),H=(e,i)=>{if(O)return;let s;try{s=localStorage.getItem(e)||void 0}catch(u){}return s||i},W=e=>{let i=document.createElement(\"style\");return e&&i.setAttribute(\"nonce\",e),i.appendChild(document.createTextNode(\"*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\")),document.head.appendChild(i),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(i)},1)}},E=e=>(e||(e=window.matchMedia(I)),e.matches?\"dark\":\"light\");export{J as ThemeProvider,z as useTheme};\n"], "names": [], "mappings": ";;;;AAAa;AAAb;;AAAqC,IAAI,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;IAAK,IAAI,IAAE,SAAS,eAAe,EAAC,IAAE;QAAC;QAAQ;KAAO;IAAC,SAAS,EAAE,CAAC;QAAE,CAAC,MAAM,OAAO,CAAC,KAAG,IAAE;YAAC;SAAE,EAAE,OAAO,CAAC,CAAA;YAAI,IAAI,IAAE,MAAI,SAAQ,IAAE,KAAG,IAAE,EAAE,GAAG,CAAC,CAAA,IAAG,CAAC,CAAC,EAAE,IAAE,KAAG;YAAE,IAAE,CAAC,EAAE,SAAS,CAAC,MAAM,IAAI,IAAG,EAAE,SAAS,CAAC,GAAG,CAAC,KAAG,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,EAAE,IAAE,EAAE,YAAY,CAAC,GAAE;QAAE,IAAG,EAAE;IAAE;IAAC,SAAS,EAAE,CAAC;QAAE,KAAG,EAAE,QAAQ,CAAC,MAAI,CAAC,EAAE,KAAK,CAAC,WAAW,GAAC,CAAC;IAAC;IAAC,SAAS;QAAI,OAAO,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAC,SAAO;IAAO;IAAC,IAAG,GAAE,EAAE;SAAQ,IAAG;QAAC,IAAI,IAAE,aAAa,OAAO,CAAC,MAAI,GAAE,IAAE,KAAG,MAAI,WAAS,MAAI;QAAE,EAAE;IAAE,EAAC,OAAM,GAAE,CAAC;AAAC;AAAE,IAAI,IAAE;IAAC;IAAQ;CAAO,EAAC,IAAE,gCAA+B,IAAE,OAAO,UAAQ,aAAY,IAAE,CAAA,GAAA,8YAAA,CAAA,gBAAe,AAAD,EAAE,KAAK,IAAG,IAAE;IAAC,UAAS,CAAA,KAAI;IAAE,QAAO,EAAE;AAAA,GAAE,IAAE;IAAK,IAAI;IAAE,OAAM,CAAC,IAAE,CAAA,GAAA,8YAAA,CAAA,aAAY,AAAD,EAAE,EAAE,KAAG,OAAK,IAAE;AAAC,GAAE,IAAE,CAAA,IAAG,CAAA,GAAA,8YAAA,CAAA,aAAY,AAAD,EAAE,KAAG,CAAA,GAAA,8YAAA,CAAA,gBAAe,AAAD,EAAE,8YAAA,CAAA,WAAU,EAAC,MAAK,EAAE,QAAQ,IAAE,CAAA,GAAA,8YAAA,CAAA,gBAAe,AAAD,EAAE,GAAE;QAAC,GAAG,CAAC;IAAA,IAAG,IAAE;IAAC;IAAQ;CAAO,EAAC,IAAE,CAAC,EAAC,aAAY,CAAC,EAAC,2BAA0B,IAAE,CAAC,CAAC,EAAC,cAAa,IAAE,CAAC,CAAC,EAAC,mBAAkB,IAAE,CAAC,CAAC,EAAC,YAAW,IAAE,OAAO,EAAC,QAAO,IAAE,CAAC,EAAC,cAAa,IAAE,IAAE,WAAS,OAAO,EAAC,WAAU,IAAE,YAAY,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC,OAAM,CAAC,EAAC,aAAY,CAAC,EAAC;IAAI,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,8YAAA,CAAA,WAAU,AAAD,EAAE,IAAI,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,8YAAA,CAAA,WAAU,AAAD,EAAE,IAAI,MAAI,WAAS,MAAI,IAAG,IAAE,IAAE,OAAO,MAAM,CAAC,KAAG,GAAE,IAAE,CAAA,GAAA,8YAAA,CAAA,cAAa,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE;QAAE,IAAG,CAAC,GAAE;QAAO,MAAI,YAAU,KAAG,CAAC,IAAE,GAAG;QAAE,IAAI,IAAE,IAAE,CAAC,CAAC,EAAE,GAAC,GAAE,IAAE,IAAE,EAAE,KAAG,MAAK,IAAE,SAAS,eAAe,EAAC,IAAE,CAAA;YAAI,MAAI,UAAQ,CAAC,EAAE,SAAS,CAAC,MAAM,IAAI,IAAG,KAAG,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,IAAE,EAAE,UAAU,CAAC,YAAU,CAAC,IAAE,EAAE,YAAY,CAAC,GAAE,KAAG,EAAE,eAAe,CAAC,EAAE;QAAC;QAAE,IAAG,MAAM,OAAO,CAAC,KAAG,EAAE,OAAO,CAAC,KAAG,EAAE,IAAG,GAAE;YAAC,IAAI,IAAE,EAAE,QAAQ,CAAC,KAAG,IAAE,MAAK,IAAE,EAAE,QAAQ,CAAC,KAAG,IAAE;YAAE,EAAE,KAAK,CAAC,WAAW,GAAC;QAAC;QAAC,KAAG,QAAM;IAAG,GAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,8YAAA,CAAA,cAAa,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE,OAAO,KAAG,aAAW,EAAE,KAAG;QAAE,EAAE;QAAG,IAAG;YAAC,aAAa,OAAO,CAAC,GAAE;QAAE,EAAC,OAAM,GAAE,CAAC;IAAC,GAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,8YAAA,CAAA,cAAa,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE,EAAE;QAAG,EAAE,IAAG,MAAI,YAAU,KAAG,CAAC,KAAG,EAAE;IAAS,GAAE;QAAC;QAAE;KAAE;IAAE,CAAA,GAAA,8YAAA,CAAA,YAAW,AAAD,EAAE;QAAK,IAAI,IAAE,OAAO,UAAU,CAAC;QAAG,OAAO,EAAE,WAAW,CAAC,IAAG,EAAE,IAAG,IAAI,EAAE,cAAc,CAAC;IAAE,GAAE;QAAC;KAAE,GAAE,CAAA,GAAA,8YAAA,CAAA,YAAW,AAAD,EAAE;QAAK,IAAI,IAAE,CAAA;YAAI,EAAE,GAAG,KAAG,KAAG,CAAC,EAAE,QAAQ,GAAC,EAAE,EAAE,QAAQ,IAAE,EAAE,EAAE;QAAC;QAAE,OAAO,OAAO,gBAAgB,CAAC,WAAU,IAAG,IAAI,OAAO,mBAAmB,CAAC,WAAU;IAAE,GAAE;QAAC;KAAE,GAAE,CAAA,GAAA,8YAAA,CAAA,YAAW,AAAD,EAAE;QAAK,EAAE,KAAG,OAAK,IAAE;IAAE,GAAE;QAAC;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,8YAAA,CAAA,UAAS,AAAD,EAAE,IAAI,CAAC;YAAC,OAAM;YAAE,UAAS;YAAE,aAAY;YAAE,eAAc,MAAI,WAAS,IAAE;YAAE,QAAO,IAAE;mBAAI;gBAAE;aAAS,GAAC;YAAE,aAAY,IAAE,IAAE,KAAK;QAAC,CAAC,GAAE;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE;IAAE,OAAO,CAAA,GAAA,8YAAA,CAAA,gBAAe,AAAD,EAAE,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,CAAA,GAAA,8YAAA,CAAA,gBAAe,AAAD,EAAE,GAAE;QAAC,aAAY;QAAE,YAAW;QAAE,WAAU;QAAE,cAAa;QAAE,mBAAkB;QAAE,cAAa;QAAE,OAAM;QAAE,QAAO;QAAE,OAAM;QAAE,aAAY;IAAC,IAAG;AAAE,GAAE,IAAE,CAAA,GAAA,8YAAA,CAAA,OAAM,AAAD,EAAE,CAAC,EAAC,aAAY,CAAC,EAAC,YAAW,CAAC,EAAC,WAAU,CAAC,EAAC,cAAa,CAAC,EAAC,mBAAkB,CAAC,EAAC,cAAa,CAAC,EAAC,OAAM,CAAC,EAAC,QAAO,CAAC,EAAC,OAAM,CAAC,EAAC,aAAY,CAAC,EAAC;IAAI,IAAI,IAAE,KAAK,SAAS,CAAC;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE,EAAE,KAAK,CAAC,GAAE,CAAC;IAAG,OAAO,CAAA,GAAA,8YAAA,CAAA,gBAAe,AAAD,EAAE,UAAS;QAAC,GAAG,CAAC;QAAC,0BAAyB,CAAC;QAAE,OAAM,OAAO,UAAQ,cAAY,IAAE;QAAG,yBAAwB;YAAC,QAAO,CAAC,CAAC,EAAE,EAAE,QAAQ,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;QAAA;IAAC;AAAE,IAAG,IAAE,CAAC,GAAE;IAAK,IAAG,GAAE;IAAO,IAAI;IAAE,IAAG;QAAC,IAAE,aAAa,OAAO,CAAC,MAAI,KAAK;IAAC,EAAC,OAAM,GAAE,CAAC;IAAC,OAAO,KAAG;AAAC,GAAE,IAAE,CAAA;IAAI,IAAI,IAAE,SAAS,aAAa,CAAC;IAAS,OAAO,KAAG,EAAE,YAAY,CAAC,SAAQ,IAAG,EAAE,WAAW,CAAC,SAAS,cAAc,CAAC,iLAAgL,SAAS,IAAI,CAAC,WAAW,CAAC,IAAG;QAAK,OAAO,gBAAgB,CAAC,SAAS,IAAI,GAAE,WAAW;YAAK,SAAS,IAAI,CAAC,WAAW,CAAC;QAAE,GAAE;IAAE;AAAC,GAAE,IAAE,CAAA,IAAG,CAAC,KAAG,CAAC,IAAE,OAAO,UAAU,CAAC,EAAE,GAAE,EAAE,OAAO,GAAC,SAAO,OAAO", "ignoreList": [0], "debugId": null}}]}