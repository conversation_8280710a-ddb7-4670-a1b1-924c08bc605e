{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/dist/compiled/gzip-size/index.js"], "sourcesContent": ["(()=>{var e={154:(e,r,t)=>{var n=t(781);var o=[\"write\",\"end\",\"destroy\"];var i=[\"resume\",\"pause\"];var s=[\"data\",\"close\"];var c=Array.prototype.slice;e.exports=duplex;function forEach(e,r){if(e.forEach){return e.forEach(r)}for(var t=0;t<e.length;t++){r(e[t],t)}}function duplex(e,r){var t=new n;var a=false;forEach(o,proxyWriter);forEach(i,proxyReader);forEach(s,proxyStream);r.on(\"end\",handleEnd);e.on(\"drain\",(function(){t.emit(\"drain\")}));e.on(\"error\",reemit);r.on(\"error\",reemit);t.writable=e.writable;t.readable=r.readable;return t;function proxyWriter(r){t[r]=method;function method(){return e[r].apply(e,arguments)}}function proxyReader(e){t[e]=method;function method(){t.emit(e);var n=r[e];if(n){return n.apply(r,arguments)}r.emit(e)}}function proxyStream(e){r.on(e,reemit);function reemit(){var r=c.call(arguments);r.unshift(e);t.emit.apply(t,r)}}function handleEnd(){if(a){return}a=true;var e=c.call(arguments);e.unshift(\"end\");t.emit.apply(t,e)}function reemit(e){t.emit(\"error\",e)}}},349:(e,r,t)=>{\"use strict\";const n=t(147);const o=t(781);const i=t(796);const s=t(154);const c=t(530);const getOptions=e=>Object.assign({level:9},e);e.exports=(e,r)=>{if(!e){return Promise.resolve(0)}return c(i.gzip)(e,getOptions(r)).then((e=>e.length)).catch((e=>0))};e.exports.sync=(e,r)=>i.gzipSync(e,getOptions(r)).length;e.exports.stream=e=>{const r=new o.PassThrough;const t=new o.PassThrough;const n=s(r,t);let c=0;const a=i.createGzip(getOptions(e)).on(\"data\",(e=>{c+=e.length})).on(\"error\",(()=>{n.gzipSize=0})).on(\"end\",(()=>{n.gzipSize=c;n.emit(\"gzip-size\",c);t.end()}));r.pipe(a);r.pipe(t,{end:false});return n};e.exports.file=(r,t)=>new Promise(((o,i)=>{const s=n.createReadStream(r);s.on(\"error\",i);const c=s.pipe(e.exports.stream(t));c.on(\"error\",i);c.on(\"gzip-size\",o)}));e.exports.fileSync=(r,t)=>e.exports.sync(n.readFileSync(r),t)},530:e=>{\"use strict\";const processFn=(e,r)=>function(...t){const n=r.promiseModule;return new n(((n,o)=>{if(r.multiArgs){t.push(((...e)=>{if(r.errorFirst){if(e[0]){o(e)}else{e.shift();n(e)}}else{n(e)}}))}else if(r.errorFirst){t.push(((e,r)=>{if(e){o(e)}else{n(r)}}))}else{t.push(n)}e.apply(this,t)}))};e.exports=(e,r)=>{r=Object.assign({exclude:[/.+(Sync|Stream)$/],errorFirst:true,promiseModule:Promise},r);const t=typeof e;if(!(e!==null&&(t===\"object\"||t===\"function\"))){throw new TypeError(`Expected \\`input\\` to be a \\`Function\\` or \\`Object\\`, got \\`${e===null?\"null\":t}\\``)}const filter=e=>{const match=r=>typeof r===\"string\"?e===r:r.test(e);return r.include?r.include.some(match):!r.exclude.some(match)};let n;if(t===\"function\"){n=function(...t){return r.excludeMain?e(...t):processFn(e,r).apply(this,t)}}else{n=Object.create(Object.getPrototypeOf(e))}for(const t in e){const o=e[t];n[t]=typeof o===\"function\"&&filter(t)?processFn(o,r):o}return n}},147:e=>{\"use strict\";e.exports=require(\"fs\")},781:e=>{\"use strict\";e.exports=require(\"stream\")},796:e=>{\"use strict\";e.exports=require(\"zlib\")}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var o=r[t]={exports:{}};var i=true;try{e[t](o,o.exports,__nccwpck_require__);i=false}finally{if(i)delete r[t]}return o.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(349);module.exports=t})();"], "names": [], "mappings": "AAAA,CAAC;IAAK,IAAI,IAAE;QAAC,KAAI,CAAC,GAAE,GAAE;YAAK,IAAI,IAAE,EAAE;YAAK,IAAI,IAAE;gBAAC;gBAAQ;gBAAM;aAAU;YAAC,IAAI,IAAE;gBAAC;gBAAS;aAAQ;YAAC,IAAI,IAAE;gBAAC;gBAAO;aAAQ;YAAC,IAAI,IAAE,MAAM,SAAS,CAAC,KAAK;YAAC,EAAE,OAAO,GAAC;YAAO,SAAS,QAAQ,CAAC,EAAC,CAAC;gBAAE,IAAG,EAAE,OAAO,EAAC;oBAAC,OAAO,EAAE,OAAO,CAAC;gBAAE;gBAAC,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;oBAAC,EAAE,CAAC,CAAC,EAAE,EAAC;gBAAE;YAAC;YAAC,SAAS,OAAO,CAAC,EAAC,CAAC;gBAAE,IAAI,IAAE,IAAI;gBAAE,IAAI,IAAE;gBAAM,QAAQ,GAAE;gBAAa,QAAQ,GAAE;gBAAa,QAAQ,GAAE;gBAAa,EAAE,EAAE,CAAC,OAAM;gBAAW,EAAE,EAAE,CAAC,SAAS;oBAAW,EAAE,IAAI,CAAC;gBAAQ;gBAAI,EAAE,EAAE,CAAC,SAAQ;gBAAQ,EAAE,EAAE,CAAC,SAAQ;gBAAQ,EAAE,QAAQ,GAAC,EAAE,QAAQ;gBAAC,EAAE,QAAQ,GAAC,EAAE,QAAQ;gBAAC,OAAO;;gBAAE,SAAS,YAAY,CAAC;oBAAE,CAAC,CAAC,EAAE,GAAC;oBAAO,SAAS;wBAAS,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAE;oBAAU;gBAAC;gBAAC,SAAS,YAAY,CAAC;oBAAE,CAAC,CAAC,EAAE,GAAC;oBAAO,SAAS;wBAAS,EAAE,IAAI,CAAC;wBAAG,IAAI,IAAE,CAAC,CAAC,EAAE;wBAAC,IAAG,GAAE;4BAAC,OAAO,EAAE,KAAK,CAAC,GAAE;wBAAU;wBAAC,EAAE,IAAI,CAAC;oBAAE;gBAAC;gBAAC,SAAS,YAAY,CAAC;oBAAE,EAAE,EAAE,CAAC,GAAE;oBAAQ,SAAS;wBAAS,IAAI,IAAE,EAAE,IAAI,CAAC;wBAAW,EAAE,OAAO,CAAC;wBAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAE;oBAAE;gBAAC;gBAAC,SAAS;oBAAY,IAAG,GAAE;wBAAC;oBAAM;oBAAC,IAAE;oBAAK,IAAI,IAAE,EAAE,IAAI,CAAC;oBAAW,EAAE,OAAO,CAAC;oBAAO,EAAE,IAAI,CAAC,KAAK,CAAC,GAAE;gBAAE;gBAAC,SAAS,OAAO,CAAC;oBAAE,EAAE,IAAI,CAAC,SAAQ;gBAAE;YAAC;QAAC;QAAE,KAAI,CAAC,GAAE,GAAE;YAAK;YAAa,MAAM,IAAE,EAAE;YAAK,MAAM,IAAE,EAAE;YAAK,MAAM,IAAE,EAAE;YAAK,MAAM,IAAE,EAAE;YAAK,MAAM,IAAE,EAAE;YAAK,MAAM,aAAW,CAAA,IAAG,OAAO,MAAM,CAAC;oBAAC,OAAM;gBAAC,GAAE;YAAG,EAAE,OAAO,GAAC,CAAC,GAAE;gBAAK,IAAG,CAAC,GAAE;oBAAC,OAAO,QAAQ,OAAO,CAAC;gBAAE;gBAAC,OAAO,EAAE,EAAE,IAAI,EAAE,GAAE,WAAW,IAAI,IAAI,CAAE,CAAA,IAAG,EAAE,MAAM,EAAG,KAAK,CAAE,CAAA,IAAG;YAAG;YAAE,EAAE,OAAO,CAAC,IAAI,GAAC,CAAC,GAAE,IAAI,EAAE,QAAQ,CAAC,GAAE,WAAW,IAAI,MAAM;YAAC,EAAE,OAAO,CAAC,MAAM,GAAC,CAAA;gBAAI,MAAM,IAAE,IAAI,EAAE,WAAW;gBAAC,MAAM,IAAE,IAAI,EAAE,WAAW;gBAAC,MAAM,IAAE,EAAE,GAAE;gBAAG,IAAI,IAAE;gBAAE,MAAM,IAAE,EAAE,UAAU,CAAC,WAAW,IAAI,EAAE,CAAC,QAAQ,CAAA;oBAAI,KAAG,EAAE,MAAM;gBAAA,GAAI,EAAE,CAAC,SAAS;oBAAK,EAAE,QAAQ,GAAC;gBAAC,GAAI,EAAE,CAAC,OAAO;oBAAK,EAAE,QAAQ,GAAC;oBAAE,EAAE,IAAI,CAAC,aAAY;oBAAG,EAAE,GAAG;gBAAE;gBAAI,EAAE,IAAI,CAAC;gBAAG,EAAE,IAAI,CAAC,GAAE;oBAAC,KAAI;gBAAK;gBAAG,OAAO;YAAC;YAAE,EAAE,OAAO,CAAC,IAAI,GAAC,CAAC,GAAE,IAAI,IAAI,QAAS,CAAC,GAAE;oBAAK,MAAM,IAAE,EAAE,gBAAgB,CAAC;oBAAG,EAAE,EAAE,CAAC,SAAQ;oBAAG,MAAM,IAAE,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC;oBAAI,EAAE,EAAE,CAAC,SAAQ;oBAAG,EAAE,EAAE,CAAC,aAAY;gBAAE;YAAI,EAAE,OAAO,CAAC,QAAQ,GAAC,CAAC,GAAE,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,YAAY,CAAC,IAAG;QAAE;QAAE,KAAI,CAAA;YAAI;YAAa,MAAM,YAAU,CAAC,GAAE,IAAI,SAAS,GAAG,CAAC;oBAAE,MAAM,IAAE,EAAE,aAAa;oBAAC,OAAO,IAAI,EAAG,CAAC,GAAE;wBAAK,IAAG,EAAE,SAAS,EAAC;4BAAC,EAAE,IAAI,CAAE,CAAC,GAAG;gCAAK,IAAG,EAAE,UAAU,EAAC;oCAAC,IAAG,CAAC,CAAC,EAAE,EAAC;wCAAC,EAAE;oCAAE,OAAK;wCAAC,EAAE,KAAK;wCAAG,EAAE;oCAAE;gCAAC,OAAK;oCAAC,EAAE;gCAAE;4BAAC;wBAAG,OAAM,IAAG,EAAE,UAAU,EAAC;4BAAC,EAAE,IAAI,CAAE,CAAC,GAAE;gCAAK,IAAG,GAAE;oCAAC,EAAE;gCAAE,OAAK;oCAAC,EAAE;gCAAE;4BAAC;wBAAG,OAAK;4BAAC,EAAE,IAAI,CAAC;wBAAE;wBAAC,EAAE,KAAK,CAAC,IAAI,EAAC;oBAAE;gBAAG;YAAE,EAAE,OAAO,GAAC,CAAC,GAAE;gBAAK,IAAE,OAAO,MAAM,CAAC;oBAAC,SAAQ;wBAAC;qBAAmB;oBAAC,YAAW;oBAAK,eAAc;gBAAO,GAAE;gBAAG,MAAM,IAAE,OAAO;gBAAE,IAAG,CAAC,CAAC,MAAI,QAAM,CAAC,MAAI,YAAU,MAAI,UAAU,CAAC,GAAE;oBAAC,MAAM,IAAI,UAAU,CAAC,6DAA6D,EAAE,MAAI,OAAK,SAAO,EAAE,EAAE,CAAC;gBAAC;gBAAC,MAAM,SAAO,CAAA;oBAAI,MAAM,QAAM,CAAA,IAAG,OAAO,MAAI,WAAS,MAAI,IAAE,EAAE,IAAI,CAAC;oBAAG,OAAO,EAAE,OAAO,GAAC,EAAE,OAAO,CAAC,IAAI,CAAC,SAAO,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC;gBAAM;gBAAE,IAAI;gBAAE,IAAG,MAAI,YAAW;oBAAC,IAAE,SAAS,GAAG,CAAC;wBAAE,OAAO,EAAE,WAAW,GAAC,KAAK,KAAG,UAAU,GAAE,GAAG,KAAK,CAAC,IAAI,EAAC;oBAAE;gBAAC,OAAK;oBAAC,IAAE,OAAO,MAAM,CAAC,OAAO,cAAc,CAAC;gBAAG;gBAAC,IAAI,MAAM,KAAK,EAAE;oBAAC,MAAM,IAAE,CAAC,CAAC,EAAE;oBAAC,CAAC,CAAC,EAAE,GAAC,OAAO,MAAI,cAAY,OAAO,KAAG,UAAU,GAAE,KAAG;gBAAC;gBAAC,OAAO;YAAC;QAAC;QAAE,KAAI,CAAA;YAAI;YAAa,EAAE,OAAO;QAAc;QAAE,KAAI,CAAA;YAAI;YAAa,EAAE,OAAO;QAAkB;QAAE,KAAI,CAAA;YAAI;YAAa,EAAE,OAAO;QAAgB;IAAC;IAAE,IAAI,IAAE,CAAC;IAAE,SAAS,oBAAoB,CAAC;QAAE,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,IAAG,MAAI,WAAU;YAAC,OAAO,EAAE,OAAO;QAAA;QAAC,IAAI,IAAE,CAAC,CAAC,EAAE,GAAC;YAAC,SAAQ,CAAC;QAAC;QAAE,IAAI,IAAE;QAAK,IAAG;YAAC,CAAC,CAAC,EAAE,CAAC,GAAE,EAAE,OAAO,EAAC;YAAqB,IAAE;QAAK,SAAQ;YAAC,IAAG,GAAE,OAAO,CAAC,CAAC,EAAE;QAAA;QAAC,OAAO,EAAE,OAAO;IAAA;IAAC,IAAG,OAAO,wBAAsB,aAAY,oBAAoB,EAAE,GAAC,YAAU;IAAI,IAAI,IAAE,oBAAoB;IAAK,OAAO,OAAO,GAAC;AAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/bloom-filter.ts"], "sourcesContent": ["// minimal implementation MurmurHash2 hash function\nfunction murmurhash2(str: string) {\n  let h = 0\n  for (let i = 0; i < str.length; i++) {\n    const c = str.charCodeAt(i)\n    h = Math.imul(h ^ c, 0x5bd1e995)\n    h ^= h >>> 13\n    h = Math.imul(h, 0x5bd1e995)\n  }\n  return h >>> 0\n}\n\n// default to 0.01% error rate as the filter compresses very well\nconst DEFAULT_ERROR_RATE = 0.0001\n\nexport class BloomFilter {\n  numItems: number\n  errorRate: number\n  numBits: number\n  numHashes: number\n  bitArray: number[]\n\n  constructor(numItems: number, errorRate: number = DEFAULT_ERROR_RATE) {\n    this.numItems = numItems\n    this.errorRate = errorRate\n    this.numBits = Math.ceil(\n      -(numItems * Math.log(errorRate)) / (Math.log(2) * Math.log(2))\n    )\n    this.numHashes = Math.ceil((this.numBits / numItems) * Math.log(2))\n    this.bitArray = new Array(this.numBits).fill(0)\n  }\n\n  static from(items: string[], errorRate = DEFAULT_ERROR_RATE) {\n    const filter = new BloomFilter(items.length, errorRate)\n\n    for (const item of items) {\n      filter.add(item)\n    }\n    return filter\n  }\n\n  export() {\n    const data = {\n      numItems: this.numItems,\n      errorRate: this.errorRate,\n      numBits: this.numBits,\n      numHashes: this.numHashes,\n      bitArray: this.bitArray,\n    }\n\n    if (process.env.NEXT_RUNTIME === 'nodejs') {\n      if (this.errorRate < DEFAULT_ERROR_RATE) {\n        const filterData = JSON.stringify(data)\n        const gzipSize = require('next/dist/compiled/gzip-size').sync(\n          filterData\n        )\n\n        if (gzipSize > 1024) {\n          console.warn(\n            `Creating filter with error rate less than 0.1% (0.001) can increase the size dramatically proceed with caution. Received error rate ${this.errorRate} resulted in size ${filterData.length} bytes, ${gzipSize} bytes (gzip)`\n          )\n        }\n      }\n    }\n\n    return data\n  }\n\n  import(data: ReturnType<(typeof this)['export']>) {\n    this.numItems = data.numItems\n    this.errorRate = data.errorRate\n    this.numBits = data.numBits\n    this.numHashes = data.numHashes\n    this.bitArray = data.bitArray\n  }\n\n  add(item: string) {\n    const hashValues = this.getHashValues(item)\n    hashValues.forEach((hash) => {\n      this.bitArray[hash] = 1\n    })\n  }\n\n  contains(item: string) {\n    const hashValues = this.getHashValues(item)\n    return hashValues.every((hash) => this.bitArray[hash])\n  }\n\n  getHashValues(item: string) {\n    const hashValues = []\n    for (let i = 1; i <= this.numHashes; i++) {\n      const hash = murmurhash2(`${item}${i}`) % this.numBits\n      hashValues.push(hash)\n    }\n    return hashValues\n  }\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "murmurhash2", "str", "h", "i", "length", "c", "charCodeAt", "Math", "imul", "DEFAULT_ERROR_RATE", "from", "items", "errorRate", "filter", "item", "add", "export", "data", "numItems", "numBits", "numHashes", "bitArray", "process", "env", "NEXT_RUNTIME", "filterData", "JSON", "stringify", "gzipSize", "require", "sync", "console", "warn", "import", "hashValues", "getHashValues", "for<PERSON>ach", "hash", "contains", "every", "push", "constructor", "ceil", "log", "Array", "fill"], "mappings": "AAAA,mDAAmD;;;;;+BAetCA,eAAAA;;;eAAAA;;;AAdb,SAASC,YAAYC,GAAW;IAC9B,IAAIC,IAAI;IACR,IAAK,IAAIC,IAAI,GAAGA,IAAIF,IAAIG,MAAM,EAAED,IAAK;QACnC,MAAME,IAAIJ,IAAIK,UAAU,CAACH;QACzBD,IAAIK,KAAKC,IAAI,CAACN,IAAIG,GAAG;QACrBH,KAAKA,MAAM;QACXA,IAAIK,KAAKC,IAAI,CAACN,GAAG;IACnB;IACA,OAAOA,MAAM;AACf;AAEA,iEAAiE;AACjE,MAAMO,qBAAqB;AAEpB,MAAMV;IAiBX,OAAOW,KAAKC,KAAe,EAAEC,SAA8B,EAAE;QAAhCA,IAAAA,cAAAA,KAAAA,GAAAA,YAAYH;QACvC,MAAMI,SAAS,IAAId,YAAYY,MAAMP,MAAM,EAAEQ;QAE7C,KAAK,MAAME,QAAQH,MAAO;YACxBE,OAAOE,GAAG,CAACD;QACb;QACA,OAAOD;IACT;IAEAG,SAAS;QACP,MAAMC,OAAO;YACXC,UAAU,IAAI,CAACA,QAAQ;YACvBN,WAAW,IAAI,CAACA,SAAS;YACzBO,SAAS,IAAI,CAACA,OAAO;YACrBC,WAAW,IAAI,CAACA,SAAS;YACzBC,UAAU,IAAI,CAACA,QAAQ;QACzB;QAEA,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,OAAU;YACzC,IAAI,IAAI,CAACZ,SAAS,GAAGH,oBAAoB;gBACvC,MAAMgB,aAAaC,KAAKC,SAAS,CAACV;gBAClC,MAAMW,WAAWC,QAAQ,8NAAgCC,IAAI,CAC3DL;gBAGF,IAAIG,WAAW,MAAM;oBACnBG,QAAQC,IAAI,CACT,yIAAsI,IAAI,CAACpB,SAAS,GAAC,uBAAoBa,WAAWrB,MAAM,GAAC,aAAUwB,WAAS;gBAEnN;YACF;QACF;QAEA,OAAOX;IACT;IAEAgB,OAAOhB,IAAyC,EAAE;QAChD,IAAI,CAACC,QAAQ,GAAGD,KAAKC,QAAQ;QAC7B,IAAI,CAACN,SAAS,GAAGK,KAAKL,SAAS;QAC/B,IAAI,CAACO,OAAO,GAAGF,KAAKE,OAAO;QAC3B,IAAI,CAACC,SAAS,GAAGH,KAAKG,SAAS;QAC/B,IAAI,CAACC,QAAQ,GAAGJ,KAAKI,QAAQ;IAC/B;IAEAN,IAAID,IAAY,EAAE;QAChB,MAAMoB,aAAa,IAAI,CAACC,aAAa,CAACrB;QACtCoB,WAAWE,OAAO,CAAC,CAACC;YAClB,IAAI,CAAChB,QAAQ,CAACgB,KAAK,GAAG;QACxB;IACF;IAEAC,SAASxB,IAAY,EAAE;QACrB,MAAMoB,aAAa,IAAI,CAACC,aAAa,CAACrB;QACtC,OAAOoB,WAAWK,KAAK,CAAC,CAACF,OAAS,IAAI,CAAChB,QAAQ,CAACgB,KAAK;IACvD;IAEAF,cAAcrB,IAAY,EAAE;QAC1B,MAAMoB,aAAa,EAAE;QACrB,IAAK,IAAI/B,IAAI,GAAGA,KAAK,IAAI,CAACiB,SAAS,EAAEjB,IAAK;YACxC,MAAMkC,OAAOrC,YAAa,KAAEc,OAAOX,KAAO,IAAI,CAACgB,OAAO;YACtDe,WAAWM,IAAI,CAACH;QAClB;QACA,OAAOH;IACT;IAzEAO,YAAYvB,QAAgB,EAAEN,YAAoBH,kBAAkB,CAAE;QACpE,IAAI,CAACS,QAAQ,GAAGA;QAChB,IAAI,CAACN,SAAS,GAAGA;QACjB,IAAI,CAACO,OAAO,GAAGZ,KAAKmC,IAAI,CACtB,CAAExB,CAAAA,WAAWX,KAAKoC,GAAG,CAAC/B,UAAS,IAAML,CAAAA,KAAKoC,GAAG,CAAC,KAAKpC,KAAKoC,GAAG,CAAC,EAAC;QAE/D,IAAI,CAACvB,SAAS,GAAGb,KAAKmC,IAAI,CAAE,IAAI,CAACvB,OAAO,GAAGD,WAAYX,KAAKoC,GAAG,CAAC;QAChE,IAAI,CAACtB,QAAQ,GAAG,IAAIuB,MAAM,IAAI,CAACzB,OAAO,EAAE0B,IAAI,CAAC;IAC/C;AAkEF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/dist/compiled/react-is/cjs/react-is.development.js"], "sourcesContent": ["/**\n * @license React\n * react-is.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function typeOf(object) {\n      if (\"object\" === typeof object && null !== object) {\n        var $$typeof = object.$$typeof;\n        switch ($$typeof) {\n          case REACT_ELEMENT_TYPE:\n            switch (((object = object.type), object)) {\n              case REACT_FRAGMENT_TYPE:\n              case REACT_PROFILER_TYPE:\n              case REACT_STRICT_MODE_TYPE:\n              case REACT_SUSPENSE_TYPE:\n              case REACT_SUSPENSE_LIST_TYPE:\n              case REACT_VIEW_TRANSITION_TYPE:\n                return object;\n              default:\n                switch (((object = object && object.$$typeof), object)) {\n                  case REACT_CONTEXT_TYPE:\n                  case REACT_FORWARD_REF_TYPE:\n                  case REACT_LAZY_TYPE:\n                  case REACT_MEMO_TYPE:\n                    return object;\n                  case REACT_CONSUMER_TYPE:\n                    return object;\n                  default:\n                    return $$typeof;\n                }\n            }\n          case REACT_PORTAL_TYPE:\n            return $$typeof;\n        }\n      }\n    }\n    var REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_VIEW_TRANSITION_TYPE = Symbol.for(\"react.view_transition\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\");\n    exports.ContextConsumer = REACT_CONSUMER_TYPE;\n    exports.ContextProvider = REACT_CONTEXT_TYPE;\n    exports.Element = REACT_ELEMENT_TYPE;\n    exports.ForwardRef = REACT_FORWARD_REF_TYPE;\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.Lazy = REACT_LAZY_TYPE;\n    exports.Memo = REACT_MEMO_TYPE;\n    exports.Portal = REACT_PORTAL_TYPE;\n    exports.Profiler = REACT_PROFILER_TYPE;\n    exports.StrictMode = REACT_STRICT_MODE_TYPE;\n    exports.Suspense = REACT_SUSPENSE_TYPE;\n    exports.SuspenseList = REACT_SUSPENSE_LIST_TYPE;\n    exports.isContextConsumer = function (object) {\n      return typeOf(object) === REACT_CONSUMER_TYPE;\n    };\n    exports.isContextProvider = function (object) {\n      return typeOf(object) === REACT_CONTEXT_TYPE;\n    };\n    exports.isElement = function (object) {\n      return (\n        \"object\" === typeof object &&\n        null !== object &&\n        object.$$typeof === REACT_ELEMENT_TYPE\n      );\n    };\n    exports.isForwardRef = function (object) {\n      return typeOf(object) === REACT_FORWARD_REF_TYPE;\n    };\n    exports.isFragment = function (object) {\n      return typeOf(object) === REACT_FRAGMENT_TYPE;\n    };\n    exports.isLazy = function (object) {\n      return typeOf(object) === REACT_LAZY_TYPE;\n    };\n    exports.isMemo = function (object) {\n      return typeOf(object) === REACT_MEMO_TYPE;\n    };\n    exports.isPortal = function (object) {\n      return typeOf(object) === REACT_PORTAL_TYPE;\n    };\n    exports.isProfiler = function (object) {\n      return typeOf(object) === REACT_PROFILER_TYPE;\n    };\n    exports.isStrictMode = function (object) {\n      return typeOf(object) === REACT_STRICT_MODE_TYPE;\n    };\n    exports.isSuspense = function (object) {\n      return typeOf(object) === REACT_SUSPENSE_TYPE;\n    };\n    exports.isSuspenseList = function (object) {\n      return typeOf(object) === REACT_SUSPENSE_LIST_TYPE;\n    };\n    exports.isValidElementType = function (type) {\n      return \"string\" === typeof type ||\n        \"function\" === typeof type ||\n        type === REACT_FRAGMENT_TYPE ||\n        type === REACT_PROFILER_TYPE ||\n        type === REACT_STRICT_MODE_TYPE ||\n        type === REACT_SUSPENSE_TYPE ||\n        type === REACT_SUSPENSE_LIST_TYPE ||\n        (\"object\" === typeof type &&\n          null !== type &&\n          (type.$$typeof === REACT_LAZY_TYPE ||\n            type.$$typeof === REACT_MEMO_TYPE ||\n            type.$$typeof === REACT_CONTEXT_TYPE ||\n            type.$$typeof === REACT_CONSUMER_TYPE ||\n            type.$$typeof === REACT_FORWARD_REF_TYPE ||\n            type.$$typeof === REACT_CLIENT_REFERENCE ||\n            void 0 !== type.getModuleId))\n        ? !0\n        : !1;\n    };\n    exports.typeOf = typeOf;\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAED;AACA,oEACE,AAAC;IACC,SAAS,OAAO,MAAM;QACpB,IAAI,aAAa,OAAO,UAAU,SAAS,QAAQ;YACjD,IAAI,WAAW,OAAO,QAAQ;YAC9B,OAAQ;gBACN,KAAK;oBACH,OAAS,AAAC,SAAS,OAAO,IAAI,EAAG;wBAC/B,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;4BACH,OAAO;wBACT;4BACE,OAAS,AAAC,SAAS,UAAU,OAAO,QAAQ,EAAG;gCAC7C,KAAK;gCACL,KAAK;gCACL,KAAK;gCACL,KAAK;oCACH,OAAO;gCACT,KAAK;oCACH,OAAO;gCACT;oCACE,OAAO;4BACX;oBACJ;gBACF,KAAK;oBACH,OAAO;YACX;QACF;IACF;IACA,IAAI,qBAAqB,OAAO,GAAG,CAAC,+BAClC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,6BAA6B,OAAO,GAAG,CAAC,0BACxC,yBAAyB,OAAO,GAAG,CAAC;IACtC,QAAQ,eAAe,GAAG;IAC1B,QAAQ,eAAe,GAAG;IAC1B,QAAQ,OAAO,GAAG;IAClB,QAAQ,UAAU,GAAG;IACrB,QAAQ,QAAQ,GAAG;IACnB,QAAQ,IAAI,GAAG;IACf,QAAQ,IAAI,GAAG;IACf,QAAQ,MAAM,GAAG;IACjB,QAAQ,QAAQ,GAAG;IACnB,QAAQ,UAAU,GAAG;IACrB,QAAQ,QAAQ,GAAG;IACnB,QAAQ,YAAY,GAAG;IACvB,QAAQ,iBAAiB,GAAG,SAAU,MAAM;QAC1C,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,iBAAiB,GAAG,SAAU,MAAM;QAC1C,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,SAAS,GAAG,SAAU,MAAM;QAClC,OACE,aAAa,OAAO,UACpB,SAAS,UACT,OAAO,QAAQ,KAAK;IAExB;IACA,QAAQ,YAAY,GAAG,SAAU,MAAM;QACrC,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,UAAU,GAAG,SAAU,MAAM;QACnC,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,MAAM,GAAG,SAAU,MAAM;QAC/B,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,MAAM,GAAG,SAAU,MAAM;QAC/B,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,QAAQ,GAAG,SAAU,MAAM;QACjC,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,UAAU,GAAG,SAAU,MAAM;QACnC,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,YAAY,GAAG,SAAU,MAAM;QACrC,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,UAAU,GAAG,SAAU,MAAM;QACnC,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,cAAc,GAAG,SAAU,MAAM;QACvC,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,kBAAkB,GAAG,SAAU,IAAI;QACzC,OAAO,aAAa,OAAO,QACzB,eAAe,OAAO,QACtB,SAAS,uBACT,SAAS,uBACT,SAAS,0BACT,SAAS,uBACT,SAAS,4BACR,aAAa,OAAO,QACnB,SAAS,QACT,CAAC,KAAK,QAAQ,KAAK,mBACjB,KAAK,QAAQ,KAAK,mBAClB,KAAK,QAAQ,KAAK,sBAClB,KAAK,QAAQ,KAAK,uBAClB,KAAK,QAAQ,KAAK,0BAClB,KAAK,QAAQ,KAAK,0BAClB,KAAK,MAAM,KAAK,WAAW,IAC7B,CAAC,IACD,CAAC;IACP;IACA,QAAQ,MAAM,GAAG;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/dist/compiled/react-is/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n"], "names": [], "mappings": "AAAA;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 439, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/remove-trailing-slash.ts"], "sourcesContent": ["/**\n * Removes the trailing slash for a given route or page path. Preserves the\n * root page. Examples:\n *   - `/foo/bar/` -> `/foo/bar`\n *   - `/foo/bar` -> `/foo/bar`\n *   - `/` -> `/`\n */\nexport function removeTrailingSlash(route: string) {\n  return route.replace(/\\/$/, '') || '/'\n}\n"], "names": ["removeTrailingSlash", "route", "replace"], "mappings": "AAAA;;;;;;CAMC,GAAA;;;;+BACeA,uBAAAA;;;eAAAA;;;AAAT,SAASA,oBAAoBC,KAAa;IAC/C,OAAOA,MAAMC,OAAO,CAAC,OAAO,OAAO;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 463, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/get-asset-path-from-route.ts"], "sourcesContent": ["// Translates a logical route into its pages asset path (relative from a common prefix)\n// \"asset path\" being its javascript file, data file, prerendered html,...\nexport default function getAssetPathFromRoute(\n  route: string,\n  ext: string = ''\n): string {\n  const path =\n    route === '/'\n      ? '/index'\n      : /^\\/index(\\/|$)/.test(route)\n        ? `/index${route}`\n        : route\n  return path + ext\n}\n"], "names": ["getAssetPathFromRoute", "route", "ext", "path", "test"], "mappings": "AAAA,uFAAuF;AACvF,0EAA0E;;;;;+BAC1E,WAAA;;;eAAwBA;;;AAAT,SAASA,sBACtBC,KAAa,EACbC,GAAgB;IAAhBA,IAAAA,QAAAA,KAAAA,GAAAA,MAAc;IAEd,MAAMC,OACJF,UAAU,MACN,WACA,iBAAiBG,IAAI,CAACH,SACnB,WAAQA,QACTA;IACR,OAAOE,OAAOD;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/client/trusted-types.ts"], "sourcesContent": ["/**\n * Stores the Trusted Types Policy. Starts as undefined and can be set to null\n * if Trusted Types is not supported in the browser.\n */\nlet policy: TrustedTypePolicy | null | undefined\n\n/**\n * Getter for the Trusted Types Policy. If it is undefined, it is instantiated\n * here or set to null if Trusted Types is not supported in the browser.\n */\nfunction getPolicy() {\n  if (typeof policy === 'undefined' && typeof window !== 'undefined') {\n    policy =\n      window.trustedTypes?.createPolicy('nextjs', {\n        createHTML: (input) => input,\n        createScript: (input) => input,\n        createScriptURL: (input) => input,\n      }) || null\n  }\n\n  return policy\n}\n\n/**\n * Unsafely promote a string to a TrustedScriptURL, falling back to strings\n * when Trusted Types are not available.\n * This is a security-sensitive function; any use of this function\n * must go through security review. In particular, it must be assured that the\n * provided string will never cause an XSS vulnerability if used in a context\n * that will cause a browser to load and execute a resource, e.g. when\n * assigning to script.src.\n */\nexport function __unsafeCreateTrustedScriptURL(\n  url: string\n): TrustedScriptURL | string {\n  return getPolicy()?.createScriptURL(url) || url\n}\n"], "names": ["__unsafeCreateTrustedScriptURL", "policy", "getPolicy", "window", "trustedTypes", "createPolicy", "createHTML", "input", "createScript", "createScriptURL", "url"], "mappings": "AAAA;;;CAGC,GAAA;;;;+BA6BeA,kCAAAA;;;eAAAA;;;AA5BhB,IAAIC;AAEJ;;;CAGC,GACD,SAASC;IACP,IAAI,OAAOD,WAAW,eAAe,OAAOE,WAAW,aAAa;YAEhEA;QADFF,SACEE,CAAAA,CAAAA,uBAAAA,OAAOC,YAAY,KAAA,OAAA,KAAA,IAAnBD,qBAAqBE,YAAY,CAAC,UAAU;YAC1CC,YAAY,CAACC,QAAUA;YACvBC,cAAc,CAACD,QAAUA;YACzBE,iBAAiB,CAACF,QAAUA;QAC9B,EAAA,KAAM;IACV;IAEA,OAAON;AACT;AAWO,SAASD,+BACdU,GAAW;QAEJR;IAAP,OAAOA,CAAAA,CAAAA,aAAAA,WAAAA,KAAAA,OAAAA,KAAAA,IAAAA,WAAaO,eAAe,CAACC,IAAAA,KAAQA;AAC9C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/build/deployment-id.ts"], "sourcesContent": ["export function getDeploymentIdQueryOrEmptyString(): string {\n  if (process.env.NEXT_DEPLOYMENT_ID) {\n    return `?dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n  }\n  return ''\n}\n"], "names": ["getDeploymentIdQueryOrEmptyString", "process", "env", "NEXT_DEPLOYMENT_ID"], "mappings": ";;;;+BAAgBA,qCAAAA;;;eAAAA;;;AAAT,SAASA;IACd,IAAIC,QAAQC,GAAG,CAACC,kBAAkB,KAAE;;IAEpC;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 550, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/client/route-loader.ts"], "sourcesContent": ["import type { ComponentType } from 'react'\nimport type { MiddlewareMatcher } from '../build/analysis/get-page-static-info'\nimport getAssetPathFromRoute from '../shared/lib/router/utils/get-asset-path-from-route'\nimport { __unsafeCreateTrustedScriptURL } from './trusted-types'\nimport { requestIdleCallback } from './request-idle-callback'\nimport { getDeploymentIdQueryOrEmptyString } from '../build/deployment-id'\nimport { encodeURIPath } from '../shared/lib/encode-uri-path'\n\n// 3.8s was arbitrarily chosen as it's what https://web.dev/interactive\n// considers as \"Good\" time-to-interactive. We must assume something went\n// wrong beyond this point, and then fall-back to a full page transition to\n// show the user something of value.\nconst MS_MAX_IDLE_DELAY = 3800\n\ndeclare global {\n  interface Window {\n    __BUILD_MANIFEST?: Record<string, string[]>\n    __BUILD_MANIFEST_CB?: Function\n    __MIDDLEWARE_MATCHERS?: MiddlewareMatcher[]\n    __MIDDLEWARE_MANIFEST_CB?: Function\n    __REACT_LOADABLE_MANIFEST?: any\n    __DYNAMIC_CSS_MANIFEST?: any\n    __RSC_MANIFEST?: any\n    __RSC_SERVER_MANIFEST?: any\n    __NEXT_FONT_MANIFEST?: any\n    __SUBRESOURCE_INTEGRITY_MANIFEST?: string\n    __INTERCEPTION_ROUTE_REWRITE_MANIFEST?: string\n  }\n}\n\ninterface LoadedEntrypointSuccess {\n  component: ComponentType\n  exports: any\n}\ninterface LoadedEntrypointFailure {\n  error: unknown\n}\ntype RouteEntrypoint = LoadedEntrypointSuccess | LoadedEntrypointFailure\n\ninterface RouteStyleSheet {\n  href: string\n  content: string\n}\n\ninterface LoadedRouteSuccess extends LoadedEntrypointSuccess {\n  styles: RouteStyleSheet[]\n}\ninterface LoadedRouteFailure {\n  error: unknown\n}\ntype RouteLoaderEntry = LoadedRouteSuccess | LoadedRouteFailure\n\ninterface Future<V> {\n  resolve: (entrypoint: V) => void\n  future: Promise<V>\n}\nfunction withFuture<T extends object>(\n  key: string,\n  map: Map<string, Future<T> | T>,\n  generator?: () => Promise<T>\n): Promise<T> {\n  let entry = map.get(key)\n  if (entry) {\n    if ('future' in entry) {\n      return entry.future\n    }\n    return Promise.resolve(entry)\n  }\n  let resolver: (entrypoint: T) => void\n  const prom: Promise<T> = new Promise<T>((resolve) => {\n    resolver = resolve\n  })\n  map.set(key, { resolve: resolver!, future: prom })\n  return generator\n    ? generator()\n        .then((value) => {\n          resolver(value)\n          return value\n        })\n        .catch((err) => {\n          map.delete(key)\n          throw err\n        })\n    : prom\n}\n\nexport interface RouteLoader {\n  whenEntrypoint(route: string): Promise<RouteEntrypoint>\n  onEntrypoint(route: string, execute: () => unknown): void\n  loadRoute(route: string, prefetch?: boolean): Promise<RouteLoaderEntry>\n  prefetch(route: string): Promise<void>\n}\n\nconst ASSET_LOAD_ERROR = Symbol('ASSET_LOAD_ERROR')\n// TODO: unexport\nexport function markAssetError(err: Error): Error {\n  return Object.defineProperty(err, ASSET_LOAD_ERROR, {})\n}\n\nexport function isAssetError(err?: Error): boolean | undefined {\n  return err && ASSET_LOAD_ERROR in err\n}\n\nfunction hasPrefetch(link?: HTMLLinkElement): boolean {\n  try {\n    link = document.createElement('link')\n    return (\n      // detect IE11 since it supports prefetch but isn't detected\n      // with relList.support\n      (!!window.MSInputMethodContext && !!(document as any).documentMode) ||\n      link.relList.supports('prefetch')\n    )\n  } catch {\n    return false\n  }\n}\n\nconst canPrefetch: boolean = hasPrefetch()\n\nconst getAssetQueryString = () => {\n  return getDeploymentIdQueryOrEmptyString()\n}\n\nfunction prefetchViaDom(\n  href: string,\n  as: string,\n  link?: HTMLLinkElement\n): Promise<any> {\n  return new Promise<void>((resolve, reject) => {\n    const selector = `\n      link[rel=\"prefetch\"][href^=\"${href}\"],\n      link[rel=\"preload\"][href^=\"${href}\"],\n      script[src^=\"${href}\"]`\n    if (document.querySelector(selector)) {\n      return resolve()\n    }\n\n    link = document.createElement('link')\n\n    // The order of property assignment here is intentional:\n    if (as) link!.as = as\n    link!.rel = `prefetch`\n    link!.crossOrigin = process.env.__NEXT_CROSS_ORIGIN!\n    link!.onload = resolve as any\n    link!.onerror = () =>\n      reject(markAssetError(new Error(`Failed to prefetch: ${href}`)))\n\n    // `href` should always be last:\n    link!.href = href\n\n    document.head.appendChild(link)\n  })\n}\n\nfunction appendScript(\n  src: TrustedScriptURL | string,\n  script?: HTMLScriptElement\n): Promise<unknown> {\n  return new Promise((resolve, reject) => {\n    script = document.createElement('script')\n\n    // The order of property assignment here is intentional.\n    // 1. Setup success/failure hooks in case the browser synchronously\n    //    executes when `src` is set.\n    script.onload = resolve\n    script.onerror = () =>\n      reject(markAssetError(new Error(`Failed to load script: ${src}`)))\n\n    // 2. Configure the cross-origin attribute before setting `src` in case the\n    //    browser begins to fetch.\n    script.crossOrigin = process.env.__NEXT_CROSS_ORIGIN!\n\n    // 3. Finally, set the source and inject into the DOM in case the child\n    //    must be appended for fetching to start.\n    script.src = src as string\n    document.body.appendChild(script)\n  })\n}\n\n// We wait for pages to be built in dev before we start the route transition\n// timeout to prevent an un-necessary hard navigation in development.\nlet devBuildPromise: Promise<void> | undefined\n\n// Resolve a promise that times out after given amount of milliseconds.\nfunction resolvePromiseWithTimeout<T>(\n  p: Promise<T>,\n  ms: number,\n  err: Error\n): Promise<T> {\n  return new Promise((resolve, reject) => {\n    let cancelled = false\n\n    p.then((r) => {\n      // Resolved, cancel the timeout\n      cancelled = true\n      resolve(r)\n    }).catch(reject)\n\n    // We wrap these checks separately for better dead-code elimination in\n    // production bundles.\n    if (process.env.NODE_ENV === 'development') {\n      ;(devBuildPromise || Promise.resolve()).then(() => {\n        requestIdleCallback(() =>\n          setTimeout(() => {\n            if (!cancelled) {\n              reject(err)\n            }\n          }, ms)\n        )\n      })\n    }\n\n    if (process.env.NODE_ENV !== 'development') {\n      requestIdleCallback(() =>\n        setTimeout(() => {\n          if (!cancelled) {\n            reject(err)\n          }\n        }, ms)\n      )\n    }\n  })\n}\n\n// TODO: stop exporting or cache the failure\n// It'd be best to stop exporting this. It's an implementation detail. We're\n// only exporting it for backwards compatibility with the `page-loader`.\n// Only cache this response as a last resort if we cannot eliminate all other\n// code branches that use the Build Manifest Callback and push them through\n// the Route Loader interface.\nexport function getClientBuildManifest() {\n  if (self.__BUILD_MANIFEST) {\n    return Promise.resolve(self.__BUILD_MANIFEST)\n  }\n\n  const onBuildManifest = new Promise<Record<string, string[]>>((resolve) => {\n    // Mandatory because this is not concurrent safe:\n    const cb = self.__BUILD_MANIFEST_CB\n    self.__BUILD_MANIFEST_CB = () => {\n      resolve(self.__BUILD_MANIFEST!)\n      cb && cb()\n    }\n  })\n\n  return resolvePromiseWithTimeout(\n    onBuildManifest,\n    MS_MAX_IDLE_DELAY,\n    markAssetError(new Error('Failed to load client build manifest'))\n  )\n}\n\ninterface RouteFiles {\n  scripts: (TrustedScriptURL | string)[]\n  css: string[]\n}\nfunction getFilesForRoute(\n  assetPrefix: string,\n  route: string\n): Promise<RouteFiles> {\n  if (process.env.NODE_ENV === 'development') {\n    const scriptUrl =\n      assetPrefix +\n      '/_next/static/chunks/pages' +\n      encodeURIPath(getAssetPathFromRoute(route, '.js')) +\n      getAssetQueryString()\n    return Promise.resolve({\n      scripts: [__unsafeCreateTrustedScriptURL(scriptUrl)],\n      // Styles are handled by `style-loader` in development:\n      css: [],\n    })\n  }\n  return getClientBuildManifest().then((manifest) => {\n    if (!(route in manifest)) {\n      throw markAssetError(new Error(`Failed to lookup route: ${route}`))\n    }\n    const allFiles = manifest[route].map(\n      (entry) => assetPrefix + '/_next/' + encodeURIPath(entry)\n    )\n    return {\n      scripts: allFiles\n        .filter((v) => v.endsWith('.js'))\n        .map((v) => __unsafeCreateTrustedScriptURL(v) + getAssetQueryString()),\n      css: allFiles\n        .filter((v) => v.endsWith('.css'))\n        .map((v) => v + getAssetQueryString()),\n    }\n  })\n}\n\nexport function createRouteLoader(assetPrefix: string): RouteLoader {\n  const entrypoints: Map<string, Future<RouteEntrypoint> | RouteEntrypoint> =\n    new Map()\n  const loadedScripts: Map<string, Promise<unknown>> = new Map()\n  const styleSheets: Map<string, Promise<RouteStyleSheet>> = new Map()\n  const routes: Map<string, Future<RouteLoaderEntry> | RouteLoaderEntry> =\n    new Map()\n\n  function maybeExecuteScript(\n    src: TrustedScriptURL | string\n  ): Promise<unknown> {\n    // With HMR we might need to \"reload\" scripts when they are\n    // disposed and readded. Executing scripts twice has no functional\n    // differences\n    if (process.env.NODE_ENV !== 'development') {\n      let prom: Promise<unknown> | undefined = loadedScripts.get(src.toString())\n      if (prom) {\n        return prom\n      }\n\n      // Skip executing script if it's already in the DOM:\n      if (document.querySelector(`script[src^=\"${src}\"]`)) {\n        return Promise.resolve()\n      }\n\n      loadedScripts.set(src.toString(), (prom = appendScript(src)))\n      return prom\n    } else {\n      return appendScript(src)\n    }\n  }\n\n  function fetchStyleSheet(href: string): Promise<RouteStyleSheet> {\n    let prom: Promise<RouteStyleSheet> | undefined = styleSheets.get(href)\n    if (prom) {\n      return prom\n    }\n\n    styleSheets.set(\n      href,\n      (prom = fetch(href, { credentials: 'same-origin' })\n        .then((res) => {\n          if (!res.ok) {\n            throw new Error(`Failed to load stylesheet: ${href}`)\n          }\n          return res.text().then((text) => ({ href: href, content: text }))\n        })\n        .catch((err) => {\n          throw markAssetError(err)\n        }))\n    )\n    return prom\n  }\n\n  return {\n    whenEntrypoint(route: string) {\n      return withFuture(route, entrypoints)\n    },\n    onEntrypoint(route: string, execute: undefined | (() => unknown)) {\n      ;(execute\n        ? Promise.resolve()\n            .then(() => execute())\n            .then(\n              (exports: any) => ({\n                component: (exports && exports.default) || exports,\n                exports: exports,\n              }),\n              (err) => ({ error: err })\n            )\n        : Promise.resolve(undefined)\n      ).then((input: RouteEntrypoint | undefined) => {\n        const old = entrypoints.get(route)\n        if (old && 'resolve' in old) {\n          if (input) {\n            entrypoints.set(route, input)\n            old.resolve(input)\n          }\n        } else {\n          if (input) {\n            entrypoints.set(route, input)\n          } else {\n            entrypoints.delete(route)\n          }\n          // when this entrypoint has been resolved before\n          // the route is outdated and we want to invalidate\n          // this cache entry\n          routes.delete(route)\n        }\n      })\n    },\n    loadRoute(route: string, prefetch?: boolean) {\n      return withFuture<RouteLoaderEntry>(route, routes, () => {\n        let devBuildPromiseResolve: () => void\n\n        if (process.env.NODE_ENV === 'development') {\n          devBuildPromise = new Promise<void>((resolve) => {\n            devBuildPromiseResolve = resolve\n          })\n        }\n\n        return resolvePromiseWithTimeout(\n          getFilesForRoute(assetPrefix, route)\n            .then(({ scripts, css }) => {\n              return Promise.all([\n                entrypoints.has(route)\n                  ? []\n                  : Promise.all(scripts.map(maybeExecuteScript)),\n                Promise.all(css.map(fetchStyleSheet)),\n              ] as const)\n            })\n            .then((res) => {\n              return this.whenEntrypoint(route).then((entrypoint) => ({\n                entrypoint,\n                styles: res[1],\n              }))\n            }),\n          MS_MAX_IDLE_DELAY,\n          markAssetError(new Error(`Route did not complete loading: ${route}`))\n        )\n          .then(({ entrypoint, styles }) => {\n            const res: RouteLoaderEntry = Object.assign<\n              { styles: RouteStyleSheet[] },\n              RouteEntrypoint\n            >({ styles: styles! }, entrypoint)\n            return 'error' in entrypoint ? entrypoint : res\n          })\n          .catch((err) => {\n            if (prefetch) {\n              // we don't want to cache errors during prefetch\n              throw err\n            }\n            return { error: err }\n          })\n          .finally(() => devBuildPromiseResolve?.())\n      })\n    },\n    prefetch(route: string): Promise<void> {\n      // https://github.com/GoogleChromeLabs/quicklink/blob/453a661fa1fa940e2d2e044452398e38c67a98fb/src/index.mjs#L115-L118\n      // License: Apache 2.0\n      let cn\n      if ((cn = (navigator as any).connection)) {\n        // Don't prefetch if using 2G or if Save-Data is enabled.\n        if (cn.saveData || /2g/.test(cn.effectiveType)) return Promise.resolve()\n      }\n      return getFilesForRoute(assetPrefix, route)\n        .then((output) =>\n          Promise.all(\n            canPrefetch\n              ? output.scripts.map((script) =>\n                  prefetchViaDom(script.toString(), 'script')\n                )\n              : []\n          )\n        )\n        .then(() => {\n          requestIdleCallback(() => this.loadRoute(route, true).catch(() => {}))\n        })\n        .catch(\n          // swallow prefetch errors\n          () => {}\n        )\n    },\n  }\n}\n"], "names": ["createRouteLoader", "getClientBuildManifest", "isAssetError", "<PERSON><PERSON><PERSON><PERSON>", "MS_MAX_IDLE_DELAY", "withFuture", "key", "map", "generator", "entry", "get", "future", "Promise", "resolve", "resolver", "prom", "set", "then", "value", "catch", "err", "delete", "ASSET_LOAD_ERROR", "Symbol", "Object", "defineProperty", "hasPrefetch", "link", "document", "createElement", "window", "MSInputMethodContext", "documentMode", "relList", "supports", "canPrefetch", "getAssetQueryString", "getDeploymentIdQueryOrEmptyString", "prefetchViaDom", "href", "as", "reject", "selector", "querySelector", "rel", "crossOrigin", "process", "env", "__NEXT_CROSS_ORIGIN", "onload", "onerror", "Error", "head", "append<PERSON><PERSON><PERSON>", "appendScript", "src", "script", "body", "devBuildPromise", "resolvePromiseWithTimeout", "p", "ms", "cancelled", "r", "NODE_ENV", "requestIdleCallback", "setTimeout", "self", "__BUILD_MANIFEST", "onBuildManifest", "cb", "__BUILD_MANIFEST_CB", "getFilesForRoute", "assetPrefix", "route", "scriptUrl", "encodeURIPath", "getAssetPathFromRoute", "scripts", "__unsafeCreateTrustedScriptURL", "css", "manifest", "allFiles", "filter", "v", "endsWith", "entrypoints", "Map", "loadedScripts", "styleSheets", "routes", "maybeExecuteScript", "toString", "fetchStyleSheet", "fetch", "credentials", "res", "ok", "text", "content", "whenEntrypoint", "onEntrypoint", "execute", "exports", "component", "default", "error", "undefined", "input", "old", "loadRoute", "prefetch", "devBuildPromiseResolve", "all", "has", "entrypoint", "styles", "assign", "finally", "cn", "navigator", "connection", "saveData", "test", "effectiveType", "output"], "mappings": ";;;;;;;;;;;;;;;;;IAiSgBA,iBAAiB,EAAA;eAAjBA;;IA3DAC,sBAAsB,EAAA;eAAtBA;;IAnIAC,YAAY,EAAA;eAAZA;;IAJAC,cAAc,EAAA;eAAdA;;;;gFA7FkB;8BACa;qCACX;8BACc;+BACpB;AAE9B,uEAAuE;AACvE,yEAAyE;AACzE,2EAA2E;AAC3E,oCAAoC;AACpC,MAAMC,oBAAoB;AA4C1B,SAASC,WACPC,GAAW,EACXC,GAA+B,EAC/BC,SAA4B;IAE5B,IAAIC,QAAQF,IAAIG,GAAG,CAACJ;IACpB,IAAIG,OAAO;QACT,IAAI,YAAYA,OAAO;YACrB,OAAOA,MAAME,MAAM;QACrB;QACA,OAAOC,QAAQC,OAAO,CAACJ;IACzB;IACA,IAAIK;IACJ,MAAMC,OAAmB,IAAIH,QAAW,CAACC;QACvCC,WAAWD;IACb;IACAN,IAAIS,GAAG,CAACV,KAAK;QAAEO,SAASC;QAAWH,QAAQI;IAAK;IAChD,OAAOP,YACHA,YACGS,IAAI,CAAC,CAACC;QACLJ,SAASI;QACT,OAAOA;IACT,GACCC,KAAK,CAAC,CAACC;QACNb,IAAIc,MAAM,CAACf;QACX,MAAMc;IACR,KACFL;AACN;AASA,MAAMO,mBAAmBC,OAAO;AAEzB,SAASpB,eAAeiB,GAAU;IACvC,OAAOI,OAAOC,cAAc,CAACL,KAAKE,kBAAkB,CAAC;AACvD;AAEO,SAASpB,aAAakB,GAAW;IACtC,OAAOA,OAAOE,oBAAoBF;AACpC;AAEA,SAASM,YAAYC,IAAsB;IACzC,IAAI;QACFA,OAAOC,SAASC,aAAa,CAAC;QAC9B,OAEE,AADA,AAEA,uBADuB,qCADqC;QAE3D,CAAC,CAACC,OAAOC,oBAAoB,IAAI,CAAC,CAAEH,SAAiBI,YAAY,IAClEL,KAAKM,OAAO,CAACC,QAAQ,CAAC;IAE1B,EAAE,OAAA,GAAM;QACN,OAAO;IACT;AACF;AAEA,MAAMC,cAAuBT;AAE7B,MAAMU,sBAAsB;IAC1B,OAAOC,CAAAA,GAAAA,cAAAA,iCAAiC;AAC1C;AAEA,SAASC,eACPC,IAAY,EACZC,EAAU,EACVb,IAAsB;IAEtB,OAAO,IAAIf,QAAc,CAACC,SAAS4B;QACjC,MAAMC,WAAY,yCACcH,OAAK,2CACNA,OAAK,6BACnBA,OAAK;QACtB,IAAIX,SAASe,aAAa,CAACD,WAAW;YACpC,OAAO7B;QACT;QAEAc,OAAOC,SAASC,aAAa,CAAC;QAE9B,wDAAwD;QACxD,IAAIW,IAAIb,KAAMa,EAAE,GAAGA;QACnBb,KAAMiB,GAAG,GAAI;QACbjB,KAAMkB,WAAW,GAAGC,QAAQC,GAAG,CAACC,mBAAmB;QACnDrB,KAAMsB,MAAM,GAAGpC;QACfc,KAAMuB,OAAO,GAAG,IACdT,OAAOtC,eAAe,OAAA,cAAwC,CAAxC,IAAIgD,MAAO,yBAAsBZ,OAAjC,qBAAA;uBAAA;4BAAA;8BAAA;YAAuC;QAE/D,gCAAgC;QAChCZ,KAAMY,IAAI,GAAGA;QAEbX,SAASwB,IAAI,CAACC,WAAW,CAAC1B;IAC5B;AACF;AAEA,SAAS2B,aACPC,GAA8B,EAC9BC,MAA0B;IAE1B,OAAO,IAAI5C,QAAQ,CAACC,SAAS4B;QAC3Be,SAAS5B,SAASC,aAAa,CAAC;QAEhC,wDAAwD;QACxD,mEAAmE;QACnE,iCAAiC;QACjC2B,OAAOP,MAAM,GAAGpC;QAChB2C,OAAON,OAAO,GAAG,IACfT,OAAOtC,eAAe,OAAA,cAA0C,CAA1C,IAAIgD,MAAO,4BAAyBI,MAApC,qBAAA;uBAAA;4BAAA;8BAAA;YAAyC;QAEjE,2EAA2E;QAC3E,8BAA8B;QAC9BC,OAAOX,WAAW,GAAGC,QAAQC,GAAG,CAACC,mBAAmB;QAEpD,uEAAuE;QACvE,6CAA6C;QAC7CQ,OAAOD,GAAG,GAAGA;QACb3B,SAAS6B,IAAI,CAACJ,WAAW,CAACG;IAC5B;AACF;AAEA,4EAA4E;AAC5E,qEAAqE;AACrE,IAAIE;AAEJ,uEAAuE;AACvE,SAASC,0BACPC,CAAa,EACbC,EAAU,EACVzC,GAAU;IAEV,OAAO,IAAIR,QAAQ,CAACC,SAAS4B;QAC3B,IAAIqB,YAAY;QAEhBF,EAAE3C,IAAI,CAAC,CAAC8C;YACN,+BAA+B;YAC/BD,YAAY;YACZjD,QAAQkD;QACV,GAAG5C,KAAK,CAACsB;QAET,sEAAsE;QACtE,sBAAsB;QACtB,IAAIK,QAAQC,GAAG,CAACiB,QAAQ,KAAK,WAAe;;YACxCN,CAAAA,mBAAmB9C,QAAQC,OAAO,EAAC,EAAGI,IAAI,CAAC;gBAC3CgD,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC,IAClBC,WAAW;wBACT,IAAI,CAACJ,WAAW;4BACdrB,OAAOrB;wBACT;oBACF,GAAGyC;YAEP;QACF;QAEA,IAAIf,QAAQC,GAAG,CAACiB,QAAQ,KAAK,UAAe;;QAQ5C;IACF;AACF;AAQO,SAAS/D;IACd,IAAIkE,KAAKC,gBAAgB,EAAE;QACzB,OAAOxD,QAAQC,OAAO,CAACsD,KAAKC,gBAAgB;IAC9C;IAEA,MAAMC,kBAAkB,IAAIzD,QAAkC,CAACC;QAC7D,iDAAiD;QACjD,MAAMyD,KAAKH,KAAKI,mBAAmB;QACnCJ,KAAKI,mBAAmB,GAAG;YACzB1D,QAAQsD,KAAKC,gBAAgB;YAC7BE,MAAMA;QACR;IACF;IAEA,OAAOX,0BACLU,iBACAjE,mBACAD,eAAe,OAAA,cAAiD,CAAjD,IAAIgD,MAAM,yCAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgD;AAEnE;AAMA,SAASqB,iBACPC,WAAmB,EACnBC,KAAa;IAEb,IAAI5B,QAAQC,GAAG,CAACiB,QAAQ,KAAK,WAAe;QAC1C,MAAMW,YACJF,cACA,+BACAG,CAAAA,GAAAA,eAAAA,aAAa,EAACC,CAAAA,GAAAA,uBAAAA,OAAqB,EAACH,OAAO,UAC3CtC;QACF,OAAOxB,QAAQC,OAAO,CAAC;YACrBiE,SAAS;gBAACC,CAAAA,GAAAA,cAAAA,8BAA8B,EAACJ;aAAW;YACpD,uDAAuD;YACvDK,KAAK,EAAE;QACT;IACF;;AAiBF;AAEO,SAAShF,kBAAkByE,WAAmB;IACnD,MAAMa,cACJ,IAAIC;IACN,MAAMC,gBAA+C,IAAID;IACzD,MAAME,cAAqD,IAAIF;IAC/D,MAAMG,SACJ,IAAIH;IAEN,SAASI,mBACPpC,GAA8B;QAE9B,2DAA2D;QAC3D,kEAAkE;QAClE,cAAc;QACd,IAAIT,QAAQC,GAAG,CAACiB,QAAQ,KAAK,UAAe;;QAa5C,OAAO;YACL,OAAOV,aAAaC;QACtB;IACF;IAEA,SAASsC,gBAAgBtD,IAAY;QACnC,IAAIxB,OAA6C0E,YAAY/E,GAAG,CAAC6B;QACjE,IAAIxB,MAAM;YACR,OAAOA;QACT;QAEA0E,YAAYzE,GAAG,CACbuB,MACCxB,OAAO+E,MAAMvD,MAAM;YAAEwD,aAAa;QAAc,GAC9C9E,IAAI,CAAC,CAAC+E;YACL,IAAI,CAACA,IAAIC,EAAE,EAAE;gBACX,MAAM,OAAA,cAA+C,CAA/C,IAAI9C,MAAO,gCAA6BZ,OAAxC,qBAAA;2BAAA;gCAAA;kCAAA;gBAA8C;YACtD;YACA,OAAOyD,IAAIE,IAAI,GAAGjF,IAAI,CAAC,CAACiF,OAAU,CAAA;oBAAE3D,MAAMA;oBAAM4D,SAASD;gBAAK,CAAA;QAChE,GACC/E,KAAK,CAAC,CAACC;YACN,MAAMjB,eAAeiB;QACvB;QAEJ,OAAOL;IACT;IAEA,OAAO;QACLqF,gBAAe1B,KAAa;YAC1B,OAAOrE,WAAWqE,OAAOY;QAC3B;QACAe,cAAa3B,KAAa,EAAE4B,OAAoC;;YAC5DA,CAAAA,UACE1F,QAAQC,OAAO,GACZI,IAAI,CAAC,IAAMqF,WACXrF,IAAI,CACH,CAACsF,WAAkB,CAAA;oBACjBC,WAAYD,YAAWA,SAAQE,OAAO,IAAKF;oBAC3CA,SAASA;gBACX,CAAA,GACA,CAACnF,MAAS,CAAA;oBAAEsF,OAAOtF;gBAAI,CAAA,KAE3BR,QAAQC,OAAO,CAAC8F,UAAS,EAC3B1F,IAAI,CAAC,CAAC2F;gBACN,MAAMC,MAAMvB,YAAY5E,GAAG,CAACgE;gBAC5B,IAAImC,OAAO,aAAaA,KAAK;oBAC3B,IAAID,OAAO;wBACTtB,YAAYtE,GAAG,CAAC0D,OAAOkC;wBACvBC,IAAIhG,OAAO,CAAC+F;oBACd;gBACF,OAAO;oBACL,IAAIA,OAAO;wBACTtB,YAAYtE,GAAG,CAAC0D,OAAOkC;oBACzB,OAAO;wBACLtB,YAAYjE,MAAM,CAACqD;oBACrB;oBACA,gDAAgD;oBAChD,kDAAkD;oBAClD,mBAAmB;oBACnBgB,OAAOrE,MAAM,CAACqD;gBAChB;YACF;QACF;QACAoC,WAAUpC,KAAa,EAAEqC,QAAkB;YACzC,OAAO1G,WAA6BqE,OAAOgB,QAAQ;gBACjD,IAAIsB;gBAEJ,IAAIlE,QAAQC,GAAG,CAACiB,QAAQ,KAAK,WAAe;oBAC1CN,kBAAkB,IAAI9C,QAAc,CAACC;wBACnCmG,yBAAyBnG;oBAC3B;gBACF;gBAEA,OAAO8C,0BACLa,iBAAiBC,aAAaC,OAC3BzD,IAAI,CAAC,CAAA;wBAAC,EAAE6D,OAAO,EAAEE,GAAG,EAAE,GAAA;oBACrB,OAAOpE,QAAQqG,GAAG,CAAC;wBACjB3B,YAAY4B,GAAG,CAACxC,SACZ,EAAE,GACF9D,QAAQqG,GAAG,CAACnC,QAAQvE,GAAG,CAACoF;wBAC5B/E,QAAQqG,GAAG,CAACjC,IAAIzE,GAAG,CAACsF;qBACrB;gBACH,GACC5E,IAAI,CAAC,CAAC+E;oBACL,OAAO,IAAI,CAACI,cAAc,CAAC1B,OAAOzD,IAAI,CAAC,CAACkG,aAAgB,CAAA;4BACtDA;4BACAC,QAAQpB,GAAG,CAAC,EAAE;wBAChB,CAAA;gBACF,IACF5F,mBACAD,eAAe,OAAA,cAAqD,CAArD,IAAIgD,MAAO,qCAAkCuB,QAA7C,qBAAA;2BAAA;gCAAA;kCAAA;gBAAoD,KAElEzD,IAAI,CAAC,CAAA;wBAAC,EAAEkG,UAAU,EAAEC,MAAM,EAAE,GAAA;oBAC3B,MAAMpB,MAAwBxE,OAAO6F,MAAM,CAGzC;wBAAED,QAAQA;oBAAQ,GAAGD;oBACvB,OAAO,WAAWA,aAAaA,aAAanB;gBAC9C,GACC7E,KAAK,CAAC,CAACC;oBACN,IAAI2F,UAAU;wBACZ,gDAAgD;wBAChD,MAAM3F;oBACR;oBACA,OAAO;wBAAEsF,OAAOtF;oBAAI;gBACtB,GACCkG,OAAO,CAAC,IAAMN,0BAAAA,OAAAA,KAAAA,IAAAA;YACnB;QACF;QACAD,UAASrC,KAAa;YACpB,sHAAsH;YACtH,sBAAsB;YACtB,IAAI6C;YACJ,IAAKA,KAAMC,UAAkBC,UAAU,EAAG;gBACxC,yDAAyD;gBACzD,IAAIF,GAAGG,QAAQ,IAAI,KAAKC,IAAI,CAACJ,GAAGK,aAAa,GAAG,OAAOhH,QAAQC,OAAO;YACxE;YACA,OAAO2D,iBAAiBC,aAAaC,OAClCzD,IAAI,CAAC,CAAC4G,SACLjH,QAAQqG,GAAG,CACT9E,cACI0F,OAAO/C,OAAO,CAACvE,GAAG,CAAC,CAACiD,SAClBlB,eAAekB,OAAOoC,QAAQ,IAAI,aAEpC,EAAE,GAGT3E,IAAI,CAAC;gBACJgD,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC,IAAM,IAAI,CAAC6C,SAAS,CAACpC,OAAO,MAAMvD,KAAK,CAAC,KAAO;YACrE,GACCA,KAAK,CACJ,AACA,KAAO,qBADmB;QAGhC;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 874, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/is-plain-object.ts"], "sourcesContent": ["export function getObjectClassLabel(value: any): string {\n  return Object.prototype.toString.call(value)\n}\n\nexport function isPlainObject(value: any): boolean {\n  if (getObjectClassLabel(value) !== '[object Object]') {\n    return false\n  }\n\n  const prototype = Object.getPrototypeOf(value)\n\n  /**\n   * this used to be previously:\n   *\n   * `return prototype === null || prototype === Object.prototype`\n   *\n   * but Edge Runtime expose Object from vm, being that kind of type-checking wrongly fail.\n   *\n   * It was changed to the current implementation since it's resilient to serialization.\n   */\n  return prototype === null || prototype.hasOwnProperty('isPrototypeOf')\n}\n"], "names": ["getObjectClassLabel", "isPlainObject", "value", "Object", "prototype", "toString", "call", "getPrototypeOf", "hasOwnProperty"], "mappings": ";;;;;;;;;;;;;;;IAAgBA,mBAAmB,EAAA;eAAnBA;;IAIAC,aAAa,EAAA;eAAbA;;;AAJT,SAASD,oBAAoBE,KAAU;IAC5C,OAAOC,OAAOC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ;AACxC;AAEO,SAASD,cAAcC,KAAU;IACtC,IAAIF,oBAAoBE,WAAW,mBAAmB;QACpD,OAAO;IACT;IAEA,MAAME,YAAYD,OAAOI,cAAc,CAACL;IAExC;;;;;;;;GAQC,GACD,OAAOE,cAAc,QAAQA,UAAUI,cAAc,CAAC;AACxD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 919, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/lib/is-error.ts"], "sourcesContent": ["import { isPlainObject } from '../shared/lib/is-plain-object'\n\n// We allow some additional attached properties for Next.js errors\nexport interface NextError extends Error {\n  type?: string\n  page?: string\n  code?: string | number\n  cancelled?: boolean\n  digest?: number\n}\n\n/**\n * Checks whether the given value is a NextError.\n * This can be used to print a more detailed error message with properties like `code` & `digest`.\n */\nexport default function isError(err: unknown): err is NextError {\n  return (\n    typeof err === 'object' && err !== null && 'name' in err && 'message' in err\n  )\n}\n\nfunction safeStringify(obj: any) {\n  const seen = new WeakSet()\n\n  return JSON.stringify(obj, (_key, value) => {\n    // If value is an object and already seen, replace with \"[Circular]\"\n    if (typeof value === 'object' && value !== null) {\n      if (seen.has(value)) {\n        return '[Circular]'\n      }\n      seen.add(value)\n    }\n    return value\n  })\n}\n\nexport function getProperError(err: unknown): Error {\n  if (isError(err)) {\n    return err\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    // provide better error for case where `throw undefined`\n    // is called in development\n    if (typeof err === 'undefined') {\n      return new Error(\n        'An undefined error was thrown, ' +\n          'see here for more info: https://nextjs.org/docs/messages/threw-undefined'\n      )\n    }\n\n    if (err === null) {\n      return new Error(\n        'A null error was thrown, ' +\n          'see here for more info: https://nextjs.org/docs/messages/threw-undefined'\n      )\n    }\n  }\n\n  return new Error(isPlainObject(err) ? safeStringify(err) : err + '')\n}\n"], "names": ["isError", "getProperError", "err", "safeStringify", "obj", "seen", "WeakSet", "JSON", "stringify", "_key", "value", "has", "add", "process", "env", "NODE_ENV", "Error", "isPlainObject"], "mappings": ";;;;;;;;;;;;;;;IAWA;;;CAGC,GACD,OAIC,EAAA;eAJuBA;;IAqBRC,cAAc,EAAA;eAAdA;;;+BApCc;AAef,SAASD,QAAQE,GAAY;IAC1C,OACE,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,UAAUA,OAAO,aAAaA;AAE7E;AAEA,SAASC,cAAcC,GAAQ;IAC7B,MAAMC,OAAO,IAAIC;IAEjB,OAAOC,KAAKC,SAAS,CAACJ,KAAK,CAACK,MAAMC;QAChC,oEAAoE;QACpE,IAAI,OAAOA,UAAU,YAAYA,UAAU,MAAM;YAC/C,IAAIL,KAAKM,GAAG,CAACD,QAAQ;gBACnB,OAAO;YACT;YACAL,KAAKO,GAAG,CAACF;QACX;QACA,OAAOA;IACT;AACF;AAEO,SAAST,eAAeC,GAAY;IACzC,IAAIF,QAAQE,MAAM;QAChB,OAAOA;IACT;IAEA,IAAIW,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;QAC1C,wDAAwD;QACxD,2BAA2B;QAC3B,IAAI,OAAOb,QAAQ,aAAa;YAC9B,OAAO,OAAA,cAGN,CAHM,IAAIc,MACT,oCACE,6EAFG,qBAAA;uBAAA;4BAAA;8BAAA;YAGP;QACF;QAEA,IAAId,QAAQ,MAAM;YAChB,OAAO,OAAA,cAGN,CAHM,IAAIc,MACT,8BACE,6EAFG,qBAAA;uBAAA;4BAAA;8BAAA;YAGP;QACF;IACF;IAEA,OAAO,OAAA,cAA6D,CAA7D,IAAIA,MAAMC,CAAAA,GAAAA,eAAAA,aAAa,EAACf,OAAOC,cAAcD,OAAOA,MAAM,KAA1D,qBAAA;eAAA;oBAAA;sBAAA;IAA4D;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 994, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/sorted-routes.ts"], "sourcesContent": ["class UrlNode {\n  placeholder: boolean = true\n  children: Map<string, UrlNode> = new Map()\n  slugName: string | null = null\n  restSlugName: string | null = null\n  optionalRestSlugName: string | null = null\n\n  insert(urlPath: string): void {\n    this._insert(urlPath.split('/').filter(Boolean), [], false)\n  }\n\n  smoosh(): string[] {\n    return this._smoosh()\n  }\n\n  private _smoosh(prefix: string = '/'): string[] {\n    const childrenPaths = [...this.children.keys()].sort()\n    if (this.slugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[]'), 1)\n    }\n    if (this.restSlugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[...]'), 1)\n    }\n    if (this.optionalRestSlugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[[...]]'), 1)\n    }\n\n    const routes = childrenPaths\n      .map((c) => this.children.get(c)!._smoosh(`${prefix}${c}/`))\n      .reduce((prev, curr) => [...prev, ...curr], [])\n\n    if (this.slugName !== null) {\n      routes.push(\n        ...this.children.get('[]')!._smoosh(`${prefix}[${this.slugName}]/`)\n      )\n    }\n\n    if (!this.placeholder) {\n      const r = prefix === '/' ? '/' : prefix.slice(0, -1)\n      if (this.optionalRestSlugName != null) {\n        throw new Error(\n          `You cannot define a route with the same specificity as a optional catch-all route (\"${r}\" and \"${r}[[...${this.optionalRestSlugName}]]\").`\n        )\n      }\n\n      routes.unshift(r)\n    }\n\n    if (this.restSlugName !== null) {\n      routes.push(\n        ...this.children\n          .get('[...]')!\n          ._smoosh(`${prefix}[...${this.restSlugName}]/`)\n      )\n    }\n\n    if (this.optionalRestSlugName !== null) {\n      routes.push(\n        ...this.children\n          .get('[[...]]')!\n          ._smoosh(`${prefix}[[...${this.optionalRestSlugName}]]/`)\n      )\n    }\n\n    return routes\n  }\n\n  private _insert(\n    urlPaths: string[],\n    slugNames: string[],\n    isCatchAll: boolean\n  ): void {\n    if (urlPaths.length === 0) {\n      this.placeholder = false\n      return\n    }\n\n    if (isCatchAll) {\n      throw new Error(`Catch-all must be the last part of the URL.`)\n    }\n\n    // The next segment in the urlPaths list\n    let nextSegment = urlPaths[0]\n\n    // Check if the segment matches `[something]`\n    if (nextSegment.startsWith('[') && nextSegment.endsWith(']')) {\n      // Strip `[` and `]`, leaving only `something`\n      let segmentName = nextSegment.slice(1, -1)\n\n      let isOptional = false\n      if (segmentName.startsWith('[') && segmentName.endsWith(']')) {\n        // Strip optional `[` and `]`, leaving only `something`\n        segmentName = segmentName.slice(1, -1)\n        isOptional = true\n      }\n\n      if (segmentName.startsWith('…')) {\n        throw new Error(\n          `Detected a three-dot character ('…') at ('${segmentName}'). Did you mean ('...')?`\n        )\n      }\n\n      if (segmentName.startsWith('...')) {\n        // Strip `...`, leaving only `something`\n        segmentName = segmentName.substring(3)\n        isCatchAll = true\n      }\n\n      if (segmentName.startsWith('[') || segmentName.endsWith(']')) {\n        throw new Error(\n          `Segment names may not start or end with extra brackets ('${segmentName}').`\n        )\n      }\n\n      if (segmentName.startsWith('.')) {\n        throw new Error(\n          `Segment names may not start with erroneous periods ('${segmentName}').`\n        )\n      }\n\n      function handleSlug(previousSlug: string | null, nextSlug: string) {\n        if (previousSlug !== null) {\n          // If the specific segment already has a slug but the slug is not `something`\n          // This prevents collisions like:\n          // pages/[post]/index.js\n          // pages/[id]/index.js\n          // Because currently multiple dynamic params on the same segment level are not supported\n          if (previousSlug !== nextSlug) {\n            // TODO: This error seems to be confusing for users, needs an error link, the description can be based on above comment.\n            throw new Error(\n              `You cannot use different slug names for the same dynamic path ('${previousSlug}' !== '${nextSlug}').`\n            )\n          }\n        }\n\n        slugNames.forEach((slug) => {\n          if (slug === nextSlug) {\n            throw new Error(\n              `You cannot have the same slug name \"${nextSlug}\" repeat within a single dynamic path`\n            )\n          }\n\n          if (slug.replace(/\\W/g, '') === nextSegment.replace(/\\W/g, '')) {\n            throw new Error(\n              `You cannot have the slug names \"${slug}\" and \"${nextSlug}\" differ only by non-word symbols within a single dynamic path`\n            )\n          }\n        })\n\n        slugNames.push(nextSlug)\n      }\n\n      if (isCatchAll) {\n        if (isOptional) {\n          if (this.restSlugName != null) {\n            throw new Error(\n              `You cannot use both an required and optional catch-all route at the same level (\"[...${this.restSlugName}]\" and \"${urlPaths[0]}\" ).`\n            )\n          }\n\n          handleSlug(this.optionalRestSlugName, segmentName)\n          // slugName is kept as it can only be one particular slugName\n          this.optionalRestSlugName = segmentName\n          // nextSegment is overwritten to [[...]] so that it can later be sorted specifically\n          nextSegment = '[[...]]'\n        } else {\n          if (this.optionalRestSlugName != null) {\n            throw new Error(\n              `You cannot use both an optional and required catch-all route at the same level (\"[[...${this.optionalRestSlugName}]]\" and \"${urlPaths[0]}\").`\n            )\n          }\n\n          handleSlug(this.restSlugName, segmentName)\n          // slugName is kept as it can only be one particular slugName\n          this.restSlugName = segmentName\n          // nextSegment is overwritten to [...] so that it can later be sorted specifically\n          nextSegment = '[...]'\n        }\n      } else {\n        if (isOptional) {\n          throw new Error(\n            `Optional route parameters are not yet supported (\"${urlPaths[0]}\").`\n          )\n        }\n        handleSlug(this.slugName, segmentName)\n        // slugName is kept as it can only be one particular slugName\n        this.slugName = segmentName\n        // nextSegment is overwritten to [] so that it can later be sorted specifically\n        nextSegment = '[]'\n      }\n    }\n\n    // If this UrlNode doesn't have the nextSegment yet we create a new child UrlNode\n    if (!this.children.has(nextSegment)) {\n      this.children.set(nextSegment, new UrlNode())\n    }\n\n    this.children\n      .get(nextSegment)!\n      ._insert(urlPaths.slice(1), slugNames, isCatchAll)\n  }\n}\n\nexport function getSortedRoutes(\n  normalizedPages: ReadonlyArray<string>\n): string[] {\n  // First the UrlNode is created, and every UrlNode can have only 1 dynamic segment\n  // Eg you can't have pages/[post]/abc.js and pages/[hello]/something-else.js\n  // Only 1 dynamic segment per nesting level\n\n  // So in the case that is test/integration/dynamic-routing it'll be this:\n  // pages/[post]/comments.js\n  // pages/blog/[post]/comment/[id].js\n  // Both are fine because `pages/[post]` and `pages/blog` are on the same level\n  // So in this case `UrlNode` created here has `this.slugName === 'post'`\n  // And since your PR passed through `slugName` as an array basically it'd including it in too many possibilities\n  // Instead what has to be passed through is the upwards path's dynamic names\n  const root = new UrlNode()\n\n  // Here the `root` gets injected multiple paths, and insert will break them up into sublevels\n  normalizedPages.forEach((pagePath) => root.insert(pagePath))\n  // Smoosh will then sort those sublevels up to the point where you get the correct route definition priority\n  return root.smoosh()\n}\n\nexport function getSortedRouteObjects<T>(\n  objects: T[],\n  getter: (obj: T) => string\n): T[] {\n  // We're assuming here that all the pathnames are unique, that way we can\n  // sort the list and use the index as the key.\n  const indexes: Record<string, number> = {}\n  const pathnames: string[] = []\n  for (let i = 0; i < objects.length; i++) {\n    const pathname = getter(objects[i])\n    indexes[pathname] = i\n    pathnames[i] = pathname\n  }\n\n  // Sort the pathnames.\n  const sorted = getSortedRoutes(pathnames)\n\n  // Map the sorted pathnames back to the original objects using the new sorted\n  // index.\n  return sorted.map((pathname) => objects[indexes[pathname]])\n}\n"], "names": ["getSortedRouteObjects", "getSortedRoutes", "UrlNode", "insert", "url<PERSON><PERSON>", "_insert", "split", "filter", "Boolean", "smoosh", "_smoosh", "prefix", "childrenPaths", "children", "keys", "sort", "slug<PERSON><PERSON>", "splice", "indexOf", "restSlugName", "optionalRestSlugName", "routes", "map", "c", "get", "reduce", "prev", "curr", "push", "placeholder", "r", "slice", "Error", "unshift", "url<PERSON><PERSON>s", "slug<PERSON><PERSON><PERSON>", "isCatchAll", "length", "nextSegment", "startsWith", "endsWith", "segmentName", "isOptional", "substring", "handleSlug", "previousSlug", "nextSlug", "for<PERSON>ach", "slug", "replace", "has", "set", "Map", "normalizedPages", "root", "pagePath", "objects", "getter", "indexes", "pathnames", "i", "pathname", "sorted"], "mappings": ";;;;;;;;;;;;;;;IAiOgBA,qBAAqB,EAAA;eAArBA;;IAtBAC,eAAe,EAAA;eAAfA;;;AA3MhB,MAAMC;IAOJC,OAAOC,OAAe,EAAQ;QAC5B,IAAI,CAACC,OAAO,CAACD,QAAQE,KAAK,CAAC,KAAKC,MAAM,CAACC,UAAU,EAAE,EAAE;IACvD;IAEAC,SAAmB;QACjB,OAAO,IAAI,CAACC,OAAO;IACrB;IAEQA,QAAQC,MAAoB,EAAY;QAAhCA,IAAAA,WAAAA,KAAAA,GAAAA,SAAiB;QAC/B,MAAMC,gBAAgB;eAAI,IAAI,CAACC,QAAQ,CAACC,IAAI;SAAG,CAACC,IAAI;QACpD,IAAI,IAAI,CAACC,QAAQ,KAAK,MAAM;YAC1BJ,cAAcK,MAAM,CAACL,cAAcM,OAAO,CAAC,OAAO;QACpD;QACA,IAAI,IAAI,CAACC,YAAY,KAAK,MAAM;YAC9BP,cAAcK,MAAM,CAACL,cAAcM,OAAO,CAAC,UAAU;QACvD;QACA,IAAI,IAAI,CAACE,oBAAoB,KAAK,MAAM;YACtCR,cAAcK,MAAM,CAACL,cAAcM,OAAO,CAAC,YAAY;QACzD;QAEA,MAAMG,SAAST,cACZU,GAAG,CAAC,CAACC,IAAM,IAAI,CAACV,QAAQ,CAACW,GAAG,CAACD,GAAIb,OAAO,CAAE,KAAEC,SAASY,IAAE,MACvDE,MAAM,CAAC,CAACC,MAAMC,OAAS;mBAAID;mBAASC;aAAK,EAAE,EAAE;QAEhD,IAAI,IAAI,CAACX,QAAQ,KAAK,MAAM;YAC1BK,OAAOO,IAAI,IACN,IAAI,CAACf,QAAQ,CAACW,GAAG,CAAC,MAAOd,OAAO,CAAIC,SAAO,MAAG,IAAI,CAACK,QAAQ,GAAC;QAEnE;QAEA,IAAI,CAAC,IAAI,CAACa,WAAW,EAAE;YACrB,MAAMC,IAAInB,WAAW,MAAM,MAAMA,OAAOoB,KAAK,CAAC,GAAG,CAAC;YAClD,IAAI,IAAI,CAACX,oBAAoB,IAAI,MAAM;gBACrC,MAAM,OAAA,cAEL,CAFK,IAAIY,MACP,yFAAsFF,IAAE,YAASA,IAAE,UAAO,IAAI,CAACV,oBAAoB,GAAC,UADjI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAC,OAAOY,OAAO,CAACH;QACjB;QAEA,IAAI,IAAI,CAACX,YAAY,KAAK,MAAM;YAC9BE,OAAOO,IAAI,IACN,IAAI,CAACf,QAAQ,CACbW,GAAG,CAAC,SACJd,OAAO,CAAIC,SAAO,SAAM,IAAI,CAACQ,YAAY,GAAC;QAEjD;QAEA,IAAI,IAAI,CAACC,oBAAoB,KAAK,MAAM;YACtCC,OAAOO,IAAI,IACN,IAAI,CAACf,QAAQ,CACbW,GAAG,CAAC,WACJd,OAAO,CAAIC,SAAO,UAAO,IAAI,CAACS,oBAAoB,GAAC;QAE1D;QAEA,OAAOC;IACT;IAEQhB,QACN6B,QAAkB,EAClBC,SAAmB,EACnBC,UAAmB,EACb;QACN,IAAIF,SAASG,MAAM,KAAK,GAAG;YACzB,IAAI,CAACR,WAAW,GAAG;YACnB;QACF;QAEA,IAAIO,YAAY;YACd,MAAM,OAAA,cAAwD,CAAxD,IAAIJ,MAAO,gDAAX,qBAAA;uBAAA;4BAAA;8BAAA;YAAuD;QAC/D;QAEA,wCAAwC;QACxC,IAAIM,cAAcJ,QAAQ,CAAC,EAAE;QAE7B,6CAA6C;QAC7C,IAAII,YAAYC,UAAU,CAAC,QAAQD,YAAYE,QAAQ,CAAC,MAAM;YAC5D,8CAA8C;YAC9C,IAAIC,cAAcH,YAAYP,KAAK,CAAC,GAAG,CAAC;YAExC,IAAIW,aAAa;YACjB,IAAID,YAAYF,UAAU,CAAC,QAAQE,YAAYD,QAAQ,CAAC,MAAM;gBAC5D,uDAAuD;gBACvDC,cAAcA,YAAYV,KAAK,CAAC,GAAG,CAAC;gBACpCW,aAAa;YACf;YAEA,IAAID,YAAYF,UAAU,CAAC,MAAM;gBAC/B,MAAM,OAAA,cAEL,CAFK,IAAIP,MACP,+CAA4CS,cAAY,8BADrD,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIA,YAAYF,UAAU,CAAC,QAAQ;gBACjC,wCAAwC;gBACxCE,cAAcA,YAAYE,SAAS,CAAC;gBACpCP,aAAa;YACf;YAEA,IAAIK,YAAYF,UAAU,CAAC,QAAQE,YAAYD,QAAQ,CAAC,MAAM;gBAC5D,MAAM,OAAA,cAEL,CAFK,IAAIR,MACP,8DAA2DS,cAAY,QADpE,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIA,YAAYF,UAAU,CAAC,MAAM;gBAC/B,MAAM,OAAA,cAEL,CAFK,IAAIP,MACP,0DAAuDS,cAAY,QADhE,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,SAASG,WAAWC,YAA2B,EAAEC,QAAgB;gBAC/D,IAAID,iBAAiB,MAAM;oBACzB,6EAA6E;oBAC7E,iCAAiC;oBACjC,wBAAwB;oBACxB,sBAAsB;oBACtB,wFAAwF;oBACxF,IAAIA,iBAAiBC,UAAU;wBAC7B,wHAAwH;wBACxH,MAAM,OAAA,cAEL,CAFK,IAAId,MACP,qEAAkEa,eAAa,YAASC,WAAS,QAD9F,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;gBAEAX,UAAUY,OAAO,CAAC,CAACC;oBACjB,IAAIA,SAASF,UAAU;wBACrB,MAAM,OAAA,cAEL,CAFK,IAAId,MACP,yCAAsCc,WAAS,0CAD5C,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEA,IAAIE,KAAKC,OAAO,CAAC,OAAO,QAAQX,YAAYW,OAAO,CAAC,OAAO,KAAK;wBAC9D,MAAM,OAAA,cAEL,CAFK,IAAIjB,MACP,qCAAkCgB,OAAK,YAASF,WAAS,mEADtD,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;gBAEAX,UAAUP,IAAI,CAACkB;YACjB;YAEA,IAAIV,YAAY;gBACd,IAAIM,YAAY;oBACd,IAAI,IAAI,CAACvB,YAAY,IAAI,MAAM;wBAC7B,MAAM,OAAA,cAEL,CAFK,IAAIa,MACP,0FAAuF,IAAI,CAACb,YAAY,GAAC,aAAUe,QAAQ,CAAC,EAAE,GAAC,SAD5H,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEAU,WAAW,IAAI,CAACxB,oBAAoB,EAAEqB;oBACtC,6DAA6D;oBAC7D,IAAI,CAACrB,oBAAoB,GAAGqB;oBAC5B,oFAAoF;oBACpFH,cAAc;gBAChB,OAAO;oBACL,IAAI,IAAI,CAAClB,oBAAoB,IAAI,MAAM;wBACrC,MAAM,OAAA,cAEL,CAFK,IAAIY,MACP,2FAAwF,IAAI,CAACZ,oBAAoB,GAAC,cAAWc,QAAQ,CAAC,EAAE,GAAC,QADtI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEAU,WAAW,IAAI,CAACzB,YAAY,EAAEsB;oBAC9B,6DAA6D;oBAC7D,IAAI,CAACtB,YAAY,GAAGsB;oBACpB,kFAAkF;oBAClFH,cAAc;gBAChB;YACF,OAAO;gBACL,IAAII,YAAY;oBACd,MAAM,OAAA,cAEL,CAFK,IAAIV,MACP,uDAAoDE,QAAQ,CAAC,EAAE,GAAC,QAD7D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACAU,WAAW,IAAI,CAAC5B,QAAQ,EAAEyB;gBAC1B,6DAA6D;gBAC7D,IAAI,CAACzB,QAAQ,GAAGyB;gBAChB,+EAA+E;gBAC/EH,cAAc;YAChB;QACF;QAEA,iFAAiF;QACjF,IAAI,CAAC,IAAI,CAACzB,QAAQ,CAACqC,GAAG,CAACZ,cAAc;YACnC,IAAI,CAACzB,QAAQ,CAACsC,GAAG,CAACb,aAAa,IAAIpC;QACrC;QAEA,IAAI,CAACW,QAAQ,CACVW,GAAG,CAACc,aACJjC,OAAO,CAAC6B,SAASH,KAAK,CAAC,IAAII,WAAWC;IAC3C;;aAvMAP,WAAAA,GAAuB;aACvBhB,QAAAA,GAAiC,IAAIuC;aACrCpC,QAAAA,GAA0B;aAC1BG,YAAAA,GAA8B;aAC9BC,oBAAAA,GAAsC;;AAoMxC;AAEO,SAASnB,gBACdoD,eAAsC;IAEtC,kFAAkF;IAClF,4EAA4E;IAC5E,2CAA2C;IAE3C,yEAAyE;IACzE,2BAA2B;IAC3B,oCAAoC;IACpC,8EAA8E;IAC9E,wEAAwE;IACxE,gHAAgH;IAChH,4EAA4E;IAC5E,MAAMC,OAAO,IAAIpD;IAEjB,6FAA6F;IAC7FmD,gBAAgBN,OAAO,CAAC,CAACQ,WAAaD,KAAKnD,MAAM,CAACoD;IAClD,4GAA4G;IAC5G,OAAOD,KAAK7C,MAAM;AACpB;AAEO,SAAST,sBACdwD,OAAY,EACZC,MAA0B;IAE1B,yEAAyE;IACzE,8CAA8C;IAC9C,MAAMC,UAAkC,CAAC;IACzC,MAAMC,YAAsB,EAAE;IAC9B,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,QAAQnB,MAAM,EAAEuB,IAAK;QACvC,MAAMC,WAAWJ,OAAOD,OAAO,CAACI,EAAE;QAClCF,OAAO,CAACG,SAAS,GAAGD;QACpBD,SAAS,CAACC,EAAE,GAAGC;IACjB;IAEA,sBAAsB;IACtB,MAAMC,SAAS7D,gBAAgB0D;IAE/B,6EAA6E;IAC7E,SAAS;IACT,OAAOG,OAAOxC,GAAG,CAAC,CAACuC,WAAaL,OAAO,CAACE,OAAO,CAACG,SAAS,CAAC;AAC5D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1242, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/page-path/ensure-leading-slash.ts"], "sourcesContent": ["/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */\nexport function ensureLeadingSlash(path: string) {\n  return path.startsWith('/') ? path : `/${path}`\n}\n"], "names": ["ensureLeadingSlash", "path", "startsWith"], "mappings": "AAAA;;;CAGC,GAAA;;;;+BACeA,sBAAAA;;;eAAAA;;;AAAT,SAASA,mBAAmBC,IAAY;IAC7C,OAAOA,KAAKC,UAAU,CAAC,OAAOD,OAAQ,MAAGA;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1263, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/app-paths.ts"], "sourcesContent": ["import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash'\nimport { isGroupSegment } from '../../segment'\n\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */\nexport function normalizeAppPath(route: string) {\n  return ensureLeadingSlash(\n    route.split('/').reduce((pathname, segment, index, segments) => {\n      // Empty segments are ignored.\n      if (!segment) {\n        return pathname\n      }\n\n      // Groups are ignored.\n      if (isGroupSegment(segment)) {\n        return pathname\n      }\n\n      // Parallel segments are ignored.\n      if (segment[0] === '@') {\n        return pathname\n      }\n\n      // The last segment (if it's a leaf) should be ignored.\n      if (\n        (segment === 'page' || segment === 'route') &&\n        index === segments.length - 1\n      ) {\n        return pathname\n      }\n\n      return `${pathname}/${segment}`\n    }, '')\n  )\n}\n\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */\nexport function normalizeRscURL(url: string) {\n  return url.replace(\n    /\\.rsc($|\\?)/,\n    // $1 ensures `?` is preserved\n    '$1'\n  )\n}\n"], "names": ["normalizeAppPath", "normalizeRscURL", "route", "ensureLeadingSlash", "split", "reduce", "pathname", "segment", "index", "segments", "isGroupSegment", "length", "url", "replace"], "mappings": ";;;;;;;;;;;;;;;IAsBgBA,gBAAgB,EAAA;eAAhBA;;IAmCAC,eAAe,EAAA;eAAfA;;;oCAzDmB;yBACJ;AAqBxB,SAASD,iBAAiBE,KAAa;IAC5C,OAAOC,CAAAA,GAAAA,oBAAAA,kBAAkB,EACvBD,MAAME,KAAK,CAAC,KAAKC,MAAM,CAAC,CAACC,UAAUC,SAASC,OAAOC;QACjD,8BAA8B;QAC9B,IAAI,CAACF,SAAS;YACZ,OAAOD;QACT;QAEA,sBAAsB;QACtB,IAAII,CAAAA,GAAAA,SAAAA,cAAc,EAACH,UAAU;YAC3B,OAAOD;QACT;QAEA,iCAAiC;QACjC,IAAIC,OAAO,CAAC,EAAE,KAAK,KAAK;YACtB,OAAOD;QACT;QAEA,uDAAuD;QACvD,IACGC,CAAAA,YAAY,UAAUA,YAAY,OAAM,KACzCC,UAAUC,SAASE,MAAM,GAAG,GAC5B;YACA,OAAOL;QACT;QAEA,OAAUA,WAAS,MAAGC;IACxB,GAAG;AAEP;AAMO,SAASN,gBAAgBW,GAAW;IACzC,OAAOA,IAAIC,OAAO,CAChB,eACA,AACA,8BAD8B;AAGlC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1316, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/interception-routes.ts"], "sourcesContent": ["import { normalizeAppPath } from './app-paths'\n\n// order matters here, the first match will be used\nexport const INTERCEPTION_ROUTE_MARKERS = [\n  '(..)(..)',\n  '(.)',\n  '(..)',\n  '(...)',\n] as const\n\nexport function isInterceptionRouteAppPath(path: string): boolean {\n  // TODO-APP: add more serious validation\n  return (\n    path\n      .split('/')\n      .find((segment) =>\n        INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n      ) !== undefined\n  )\n}\n\nexport function extractInterceptionRouteInformation(path: string) {\n  let interceptingRoute: string | undefined,\n    marker: (typeof INTERCEPTION_ROUTE_MARKERS)[number] | undefined,\n    interceptedRoute: string | undefined\n\n  for (const segment of path.split('/')) {\n    marker = INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n    if (marker) {\n      ;[interceptingRoute, interceptedRoute] = path.split(marker, 2)\n      break\n    }\n  }\n\n  if (!interceptingRoute || !marker || !interceptedRoute) {\n    throw new Error(\n      `Invalid interception route: ${path}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`\n    )\n  }\n\n  interceptingRoute = normalizeAppPath(interceptingRoute) // normalize the path, e.g. /(blog)/feed -> /feed\n\n  switch (marker) {\n    case '(.)':\n      // (.) indicates that we should match with sibling routes, so we just need to append the intercepted route to the intercepting route\n      if (interceptingRoute === '/') {\n        interceptedRoute = `/${interceptedRoute}`\n      } else {\n        interceptedRoute = interceptingRoute + '/' + interceptedRoute\n      }\n      break\n    case '(..)':\n      // (..) indicates that we should match at one level up, so we need to remove the last segment of the intercepting route\n      if (interceptingRoute === '/') {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..) marker at the root level, use (.) instead.`\n        )\n      }\n      interceptedRoute = interceptingRoute\n        .split('/')\n        .slice(0, -1)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    case '(...)':\n      // (...) will match the route segment in the root directory, so we need to use the root directory to prepend the intercepted route\n      interceptedRoute = '/' + interceptedRoute\n      break\n    case '(..)(..)':\n      // (..)(..) indicates that we should match at two levels up, so we need to remove the last two segments of the intercepting route\n\n      const splitInterceptingRoute = interceptingRoute.split('/')\n      if (splitInterceptingRoute.length <= 2) {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..)(..) marker at the root level or one level up.`\n        )\n      }\n\n      interceptedRoute = splitInterceptingRoute\n        .slice(0, -2)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    default:\n      throw new Error('Invariant: unexpected marker')\n  }\n\n  return { interceptingRoute, interceptedRoute }\n}\n"], "names": ["INTERCEPTION_ROUTE_MARKERS", "extractInterceptionRouteInformation", "isInterceptionRouteAppPath", "path", "split", "find", "segment", "m", "startsWith", "undefined", "interceptingRoute", "marker", "interceptedRoute", "Error", "normalizeAppPath", "slice", "concat", "join", "splitInterceptingRoute", "length"], "mappings": ";;;;;;;;;;;;;;;;IAGaA,0BAA0B,EAAA;eAA1BA;;IAkBGC,mCAAmC,EAAA;eAAnCA;;IAXAC,0BAA0B,EAAA;eAA1BA;;;0BAViB;AAG1B,MAAMF,6BAA6B;IACxC;IACA;IACA;IACA;CACD;AAEM,SAASE,2BAA2BC,IAAY;IACrD,wCAAwC;IACxC,OACEA,KACGC,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UACLN,2BAA2BK,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD,SACtDE;AAEZ;AAEO,SAASR,oCAAoCE,IAAY;IAC9D,IAAIO,mBACFC,QACAC;IAEF,KAAK,MAAMN,WAAWH,KAAKC,KAAK,CAAC,KAAM;QACrCO,SAASX,2BAA2BK,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD;QACnE,IAAII,QAAQ;;YACT,CAACD,mBAAmBE,iBAAiB,GAAGT,KAAKC,KAAK,CAACO,QAAQ;YAC5D;QACF;IACF;IAEA,IAAI,CAACD,qBAAqB,CAACC,UAAU,CAACC,kBAAkB;QACtD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,iCAA8BV,OAAK,sFADhC,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEAO,oBAAoBI,CAAAA,GAAAA,UAAAA,gBAAgB,EAACJ,mBAAmB,iDAAiD;;IAEzG,OAAQC;QACN,KAAK;YACH,oIAAoI;YACpI,IAAID,sBAAsB,KAAK;gBAC7BE,mBAAoB,MAAGA;YACzB,OAAO;gBACLA,mBAAmBF,oBAAoB,MAAME;YAC/C;YACA;QACF,KAAK;YACH,uHAAuH;YACvH,IAAIF,sBAAsB,KAAK;gBAC7B,MAAM,OAAA,cAEL,CAFK,IAAIG,MACP,iCAA8BV,OAAK,iEADhC,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACAS,mBAAmBF,kBAChBN,KAAK,CAAC,KACNW,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACJ,kBACPK,IAAI,CAAC;YACR;QACF,KAAK;YACH,kIAAkI;YAClIL,mBAAmB,MAAMA;YACzB;QACF,KAAK;YACH,iIAAiI;YAEjI,MAAMM,yBAAyBR,kBAAkBN,KAAK,CAAC;YACvD,IAAIc,uBAAuBC,MAAM,IAAI,GAAG;gBACtC,MAAM,OAAA,cAEL,CAFK,IAAIN,MACP,iCAA8BV,OAAK,oEADhC,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAS,mBAAmBM,uBAChBH,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACJ,kBACPK,IAAI,CAAC;YACR;QACF;YACE,MAAM,OAAA,cAAyC,CAAzC,IAAIJ,MAAM,iCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAwC;IAClD;IAEA,OAAO;QAAEH;QAAmBE;IAAiB;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1425, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/is-dynamic.ts"], "sourcesContent": ["import {\n  extractInterceptionRouteInformation,\n  isInterceptionRouteAppPath,\n} from './interception-routes'\n\n// Identify /.*[param].*/ in route string\nconst TEST_ROUTE = /\\/[^/]*\\[[^/]+\\][^/]*(?=\\/|$)/\n\n// Identify /[param]/ in route string\nconst TEST_STRICT_ROUTE = /\\/\\[[^/]+\\](?=\\/|$)/\n\n/**\n * Check if a route is dynamic.\n *\n * @param route - The route to check.\n * @param strict - Whether to use strict mode which prohibits segments with prefixes/suffixes (default: true).\n * @returns Whether the route is dynamic.\n */\nexport function isDynamicRoute(route: string, strict: boolean = true): boolean {\n  if (isInterceptionRouteAppPath(route)) {\n    route = extractInterceptionRouteInformation(route).interceptedRoute\n  }\n\n  if (strict) {\n    return TEST_STRICT_ROUTE.test(route)\n  }\n\n  return TEST_ROUTE.test(route)\n}\n"], "names": ["isDynamicRoute", "TEST_ROUTE", "TEST_STRICT_ROUTE", "route", "strict", "isInterceptionRouteAppPath", "extractInterceptionRouteInformation", "interceptedRoute", "test"], "mappings": ";;;;+BAkBgBA,kBAAAA;;;eAAAA;;;oCAfT;AAEP,yCAAyC;AACzC,MAAMC,aAAa;AAEnB,qCAAqC;AACrC,MAAMC,oBAAoB;AASnB,SAASF,eAAeG,KAAa,EAAEC,MAAsB;IAAtBA,IAAAA,WAAAA,KAAAA,GAAAA,SAAkB;IAC9D,IAAIC,CAAAA,GAAAA,oBAAAA,0BAA0B,EAACF,QAAQ;QACrCA,QAAQG,CAAAA,GAAAA,oBAAAA,mCAAmC,EAACH,OAAOI,gBAAgB;IACrE;IAEA,IAAIH,QAAQ;QACV,OAAOF,kBAAkBM,IAAI,CAACL;IAChC;IAEA,OAAOF,WAAWO,IAAI,CAACL;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1455, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/index.ts"], "sourcesContent": ["export { getSortedRoutes, getSortedRouteObjects } from './sorted-routes'\nexport { isDynamicRoute } from './is-dynamic'\n"], "names": ["getSortedRouteObjects", "getSortedRoutes", "isDynamicRoute"], "mappings": ";;;;;;;;;;;;;;;;IAA0BA,qBAAqB,EAAA;eAArBA,cAAAA,qBAAqB;;IAAtCC,eAAe,EAAA;eAAfA,cAAAA,eAAe;;IACfC,cAAc,EAAA;eAAdA,WAAAA,cAAc;;;8BADgC;2BACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1488, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/page-path/normalize-path-sep.ts"], "sourcesContent": ["/**\n * For a given page path, this function ensures that there is no backslash\n * escaping slashes in the path. Example:\n *  - `foo\\/bar\\/baz` -> `foo/bar/baz`\n */\nexport function normalizePathSep(path: string): string {\n  return path.replace(/\\\\/g, '/')\n}\n"], "names": ["normalizePathSep", "path", "replace"], "mappings": "AAAA;;;;CAIC,GAAA;;;;+BACeA,oBAAAA;;;eAAAA;;;AAAT,SAASA,iBAAiBC,IAAY;IAC3C,OAAOA,KAAKC,OAAO,CAAC,OAAO;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1510, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/page-path/denormalize-page-path.ts"], "sourcesContent": ["import { isDynamicRoute } from '../router/utils'\nimport { normalizePathSep } from './normalize-path-sep'\n\n/**\n * Performs the opposite transformation of `normalizePagePath`. Note that\n * this function is not idempotent either in cases where there are multiple\n * leading `/index` for the page. Examples:\n *  - `/index` -> `/`\n *  - `/index/foo` -> `/foo`\n *  - `/index/index` -> `/index`\n */\nexport function denormalizePagePath(page: string) {\n  let _page = normalizePathSep(page)\n  return _page.startsWith('/index/') && !isDynamicRoute(_page)\n    ? _page.slice(6)\n    : _page !== '/index'\n      ? _page\n      : '/'\n}\n"], "names": ["denormalizePagePath", "page", "_page", "normalizePathSep", "startsWith", "isDynamicRoute", "slice"], "mappings": ";;;;+BAWgBA,uBAAAA;;;eAAAA;;;uBAXe;kCACE;AAU1B,SAASA,oBAAoBC,IAAY;IAC9C,IAAIC,QAAQC,CAAAA,GAAAA,kBAAAA,gBAAgB,EAACF;IAC7B,OAAOC,MAAME,UAAU,CAAC,cAAc,CAACC,CAAAA,GAAAA,OAAAA,cAAc,EAACH,SAClDA,MAAMI,KAAK,CAAC,KACZJ,UAAU,WACRA,QACA;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1531, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/i18n/normalize-locale-path.ts"], "sourcesContent": ["export interface PathLocale {\n  detectedLocale?: string\n  pathname: string\n}\n\n/**\n * A cache of lowercased locales for each list of locales. This is stored as a\n * WeakMap so if the locales are garbage collected, the cache entry will be\n * removed as well.\n */\nconst cache = new WeakMap<readonly string[], readonly string[]>()\n\n/**\n * For a pathname that may include a locale from a list of locales, it\n * removes the locale from the pathname returning it alongside with the\n * detected locale.\n *\n * @param pathname A pathname that may include a locale.\n * @param locales A list of locales.\n * @returns The detected locale and pathname without locale\n */\nexport function normalizeLocalePath(\n  pathname: string,\n  locales?: readonly string[]\n): PathLocale {\n  // If locales is undefined, return the pathname as is.\n  if (!locales) return { pathname }\n\n  // Get the cached lowercased locales or create a new cache entry.\n  let lowercasedLocales = cache.get(locales)\n  if (!lowercasedLocales) {\n    lowercasedLocales = locales.map((locale) => locale.toLowerCase())\n    cache.set(locales, lowercasedLocales)\n  }\n\n  let detectedLocale: string | undefined\n\n  // The first segment will be empty, because it has a leading `/`. If\n  // there is no further segment, there is no locale (or it's the default).\n  const segments = pathname.split('/', 2)\n\n  // If there's no second segment (ie, the pathname is just `/`), there's no\n  // locale.\n  if (!segments[1]) return { pathname }\n\n  // The second segment will contain the locale part if any.\n  const segment = segments[1].toLowerCase()\n\n  // See if the segment matches one of the locales. If it doesn't, there is\n  // no locale (or it's the default).\n  const index = lowercasedLocales.indexOf(segment)\n  if (index < 0) return { pathname }\n\n  // Return the case-sensitive locale.\n  detectedLocale = locales[index]\n\n  // Remove the `/${locale}` part of the pathname.\n  pathname = pathname.slice(detectedLocale.length + 1) || '/'\n\n  return { pathname, detectedLocale }\n}\n"], "names": ["normalizeLocalePath", "cache", "WeakMap", "pathname", "locales", "lowercasedLocales", "get", "map", "locale", "toLowerCase", "set", "detectedLocale", "segments", "split", "segment", "index", "indexOf", "slice", "length"], "mappings": ";;;;+BAqBgBA,uBAAAA;;;eAAAA;;;AAhBhB;;;;CAIC,GACD,MAAMC,QAAQ,IAAIC;AAWX,SAASF,oBACdG,QAAgB,EAChBC,OAA2B;IAE3B,sDAAsD;IACtD,IAAI,CAACA,SAAS,OAAO;QAAED;IAAS;IAEhC,iEAAiE;IACjE,IAAIE,oBAAoBJ,MAAMK,GAAG,CAACF;IAClC,IAAI,CAACC,mBAAmB;QACtBA,oBAAoBD,QAAQG,GAAG,CAAC,CAACC,SAAWA,OAAOC,WAAW;QAC9DR,MAAMS,GAAG,CAACN,SAASC;IACrB;IAEA,IAAIM;IAEJ,oEAAoE;IACpE,yEAAyE;IACzE,MAAMC,WAAWT,SAASU,KAAK,CAAC,KAAK;IAErC,0EAA0E;IAC1E,UAAU;IACV,IAAI,CAACD,QAAQ,CAAC,EAAE,EAAE,OAAO;QAAET;IAAS;IAEpC,0DAA0D;IAC1D,MAAMW,UAAUF,QAAQ,CAAC,EAAE,CAACH,WAAW;IAEvC,yEAAyE;IACzE,mCAAmC;IACnC,MAAMM,QAAQV,kBAAkBW,OAAO,CAACF;IACxC,IAAIC,QAAQ,GAAG,OAAO;QAAEZ;IAAS;IAEjC,oCAAoC;IACpCQ,iBAAiBP,OAAO,CAACW,MAAM;IAE/B,gDAAgD;IAChDZ,WAAWA,SAASc,KAAK,CAACN,eAAeO,MAAM,GAAG,MAAM;IAExD,OAAO;QAAEf;QAAUQ;IAAe;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1588, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/mitt.ts"], "sourcesContent": ["/*\nMIT License\n\nCopyright (c) <PERSON> (https://jasonformat.com/)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/\n\n// This file is based on https://github.com/developit/mitt/blob/v1.1.3/src/index.js\n// It's been edited for the needs of this script\n// See the LICENSE at the top of the file\n\ntype Handler = (...evts: any[]) => void\n\nexport type MittEmitter<T> = {\n  on(type: T, handler: Handler): void\n  off(type: T, handler: Handler): void\n  emit(type: T, ...evts: any[]): void\n}\n\nexport default function mitt(): MittEmitter<string> {\n  const all: { [s: string]: Handler[] } = Object.create(null)\n\n  return {\n    on(type: string, handler: Handler) {\n      ;(all[type] || (all[type] = [])).push(handler)\n    },\n\n    off(type: string, handler: Handler) {\n      if (all[type]) {\n        all[type].splice(all[type].indexOf(handler) >>> 0, 1)\n      }\n    },\n\n    emit(type: string, ...evts: any[]) {\n      // eslint-disable-next-line array-callback-return\n      ;(all[type] || []).slice().map((handler: Handler) => {\n        handler(...evts)\n      })\n    },\n  }\n}\n"], "names": ["mitt", "all", "Object", "create", "on", "type", "handler", "push", "off", "splice", "indexOf", "emit", "evts", "slice", "map"], "mappings": "AAAA;;;;;;;;;;AAUA,GAEA,mFAAmF;AACnF,gDAAgD;AAChD,yCAAyC;;;;;+BAUzC,WAAA;;;eAAwBA;;;AAAT,SAASA;IACtB,MAAMC,MAAkCC,OAAOC,MAAM,CAAC;IAEtD,OAAO;QACLC,IAAGC,IAAY,EAAEC,OAAgB;;YAC7BL,CAAAA,GAAG,CAACI,KAAK,IAAKJ,CAAAA,GAAG,CAACI,KAAK,GAAG,EAAC,CAAC,EAAGE,IAAI,CAACD;QACxC;QAEAE,KAAIH,IAAY,EAAEC,OAAgB;YAChC,IAAIL,GAAG,CAACI,KAAK,EAAE;gBACbJ,GAAG,CAACI,KAAK,CAACI,MAAM,CAACR,GAAG,CAACI,KAAK,CAACK,OAAO,CAACJ,aAAa,GAAG;YACrD;QACF;QAEAK,MAAKN,IAAY;YAAE,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGO,OAAH,IAAA,MAAA,OAAA,IAAA,OAAA,IAAA,IAAA,OAAA,GAAA,OAAA,MAAA,OAAA;gBAAGA,IAAAA,CAAH,OAAA,EAAA,GAAA,SAAA,CAAA,KAAc;;YAC/B,iDAAiD;;YAC/CX,CAAAA,GAAG,CAACI,KAAK,IAAI,EAAC,EAAGQ,KAAK,GAAGC,GAAG,CAAC,CAACR;gBAC9BA,WAAWM;YACb;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1640, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/utils.ts"], "sourcesContent": ["import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n"], "names": ["DecodeError", "MiddlewareNotFoundError", "MissingStaticPage", "NormalizeError", "PageNotFoundError", "SP", "ST", "WEB_VITALS", "execOnce", "getDisplayName", "getLocationOrigin", "getURL", "isAbsoluteUrl", "isResSent", "loadGetInitialProps", "normalizeRepeatedSlashes", "stringifyError", "fn", "used", "result", "args", "ABSOLUTE_URL_REGEX", "url", "test", "protocol", "hostname", "port", "window", "location", "href", "origin", "substring", "length", "Component", "displayName", "name", "res", "finished", "headersSent", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replace", "slice", "join", "App", "ctx", "process", "env", "NODE_ENV", "prototype", "getInitialProps", "message", "Error", "pageProps", "props", "Object", "keys", "console", "warn", "performance", "every", "method", "constructor", "page", "code", "error", "JSON", "stringify", "stack"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoaaA,WAAW,EAAA;eAAXA;;IAoBAC,uBAAuB,EAAA;eAAvBA;;IAPAC,iBAAiB,EAAA;eAAjBA;;IAZAC,cAAc,EAAA;eAAdA;;IACAC,iBAAiB,EAAA;eAAjBA;;IATAC,EAAE,EAAA;eAAFA;;IACAC,EAAE,EAAA;eAAFA;;IAlXAC,UAAU,EAAA;eAAVA;;IAsQGC,QAAQ,EAAA;eAARA;;IA+BAC,cAAc,EAAA;eAAdA;;IAXAC,iBAAiB,EAAA;eAAjBA;;IAKAC,MAAM,EAAA;eAANA;;IAPHC,aAAa,EAAA;eAAbA;;IAmBGC,SAAS,EAAA;eAATA;;IAkBMC,mBAAmB,EAAA;eAAnBA;;IAdNC,wBAAwB,EAAA;eAAxBA;;IA+GAC,cAAc,EAAA;eAAdA;;;AA9ZT,MAAMT,aAAa;IAAC;IAAO;IAAO;IAAO;IAAO;IAAO;CAAO;AAsQ9D,SAASC,SACdS,EAAK;IAEL,IAAIC,OAAO;IACX,IAAIC;IAEJ,OAAQ;yCAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QACV,IAAI,CAACF,MAAM;YACTA,OAAO;YACPC,SAASF,MAAMG;QACjB;QACA,OAAOD;IACT;AACF;AAEA,0DAA0D;AAC1D,gEAAgE;AAChE,MAAME,qBAAqB;AACpB,MAAMT,gBAAgB,CAACU,MAAgBD,mBAAmBE,IAAI,CAACD;AAE/D,SAASZ;IACd,MAAM,EAAEc,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGC,OAAOC,QAAQ;IACpD,OAAUJ,WAAS,OAAIC,WAAWC,CAAAA,OAAO,MAAMA,OAAO,EAAC;AACzD;AAEO,SAASf;IACd,MAAM,EAAEkB,IAAI,EAAE,GAAGF,OAAOC,QAAQ;IAChC,MAAME,SAASpB;IACf,OAAOmB,KAAKE,SAAS,CAACD,OAAOE,MAAM;AACrC;AAEO,SAASvB,eAAkBwB,SAA2B;IAC3D,OAAO,OAAOA,cAAc,WACxBA,YACAA,UAAUC,WAAW,IAAID,UAAUE,IAAI,IAAI;AACjD;AAEO,SAAStB,UAAUuB,GAAmB;IAC3C,OAAOA,IAAIC,QAAQ,IAAID,IAAIE,WAAW;AACxC;AAEO,SAASvB,yBAAyBO,GAAW;IAClD,MAAMiB,WAAWjB,IAAIkB,KAAK,CAAC;IAC3B,MAAMC,aAAaF,QAAQ,CAAC,EAAE;IAE9B,OACEE,WACE,4DAA4D;IAC5D,0CAA0C;KACzCC,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,UAAU,OACpBH,CAAAA,QAAQ,CAAC,EAAE,GAAI,MAAGA,SAASI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS,EAAC;AAExD;AAEO,eAAe9B,oBAIpB+B,GAAgC,EAAEC,GAAM;IACxC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;YACrCJ;QAAJ,IAAA,CAAIA,iBAAAA,IAAIK,SAAS,KAAA,OAAA,KAAA,IAAbL,eAAeM,eAAe,EAAE;YAClC,MAAMC,UAAW,MAAG3C,eAClBoC,OACA;YACF,MAAM,OAAA,cAAkB,CAAlB,IAAIQ,MAAMD,UAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAiB;QACzB;IACF;IACA,iDAAiD;IACjD,MAAMhB,MAAMU,IAAIV,GAAG,IAAKU,IAAIA,GAAG,IAAIA,IAAIA,GAAG,CAACV,GAAG;IAE9C,IAAI,CAACS,IAAIM,eAAe,EAAE;QACxB,IAAIL,IAAIA,GAAG,IAAIA,IAAIb,SAAS,EAAE;YAC5B,+BAA+B;YAC/B,OAAO;gBACLqB,WAAW,MAAMxC,oBAAoBgC,IAAIb,SAAS,EAAEa,IAAIA,GAAG;YAC7D;QACF;QACA,OAAO,CAAC;IACV;IAEA,MAAMS,QAAQ,MAAMV,IAAIM,eAAe,CAACL;IAExC,IAAIV,OAAOvB,UAAUuB,MAAM;QACzB,OAAOmB;IACT;IAEA,IAAI,CAACA,OAAO;QACV,MAAMH,UAAW,MAAG3C,eAClBoC,OACA,iEAA8DU,QAAM;QACtE,MAAM,OAAA,cAAkB,CAAlB,IAAIF,MAAMD,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;IAEA,IAAIL,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAIO,OAAOC,IAAI,CAACF,OAAOvB,MAAM,KAAK,KAAK,CAACc,IAAIA,GAAG,EAAE;YAC/CY,QAAQC,IAAI,CACT,KAAElD,eACDoC,OACA;QAEN;IACF;IAEA,OAAOU;AACT;AAEO,MAAMlD,KAAK,OAAOuD,gBAAgB;AAClC,MAAMtD,KACXD,MACC;IAAC;IAAQ;IAAW;CAAmB,CAAWwD,KAAK,CACtD,CAACC,SAAW,OAAOF,WAAW,CAACE,OAAO,KAAK;AAGxC,MAAM9D,oBAAoBqD;AAAO;AACjC,MAAMlD,uBAAuBkD;AAAO;AACpC,MAAMjD,0BAA0BiD;IAGrCU,YAAYC,IAAY,CAAE;QACxB,KAAK;QACL,IAAI,CAACC,IAAI,GAAG;QACZ,IAAI,CAAC9B,IAAI,GAAG;QACZ,IAAI,CAACiB,OAAO,GAAI,kCAA+BY;IACjD;AACF;AAEO,MAAM9D,0BAA0BmD;IACrCU,YAAYC,IAAY,EAAEZ,OAAe,CAAE;QACzC,KAAK;QACL,IAAI,CAACA,OAAO,GAAI,0CAAuCY,OAAK,MAAGZ;IACjE;AACF;AAEO,MAAMnD,gCAAgCoD;IAE3CU,aAAc;QACZ,KAAK;QACL,IAAI,CAACE,IAAI,GAAG;QACZ,IAAI,CAACb,OAAO,GAAI;IAClB;AACF;AAWO,SAASpC,eAAekD,KAAY;IACzC,OAAOC,KAAKC,SAAS,CAAC;QAAEhB,SAASc,MAAMd,OAAO;QAAEiB,OAAOH,MAAMG,KAAK;IAAC;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1854, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/querystring.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nexport function searchParamsToUrlQuery(\n  searchParams: URLSearchParams\n): ParsedUrlQuery {\n  const query: ParsedUrlQuery = {}\n  for (const [key, value] of searchParams.entries()) {\n    const existing = query[key]\n    if (typeof existing === 'undefined') {\n      query[key] = value\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      query[key] = [existing, value]\n    }\n  }\n  return query\n}\n\nfunction stringifyUrlQueryParam(param: unknown): string {\n  if (typeof param === 'string') {\n    return param\n  }\n\n  if (\n    (typeof param === 'number' && !isNaN(param)) ||\n    typeof param === 'boolean'\n  ) {\n    return String(param)\n  } else {\n    return ''\n  }\n}\n\nexport function urlQueryToSearchParams(query: ParsedUrlQuery): URLSearchParams {\n  const searchParams = new URLSearchParams()\n  for (const [key, value] of Object.entries(query)) {\n    if (Array.isArray(value)) {\n      for (const item of value) {\n        searchParams.append(key, stringifyUrlQueryParam(item))\n      }\n    } else {\n      searchParams.set(key, stringifyUrlQueryParam(value))\n    }\n  }\n  return searchParams\n}\n\nexport function assign(\n  target: URLSearchParams,\n  ...searchParamsList: URLSearchParams[]\n): URLSearchParams {\n  for (const searchParams of searchParamsList) {\n    for (const key of searchParams.keys()) {\n      target.delete(key)\n    }\n\n    for (const [key, value] of searchParams.entries()) {\n      target.append(key, value)\n    }\n  }\n\n  return target\n}\n"], "names": ["assign", "searchParamsToUrlQuery", "urlQueryToSearchParams", "searchParams", "query", "key", "value", "entries", "existing", "Array", "isArray", "push", "stringifyUrlQueryParam", "param", "isNaN", "String", "URLSearchParams", "Object", "item", "append", "set", "target", "searchParamsList", "keys", "delete"], "mappings": ";;;;;;;;;;;;;;;;IAgDgBA,MAAM,EAAA;eAANA;;IA9CAC,sBAAsB,EAAA;eAAtBA;;IAgCAC,sBAAsB,EAAA;eAAtBA;;;AAhCT,SAASD,uBACdE,YAA6B;IAE7B,MAAMC,QAAwB,CAAC;IAC/B,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;QACjD,MAAMC,WAAWJ,KAAK,CAACC,IAAI;QAC3B,IAAI,OAAOG,aAAa,aAAa;YACnCJ,KAAK,CAACC,IAAI,GAAGC;QACf,OAAO,IAAIG,MAAMC,OAAO,CAACF,WAAW;YAClCA,SAASG,IAAI,CAACL;QAChB,OAAO;YACLF,KAAK,CAACC,IAAI,GAAG;gBAACG;gBAAUF;aAAM;QAChC;IACF;IACA,OAAOF;AACT;AAEA,SAASQ,uBAAuBC,KAAc;IAC5C,IAAI,OAAOA,UAAU,UAAU;QAC7B,OAAOA;IACT;IAEA,IACG,OAAOA,UAAU,YAAY,CAACC,MAAMD,UACrC,OAAOA,UAAU,WACjB;QACA,OAAOE,OAAOF;IAChB,OAAO;QACL,OAAO;IACT;AACF;AAEO,SAASX,uBAAuBE,KAAqB;IAC1D,MAAMD,eAAe,IAAIa;IACzB,KAAK,MAAM,CAACX,KAAKC,MAAM,IAAIW,OAAOV,OAAO,CAACH,OAAQ;QAChD,IAAIK,MAAMC,OAAO,CAACJ,QAAQ;YACxB,KAAK,MAAMY,QAAQZ,MAAO;gBACxBH,aAAagB,MAAM,CAACd,KAAKO,uBAAuBM;YAClD;QACF,OAAO;YACLf,aAAaiB,GAAG,CAACf,KAAKO,uBAAuBN;QAC/C;IACF;IACA,OAAOH;AACT;AAEO,SAASH,OACdqB,MAAuB;IACvB,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGC,mBAAH,IAAA,MAAA,OAAA,IAAA,OAAA,IAAA,IAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,gBAAAA,CAAH,OAAA,EAAA,GAAA,SAAA,CAAA,KAAsC;;IAEtC,KAAK,MAAMnB,gBAAgBmB,iBAAkB;QAC3C,KAAK,MAAMjB,OAAOF,aAAaoB,IAAI,GAAI;YACrCF,OAAOG,MAAM,CAACnB;QAChB;QAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;YACjDc,OAAOF,MAAM,CAACd,KAAKC;QACrB;IACF;IAEA,OAAOe;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1939, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/parse-relative-url.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\nimport { getLocationOrigin } from '../../utils'\nimport { searchParamsToUrlQuery } from './querystring'\n\nexport interface ParsedRelativeUrl {\n  hash: string\n  href: string\n  pathname: string\n  query: ParsedUrlQuery\n  search: string\n}\n\n/**\n * Parses path-relative urls (e.g. `/hello/world?foo=bar`). If url isn't path-relative\n * (e.g. `./hello`) then at least base must be.\n * Absolute urls are rejected with one exception, in the browser, absolute urls that are on\n * the current origin will be parsed as relative\n */\nexport function parseRelativeUrl(\n  url: string,\n  base?: string,\n  parseQuery?: true\n): ParsedRelativeUrl\nexport function parseRelativeUrl(\n  url: string,\n  base: string | undefined,\n  parseQuery: false\n): Omit<ParsedRelativeUrl, 'query'>\nexport function parseRelativeUrl(\n  url: string,\n  base?: string,\n  parseQuery = true\n): ParsedRelativeUrl | Omit<ParsedRelativeUrl, 'query'> {\n  const globalBase = new URL(\n    typeof window === 'undefined' ? 'http://n' : getLocationOrigin()\n  )\n\n  const resolvedBase = base\n    ? new URL(base, globalBase)\n    : url.startsWith('.')\n      ? new URL(\n          typeof window === 'undefined' ? 'http://n' : window.location.href\n        )\n      : globalBase\n\n  const { pathname, searchParams, search, hash, href, origin } = new URL(\n    url,\n    resolvedBase\n  )\n\n  if (origin !== globalBase.origin) {\n    throw new Error(`invariant: invalid relative URL, router received ${url}`)\n  }\n\n  return {\n    pathname,\n    query: parseQuery ? searchParamsToUrlQuery(searchParams) : undefined,\n    search,\n    hash,\n    href: href.slice(origin.length),\n  }\n}\n"], "names": ["parseRelativeUrl", "url", "base", "parse<PERSON><PERSON>y", "globalBase", "URL", "window", "getLocationOrigin", "resolvedBase", "startsWith", "location", "href", "pathname", "searchParams", "search", "hash", "origin", "Error", "query", "searchParamsToUrlQuery", "undefined", "slice", "length"], "mappings": ";;;;+BA4BgBA,oBAAAA;;;eAAAA;;;uBA3BkB;6BACK;AA0BhC,SAASA,iBACdC,GAAW,EACXC,IAAa,EACbC,UAAiB;IAAjBA,IAAAA,eAAAA,KAAAA,GAAAA,aAAa;IAEb,MAAMC,aAAa,IAAIC,IACrB,OAAOC,WAAW,cAAc,aAAaC,CAAAA,GAAAA,OAAAA,iBAAiB;IAGhE,MAAMC,eAAeN,OACjB,IAAIG,IAAIH,MAAME,cACdH,IAAIQ,UAAU,CAAC,OACb,IAAIJ,IACF,OAAOC,WAAW,cAAc,aAAaA,OAAOI,QAAQ,CAACC,IAAI,IAEnEP;IAEN,MAAM,EAAEQ,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAEC,IAAI,EAAEJ,IAAI,EAAEK,MAAM,EAAE,GAAG,IAAIX,IACjEJ,KACAO;IAGF,IAAIQ,WAAWZ,WAAWY,MAAM,EAAE;QAChC,MAAM,OAAA,cAAoE,CAApE,IAAIC,MAAO,sDAAmDhB,MAA9D,qBAAA;mBAAA;wBAAA;0BAAA;QAAmE;IAC3E;IAEA,OAAO;QACLW;QACAM,OAAOf,aAAagB,CAAAA,GAAAA,aAAAA,sBAAsB,EAACN,gBAAgBO;QAC3DN;QACAC;QACAJ,MAAMA,KAAKU,KAAK,CAACL,OAAOM,MAAM;IAChC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1976, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/dist/compiled/path-to-regexp/index.js"], "sourcesContent": ["(()=>{\"use strict\";if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var e={};(()=>{var r=e;Object.defineProperty(r,\"__esModule\",{value:true});function lexer(e){var r=[];var n=0;while(n<e.length){var t=e[n];if(t===\"*\"||t===\"+\"||t===\"?\"){r.push({type:\"MODIFIER\",index:n,value:e[n++]});continue}if(t===\"\\\\\"){r.push({type:\"ESCAPED_CHAR\",index:n++,value:e[n++]});continue}if(t===\"{\"){r.push({type:\"OPEN\",index:n,value:e[n++]});continue}if(t===\"}\"){r.push({type:\"CLOSE\",index:n,value:e[n++]});continue}if(t===\":\"){var i=\"\";var a=n+1;while(a<e.length){var o=e.charCodeAt(a);if(o>=48&&o<=57||o>=65&&o<=90||o>=97&&o<=122||o===95){i+=e[a++];continue}break}if(!i)throw new TypeError(\"Missing parameter name at \"+n);r.push({type:\"NAME\",index:n,value:i});n=a;continue}if(t===\"(\"){var f=1;var u=\"\";var a=n+1;if(e[a]===\"?\"){throw new TypeError('Pattern cannot start with \"?\" at '+a)}while(a<e.length){if(e[a]===\"\\\\\"){u+=e[a++]+e[a++];continue}if(e[a]===\")\"){f--;if(f===0){a++;break}}else if(e[a]===\"(\"){f++;if(e[a+1]!==\"?\"){throw new TypeError(\"Capturing groups are not allowed at \"+a)}}u+=e[a++]}if(f)throw new TypeError(\"Unbalanced pattern at \"+n);if(!u)throw new TypeError(\"Missing pattern at \"+n);r.push({type:\"PATTERN\",index:n,value:u});n=a;continue}r.push({type:\"CHAR\",index:n,value:e[n++]})}r.push({type:\"END\",index:n,value:\"\"});return r}function parse(e,r){if(r===void 0){r={}}var n=lexer(e);var t=r.prefixes,i=t===void 0?\"./\":t;var a=\"[^\"+escapeString(r.delimiter||\"/#?\")+\"]+?\";var o=[];var f=0;var u=0;var p=\"\";var tryConsume=function(e){if(u<n.length&&n[u].type===e)return n[u++].value};var mustConsume=function(e){var r=tryConsume(e);if(r!==undefined)return r;var t=n[u],i=t.type,a=t.index;throw new TypeError(\"Unexpected \"+i+\" at \"+a+\", expected \"+e)};var consumeText=function(){var e=\"\";var r;while(r=tryConsume(\"CHAR\")||tryConsume(\"ESCAPED_CHAR\")){e+=r}return e};while(u<n.length){var v=tryConsume(\"CHAR\");var c=tryConsume(\"NAME\");var s=tryConsume(\"PATTERN\");if(c||s){var d=v||\"\";if(i.indexOf(d)===-1){p+=d;d=\"\"}if(p){o.push(p);p=\"\"}o.push({name:c||f++,prefix:d,suffix:\"\",pattern:s||a,modifier:tryConsume(\"MODIFIER\")||\"\"});continue}var g=v||tryConsume(\"ESCAPED_CHAR\");if(g){p+=g;continue}if(p){o.push(p);p=\"\"}var x=tryConsume(\"OPEN\");if(x){var d=consumeText();var l=tryConsume(\"NAME\")||\"\";var h=tryConsume(\"PATTERN\")||\"\";var m=consumeText();mustConsume(\"CLOSE\");o.push({name:l||(h?f++:\"\"),pattern:l&&!h?a:h,prefix:d,suffix:m,modifier:tryConsume(\"MODIFIER\")||\"\"});continue}mustConsume(\"END\")}return o}r.parse=parse;function compile(e,r){return tokensToFunction(parse(e,r),r)}r.compile=compile;function tokensToFunction(e,r){if(r===void 0){r={}}var n=flags(r);var t=r.encode,i=t===void 0?function(e){return e}:t,a=r.validate,o=a===void 0?true:a;var f=e.map((function(e){if(typeof e===\"object\"){return new RegExp(\"^(?:\"+e.pattern+\")$\",n)}}));return function(r){var n=\"\";for(var t=0;t<e.length;t++){var a=e[t];if(typeof a===\"string\"){n+=a;continue}var u=r?r[a.name]:undefined;var p=a.modifier===\"?\"||a.modifier===\"*\";var v=a.modifier===\"*\"||a.modifier===\"+\";if(Array.isArray(u)){if(!v){throw new TypeError('Expected \"'+a.name+'\" to not repeat, but got an array')}if(u.length===0){if(p)continue;throw new TypeError('Expected \"'+a.name+'\" to not be empty')}for(var c=0;c<u.length;c++){var s=i(u[c],a);if(o&&!f[t].test(s)){throw new TypeError('Expected all \"'+a.name+'\" to match \"'+a.pattern+'\", but got \"'+s+'\"')}n+=a.prefix+s+a.suffix}continue}if(typeof u===\"string\"||typeof u===\"number\"){var s=i(String(u),a);if(o&&!f[t].test(s)){throw new TypeError('Expected \"'+a.name+'\" to match \"'+a.pattern+'\", but got \"'+s+'\"')}n+=a.prefix+s+a.suffix;continue}if(p)continue;var d=v?\"an array\":\"a string\";throw new TypeError('Expected \"'+a.name+'\" to be '+d)}return n}}r.tokensToFunction=tokensToFunction;function match(e,r){var n=[];var t=pathToRegexp(e,n,r);return regexpToFunction(t,n,r)}r.match=match;function regexpToFunction(e,r,n){if(n===void 0){n={}}var t=n.decode,i=t===void 0?function(e){return e}:t;return function(n){var t=e.exec(n);if(!t)return false;var a=t[0],o=t.index;var f=Object.create(null);var _loop_1=function(e){if(t[e]===undefined)return\"continue\";var n=r[e-1];if(n.modifier===\"*\"||n.modifier===\"+\"){f[n.name]=t[e].split(n.prefix+n.suffix).map((function(e){return i(e,n)}))}else{f[n.name]=i(t[e],n)}};for(var u=1;u<t.length;u++){_loop_1(u)}return{path:a,index:o,params:f}}}r.regexpToFunction=regexpToFunction;function escapeString(e){return e.replace(/([.+*?=^!:${}()[\\]|/\\\\])/g,\"\\\\$1\")}function flags(e){return e&&e.sensitive?\"\":\"i\"}function regexpToRegexp(e,r){if(!r)return e;var n=e.source.match(/\\((?!\\?)/g);if(n){for(var t=0;t<n.length;t++){r.push({name:t,prefix:\"\",suffix:\"\",modifier:\"\",pattern:\"\"})}}return e}function arrayToRegexp(e,r,n){var t=e.map((function(e){return pathToRegexp(e,r,n).source}));return new RegExp(\"(?:\"+t.join(\"|\")+\")\",flags(n))}function stringToRegexp(e,r,n){return tokensToRegexp(parse(e,n),r,n)}function tokensToRegexp(e,r,n){if(n===void 0){n={}}var t=n.strict,i=t===void 0?false:t,a=n.start,o=a===void 0?true:a,f=n.end,u=f===void 0?true:f,p=n.encode,v=p===void 0?function(e){return e}:p;var c=\"[\"+escapeString(n.endsWith||\"\")+\"]|$\";var s=\"[\"+escapeString(n.delimiter||\"/#?\")+\"]\";var d=o?\"^\":\"\";for(var g=0,x=e;g<x.length;g++){var l=x[g];if(typeof l===\"string\"){d+=escapeString(v(l))}else{var h=escapeString(v(l.prefix));var m=escapeString(v(l.suffix));if(l.pattern){if(r)r.push(l);if(h||m){if(l.modifier===\"+\"||l.modifier===\"*\"){var E=l.modifier===\"*\"?\"?\":\"\";d+=\"(?:\"+h+\"((?:\"+l.pattern+\")(?:\"+m+h+\"(?:\"+l.pattern+\"))*)\"+m+\")\"+E}else{d+=\"(?:\"+h+\"(\"+l.pattern+\")\"+m+\")\"+l.modifier}}else{d+=\"(\"+l.pattern+\")\"+l.modifier}}else{d+=\"(?:\"+h+m+\")\"+l.modifier}}}if(u){if(!i)d+=s+\"?\";d+=!n.endsWith?\"$\":\"(?=\"+c+\")\"}else{var T=e[e.length-1];var y=typeof T===\"string\"?s.indexOf(T[T.length-1])>-1:T===undefined;if(!i){d+=\"(?:\"+s+\"(?=\"+c+\"))?\"}if(!y){d+=\"(?=\"+s+\"|\"+c+\")\"}}return new RegExp(d,flags(n))}r.tokensToRegexp=tokensToRegexp;function pathToRegexp(e,r,n){if(e instanceof RegExp)return regexpToRegexp(e,r);if(Array.isArray(e))return arrayToRegexp(e,r,n);return stringToRegexp(e,r,n)}r.pathToRegexp=pathToRegexp})();module.exports=e})();"], "names": [], "mappings": "AAAA,CAAC;IAAK;IAAa,IAAG,OAAO,wBAAsB,aAAY,oBAAoB,EAAE,GAAC,YAAU;IAAI,IAAI,IAAE,CAAC;IAAE,CAAC;QAAK,IAAI,IAAE;QAAE,OAAO,cAAc,CAAC,GAAE,cAAa;YAAC,OAAM;QAAI;QAAG,SAAS,MAAM,CAAC;YAAE,IAAI,IAAE,EAAE;YAAC,IAAI,IAAE;YAAE,MAAM,IAAE,EAAE,MAAM,CAAC;gBAAC,IAAI,IAAE,CAAC,CAAC,EAAE;gBAAC,IAAG,MAAI,OAAK,MAAI,OAAK,MAAI,KAAI;oBAAC,EAAE,IAAI,CAAC;wBAAC,MAAK;wBAAW,OAAM;wBAAE,OAAM,CAAC,CAAC,IAAI;oBAAA;oBAAG;gBAAQ;gBAAC,IAAG,MAAI,MAAK;oBAAC,EAAE,IAAI,CAAC;wBAAC,MAAK;wBAAe,OAAM;wBAAI,OAAM,CAAC,CAAC,IAAI;oBAAA;oBAAG;gBAAQ;gBAAC,IAAG,MAAI,KAAI;oBAAC,EAAE,IAAI,CAAC;wBAAC,MAAK;wBAAO,OAAM;wBAAE,OAAM,CAAC,CAAC,IAAI;oBAAA;oBAAG;gBAAQ;gBAAC,IAAG,MAAI,KAAI;oBAAC,EAAE,IAAI,CAAC;wBAAC,MAAK;wBAAQ,OAAM;wBAAE,OAAM,CAAC,CAAC,IAAI;oBAAA;oBAAG;gBAAQ;gBAAC,IAAG,MAAI,KAAI;oBAAC,IAAI,IAAE;oBAAG,IAAI,IAAE,IAAE;oBAAE,MAAM,IAAE,EAAE,MAAM,CAAC;wBAAC,IAAI,IAAE,EAAE,UAAU,CAAC;wBAAG,IAAG,KAAG,MAAI,KAAG,MAAI,KAAG,MAAI,KAAG,MAAI,KAAG,MAAI,KAAG,OAAK,MAAI,IAAG;4BAAC,KAAG,CAAC,CAAC,IAAI;4BAAC;wBAAQ;wBAAC;oBAAK;oBAAC,IAAG,CAAC,GAAE,MAAM,IAAI,UAAU,+BAA6B;oBAAG,EAAE,IAAI,CAAC;wBAAC,MAAK;wBAAO,OAAM;wBAAE,OAAM;oBAAC;oBAAG,IAAE;oBAAE;gBAAQ;gBAAC,IAAG,MAAI,KAAI;oBAAC,IAAI,IAAE;oBAAE,IAAI,IAAE;oBAAG,IAAI,IAAE,IAAE;oBAAE,IAAG,CAAC,CAAC,EAAE,KAAG,KAAI;wBAAC,MAAM,IAAI,UAAU,sCAAoC;oBAAE;oBAAC,MAAM,IAAE,EAAE,MAAM,CAAC;wBAAC,IAAG,CAAC,CAAC,EAAE,KAAG,MAAK;4BAAC,KAAG,CAAC,CAAC,IAAI,GAAC,CAAC,CAAC,IAAI;4BAAC;wBAAQ;wBAAC,IAAG,CAAC,CAAC,EAAE,KAAG,KAAI;4BAAC;4BAAI,IAAG,MAAI,GAAE;gCAAC;gCAAI;4BAAK;wBAAC,OAAM,IAAG,CAAC,CAAC,EAAE,KAAG,KAAI;4BAAC;4BAAI,IAAG,CAAC,CAAC,IAAE,EAAE,KAAG,KAAI;gCAAC,MAAM,IAAI,UAAU,yCAAuC;4BAAE;wBAAC;wBAAC,KAAG,CAAC,CAAC,IAAI;oBAAA;oBAAC,IAAG,GAAE,MAAM,IAAI,UAAU,2BAAyB;oBAAG,IAAG,CAAC,GAAE,MAAM,IAAI,UAAU,wBAAsB;oBAAG,EAAE,IAAI,CAAC;wBAAC,MAAK;wBAAU,OAAM;wBAAE,OAAM;oBAAC;oBAAG,IAAE;oBAAE;gBAAQ;gBAAC,EAAE,IAAI,CAAC;oBAAC,MAAK;oBAAO,OAAM;oBAAE,OAAM,CAAC,CAAC,IAAI;gBAAA;YAAE;YAAC,EAAE,IAAI,CAAC;gBAAC,MAAK;gBAAM,OAAM;gBAAE,OAAM;YAAE;YAAG,OAAO;QAAC;QAAC,SAAS,MAAM,CAAC,EAAC,CAAC;YAAE,IAAG,MAAI,KAAK,GAAE;gBAAC,IAAE,CAAC;YAAC;YAAC,IAAI,IAAE,MAAM;YAAG,IAAI,IAAE,EAAE,QAAQ,EAAC,IAAE,MAAI,KAAK,IAAE,OAAK;YAAE,IAAI,IAAE,OAAK,aAAa,EAAE,SAAS,IAAE,SAAO;YAAM,IAAI,IAAE,EAAE;YAAC,IAAI,IAAE;YAAE,IAAI,IAAE;YAAE,IAAI,IAAE;YAAG,IAAI,aAAW,SAAS,CAAC;gBAAE,IAAG,IAAE,EAAE,MAAM,IAAE,CAAC,CAAC,EAAE,CAAC,IAAI,KAAG,GAAE,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK;YAAA;YAAE,IAAI,cAAY,SAAS,CAAC;gBAAE,IAAI,IAAE,WAAW;gBAAG,IAAG,MAAI,WAAU,OAAO;gBAAE,IAAI,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,EAAE,IAAI,EAAC,IAAE,EAAE,KAAK;gBAAC,MAAM,IAAI,UAAU,gBAAc,IAAE,SAAO,IAAE,gBAAc;YAAE;YAAE,IAAI,cAAY;gBAAW,IAAI,IAAE;gBAAG,IAAI;gBAAE,MAAM,IAAE,WAAW,WAAS,WAAW,gBAAgB;oBAAC,KAAG;gBAAC;gBAAC,OAAO;YAAC;YAAE,MAAM,IAAE,EAAE,MAAM,CAAC;gBAAC,IAAI,IAAE,WAAW;gBAAQ,IAAI,IAAE,WAAW;gBAAQ,IAAI,IAAE,WAAW;gBAAW,IAAG,KAAG,GAAE;oBAAC,IAAI,IAAE,KAAG;oBAAG,IAAG,EAAE,OAAO,CAAC,OAAK,CAAC,GAAE;wBAAC,KAAG;wBAAE,IAAE;oBAAE;oBAAC,IAAG,GAAE;wBAAC,EAAE,IAAI,CAAC;wBAAG,IAAE;oBAAE;oBAAC,EAAE,IAAI,CAAC;wBAAC,MAAK,KAAG;wBAAI,QAAO;wBAAE,QAAO;wBAAG,SAAQ,KAAG;wBAAE,UAAS,WAAW,eAAa;oBAAE;oBAAG;gBAAQ;gBAAC,IAAI,IAAE,KAAG,WAAW;gBAAgB,IAAG,GAAE;oBAAC,KAAG;oBAAE;gBAAQ;gBAAC,IAAG,GAAE;oBAAC,EAAE,IAAI,CAAC;oBAAG,IAAE;gBAAE;gBAAC,IAAI,IAAE,WAAW;gBAAQ,IAAG,GAAE;oBAAC,IAAI,IAAE;oBAAc,IAAI,IAAE,WAAW,WAAS;oBAAG,IAAI,IAAE,WAAW,cAAY;oBAAG,IAAI,IAAE;oBAAc,YAAY;oBAAS,EAAE,IAAI,CAAC;wBAAC,MAAK,KAAG,CAAC,IAAE,MAAI,EAAE;wBAAE,SAAQ,KAAG,CAAC,IAAE,IAAE;wBAAE,QAAO;wBAAE,QAAO;wBAAE,UAAS,WAAW,eAAa;oBAAE;oBAAG;gBAAQ;gBAAC,YAAY;YAAM;YAAC,OAAO;QAAC;QAAC,EAAE,KAAK,GAAC;QAAM,SAAS,QAAQ,CAAC,EAAC,CAAC;YAAE,OAAO,iBAAiB,MAAM,GAAE,IAAG;QAAE;QAAC,EAAE,OAAO,GAAC;QAAQ,SAAS,iBAAiB,CAAC,EAAC,CAAC;YAAE,IAAG,MAAI,KAAK,GAAE;gBAAC,IAAE,CAAC;YAAC;YAAC,IAAI,IAAE,MAAM;YAAG,IAAI,IAAE,EAAE,MAAM,EAAC,IAAE,MAAI,KAAK,IAAE,SAAS,CAAC;gBAAE,OAAO;YAAC,IAAE,GAAE,IAAE,EAAE,QAAQ,EAAC,IAAE,MAAI,KAAK,IAAE,OAAK;YAAE,IAAI,IAAE,EAAE,GAAG,CAAE,SAAS,CAAC;gBAAE,IAAG,OAAO,MAAI,UAAS;oBAAC,OAAO,IAAI,OAAO,SAAO,EAAE,OAAO,GAAC,MAAK;gBAAE;YAAC;YAAI,OAAO,SAAS,CAAC;gBAAE,IAAI,IAAE;gBAAG,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;oBAAC,IAAI,IAAE,CAAC,CAAC,EAAE;oBAAC,IAAG,OAAO,MAAI,UAAS;wBAAC,KAAG;wBAAE;oBAAQ;oBAAC,IAAI,IAAE,IAAE,CAAC,CAAC,EAAE,IAAI,CAAC,GAAC;oBAAU,IAAI,IAAE,EAAE,QAAQ,KAAG,OAAK,EAAE,QAAQ,KAAG;oBAAI,IAAI,IAAE,EAAE,QAAQ,KAAG,OAAK,EAAE,QAAQ,KAAG;oBAAI,IAAG,MAAM,OAAO,CAAC,IAAG;wBAAC,IAAG,CAAC,GAAE;4BAAC,MAAM,IAAI,UAAU,eAAa,EAAE,IAAI,GAAC;wBAAoC;wBAAC,IAAG,EAAE,MAAM,KAAG,GAAE;4BAAC,IAAG,GAAE;4BAAS,MAAM,IAAI,UAAU,eAAa,EAAE,IAAI,GAAC;wBAAoB;wBAAC,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;4BAAC,IAAI,IAAE,EAAE,CAAC,CAAC,EAAE,EAAC;4BAAG,IAAG,KAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAG;gCAAC,MAAM,IAAI,UAAU,mBAAiB,EAAE,IAAI,GAAC,iBAAe,EAAE,OAAO,GAAC,iBAAe,IAAE;4BAAI;4BAAC,KAAG,EAAE,MAAM,GAAC,IAAE,EAAE,MAAM;wBAAA;wBAAC;oBAAQ;oBAAC,IAAG,OAAO,MAAI,YAAU,OAAO,MAAI,UAAS;wBAAC,IAAI,IAAE,EAAE,OAAO,IAAG;wBAAG,IAAG,KAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAG;4BAAC,MAAM,IAAI,UAAU,eAAa,EAAE,IAAI,GAAC,iBAAe,EAAE,OAAO,GAAC,iBAAe,IAAE;wBAAI;wBAAC,KAAG,EAAE,MAAM,GAAC,IAAE,EAAE,MAAM;wBAAC;oBAAQ;oBAAC,IAAG,GAAE;oBAAS,IAAI,IAAE,IAAE,aAAW;oBAAW,MAAM,IAAI,UAAU,eAAa,EAAE,IAAI,GAAC,aAAW;gBAAE;gBAAC,OAAO;YAAC;QAAC;QAAC,EAAE,gBAAgB,GAAC;QAAiB,SAAS,MAAM,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE;YAAC,IAAI,IAAE,aAAa,GAAE,GAAE;YAAG,OAAO,iBAAiB,GAAE,GAAE;QAAE;QAAC,EAAE,KAAK,GAAC;QAAM,SAAS,iBAAiB,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAG,MAAI,KAAK,GAAE;gBAAC,IAAE,CAAC;YAAC;YAAC,IAAI,IAAE,EAAE,MAAM,EAAC,IAAE,MAAI,KAAK,IAAE,SAAS,CAAC;gBAAE,OAAO;YAAC,IAAE;YAAE,OAAO,SAAS,CAAC;gBAAE,IAAI,IAAE,EAAE,IAAI,CAAC;gBAAG,IAAG,CAAC,GAAE,OAAO;gBAAM,IAAI,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,EAAE,KAAK;gBAAC,IAAI,IAAE,OAAO,MAAM,CAAC;gBAAM,IAAI,UAAQ,SAAS,CAAC;oBAAE,IAAG,CAAC,CAAC,EAAE,KAAG,WAAU,OAAM;oBAAW,IAAI,IAAE,CAAC,CAAC,IAAE,EAAE;oBAAC,IAAG,EAAE,QAAQ,KAAG,OAAK,EAAE,QAAQ,KAAG,KAAI;wBAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,MAAM,GAAC,EAAE,MAAM,EAAE,GAAG,CAAE,SAAS,CAAC;4BAAE,OAAO,EAAE,GAAE;wBAAE;oBAAG,OAAK;wBAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAC,EAAE,CAAC,CAAC,EAAE,EAAC;oBAAE;gBAAC;gBAAE,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;oBAAC,QAAQ;gBAAE;gBAAC,OAAM;oBAAC,MAAK;oBAAE,OAAM;oBAAE,QAAO;gBAAC;YAAC;QAAC;QAAC,EAAE,gBAAgB,GAAC;QAAiB,SAAS,aAAa,CAAC;YAAE,OAAO,EAAE,OAAO,CAAC,6BAA4B;QAAO;QAAC,SAAS,MAAM,CAAC;YAAE,OAAO,KAAG,EAAE,SAAS,GAAC,KAAG;QAAG;QAAC,SAAS,eAAe,CAAC,EAAC,CAAC;YAAE,IAAG,CAAC,GAAE,OAAO;YAAE,IAAI,IAAE,EAAE,MAAM,CAAC,KAAK,CAAC;YAAa,IAAG,GAAE;gBAAC,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;oBAAC,EAAE,IAAI,CAAC;wBAAC,MAAK;wBAAE,QAAO;wBAAG,QAAO;wBAAG,UAAS;wBAAG,SAAQ;oBAAE;gBAAE;YAAC;YAAC,OAAO;QAAC;QAAC,SAAS,cAAc,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,GAAG,CAAE,SAAS,CAAC;gBAAE,OAAO,aAAa,GAAE,GAAE,GAAG,MAAM;YAAA;YAAI,OAAO,IAAI,OAAO,QAAM,EAAE,IAAI,CAAC,OAAK,KAAI,MAAM;QAAG;QAAC,SAAS,eAAe,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,OAAO,eAAe,MAAM,GAAE,IAAG,GAAE;QAAE;QAAC,SAAS,eAAe,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAG,MAAI,KAAK,GAAE;gBAAC,IAAE,CAAC;YAAC;YAAC,IAAI,IAAE,EAAE,MAAM,EAAC,IAAE,MAAI,KAAK,IAAE,QAAM,GAAE,IAAE,EAAE,KAAK,EAAC,IAAE,MAAI,KAAK,IAAE,OAAK,GAAE,IAAE,EAAE,GAAG,EAAC,IAAE,MAAI,KAAK,IAAE,OAAK,GAAE,IAAE,EAAE,MAAM,EAAC,IAAE,MAAI,KAAK,IAAE,SAAS,CAAC;gBAAE,OAAO;YAAC,IAAE;YAAE,IAAI,IAAE,MAAI,aAAa,EAAE,QAAQ,IAAE,MAAI;YAAM,IAAI,IAAE,MAAI,aAAa,EAAE,SAAS,IAAE,SAAO;YAAI,IAAI,IAAE,IAAE,MAAI;YAAG,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;gBAAC,IAAI,IAAE,CAAC,CAAC,EAAE;gBAAC,IAAG,OAAO,MAAI,UAAS;oBAAC,KAAG,aAAa,EAAE;gBAAG,OAAK;oBAAC,IAAI,IAAE,aAAa,EAAE,EAAE,MAAM;oBAAG,IAAI,IAAE,aAAa,EAAE,EAAE,MAAM;oBAAG,IAAG,EAAE,OAAO,EAAC;wBAAC,IAAG,GAAE,EAAE,IAAI,CAAC;wBAAG,IAAG,KAAG,GAAE;4BAAC,IAAG,EAAE,QAAQ,KAAG,OAAK,EAAE,QAAQ,KAAG,KAAI;gCAAC,IAAI,IAAE,EAAE,QAAQ,KAAG,MAAI,MAAI;gCAAG,KAAG,QAAM,IAAE,SAAO,EAAE,OAAO,GAAC,SAAO,IAAE,IAAE,QAAM,EAAE,OAAO,GAAC,SAAO,IAAE,MAAI;4BAAC,OAAK;gCAAC,KAAG,QAAM,IAAE,MAAI,EAAE,OAAO,GAAC,MAAI,IAAE,MAAI,EAAE,QAAQ;4BAAA;wBAAC,OAAK;4BAAC,KAAG,MAAI,EAAE,OAAO,GAAC,MAAI,EAAE,QAAQ;wBAAA;oBAAC,OAAK;wBAAC,KAAG,QAAM,IAAE,IAAE,MAAI,EAAE,QAAQ;oBAAA;gBAAC;YAAC;YAAC,IAAG,GAAE;gBAAC,IAAG,CAAC,GAAE,KAAG,IAAE;gBAAI,KAAG,CAAC,EAAE,QAAQ,GAAC,MAAI,QAAM,IAAE;YAAG,OAAK;gBAAC,IAAI,IAAE,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE;gBAAC,IAAI,IAAE,OAAO,MAAI,WAAS,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,IAAE,CAAC,IAAE,MAAI;gBAAU,IAAG,CAAC,GAAE;oBAAC,KAAG,QAAM,IAAE,QAAM,IAAE;gBAAK;gBAAC,IAAG,CAAC,GAAE;oBAAC,KAAG,QAAM,IAAE,MAAI,IAAE;gBAAG;YAAC;YAAC,OAAO,IAAI,OAAO,GAAE,MAAM;QAAG;QAAC,EAAE,cAAc,GAAC;QAAe,SAAS,aAAa,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAG,aAAa,QAAO,OAAO,eAAe,GAAE;YAAG,IAAG,MAAM,OAAO,CAAC,IAAG,OAAO,cAAc,GAAE,GAAE;YAAG,OAAO,eAAe,GAAE,GAAE;QAAE;QAAC,EAAE,YAAY,GAAC;IAAY,CAAC;IAAI,OAAO,OAAO,GAAC;AAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2368, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/path-match.ts"], "sourcesContent": ["import type { Key } from 'next/dist/compiled/path-to-regexp'\nimport { pathToRegexp } from 'next/dist/compiled/path-to-regexp'\nimport { regexpToFunction } from 'next/dist/compiled/path-to-regexp'\n\ninterface Options {\n  /**\n   * A transformer function that will be applied to the regexp generated\n   * from the provided path and path-to-regexp.\n   */\n  regexModifier?: (regex: string) => string\n  /**\n   * When true the function will remove all unnamed parameters\n   * from the matched parameters.\n   */\n  removeUnnamedParams?: boolean\n  /**\n   * When true the regexp won't allow an optional trailing delimiter\n   * to match.\n   */\n  strict?: boolean\n\n  /**\n   * When true the matcher will be case-sensitive, defaults to false\n   */\n  sensitive?: boolean\n}\n\nexport type PatchMatcher = (\n  pathname: string,\n  params?: Record<string, any>\n) => Record<string, any> | false\n\n/**\n * Generates a path matcher function for a given path and options based on\n * path-to-regexp. By default the match will be case insensitive, non strict\n * and delimited by `/`.\n */\nexport function getPathMatch(path: string, options?: Options): PatchMatcher {\n  const keys: Key[] = []\n  const regexp = pathToRegexp(path, keys, {\n    delimiter: '/',\n    sensitive:\n      typeof options?.sensitive === 'boolean' ? options.sensitive : false,\n    strict: options?.strict,\n  })\n\n  const matcher = regexpToFunction<Record<string, any>>(\n    options?.regexModifier\n      ? new RegExp(options.regexModifier(regexp.source), regexp.flags)\n      : regexp,\n    keys\n  )\n\n  /**\n   * A matcher function that will check if a given pathname matches the path\n   * given in the builder function. When the path does not match it will return\n   * `false` but if it does it will return an object with the matched params\n   * merged with the params provided in the second argument.\n   */\n  return (pathname, params) => {\n    // If no pathname is provided it's not a match.\n    if (typeof pathname !== 'string') return false\n\n    const match = matcher(pathname)\n\n    // If the path did not match `false` will be returned.\n    if (!match) return false\n\n    /**\n     * If unnamed params are not allowed they must be removed from\n     * the matched parameters. path-to-regexp uses \"string\" for named and\n     * \"number\" for unnamed parameters.\n     */\n    if (options?.removeUnnamedParams) {\n      for (const key of keys) {\n        if (typeof key.name === 'number') {\n          delete match.params[key.name]\n        }\n      }\n    }\n\n    return { ...params, ...match.params }\n  }\n}\n"], "names": ["getPathMatch", "path", "options", "keys", "regexp", "pathToRegexp", "delimiter", "sensitive", "strict", "matcher", "regexpToFunction", "regexModifier", "RegExp", "source", "flags", "pathname", "params", "match", "removeUnnamedP<PERSON>ms", "key", "name"], "mappings": ";;;;+BAqCgBA,gBAAAA;;;eAAAA;;;8BApCa;AAoCtB,SAASA,aAAaC,IAAY,EAAEC,OAAiB;IAC1D,MAAMC,OAAc,EAAE;IACtB,MAAMC,SAASC,CAAAA,GAAAA,cAAAA,YAAY,EAACJ,MAAME,MAAM;QACtCG,WAAW;QACXC,WACE,OAAA,CAAOL,WAAAA,OAAAA,KAAAA,IAAAA,QAASK,SAAS,MAAK,YAAYL,QAAQK,SAAS,GAAG;QAChEC,MAAM,EAAEN,WAAAA,OAAAA,KAAAA,IAAAA,QAASM,MAAM;IACzB;IAEA,MAAMC,UAAUC,CAAAA,GAAAA,cAAAA,gBAAgB,EAC9BR,CAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASS,aAAa,IAClB,IAAIC,OAAOV,QAAQS,aAAa,CAACP,OAAOS,MAAM,GAAGT,OAAOU,KAAK,IAC7DV,QACJD;IAGF;;;;;GAKC,GACD,OAAO,CAACY,UAAUC;QAChB,+CAA+C;QAC/C,IAAI,OAAOD,aAAa,UAAU,OAAO;QAEzC,MAAME,QAAQR,QAAQM;QAEtB,sDAAsD;QACtD,IAAI,CAACE,OAAO,OAAO;QAEnB;;;;KAIC,GACD,IAAIf,WAAAA,OAAAA,KAAAA,IAAAA,QAASgB,mBAAmB,EAAE;YAChC,KAAK,MAAMC,OAAOhB,KAAM;gBACtB,IAAI,OAAOgB,IAAIC,IAAI,KAAK,UAAU;oBAChC,OAAOH,MAAMD,MAAM,CAACG,IAAIC,IAAI,CAAC;gBAC/B;YACF;QACF;QAEA,OAAO;YAAE,GAAGJ,MAAM;YAAE,GAAGC,MAAMD,MAAM;QAAC;IACtC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2420, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/escape-regexp.ts"], "sourcesContent": ["// regexp is based on https://github.com/sindresorhus/escape-string-regexp\nconst reHasRegExp = /[|\\\\{}()[\\]^$+*?.-]/\nconst reReplaceRegExp = /[|\\\\{}()[\\]^$+*?.-]/g\n\nexport function escapeStringRegexp(str: string) {\n  // see also: https://github.com/lodash/lodash/blob/2da024c3b4f9947a48517639de7560457cd4ec6c/escapeRegExp.js#L23\n  if (reHasRegExp.test(str)) {\n    return str.replace(reReplaceRegExp, '\\\\$&')\n  }\n  return str\n}\n"], "names": ["escapeStringRegexp", "reHasRegExp", "reReplaceRegExp", "str", "test", "replace"], "mappings": "AAAA,0EAA0E;;;;;+BAI1DA,sBAAAA;;;eAAAA;;;AAHhB,MAAMC,cAAc;AACpB,MAAMC,kBAAkB;AAEjB,SAASF,mBAAmBG,GAAW;IAC5C,+GAA+G;IAC/G,IAAIF,YAAYG,IAAI,CAACD,MAAM;QACzB,OAAOA,IAAIE,OAAO,CAACH,iBAAiB;IACtC;IACA,OAAOC;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2445, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/parse-url.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nimport { searchParamsToUrlQuery } from './querystring'\nimport { parseRelativeUrl } from './parse-relative-url'\n\nexport interface ParsedUrl {\n  hash: string\n  hostname?: string | null\n  href: string\n  pathname: string\n  port?: string | null\n  protocol?: string | null\n  query: ParsedUrlQuery\n  search: string\n}\n\nexport function parseUrl(url: string): ParsedUrl {\n  if (url.startsWith('/')) {\n    return parseRelativeUrl(url)\n  }\n\n  const parsedURL = new URL(url)\n  return {\n    hash: parsedURL.hash,\n    hostname: parsedURL.hostname,\n    href: parsedURL.href,\n    pathname: parsedURL.pathname,\n    port: parsedURL.port,\n    protocol: parsedURL.protocol,\n    query: searchParamsToUrlQuery(parsedURL.searchParams),\n    search: parsedURL.search,\n  }\n}\n"], "names": ["parseUrl", "url", "startsWith", "parseRelativeUrl", "parsedURL", "URL", "hash", "hostname", "href", "pathname", "port", "protocol", "query", "searchParamsToUrlQuery", "searchParams", "search"], "mappings": ";;;;+BAgBgBA,YAAAA;;;eAAAA;;;6BAduB;kCACN;AAa1B,SAASA,SAASC,GAAW;IAClC,IAAIA,IAAIC,UAAU,CAAC,MAAM;QACvB,OAAOC,CAAAA,GAAAA,kBAAAA,gBAAgB,EAACF;IAC1B;IAEA,MAAMG,YAAY,IAAIC,IAAIJ;IAC1B,OAAO;QACLK,MAAMF,UAAUE,IAAI;QACpBC,UAAUH,UAAUG,QAAQ;QAC5BC,MAAMJ,UAAUI,IAAI;QACpBC,UAAUL,UAAUK,QAAQ;QAC5BC,MAAMN,UAAUM,IAAI;QACpBC,UAAUP,UAAUO,QAAQ;QAC5BC,OAAOC,CAAAA,GAAAA,aAAAA,sBAAsB,EAACT,UAAUU,YAAY;QACpDC,QAAQX,UAAUW,MAAM;IAC1B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2478, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/client/components/app-router-headers.ts"], "sourcesContent": ["export const RSC_HEADER = 'RSC' as const\nexport const ACTION_HEADER = 'Next-Action' as const\n// TODO: Instead of sending the full router state, we only need to send the\n// segment path. Saves bytes. Then we could also use this field for segment\n// prefetches, which also need to specify a particular segment.\nexport const NEXT_ROUTER_STATE_TREE_HEADER = 'Next-Router-State-Tree' as const\nexport const NEXT_ROUTER_PREFETCH_HEADER = 'Next-Router-Prefetch' as const\n// This contains the path to the segment being prefetched.\n// TODO: If we change Next-Router-State-Tree to be a segment path, we can use\n// that instead. Then Next-Router-Prefetch and Next-Router-Segment-Prefetch can\n// be merged into a single enum.\nexport const NEXT_ROUTER_SEGMENT_PREFETCH_HEADER =\n  'Next-Router-Segment-Prefetch' as const\nexport const NEXT_HMR_REFRESH_HEADER = 'Next-HMR-Refresh' as const\nexport const NEXT_HMR_REFRESH_HASH_COOKIE = '__next_hmr_refresh_hash__' as const\nexport const NEXT_URL = 'Next-Url' as const\nexport const RSC_CONTENT_TYPE_HEADER = 'text/x-component' as const\n\nexport const FLIGHT_HEADERS = [\n  RSC_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n] as const\n\nexport const NEXT_RSC_UNION_QUERY = '_rsc' as const\n\nexport const NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time' as const\nexport const NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed' as const\nexport const NEXT_REWRITTEN_PATH_HEADER = 'x-nextjs-rewritten-path' as const\nexport const NEXT_REWRITTEN_QUERY_HEADER = 'x-nextjs-rewritten-query' as const\nexport const NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender' as const\n"], "names": ["ACTION_HEADER", "FLIGHT_HEADERS", "NEXT_DID_POSTPONE_HEADER", "NEXT_HMR_REFRESH_HASH_COOKIE", "NEXT_HMR_REFRESH_HEADER", "NEXT_IS_PRERENDER_HEADER", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_ROUTER_STALE_TIME_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_RSC_UNION_QUERY", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "RSC_HEADER"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;IACaA,aAAa,EAAA;eAAbA;;IAiBAC,cAAc,EAAA;eAAdA;;IAWAC,wBAAwB,EAAA;eAAxBA;;IAfAC,4BAA4B,EAAA;eAA5BA;;IADAC,uBAAuB,EAAA;eAAvBA;;IAmBAC,wBAAwB,EAAA;eAAxBA;;IAFAC,0BAA0B,EAAA;eAA1BA;;IACAC,2BAA2B,EAAA;eAA3BA;;IAzBAC,2BAA2B,EAAA;eAA3BA;;IAKAC,mCAAmC,EAAA;eAAnCA;;IAiBAC,6BAA6B,EAAA;eAA7BA;;IAvBAC,6BAA6B,EAAA;eAA7BA;;IAqBAC,oBAAoB,EAAA;eAApBA;;IAXAC,QAAQ,EAAA;eAARA;;IACAC,uBAAuB,EAAA;eAAvBA;;IAhBAC,UAAU,EAAA;eAAVA;;;AAAN,MAAMA,aAAa;AACnB,MAAMf,gBAAgB;AAItB,MAAMW,gCAAgC;AACtC,MAAMH,8BAA8B;AAKpC,MAAMC,sCACX;AACK,MAAML,0BAA0B;AAChC,MAAMD,+BAA+B;AACrC,MAAMU,WAAW;AACjB,MAAMC,0BAA0B;AAEhC,MAAMb,iBAAiB;IAC5Bc;IACAJ;IACAH;IACAJ;IACAK;CACD;AAEM,MAAMG,uBAAuB;AAE7B,MAAMF,gCAAgC;AACtC,MAAMR,2BAA2B;AACjC,MAAMI,6BAA6B;AACnC,MAAMC,8BAA8B;AACpC,MAAMF,2BAA2B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2590, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/dist/compiled/cookie/index.js"], "sourcesContent": ["(()=>{\"use strict\";if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var e={};(()=>{var r=e;\n/*!\n * cookie\n * Copyright(c) 2012-2014 <PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */r.parse=parse;r.serialize=serialize;var i=decodeURIComponent;var t=encodeURIComponent;var a=/; */;var n=/^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;function parse(e,r){if(typeof e!==\"string\"){throw new TypeError(\"argument str must be a string\")}var t={};var n=r||{};var o=e.split(a);var s=n.decode||i;for(var p=0;p<o.length;p++){var f=o[p];var u=f.indexOf(\"=\");if(u<0){continue}var v=f.substr(0,u).trim();var c=f.substr(++u,f.length).trim();if('\"'==c[0]){c=c.slice(1,-1)}if(undefined==t[v]){t[v]=tryDecode(c,s)}}return t}function serialize(e,r,i){var a=i||{};var o=a.encode||t;if(typeof o!==\"function\"){throw new TypeError(\"option encode is invalid\")}if(!n.test(e)){throw new TypeError(\"argument name is invalid\")}var s=o(r);if(s&&!n.test(s)){throw new TypeError(\"argument val is invalid\")}var p=e+\"=\"+s;if(null!=a.maxAge){var f=a.maxAge-0;if(isNaN(f)||!isFinite(f)){throw new TypeError(\"option maxAge is invalid\")}p+=\"; Max-Age=\"+Math.floor(f)}if(a.domain){if(!n.test(a.domain)){throw new TypeError(\"option domain is invalid\")}p+=\"; Domain=\"+a.domain}if(a.path){if(!n.test(a.path)){throw new TypeError(\"option path is invalid\")}p+=\"; Path=\"+a.path}if(a.expires){if(typeof a.expires.toUTCString!==\"function\"){throw new TypeError(\"option expires is invalid\")}p+=\"; Expires=\"+a.expires.toUTCString()}if(a.httpOnly){p+=\"; HttpOnly\"}if(a.secure){p+=\"; Secure\"}if(a.sameSite){var u=typeof a.sameSite===\"string\"?a.sameSite.toLowerCase():a.sameSite;switch(u){case true:p+=\"; SameSite=Strict\";break;case\"lax\":p+=\"; SameSite=Lax\";break;case\"strict\":p+=\"; SameSite=Strict\";break;case\"none\":p+=\"; SameSite=None\";break;default:throw new TypeError(\"option sameSite is invalid\")}}return p}function tryDecode(e,r){try{return r(e)}catch(r){return e}}})();module.exports=e})();"], "names": [], "mappings": "AAAA,CAAC;IAAK;IAAa,IAAG,OAAO,wBAAsB,aAAY,oBAAoB,EAAE,GAAC,YAAU;IAAI,IAAI,IAAE,CAAC;IAAE,CAAC;QAAK,IAAI,IAAE;QACzH;;;;;CAKC,GAAE,EAAE,KAAK,GAAC;QAAM,EAAE,SAAS,GAAC;QAAU,IAAI,IAAE;QAAmB,IAAI,IAAE;QAAmB,IAAI,IAAE;QAAM,IAAI,IAAE;QAAwC,SAAS,MAAM,CAAC,EAAC,CAAC;YAAE,IAAG,OAAO,MAAI,UAAS;gBAAC,MAAM,IAAI,UAAU;YAAgC;YAAC,IAAI,IAAE,CAAC;YAAE,IAAI,IAAE,KAAG,CAAC;YAAE,IAAI,IAAE,EAAE,KAAK,CAAC;YAAG,IAAI,IAAE,EAAE,MAAM,IAAE;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;gBAAC,IAAI,IAAE,CAAC,CAAC,EAAE;gBAAC,IAAI,IAAE,EAAE,OAAO,CAAC;gBAAK,IAAG,IAAE,GAAE;oBAAC;gBAAQ;gBAAC,IAAI,IAAE,EAAE,MAAM,CAAC,GAAE,GAAG,IAAI;gBAAG,IAAI,IAAE,EAAE,MAAM,CAAC,EAAE,GAAE,EAAE,MAAM,EAAE,IAAI;gBAAG,IAAG,OAAK,CAAC,CAAC,EAAE,EAAC;oBAAC,IAAE,EAAE,KAAK,CAAC,GAAE,CAAC;gBAAE;gBAAC,IAAG,aAAW,CAAC,CAAC,EAAE,EAAC;oBAAC,CAAC,CAAC,EAAE,GAAC,UAAU,GAAE;gBAAE;YAAC;YAAC,OAAO;QAAC;QAAC,SAAS,UAAU,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,KAAG,CAAC;YAAE,IAAI,IAAE,EAAE,MAAM,IAAE;YAAE,IAAG,OAAO,MAAI,YAAW;gBAAC,MAAM,IAAI,UAAU;YAA2B;YAAC,IAAG,CAAC,EAAE,IAAI,CAAC,IAAG;gBAAC,MAAM,IAAI,UAAU;YAA2B;YAAC,IAAI,IAAE,EAAE;YAAG,IAAG,KAAG,CAAC,EAAE,IAAI,CAAC,IAAG;gBAAC,MAAM,IAAI,UAAU;YAA0B;YAAC,IAAI,IAAE,IAAE,MAAI;YAAE,IAAG,QAAM,EAAE,MAAM,EAAC;gBAAC,IAAI,IAAE,EAAE,MAAM,GAAC;gBAAE,IAAG,MAAM,MAAI,CAAC,SAAS,IAAG;oBAAC,MAAM,IAAI,UAAU;gBAA2B;gBAAC,KAAG,eAAa,KAAK,KAAK,CAAC;YAAE;YAAC,IAAG,EAAE,MAAM,EAAC;gBAAC,IAAG,CAAC,EAAE,IAAI,CAAC,EAAE,MAAM,GAAE;oBAAC,MAAM,IAAI,UAAU;gBAA2B;gBAAC,KAAG,cAAY,EAAE,MAAM;YAAA;YAAC,IAAG,EAAE,IAAI,EAAC;gBAAC,IAAG,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,GAAE;oBAAC,MAAM,IAAI,UAAU;gBAAyB;gBAAC,KAAG,YAAU,EAAE,IAAI;YAAA;YAAC,IAAG,EAAE,OAAO,EAAC;gBAAC,IAAG,OAAO,EAAE,OAAO,CAAC,WAAW,KAAG,YAAW;oBAAC,MAAM,IAAI,UAAU;gBAA4B;gBAAC,KAAG,eAAa,EAAE,OAAO,CAAC,WAAW;YAAE;YAAC,IAAG,EAAE,QAAQ,EAAC;gBAAC,KAAG;YAAY;YAAC,IAAG,EAAE,MAAM,EAAC;gBAAC,KAAG;YAAU;YAAC,IAAG,EAAE,QAAQ,EAAC;gBAAC,IAAI,IAAE,OAAO,EAAE,QAAQ,KAAG,WAAS,EAAE,QAAQ,CAAC,WAAW,KAAG,EAAE,QAAQ;gBAAC,OAAO;oBAAG,KAAK;wBAAK,KAAG;wBAAoB;oBAAM,KAAI;wBAAM,KAAG;wBAAiB;oBAAM,KAAI;wBAAS,KAAG;wBAAoB;oBAAM,KAAI;wBAAO,KAAG;wBAAkB;oBAAM;wBAAQ,MAAM,IAAI,UAAU;gBAA6B;YAAC;YAAC,OAAO;QAAC;QAAC,SAAS,UAAU,CAAC,EAAC,CAAC;YAAE,IAAG;gBAAC,OAAO,EAAE;YAAE,EAAC,OAAM,GAAE;gBAAC,OAAO;YAAC;QAAC;IAAC,CAAC;IAAI,OAAO,OAAO,GAAC;AAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2713, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/server/api-utils/get-cookie-parser.ts"], "sourcesContent": ["import type { NextApiRequestCookies } from '.'\n\n/**\n * Parse cookies from the `headers` of request\n * @param req request object\n */\n\nexport function getCookieParser(headers: {\n  [key: string]: string | string[] | null | undefined\n}): () => NextApiRequestCookies {\n  return function parseCookie(): NextApiRequestCookies {\n    const { cookie } = headers\n\n    if (!cookie) {\n      return {}\n    }\n\n    const { parse: parseCookieFn } = require('next/dist/compiled/cookie')\n    return parseCookieFn(Array.isArray(cookie) ? cookie.join('; ') : cookie)\n  }\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "headers", "parse<PERSON><PERSON><PERSON>", "cookie", "parse", "parseCookieFn", "require", "Array", "isArray", "join"], "mappings": ";;;;+BAOgBA,mBAAAA;;;eAAAA;;;AAAT,SAASA,gBAAgBC,OAE/B;IACC,OAAO,SAASC;QACd,MAAM,EAAEC,MAAM,EAAE,GAAGF;QAEnB,IAAI,CAACE,QAAQ;YACX,OAAO,CAAC;QACV;QAEA,MAAM,EAAEC,OAAOC,aAAa,EAAE,GAAGC,QAAQ;QACzC,OAAOD,cAAcE,MAAMC,OAAO,CAACL,UAAUA,OAAOM,IAAI,CAAC,QAAQN;IACnE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2738, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/prepare-destination.ts"], "sourcesContent": ["import type { IncomingMessage } from 'http'\nimport type { Key } from 'next/dist/compiled/path-to-regexp'\nimport type { NextParsedUrlQuery } from '../../../../server/request-meta'\nimport type { RouteHas } from '../../../../lib/load-custom-routes'\nimport type { BaseNextRequest } from '../../../../server/base-http'\n\nimport { compile, pathToRegexp } from 'next/dist/compiled/path-to-regexp'\nimport { escapeStringRegexp } from '../../escape-regexp'\nimport { parseUrl } from './parse-url'\nimport {\n  INTERCEPTION_ROUTE_MARKERS,\n  isInterceptionRouteAppPath,\n} from './interception-routes'\nimport { NEXT_RSC_UNION_QUERY } from '../../../../client/components/app-router-headers'\nimport { getCookieParser } from '../../../../server/api-utils/get-cookie-parser'\nimport type { Params } from '../../../../server/request/params'\n\n/**\n * Ensure only a-zA-Z are used for param names for proper interpolating\n * with path-to-regexp\n */\nfunction getSafeParamName(paramName: string) {\n  let newParamName = ''\n\n  for (let i = 0; i < paramName.length; i++) {\n    const charCode = paramName.charCodeAt(i)\n\n    if (\n      (charCode > 64 && charCode < 91) || // A-Z\n      (charCode > 96 && charCode < 123) // a-z\n    ) {\n      newParamName += paramName[i]\n    }\n  }\n  return newParamName\n}\n\nfunction escapeSegment(str: string, segmentName: string) {\n  return str.replace(\n    new RegExp(`:${escapeStringRegexp(segmentName)}`, 'g'),\n    `__ESC_COLON_${segmentName}`\n  )\n}\n\nfunction unescapeSegments(str: string) {\n  return str.replace(/__ESC_COLON_/gi, ':')\n}\n\nexport function matchHas(\n  req: BaseNextRequest | IncomingMessage,\n  query: Params,\n  has: RouteHas[] = [],\n  missing: RouteHas[] = []\n): false | Params {\n  const params: Params = {}\n\n  const hasMatch = (hasItem: RouteHas) => {\n    let value\n    let key = hasItem.key\n\n    switch (hasItem.type) {\n      case 'header': {\n        key = key!.toLowerCase()\n        value = req.headers[key] as string\n        break\n      }\n      case 'cookie': {\n        if ('cookies' in req) {\n          value = req.cookies[hasItem.key]\n        } else {\n          const cookies = getCookieParser(req.headers)()\n          value = cookies[hasItem.key]\n        }\n\n        break\n      }\n      case 'query': {\n        value = query[key!]\n        break\n      }\n      case 'host': {\n        const { host } = req?.headers || {}\n        // remove port from host if present\n        const hostname = host?.split(':', 1)[0].toLowerCase()\n        value = hostname\n        break\n      }\n      default: {\n        break\n      }\n    }\n\n    if (!hasItem.value && value) {\n      params[getSafeParamName(key!)] = value\n      return true\n    } else if (value) {\n      const matcher = new RegExp(`^${hasItem.value}$`)\n      const matches = Array.isArray(value)\n        ? value.slice(-1)[0].match(matcher)\n        : value.match(matcher)\n\n      if (matches) {\n        if (Array.isArray(matches)) {\n          if (matches.groups) {\n            Object.keys(matches.groups).forEach((groupKey) => {\n              params[groupKey] = matches.groups![groupKey]\n            })\n          } else if (hasItem.type === 'host' && matches[0]) {\n            params.host = matches[0]\n          }\n        }\n        return true\n      }\n    }\n    return false\n  }\n\n  const allMatch =\n    has.every((item) => hasMatch(item)) &&\n    !missing.some((item) => hasMatch(item))\n\n  if (allMatch) {\n    return params\n  }\n  return false\n}\n\nexport function compileNonPath(value: string, params: Params): string {\n  if (!value.includes(':')) {\n    return value\n  }\n\n  for (const key of Object.keys(params)) {\n    if (value.includes(`:${key}`)) {\n      value = value\n        .replace(\n          new RegExp(`:${key}\\\\*`, 'g'),\n          `:${key}--ESCAPED_PARAM_ASTERISKS`\n        )\n        .replace(\n          new RegExp(`:${key}\\\\?`, 'g'),\n          `:${key}--ESCAPED_PARAM_QUESTION`\n        )\n        .replace(new RegExp(`:${key}\\\\+`, 'g'), `:${key}--ESCAPED_PARAM_PLUS`)\n        .replace(\n          new RegExp(`:${key}(?!\\\\w)`, 'g'),\n          `--ESCAPED_PARAM_COLON${key}`\n        )\n    }\n  }\n  value = value\n    .replace(/(:|\\*|\\?|\\+|\\(|\\)|\\{|\\})/g, '\\\\$1')\n    .replace(/--ESCAPED_PARAM_PLUS/g, '+')\n    .replace(/--ESCAPED_PARAM_COLON/g, ':')\n    .replace(/--ESCAPED_PARAM_QUESTION/g, '?')\n    .replace(/--ESCAPED_PARAM_ASTERISKS/g, '*')\n\n  // the value needs to start with a forward-slash to be compiled\n  // correctly\n  return compile(`/${value}`, { validate: false })(params).slice(1)\n}\n\nexport function parseDestination(args: {\n  destination: string\n  params: Readonly<Params>\n  query: Readonly<NextParsedUrlQuery>\n}) {\n  let escaped = args.destination\n  for (const param of Object.keys({ ...args.params, ...args.query })) {\n    if (!param) continue\n\n    escaped = escapeSegment(escaped, param)\n  }\n\n  const parsed = parseUrl(escaped)\n\n  let pathname = parsed.pathname\n  if (pathname) {\n    pathname = unescapeSegments(pathname)\n  }\n\n  let href = parsed.href\n  if (href) {\n    href = unescapeSegments(href)\n  }\n\n  let hostname = parsed.hostname\n  if (hostname) {\n    hostname = unescapeSegments(hostname)\n  }\n\n  let hash = parsed.hash\n  if (hash) {\n    hash = unescapeSegments(hash)\n  }\n\n  return {\n    ...parsed,\n    pathname,\n    hostname,\n    href,\n    hash,\n  }\n}\n\nexport function prepareDestination(args: {\n  appendParamsToQuery: boolean\n  destination: string\n  params: Params\n  query: NextParsedUrlQuery\n}) {\n  const query = Object.assign({}, args.query)\n  delete query[NEXT_RSC_UNION_QUERY]\n\n  const parsedDestination = parseDestination(args)\n\n  const { hostname: destHostname, query: destQuery } = parsedDestination\n\n  // The following code assumes that the pathname here includes the hash if it's\n  // present.\n  let destPath = parsedDestination.pathname\n  if (parsedDestination.hash) {\n    destPath = `${destPath}${parsedDestination.hash}`\n  }\n\n  const destParams: (string | number)[] = []\n\n  const destPathParamKeys: Key[] = []\n  pathToRegexp(destPath, destPathParamKeys)\n  for (const key of destPathParamKeys) {\n    destParams.push(key.name)\n  }\n\n  if (destHostname) {\n    const destHostnameParamKeys: Key[] = []\n    pathToRegexp(destHostname, destHostnameParamKeys)\n    for (const key of destHostnameParamKeys) {\n      destParams.push(key.name)\n    }\n  }\n\n  const destPathCompiler = compile(\n    destPath,\n    // we don't validate while compiling the destination since we should\n    // have already validated before we got to this point and validating\n    // breaks compiling destinations with named pattern params from the source\n    // e.g. /something:hello(.*) -> /another/:hello is broken with validation\n    // since compile validation is meant for reversing and not for inserting\n    // params from a separate path-regex into another\n    { validate: false }\n  )\n\n  let destHostnameCompiler\n  if (destHostname) {\n    destHostnameCompiler = compile(destHostname, { validate: false })\n  }\n\n  // update any params in query values\n  for (const [key, strOrArray] of Object.entries(destQuery)) {\n    // the value needs to start with a forward-slash to be compiled\n    // correctly\n    if (Array.isArray(strOrArray)) {\n      destQuery[key] = strOrArray.map((value) =>\n        compileNonPath(unescapeSegments(value), args.params)\n      )\n    } else if (typeof strOrArray === 'string') {\n      destQuery[key] = compileNonPath(unescapeSegments(strOrArray), args.params)\n    }\n  }\n\n  // add path params to query if it's not a redirect and not\n  // already defined in destination query or path\n  let paramKeys = Object.keys(args.params).filter(\n    (name) => name !== 'nextInternalLocale'\n  )\n\n  if (\n    args.appendParamsToQuery &&\n    !paramKeys.some((key) => destParams.includes(key))\n  ) {\n    for (const key of paramKeys) {\n      if (!(key in destQuery)) {\n        destQuery[key] = args.params[key]\n      }\n    }\n  }\n\n  let newUrl\n\n  // The compiler also that the interception route marker is an unnamed param, hence '0',\n  // so we need to add it to the params object.\n  if (isInterceptionRouteAppPath(destPath)) {\n    for (const segment of destPath.split('/')) {\n      const marker = INTERCEPTION_ROUTE_MARKERS.find((m) =>\n        segment.startsWith(m)\n      )\n      if (marker) {\n        if (marker === '(..)(..)') {\n          args.params['0'] = '(..)'\n          args.params['1'] = '(..)'\n        } else {\n          args.params['0'] = marker\n        }\n        break\n      }\n    }\n  }\n\n  try {\n    newUrl = destPathCompiler(args.params)\n\n    const [pathname, hash] = newUrl.split('#', 2)\n    if (destHostnameCompiler) {\n      parsedDestination.hostname = destHostnameCompiler(args.params)\n    }\n    parsedDestination.pathname = pathname\n    parsedDestination.hash = `${hash ? '#' : ''}${hash || ''}`\n    delete (parsedDestination as any).search\n  } catch (err: any) {\n    if (err.message.match(/Expected .*? to not repeat, but got an array/)) {\n      throw new Error(\n        `To use a multi-match in the destination you must add \\`*\\` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match`\n      )\n    }\n    throw err\n  }\n\n  // Query merge order lowest priority to highest\n  // 1. initial URL query values\n  // 2. path segment values\n  // 3. destination specified query values\n  parsedDestination.query = {\n    ...query,\n    ...parsedDestination.query,\n  }\n\n  return {\n    newUrl,\n    destQuery,\n    parsedDestination,\n  }\n}\n"], "names": ["compileNonPath", "matchHas", "parseDestination", "prepareDestination", "getSafeParamName", "paramName", "newParamName", "i", "length", "charCode", "charCodeAt", "escapeSegment", "str", "segmentName", "replace", "RegExp", "escapeStringRegexp", "unescapeSegments", "req", "query", "has", "missing", "params", "hasMatch", "hasItem", "value", "key", "type", "toLowerCase", "headers", "cookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "host", "hostname", "split", "matcher", "matches", "Array", "isArray", "slice", "match", "groups", "Object", "keys", "for<PERSON>ach", "groupKey", "allMatch", "every", "item", "some", "includes", "compile", "validate", "args", "escaped", "destination", "param", "parsed", "parseUrl", "pathname", "href", "hash", "assign", "NEXT_RSC_UNION_QUERY", "parsedDestination", "destHostname", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "destPath", "destParams", "destPathPara<PERSON><PERSON><PERSON>s", "pathToRegexp", "push", "name", "destHostnameParamKeys", "destPathCompiler", "destHostnameCompiler", "strOrArray", "entries", "map", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "appendParamsToQuery", "newUrl", "isInterceptionRouteAppPath", "segment", "marker", "INTERCEPTION_ROUTE_MARKERS", "find", "m", "startsWith", "search", "err", "message", "Error"], "mappings": ";;;;;;;;;;;;;;;;;IA+HgBA,cAAc,EAAA;eAAdA;;IA/EAC,QAAQ,EAAA;eAARA;;IAkHAC,gBAAgB,EAAA;eAAhBA;;IA2CAC,kBAAkB,EAAA;eAAlBA;;;8BAvMsB;8BACH;0BACV;oCAIlB;kCAC8B;iCACL;AAGhC;;;CAGC,GACD,SAASC,iBAAiBC,SAAiB;IACzC,IAAIC,eAAe;IAEnB,IAAK,IAAIC,IAAI,GAAGA,IAAIF,UAAUG,MAAM,EAAED,IAAK;QACzC,MAAME,WAAWJ,UAAUK,UAAU,CAACH;QAEtC,IACGE,WAAW,MAAMA,WAAW,MAAO,MAAM;QACzCA,WAAW,MAAMA,WAAW,IAAK,MAAM;UACxC;YACAH,gBAAgBD,SAAS,CAACE,EAAE;QAC9B;IACF;IACA,OAAOD;AACT;AAEA,SAASK,cAAcC,GAAW,EAAEC,WAAmB;IACrD,OAAOD,IAAIE,OAAO,CAChB,IAAIC,OAAQ,MAAGC,CAAAA,GAAAA,cAAAA,kBAAkB,EAACH,cAAgB,MACjD,iBAAcA;AAEnB;AAEA,SAASI,iBAAiBL,GAAW;IACnC,OAAOA,IAAIE,OAAO,CAAC,kBAAkB;AACvC;AAEO,SAASb,SACdiB,GAAsC,EACtCC,KAAa,EACbC,GAAoB,EACpBC,OAAwB;IADxBD,IAAAA,QAAAA,KAAAA,GAAAA,MAAkB,EAAE;IACpBC,IAAAA,YAAAA,KAAAA,GAAAA,UAAsB,EAAE;IAExB,MAAMC,SAAiB,CAAC;IAExB,MAAMC,WAAW,CAACC;QAChB,IAAIC;QACJ,IAAIC,MAAMF,QAAQE,GAAG;QAErB,OAAQF,QAAQG,IAAI;YAClB,KAAK;gBAAU;oBACbD,MAAMA,IAAKE,WAAW;oBACtBH,QAAQP,IAAIW,OAAO,CAACH,IAAI;oBACxB;gBACF;YACA,KAAK;gBAAU;oBACb,IAAI,aAAaR,KAAK;wBACpBO,QAAQP,IAAIY,OAAO,CAACN,QAAQE,GAAG,CAAC;oBAClC,OAAO;wBACL,MAAMI,UAAUC,CAAAA,GAAAA,iBAAAA,eAAe,EAACb,IAAIW,OAAO;wBAC3CJ,QAAQK,OAAO,CAACN,QAAQE,GAAG,CAAC;oBAC9B;oBAEA;gBACF;YACA,KAAK;gBAAS;oBACZD,QAAQN,KAAK,CAACO,IAAK;oBACnB;gBACF;YACA,KAAK;gBAAQ;oBACX,MAAM,EAAEM,IAAI,EAAE,GAAGd,CAAAA,OAAAA,OAAAA,KAAAA,IAAAA,IAAKW,OAAO,KAAI,CAAC;oBAClC,mCAAmC;oBACnC,MAAMI,WAAWD,QAAAA,OAAAA,KAAAA,IAAAA,KAAME,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAACN,WAAW;oBACnDH,QAAQQ;oBACR;gBACF;YACA;gBAAS;oBACP;gBACF;QACF;QAEA,IAAI,CAACT,QAAQC,KAAK,IAAIA,OAAO;YAC3BH,MAAM,CAAClB,iBAAiBsB,KAAM,GAAGD;YACjC,OAAO;QACT,OAAO,IAAIA,OAAO;YAChB,MAAMU,UAAU,IAAIpB,OAAQ,MAAGS,QAAQC,KAAK,GAAC;YAC7C,MAAMW,UAAUC,MAAMC,OAAO,CAACb,SAC1BA,MAAMc,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAACC,KAAK,CAACL,WACzBV,MAAMe,KAAK,CAACL;YAEhB,IAAIC,SAAS;gBACX,IAAIC,MAAMC,OAAO,CAACF,UAAU;oBAC1B,IAAIA,QAAQK,MAAM,EAAE;wBAClBC,OAAOC,IAAI,CAACP,QAAQK,MAAM,EAAEG,OAAO,CAAC,CAACC;4BACnCvB,MAAM,CAACuB,SAAS,GAAGT,QAAQK,MAAO,CAACI,SAAS;wBAC9C;oBACF,OAAO,IAAIrB,QAAQG,IAAI,KAAK,UAAUS,OAAO,CAAC,EAAE,EAAE;wBAChDd,OAAOU,IAAI,GAAGI,OAAO,CAAC,EAAE;oBAC1B;gBACF;gBACA,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA,MAAMU,WACJ1B,IAAI2B,KAAK,CAAC,CAACC,OAASzB,SAASyB,UAC7B,CAAC3B,QAAQ4B,IAAI,CAAC,CAACD,OAASzB,SAASyB;IAEnC,IAAIF,UAAU;QACZ,OAAOxB;IACT;IACA,OAAO;AACT;AAEO,SAAStB,eAAeyB,KAAa,EAAEH,MAAc;IAC1D,IAAI,CAACG,MAAMyB,QAAQ,CAAC,MAAM;QACxB,OAAOzB;IACT;IAEA,KAAK,MAAMC,OAAOgB,OAAOC,IAAI,CAACrB,QAAS;QACrC,IAAIG,MAAMyB,QAAQ,CAAE,MAAGxB,MAAQ;YAC7BD,QAAQA,MACLX,OAAO,CACN,IAAIC,OAAQ,MAAGW,MAAI,OAAM,MACxB,MAAGA,MAAI,6BAETZ,OAAO,CACN,IAAIC,OAAQ,MAAGW,MAAI,OAAM,MACxB,MAAGA,MAAI,4BAETZ,OAAO,CAAC,IAAIC,OAAQ,MAAGW,MAAI,OAAM,MAAO,MAAGA,MAAI,wBAC/CZ,OAAO,CACN,IAAIC,OAAQ,MAAGW,MAAI,WAAU,MAC5B,0BAAuBA;QAE9B;IACF;IACAD,QAAQA,MACLX,OAAO,CAAC,6BAA6B,QACrCA,OAAO,CAAC,yBAAyB,KACjCA,OAAO,CAAC,0BAA0B,KAClCA,OAAO,CAAC,6BAA6B,KACrCA,OAAO,CAAC,8BAA8B;IAEzC,+DAA+D;IAC/D,YAAY;IACZ,OAAOqC,CAAAA,GAAAA,cAAAA,OAAO,EAAE,MAAG1B,OAAS;QAAE2B,UAAU;IAAM,GAAG9B,QAAQiB,KAAK,CAAC;AACjE;AAEO,SAASrC,iBAAiBmD,IAIhC;IACC,IAAIC,UAAUD,KAAKE,WAAW;IAC9B,KAAK,MAAMC,SAASd,OAAOC,IAAI,CAAC;QAAE,GAAGU,KAAK/B,MAAM;QAAE,GAAG+B,KAAKlC,KAAK;IAAC,GAAI;QAClE,IAAI,CAACqC,OAAO;QAEZF,UAAU3C,cAAc2C,SAASE;IACnC;IAEA,MAAMC,SAASC,CAAAA,GAAAA,UAAAA,QAAQ,EAACJ;IAExB,IAAIK,WAAWF,OAAOE,QAAQ;IAC9B,IAAIA,UAAU;QACZA,WAAW1C,iBAAiB0C;IAC9B;IAEA,IAAIC,OAAOH,OAAOG,IAAI;IACtB,IAAIA,MAAM;QACRA,OAAO3C,iBAAiB2C;IAC1B;IAEA,IAAI3B,WAAWwB,OAAOxB,QAAQ;IAC9B,IAAIA,UAAU;QACZA,WAAWhB,iBAAiBgB;IAC9B;IAEA,IAAI4B,OAAOJ,OAAOI,IAAI;IACtB,IAAIA,MAAM;QACRA,OAAO5C,iBAAiB4C;IAC1B;IAEA,OAAO;QACL,GAAGJ,MAAM;QACTE;QACA1B;QACA2B;QACAC;IACF;AACF;AAEO,SAAS1D,mBAAmBkD,IAKlC;IACC,MAAMlC,QAAQuB,OAAOoB,MAAM,CAAC,CAAC,GAAGT,KAAKlC,KAAK;IAC1C,OAAOA,KAAK,CAAC4C,kBAAAA,oBAAoB,CAAC;IAElC,MAAMC,oBAAoB9D,iBAAiBmD;IAE3C,MAAM,EAAEpB,UAAUgC,YAAY,EAAE9C,OAAO+C,SAAS,EAAE,GAAGF;IAErD,8EAA8E;IAC9E,WAAW;IACX,IAAIG,WAAWH,kBAAkBL,QAAQ;IACzC,IAAIK,kBAAkBH,IAAI,EAAE;QAC1BM,WAAY,KAAEA,WAAWH,kBAAkBH,IAAI;IACjD;IAEA,MAAMO,aAAkC,EAAE;IAE1C,MAAMC,oBAA2B,EAAE;IACnCC,CAAAA,GAAAA,cAAAA,YAAY,EAACH,UAAUE;IACvB,KAAK,MAAM3C,OAAO2C,kBAAmB;QACnCD,WAAWG,IAAI,CAAC7C,IAAI8C,IAAI;IAC1B;IAEA,IAAIP,cAAc;QAChB,MAAMQ,wBAA+B,EAAE;QACvCH,CAAAA,GAAAA,cAAAA,YAAY,EAACL,cAAcQ;QAC3B,KAAK,MAAM/C,OAAO+C,sBAAuB;YACvCL,WAAWG,IAAI,CAAC7C,IAAI8C,IAAI;QAC1B;IACF;IAEA,MAAME,mBAAmBvB,CAAAA,GAAAA,cAAAA,OAAO,EAC9BgB,UAEA,AADA,oEACoE,AADA;IAEpE,0EAA0E;IAC1E,yEAAyE;IACzE,wEAAwE;IACxE,iDAAiD;IACjD;QAAEf,UAAU;IAAM;IAGpB,IAAIuB;IACJ,IAAIV,cAAc;QAChBU,uBAAuBxB,CAAAA,GAAAA,cAAAA,OAAO,EAACc,cAAc;YAAEb,UAAU;QAAM;IACjE;IAEA,oCAAoC;IACpC,KAAK,MAAM,CAAC1B,KAAKkD,WAAW,IAAIlC,OAAOmC,OAAO,CAACX,WAAY;QACzD,+DAA+D;QAC/D,YAAY;QACZ,IAAI7B,MAAMC,OAAO,CAACsC,aAAa;YAC7BV,SAAS,CAACxC,IAAI,GAAGkD,WAAWE,GAAG,CAAC,CAACrD,QAC/BzB,eAAeiB,iBAAiBQ,QAAQ4B,KAAK/B,MAAM;QAEvD,OAAO,IAAI,OAAOsD,eAAe,UAAU;YACzCV,SAAS,CAACxC,IAAI,GAAG1B,eAAeiB,iBAAiB2D,aAAavB,KAAK/B,MAAM;QAC3E;IACF;IAEA,0DAA0D;IAC1D,+CAA+C;IAC/C,IAAIyD,YAAYrC,OAAOC,IAAI,CAACU,KAAK/B,MAAM,EAAE0D,MAAM,CAC7C,CAACR,OAASA,SAAS;IAGrB,IACEnB,KAAK4B,mBAAmB,IACxB,CAACF,UAAU9B,IAAI,CAAC,CAACvB,MAAQ0C,WAAWlB,QAAQ,CAACxB,OAC7C;QACA,KAAK,MAAMA,OAAOqD,UAAW;YAC3B,IAAI,CAAErD,CAAAA,OAAOwC,SAAQ,GAAI;gBACvBA,SAAS,CAACxC,IAAI,GAAG2B,KAAK/B,MAAM,CAACI,IAAI;YACnC;QACF;IACF;IAEA,IAAIwD;IAEJ,uFAAuF;IACvF,6CAA6C;IAC7C,IAAIC,CAAAA,GAAAA,oBAAAA,0BAA0B,EAAChB,WAAW;QACxC,KAAK,MAAMiB,WAAWjB,SAASjC,KAAK,CAAC,KAAM;YACzC,MAAMmD,SAASC,oBAAAA,0BAA0B,CAACC,IAAI,CAAC,CAACC,IAC9CJ,QAAQK,UAAU,CAACD;YAErB,IAAIH,QAAQ;gBACV,IAAIA,WAAW,YAAY;oBACzBhC,KAAK/B,MAAM,CAAC,IAAI,GAAG;oBACnB+B,KAAK/B,MAAM,CAAC,IAAI,GAAG;gBACrB,OAAO;oBACL+B,KAAK/B,MAAM,CAAC,IAAI,GAAG+D;gBACrB;gBACA;YACF;QACF;IACF;IAEA,IAAI;QACFH,SAASR,iBAAiBrB,KAAK/B,MAAM;QAErC,MAAM,CAACqC,UAAUE,KAAK,GAAGqB,OAAOhD,KAAK,CAAC,KAAK;QAC3C,IAAIyC,sBAAsB;YACxBX,kBAAkB/B,QAAQ,GAAG0C,qBAAqBtB,KAAK/B,MAAM;QAC/D;QACA0C,kBAAkBL,QAAQ,GAAGA;QAC7BK,kBAAkBH,IAAI,GAAI,KAAEA,CAAAA,OAAO,MAAM,EAAC,IAAIA,CAAAA,QAAQ,EAAC;QACvD,OAAQG,kBAA0B0B,MAAM;IAC1C,EAAE,OAAOC,KAAU;QACjB,IAAIA,IAAIC,OAAO,CAACpD,KAAK,CAAC,iDAAiD;YACrE,MAAM,OAAA,cAEL,CAFK,IAAIqD,MACP,4KADG,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,MAAMF;IACR;IAEA,+CAA+C;IAC/C,8BAA8B;IAC9B,yBAAyB;IACzB,wCAAwC;IACxC3B,kBAAkB7C,KAAK,GAAG;QACxB,GAAGA,KAAK;QACR,GAAG6C,kBAAkB7C,KAAK;IAC5B;IAEA,OAAO;QACL+D;QACAhB;QACAF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3027, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/parse-path.ts"], "sourcesContent": ["/**\n * Given a path this function will find the pathname, query and hash and return\n * them. This is useful to parse full paths on the client side.\n * @param path A path to parse e.g. /foo/bar?id=1#hash\n */\nexport function parsePath(path: string) {\n  const hashIndex = path.indexOf('#')\n  const queryIndex = path.indexOf('?')\n  const hasQuery = queryIndex > -1 && (hashIndex < 0 || queryIndex < hashIndex)\n\n  if (hasQuery || hashIndex > -1) {\n    return {\n      pathname: path.substring(0, hasQuery ? queryIndex : hashIndex),\n      query: hasQuery\n        ? path.substring(queryIndex, hashIndex > -1 ? hashIndex : undefined)\n        : '',\n      hash: hashIndex > -1 ? path.slice(hashIndex) : '',\n    }\n  }\n\n  return { pathname: path, query: '', hash: '' }\n}\n"], "names": ["parsePath", "path", "hashIndex", "indexOf", "queryIndex", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "substring", "query", "undefined", "hash", "slice"], "mappings": "AAAA;;;;CAIC,GAAA;;;;+BACeA,aAAAA;;;eAAAA;;;AAAT,SAASA,UAAUC,IAAY;IACpC,MAAMC,YAAYD,KAAKE,OAAO,CAAC;IAC/B,MAAMC,aAAaH,KAAKE,OAAO,CAAC;IAChC,MAAME,WAAWD,aAAa,CAAC,KAAMF,CAAAA,YAAY,KAAKE,aAAaF,SAAQ;IAE3E,IAAIG,YAAYH,YAAY,CAAC,GAAG;QAC9B,OAAO;YACLI,UAAUL,KAAKM,SAAS,CAAC,GAAGF,WAAWD,aAAaF;YACpDM,OAAOH,WACHJ,KAAKM,SAAS,CAACH,YAAYF,YAAY,CAAC,IAAIA,YAAYO,aACxD;YACJC,MAAMR,YAAY,CAAC,IAAID,KAAKU,KAAK,CAACT,aAAa;QACjD;IACF;IAEA,OAAO;QAAEI,UAAUL;QAAMO,OAAO;QAAIE,MAAM;IAAG;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3063, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/path-has-prefix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Checks if a given path starts with a given prefix. It ensures it matches\n * exactly without containing extra chars. e.g. prefix /docs should replace\n * for /docs, /docs/, /docs/a but not /docsss\n * @param path The path to check.\n * @param prefix The prefix to check against.\n */\nexport function pathHasPrefix(path: string, prefix: string) {\n  if (typeof path !== 'string') {\n    return false\n  }\n\n  const { pathname } = parsePath(path)\n  return pathname === prefix || pathname.startsWith(prefix + '/')\n}\n"], "names": ["pathHasPrefix", "path", "prefix", "pathname", "parsePath", "startsWith"], "mappings": ";;;;+BASgBA,iBAAAA;;;eAAAA;;;2BATU;AASnB,SAASA,cAAcC,IAAY,EAAEC,MAAc;IACxD,IAAI,OAAOD,SAAS,UAAU;QAC5B,OAAO;IACT;IAEA,MAAM,EAAEE,QAAQ,EAAE,GAAGC,CAAAA,GAAAA,WAAAA,SAAS,EAACH;IAC/B,OAAOE,aAAaD,UAAUC,SAASE,UAAU,CAACH,SAAS;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3086, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/client/has-base-path.ts"], "sourcesContent": ["import { pathHasPrefix } from '../shared/lib/router/utils/path-has-prefix'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function hasBasePath(path: string): boolean {\n  return pathHasPrefix(path, basePath)\n}\n"], "names": ["has<PERSON>ase<PERSON><PERSON>", "basePath", "process", "env", "__NEXT_ROUTER_BASEPATH", "path", "pathHasPrefix"], "mappings": ";;;;+BAIg<PERSON>,eAAAA;;;eAAAA;;;+BAJc;AAE9B,MAAMC,WAAYC,QAAQC,GAAG,CAACC,sBAAsB,MAAe;AAE5D,SAASJ,YAAYK,IAAY;IACtC,OAAOC,CAAAA,GAAAA,eAAAA,aAAa,EAACD,MAAMJ;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3113, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/client/remove-base-path.ts"], "sourcesContent": ["import { hasBasePath } from './has-base-path'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function removeBasePath(path: string): string {\n  if (process.env.__NEXT_MANUAL_CLIENT_BASE_PATH) {\n    if (!hasBasePath(path)) {\n      return path\n    }\n  }\n\n  // Can't trim the basePath if it has zero length!\n  if (basePath.length === 0) return path\n\n  path = path.slice(basePath.length)\n  if (!path.startsWith('/')) path = `/${path}`\n  return path\n}\n"], "names": ["removeBasePath", "basePath", "process", "env", "__NEXT_ROUTER_BASEPATH", "path", "__NEXT_MANUAL_CLIENT_BASE_PATH", "has<PERSON>ase<PERSON><PERSON>", "length", "slice", "startsWith"], "mappings": ";;;;+BAIg<PERSON>,kBAAAA;;;eAAAA;;;6BAJY;AAE5B,MAAMC,WAAYC,QAAQC,GAAG,CAACC,sBAAsB,MAAe;AAE5D,SAASJ,eAAeK,IAAY;IACzC,IAAIH,QAAQC,GAAG,CAACG,uBAAgC,OAAF;;IAI9C;IAEA,iDAAiD;IACjD,IAAIL,SAASO,MAAM,KAAK,GAAG,OAAOH;IAElCA,OAAOA,KAAKI,KAAK,CAACR,SAASO,MAAM;IACjC,IAAI,CAACH,KAAKK,UAAU,CAAC,MAAML,OAAQ,MAAGA;IACtC,OAAOA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3147, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/resolve-rewrites.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\nimport type { Rewrite } from '../../../../lib/load-custom-routes'\nimport { getPathMatch } from './path-match'\nimport { matchHas, prepareDestination } from './prepare-destination'\nimport { removeTrailingSlash } from './remove-trailing-slash'\nimport { normalizeLocalePath } from '../../i18n/normalize-locale-path'\nimport { removeBasePath } from '../../../../client/remove-base-path'\nimport { parseRelativeUrl, type ParsedRelativeUrl } from './parse-relative-url'\n\nexport default function resolveRewrites(\n  asPath: string,\n  pages: string[],\n  rewrites: {\n    beforeFiles: Rewrite[]\n    afterFiles: Rewrite[]\n    fallback: Rewrite[]\n  },\n  query: ParsedUrlQuery,\n  resolveHref: (path: string) => string,\n  locales?: readonly string[]\n): {\n  matchedPage: boolean\n  parsedAs: ParsedRelativeUrl\n  asPath: string\n  resolvedHref?: string\n  externalDest?: boolean\n} {\n  let matchedPage = false\n  let externalDest = false\n  let parsedAs = parseRelativeUrl(asPath)\n  let fsPathname = removeTrailingSlash(\n    normalizeLocalePath(removeBasePath(parsedAs.pathname), locales).pathname\n  )\n  let resolvedHref\n\n  const handleRewrite = (rewrite: Rewrite) => {\n    const matcher = getPathMatch(\n      rewrite.source + (process.env.__NEXT_TRAILING_SLASH ? '(/)?' : ''),\n      {\n        removeUnnamedParams: true,\n        strict: true,\n      }\n    )\n\n    let params = matcher(parsedAs.pathname)\n\n    if ((rewrite.has || rewrite.missing) && params) {\n      const hasParams = matchHas(\n        {\n          headers: {\n            host: document.location.hostname,\n            'user-agent': navigator.userAgent,\n          },\n          cookies: document.cookie\n            .split('; ')\n            .reduce<Record<string, string>>((acc, item) => {\n              const [key, ...value] = item.split('=')\n              acc[key] = value.join('=')\n              return acc\n            }, {}),\n        } as any,\n        parsedAs.query,\n        rewrite.has,\n        rewrite.missing\n      )\n\n      if (hasParams) {\n        Object.assign(params, hasParams)\n      } else {\n        params = false\n      }\n    }\n\n    if (params) {\n      if (!rewrite.destination) {\n        // this is a proxied rewrite which isn't handled on the client\n        externalDest = true\n        return true\n      }\n      const destRes = prepareDestination({\n        appendParamsToQuery: true,\n        destination: rewrite.destination,\n        params: params,\n        query: query,\n      })\n      parsedAs = destRes.parsedDestination\n      asPath = destRes.newUrl\n      Object.assign(query, destRes.parsedDestination.query)\n\n      fsPathname = removeTrailingSlash(\n        normalizeLocalePath(removeBasePath(asPath), locales).pathname\n      )\n\n      if (pages.includes(fsPathname)) {\n        // check if we now match a page as this means we are done\n        // resolving the rewrites\n        matchedPage = true\n        resolvedHref = fsPathname\n        return true\n      }\n\n      // check if we match a dynamic-route, if so we break the rewrites chain\n      resolvedHref = resolveHref(fsPathname)\n\n      if (resolvedHref !== asPath && pages.includes(resolvedHref)) {\n        matchedPage = true\n        return true\n      }\n    }\n  }\n  let finished = false\n\n  for (let i = 0; i < rewrites.beforeFiles.length; i++) {\n    // we don't end after match in beforeFiles to allow\n    // continuing through all beforeFiles rewrites\n    handleRewrite(rewrites.beforeFiles[i])\n  }\n  matchedPage = pages.includes(fsPathname)\n\n  if (!matchedPage) {\n    if (!finished) {\n      for (let i = 0; i < rewrites.afterFiles.length; i++) {\n        if (handleRewrite(rewrites.afterFiles[i])) {\n          finished = true\n          break\n        }\n      }\n    }\n\n    // check dynamic route before processing fallback rewrites\n    if (!finished) {\n      resolvedHref = resolveHref(fsPathname)\n      matchedPage = pages.includes(resolvedHref)\n      finished = matchedPage\n    }\n\n    if (!finished) {\n      for (let i = 0; i < rewrites.fallback.length; i++) {\n        if (handleRewrite(rewrites.fallback[i])) {\n          finished = true\n          break\n        }\n      }\n    }\n  }\n\n  return {\n    asPath,\n    parsedAs,\n    matchedPage,\n    resolvedHref,\n    externalDest,\n  }\n}\n"], "names": ["resolveRewrites", "<PERSON><PERSON><PERSON>", "pages", "rewrites", "query", "resolveHref", "locales", "matchedPage", "externalDest", "parsedAs", "parseRelativeUrl", "fsPathname", "removeTrailingSlash", "normalizeLocalePath", "removeBasePath", "pathname", "resolvedHref", "handleRewrite", "rewrite", "matcher", "getPathMatch", "source", "process", "env", "__NEXT_TRAILING_SLASH", "removeUnnamedP<PERSON>ms", "strict", "params", "has", "missing", "hasParams", "matchHas", "headers", "host", "document", "location", "hostname", "navigator", "userAgent", "cookies", "cookie", "split", "reduce", "acc", "item", "key", "value", "join", "Object", "assign", "destination", "destRes", "prepareDestination", "appendParamsToQuery", "parsedDestination", "newUrl", "includes", "finished", "i", "beforeFiles", "length", "afterFiles", "fallback"], "mappings": ";;;;+BASA,WAAA;;;eAAwBA;;;2BAPK;oCACgB;qCACT;qCACA;gCACL;kCAC0B;AAE1C,SAASA,gBACtBC,MAAc,EACdC,KAAe,EACfC,QAIC,EACDC,KAAqB,EACrBC,WAAqC,EACrCC,OAA2B;IAQ3B,IAAIC,cAAc;IAClB,IAAIC,eAAe;IACnB,IAAIC,WAAWC,CAAAA,GAAAA,kBAAAA,gBAAgB,EAACT;IAChC,IAAIU,aAAaC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAClCC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACC,CAAAA,GAAAA,gBAAAA,cAAc,EAACL,SAASM,QAAQ,GAAGT,SAASS,QAAQ;IAE1E,IAAIC;IAEJ,MAAMC,gBAAgB,CAACC;QACrB,MAAMC,UAAUC,CAAAA,GAAAA,WAAAA,YAAY,EAC1BF,QAAQG,MAAM,GAAIC,CAAAA,QAAQC,GAAG,CAACC,qBAAqB,GAAG,yCAAS,EAAC,GAChE;YACEC,qBAAqB;YACrBC,QAAQ;QACV;QAGF,IAAIC,SAASR,QAAQV,SAASM,QAAQ;QAEtC,IAAKG,CAAAA,QAAQU,GAAG,IAAIV,QAAQW,OAAM,KAAMF,QAAQ;YAC9C,MAAMG,YAAYC,CAAAA,GAAAA,oBAAAA,QAAQ,EACxB;gBACEC,SAAS;oBACPC,MAAMC,SAASC,QAAQ,CAACC,QAAQ;oBAChC,cAAcC,UAAUC,SAAS;gBACnC;gBACAC,SAASL,SAASM,MAAM,CACrBC,KAAK,CAAC,MACNC,MAAM,CAAyB,CAACC,KAAKC;oBACpC,MAAM,CAACC,KAAK,GAAGC,MAAM,GAAGF,KAAKH,KAAK,CAAC;oBACnCE,GAAG,CAACE,IAAI,GAAGC,MAAMC,IAAI,CAAC;oBACtB,OAAOJ;gBACT,GAAG,CAAC;YACR,GACAlC,SAASL,KAAK,EACdc,QAAQU,GAAG,EACXV,QAAQW,OAAO;YAGjB,IAAIC,WAAW;gBACbkB,OAAOC,MAAM,CAACtB,QAAQG;YACxB,OAAO;gBACLH,SAAS;YACX;QACF;QAEA,IAAIA,QAAQ;YACV,IAAI,CAACT,QAAQgC,WAAW,EAAE;gBACxB,8DAA8D;gBAC9D1C,eAAe;gBACf,OAAO;YACT;YACA,MAAM2C,UAAUC,CAAAA,GAAAA,oBAAAA,kBAAkB,EAAC;gBACjCC,qBAAqB;gBACrBH,aAAahC,QAAQgC,WAAW;gBAChCvB,QAAQA;gBACRvB,OAAOA;YACT;YACAK,WAAW0C,QAAQG,iBAAiB;YACpCrD,SAASkD,QAAQI,MAAM;YACvBP,OAAOC,MAAM,CAAC7C,OAAO+C,QAAQG,iBAAiB,CAAClD,KAAK;YAEpDO,aAAaC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAC9BC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACC,CAAAA,GAAAA,gBAAAA,cAAc,EAACb,SAASK,SAASS,QAAQ;YAG/D,IAAIb,MAAMsD,QAAQ,CAAC7C,aAAa;gBAC9B,yDAAyD;gBACzD,yBAAyB;gBACzBJ,cAAc;gBACdS,eAAeL;gBACf,OAAO;YACT;YAEA,uEAAuE;YACvEK,eAAeX,YAAYM;YAE3B,IAAIK,iBAAiBf,UAAUC,MAAMsD,QAAQ,CAACxC,eAAe;gBAC3DT,cAAc;gBACd,OAAO;YACT;QACF;IACF;IACA,IAAIkD,WAAW;IAEf,IAAK,IAAIC,IAAI,GAAGA,IAAIvD,SAASwD,WAAW,CAACC,MAAM,EAAEF,IAAK;QACpD,mDAAmD;QACnD,8CAA8C;QAC9CzC,cAAcd,SAASwD,WAAW,CAACD,EAAE;IACvC;IACAnD,cAAcL,MAAMsD,QAAQ,CAAC7C;IAE7B,IAAI,CAACJ,aAAa;QAChB,IAAI,CAACkD,UAAU;YACb,IAAK,IAAIC,IAAI,GAAGA,IAAIvD,SAAS0D,UAAU,CAACD,MAAM,EAAEF,IAAK;gBACnD,IAAIzC,cAAcd,SAAS0D,UAAU,CAACH,EAAE,GAAG;oBACzCD,WAAW;oBACX;gBACF;YACF;QACF;QAEA,0DAA0D;QAC1D,IAAI,CAACA,UAAU;YACbzC,eAAeX,YAAYM;YAC3BJ,cAAcL,MAAMsD,QAAQ,CAACxC;YAC7ByC,WAAWlD;QACb;QAEA,IAAI,CAACkD,UAAU;YACb,IAAK,IAAIC,IAAI,GAAGA,IAAIvD,SAAS2D,QAAQ,CAACF,MAAM,EAAEF,IAAK;gBACjD,IAAIzC,cAAcd,SAAS2D,QAAQ,CAACJ,EAAE,GAAG;oBACvCD,WAAW;oBACX;gBACF;YACF;QACF;IACF;IAEA,OAAO;QACLxD;QACAQ;QACAF;QACAS;QACAR;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3268, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/route-matcher.ts"], "sourcesContent": ["import type { Group } from './route-regex'\nimport { DecodeError } from '../../utils'\nimport type { Params } from '../../../../server/request/params'\n\nexport interface RouteMatchFn {\n  (pathname: string): false | Params\n}\n\ntype RouteMatcherOptions = {\n  // We only use the exec method of the RegExp object. This helps us avoid using\n  // type assertions that the passed in properties are of the correct type.\n  re: Pick<RegExp, 'exec'>\n  groups: Record<string, Group>\n}\n\nexport function getRouteMatcher({\n  re,\n  groups,\n}: RouteMatcherOptions): RouteMatchFn {\n  return (pathname: string) => {\n    const routeMatch = re.exec(pathname)\n    if (!routeMatch) return false\n\n    const decode = (param: string) => {\n      try {\n        return decodeURIComponent(param)\n      } catch {\n        throw new DecodeError('failed to decode param')\n      }\n    }\n\n    const params: Params = {}\n    for (const [key, group] of Object.entries(groups)) {\n      const match = routeMatch[group.pos]\n      if (match !== undefined) {\n        if (group.repeat) {\n          params[key] = match.split('/').map((entry) => decode(entry))\n        } else {\n          params[key] = decode(match)\n        }\n      }\n    }\n\n    return params\n  }\n}\n"], "names": ["getRouteMatcher", "re", "groups", "pathname", "routeMatch", "exec", "decode", "param", "decodeURIComponent", "DecodeError", "params", "key", "group", "Object", "entries", "match", "pos", "undefined", "repeat", "split", "map", "entry"], "mappings": ";;;;+BAeg<PERSON>,mBAAAA;;;eAAAA;;;uBAdY;AAcrB,SAASA,gBAAgB,KAGV;IAHU,IAAA,EAC9BC,EAAE,EACFC,MAAM,EACc,GAHU;IAI9B,OAAO,CAACC;QACN,MAAMC,aAAaH,GAAGI,IAAI,CAACF;QAC3B,IAAI,CAACC,YAAY,OAAO;QAExB,MAAME,SAAS,CAACC;YACd,IAAI;gBACF,OAAOC,mBAAmBD;YAC5B,EAAE,OAAA,GAAM;gBACN,MAAM,OAAA,cAAyC,CAAzC,IAAIE,OAAAA,WAAW,CAAC,2BAAhB,qBAAA;2BAAA;gCAAA;kCAAA;gBAAwC;YAChD;QACF;QAEA,MAAMC,SAAiB,CAAC;QACxB,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACZ,QAAS;YACjD,MAAMa,QAAQX,UAAU,CAACQ,MAAMI,GAAG,CAAC;YACnC,IAAID,UAAUE,WAAW;gBACvB,IAAIL,MAAMM,MAAM,EAAE;oBAChBR,MAAM,CAACC,IAAI,GAAGI,MAAMI,KAAK,CAAC,KAAKC,GAAG,CAAC,CAACC,QAAUf,OAAOe;gBACvD,OAAO;oBACLX,MAAM,CAACC,IAAI,GAAGL,OAAOS;gBACvB;YACF;QACF;QAEA,OAAOL;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3314, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/lib/constants.ts"], "sourcesContent": ["import type { ServerRuntime } from '../types'\n\nexport const NEXT_QUERY_PARAM_PREFIX = 'nxtP'\nexport const NEXT_INTERCEPTION_MARKER_PREFIX = 'nxtI'\n\nexport const MATCHED_PATH_HEADER = 'x-matched-path'\nexport const PRERENDER_REVALIDATE_HEADER = 'x-prerender-revalidate'\nexport const PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER =\n  'x-prerender-revalidate-if-generated'\n\nexport const RSC_PREFETCH_SUFFIX = '.prefetch.rsc'\nexport const RSC_SEGMENTS_DIR_SUFFIX = '.segments'\nexport const RSC_SEGMENT_SUFFIX = '.segment.rsc'\nexport const RSC_SUFFIX = '.rsc'\nexport const ACTION_SUFFIX = '.action'\nexport const NEXT_DATA_SUFFIX = '.json'\nexport const NEXT_META_SUFFIX = '.meta'\nexport const NEXT_BODY_SUFFIX = '.body'\n\nexport const NEXT_CACHE_TAGS_HEADER = 'x-next-cache-tags'\nexport const NEXT_CACHE_REVALIDATED_TAGS_HEADER = 'x-next-revalidated-tags'\nexport const NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER =\n  'x-next-revalidate-tag-token'\n\nexport const NEXT_RESUME_HEADER = 'next-resume'\n\n// if these change make sure we update the related\n// documentation as well\nexport const NEXT_CACHE_TAG_MAX_ITEMS = 128\nexport const NEXT_CACHE_TAG_MAX_LENGTH = 256\nexport const NEXT_CACHE_SOFT_TAG_MAX_LENGTH = 1024\nexport const NEXT_CACHE_IMPLICIT_TAG_ID = '_N_T_'\n\n// in seconds\nexport const CACHE_ONE_YEAR = 31536000\n\n// in seconds, represents revalidate=false. I.e. never revaliate.\n// We use this value since it can be represented as a V8 SMI for optimal performance.\n// It can also be serialized as JSON if it ever leaks accidentally as an actual value.\nexport const INFINITE_CACHE = 0xfffffffe\n\n// Patterns to detect middleware files\nexport const MIDDLEWARE_FILENAME = 'middleware'\nexport const MIDDLEWARE_LOCATION_REGEXP = `(?:src/)?${MIDDLEWARE_FILENAME}`\n\n// Pattern to detect instrumentation hooks file\nexport const INSTRUMENTATION_HOOK_FILENAME = 'instrumentation'\n\n// Because on Windows absolute paths in the generated code can break because of numbers, eg 1 in the path,\n// we have to use a private alias\nexport const PAGES_DIR_ALIAS = 'private-next-pages'\nexport const DOT_NEXT_ALIAS = 'private-dot-next'\nexport const ROOT_DIR_ALIAS = 'private-next-root-dir'\nexport const APP_DIR_ALIAS = 'private-next-app-dir'\nexport const RSC_MOD_REF_PROXY_ALIAS = 'private-next-rsc-mod-ref-proxy'\nexport const RSC_ACTION_VALIDATE_ALIAS = 'private-next-rsc-action-validate'\nexport const RSC_ACTION_PROXY_ALIAS = 'private-next-rsc-server-reference'\nexport const RSC_CACHE_WRAPPER_ALIAS = 'private-next-rsc-cache-wrapper'\nexport const RSC_ACTION_ENCRYPTION_ALIAS = 'private-next-rsc-action-encryption'\nexport const RSC_ACTION_CLIENT_WRAPPER_ALIAS =\n  'private-next-rsc-action-client-wrapper'\n\nexport const PUBLIC_DIR_MIDDLEWARE_CONFLICT = `You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict`\n\nexport const SSG_GET_INITIAL_PROPS_CONFLICT = `You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps`\n\nexport const SERVER_PROPS_GET_INIT_PROPS_CONFLICT = `You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.`\n\nexport const SERVER_PROPS_SSG_CONFLICT = `You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps`\n\nexport const STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR = `can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props`\n\nexport const SERVER_PROPS_EXPORT_ERROR = `pages with \\`getServerSideProps\\` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export`\n\nexport const GSP_NO_RETURNED_VALUE =\n  'Your `getStaticProps` function did not return an object. Did you forget to add a `return`?'\nexport const GSSP_NO_RETURNED_VALUE =\n  'Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?'\n\nexport const UNSTABLE_REVALIDATE_RENAME_ERROR =\n  'The `unstable_revalidate` property is available for general use.\\n' +\n  'Please use `revalidate` instead.'\n\nexport const GSSP_COMPONENT_MEMBER_ERROR = `can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member`\n\nexport const NON_STANDARD_NODE_ENV = `You are using a non-standard \"NODE_ENV\" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env`\n\nexport const SSG_FALLBACK_EXPORT_ERROR = `Pages with \\`fallback\\` enabled in \\`getStaticPaths\\` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export`\n\nexport const ESLINT_DEFAULT_DIRS = ['app', 'pages', 'components', 'lib', 'src']\n\nexport const SERVER_RUNTIME: Record<string, ServerRuntime> = {\n  edge: 'edge',\n  experimentalEdge: 'experimental-edge',\n  nodejs: 'nodejs',\n}\n\n/**\n * The names of the webpack layers. These layers are the primitives for the\n * webpack chunks.\n */\nconst WEBPACK_LAYERS_NAMES = {\n  /**\n   * The layer for the shared code between the client and server bundles.\n   */\n  shared: 'shared',\n  /**\n   * The layer for server-only runtime and picking up `react-server` export conditions.\n   * Including app router RSC pages and app router custom routes and metadata routes.\n   */\n  reactServerComponents: 'rsc',\n  /**\n   * Server Side Rendering layer for app (ssr).\n   */\n  serverSideRendering: 'ssr',\n  /**\n   * The browser client bundle layer for actions.\n   */\n  actionBrowser: 'action-browser',\n  /**\n   * The Node.js bundle layer for the API routes.\n   */\n  apiNode: 'api-node',\n  /**\n   * The Edge Lite bundle layer for the API routes.\n   */\n  apiEdge: 'api-edge',\n  /**\n   * The layer for the middleware code.\n   */\n  middleware: 'middleware',\n  /**\n   * The layer for the instrumentation hooks.\n   */\n  instrument: 'instrument',\n  /**\n   * The layer for assets on the edge.\n   */\n  edgeAsset: 'edge-asset',\n  /**\n   * The browser client bundle layer for App directory.\n   */\n  appPagesBrowser: 'app-pages-browser',\n  /**\n   * The browser client bundle layer for Pages directory.\n   */\n  pagesDirBrowser: 'pages-dir-browser',\n  /**\n   * The Edge Lite bundle layer for Pages directory.\n   */\n  pagesDirEdge: 'pages-dir-edge',\n  /**\n   * The Node.js bundle layer for Pages directory.\n   */\n  pagesDirNode: 'pages-dir-node',\n} as const\n\nexport type WebpackLayerName =\n  (typeof WEBPACK_LAYERS_NAMES)[keyof typeof WEBPACK_LAYERS_NAMES]\n\nconst WEBPACK_LAYERS = {\n  ...WEBPACK_LAYERS_NAMES,\n  GROUP: {\n    builtinReact: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n    serverOnly: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.instrument,\n      WEBPACK_LAYERS_NAMES.middleware,\n    ],\n    neutralTarget: [\n      // pages api\n      WEBPACK_LAYERS_NAMES.apiNode,\n      WEBPACK_LAYERS_NAMES.apiEdge,\n    ],\n    clientOnly: [\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n    ],\n    bundled: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.shared,\n      WEBPACK_LAYERS_NAMES.instrument,\n      WEBPACK_LAYERS_NAMES.middleware,\n    ],\n    appPages: [\n      // app router pages and layouts\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n  },\n}\n\nconst WEBPACK_RESOURCE_QUERIES = {\n  edgeSSREntry: '__next_edge_ssr_entry__',\n  metadata: '__next_metadata__',\n  metadataRoute: '__next_metadata_route__',\n  metadataImageMeta: '__next_metadata_image_meta__',\n}\n\nexport { WEBPACK_LAYERS, WEBPACK_RESOURCE_QUERIES }\n"], "names": ["ACTION_SUFFIX", "APP_DIR_ALIAS", "CACHE_ONE_YEAR", "DOT_NEXT_ALIAS", "ESLINT_DEFAULT_DIRS", "GSP_NO_RETURNED_VALUE", "GSSP_COMPONENT_MEMBER_ERROR", "GSSP_NO_RETURNED_VALUE", "INFINITE_CACHE", "INSTRUMENTATION_HOOK_FILENAME", "MATCHED_PATH_HEADER", "MIDDLEWARE_FILENAME", "MIDDLEWARE_LOCATION_REGEXP", "NEXT_BODY_SUFFIX", "NEXT_CACHE_IMPLICIT_TAG_ID", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "NEXT_CACHE_SOFT_TAG_MAX_LENGTH", "NEXT_CACHE_TAGS_HEADER", "NEXT_CACHE_TAG_MAX_ITEMS", "NEXT_CACHE_TAG_MAX_LENGTH", "NEXT_DATA_SUFFIX", "NEXT_INTERCEPTION_MARKER_PREFIX", "NEXT_META_SUFFIX", "NEXT_QUERY_PARAM_PREFIX", "NEXT_RESUME_HEADER", "NON_STANDARD_NODE_ENV", "PAGES_DIR_ALIAS", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "PUBLIC_DIR_MIDDLEWARE_CONFLICT", "ROOT_DIR_ALIAS", "RSC_ACTION_CLIENT_WRAPPER_ALIAS", "RSC_ACTION_ENCRYPTION_ALIAS", "RSC_ACTION_PROXY_ALIAS", "RSC_ACTION_VALIDATE_ALIAS", "RSC_CACHE_WRAPPER_ALIAS", "RSC_MOD_REF_PROXY_ALIAS", "RSC_PREFETCH_SUFFIX", "RSC_SEGMENTS_DIR_SUFFIX", "RSC_SEGMENT_SUFFIX", "RSC_SUFFIX", "SERVER_PROPS_EXPORT_ERROR", "SERVER_PROPS_GET_INIT_PROPS_CONFLICT", "SERVER_PROPS_SSG_CONFLICT", "SERVER_RUNTIME", "SSG_FALLBACK_EXPORT_ERROR", "SSG_GET_INITIAL_PROPS_CONFLICT", "STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR", "UNSTABLE_REVALIDATE_RENAME_ERROR", "WEBPACK_LAYERS", "WEBPACK_RESOURCE_QUERIES", "edge", "experimentalEdge", "nodejs", "WEBPACK_LAYERS_NAMES", "shared", "reactServerComponents", "serverSideRendering", "<PERSON><PERSON><PERSON><PERSON>", "apiNode", "apiEdge", "middleware", "instrument", "edgeAsset", "appPagesBrowser", "pagesDirBrowser", "pagesDirEdge", "pagesDirNode", "GROUP", "builtinReact", "serverOnly", "neutralTarget", "clientOnly", "bundled", "appPages", "edgeSSREntry", "metadata", "metadataRoute", "metadataImageMeta"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAcaA,aAAa,EAAA;eAAbA;;IAuCAC,aAAa,EAAA;eAAbA;;IAnBAC,cAAc,EAAA;eAAdA;;IAiBAC,cAAc,EAAA;eAAdA;;IAsCAC,mBAAmB,EAAA;eAAnBA;;IAfAC,qBAAqB,EAAA;eAArBA;;IASAC,2BAA2B,EAAA;eAA3BA;;IAPAC,sBAAsB,EAAA;eAAtBA;;IArCAC,cAAc,EAAA;eAAdA;;IAOAC,6BAA6B,EAAA;eAA7BA;;IAzCAC,mBAAmB,EAAA;eAAnBA;;IAqCAC,mBAAmB,EAAA;eAAnBA;;IACAC,0BAA0B,EAAA;eAA1BA;;IA1BAC,gBAAgB,EAAA;eAAhBA;;IAcAC,0BAA0B,EAAA;eAA1BA;;IAXAC,kCAAkC,EAAA;eAAlCA;;IACAC,sCAAsC,EAAA;eAAtCA;;IASAC,8BAA8B,EAAA;eAA9BA;;IAXAC,sBAAsB,EAAA;eAAtBA;;IASAC,wBAAwB,EAAA;eAAxBA;;IACAC,yBAAyB,EAAA;eAAzBA;;IAdAC,gBAAgB,EAAA;eAAhBA;;IAZAC,+BAA+B,EAAA;eAA/BA;;IAaAC,gBAAgB,EAAA;eAAhBA;;IAdAC,uBAAuB,EAAA;eAAvBA;;IAsBAC,kBAAkB,EAAA;eAAlBA;;IA6DAC,qBAAqB,EAAA;eAArBA;;IAnCAC,eAAe,EAAA;eAAfA;;IA5CAC,2BAA2B,EAAA;eAA3BA;;IACAC,0CAA0C,EAAA;eAA1CA;;IAuDAC,8BAA8B,EAAA;eAA9BA;;IAVAC,cAAc,EAAA;eAAdA;;IAOAC,+BAA+B,EAAA;eAA/BA;;IADAC,2BAA2B,EAAA;eAA3BA;;IAFAC,sBAAsB,EAAA;eAAtBA;;IADAC,yBAAyB,EAAA;eAAzBA;;IAEAC,uBAAuB,EAAA;eAAvBA;;IAHAC,uBAAuB,EAAA;eAAvBA;;IA5CAC,mBAAmB,EAAA;eAAnBA;;IACAC,uBAAuB,EAAA;eAAvBA;;IACAC,kBAAkB,EAAA;eAAlBA;;IACAC,UAAU,EAAA;eAAVA;;IA2DAC,yBAAyB,EAAA;eAAzBA;;IANAC,oCAAoC,EAAA;eAApCA;;IAEAC,yBAAyB,EAAA;eAAzBA;;IAuBAC,cAAc,EAAA;eAAdA;;IAJAC,yBAAyB,EAAA;eAAzBA;;IAvBAC,8BAA8B,EAAA;eAA9BA;;IAMAC,0CAA0C,EAAA;eAA1CA;;IASAC,gCAAgC,EAAA;eAAhCA;;IAiIJC,cAAc,EAAA;eAAdA;;IAAgBC,wBAAwB,EAAA;eAAxBA;;;AA9MlB,MAAM3B,0BAA0B;AAChC,MAAMF,kCAAkC;AAExC,MAAMZ,sBAAsB;AAC5B,MAAMkB,8BAA8B;AACpC,MAAMC,6CACX;AAEK,MAAMS,sBAAsB;AAC5B,MAAMC,0BAA0B;AAChC,MAAMC,qBAAqB;AAC3B,MAAMC,aAAa;AACnB,MAAMzC,gBAAgB;AACtB,MAAMqB,mBAAmB;AACzB,MAAME,mBAAmB;AACzB,MAAMV,mBAAmB;AAEzB,MAAMK,yBAAyB;AAC/B,MAAMH,qCAAqC;AAC3C,MAAMC,yCACX;AAEK,MAAMS,qBAAqB;AAI3B,MAAMN,2BAA2B;AACjC,MAAMC,4BAA4B;AAClC,MAAMH,iCAAiC;AACvC,MAAMH,6BAA6B;AAGnC,MAAMZ,iBAAiB;AAKvB,MAAMM,iBAAiB;AAGvB,MAAMG,sBAAsB;AAC5B,MAAMC,6BAA6B,CAAC,SAAS,EAAED,qBAAqB;AAGpE,MAAMF,gCAAgC;AAItC,MAAMkB,kBAAkB;AACxB,MAAMxB,iBAAiB;AACvB,MAAM4B,iBAAiB;AACvB,MAAM9B,gBAAgB;AACtB,MAAMoC,0BAA0B;AAChC,MAAMF,4BAA4B;AAClC,MAAMD,yBAAyB;AAC/B,MAAME,0BAA0B;AAChC,MAAMH,8BAA8B;AACpC,MAAMD,kCACX;AAEK,MAAMF,iCAAiC,CAAC,6KAA6K,CAAC;AAEtN,MAAMiB,iCAAiC,CAAC,mGAAmG,CAAC;AAE5I,MAAMJ,uCAAuC,CAAC,uFAAuF,CAAC;AAEtI,MAAMC,4BAA4B,CAAC,sHAAsH,CAAC;AAE1J,MAAMI,6CAA6C,CAAC,uGAAuG,CAAC;AAE5J,MAAMN,4BAA4B,CAAC,uHAAuH,CAAC;AAE3J,MAAMrC,wBACX;AACK,MAAME,yBACX;AAEK,MAAM0C,mCACX,uEACA;AAEK,MAAM3C,8BAA8B,CAAC,wJAAwJ,CAAC;AAE9L,MAAMoB,wBAAwB,CAAC,iNAAiN,CAAC;AAEjP,MAAMoB,4BAA4B,CAAC,wJAAwJ,CAAC;AAE5L,MAAM1C,sBAAsB;IAAC;IAAO;IAAS;IAAc;IAAO;CAAM;AAExE,MAAMyC,iBAAgD;IAC3DO,MAAM;IACNC,kBAAkB;IAClBC,QAAQ;AACV;AAEA;;;CAGC,GACD,MAAMC,uBAAuB;IAC3B;;GAEC,GACDC,QAAQ;IACR;;;GAGC,GACDC,uBAAuB;IACvB;;GAEC,GACDC,qBAAqB;IACrB;;GAEC,GACDC,eAAe;IACf;;GAEC,GACDC,SAAS;IACT;;GAEC,GACDC,SAAS;IACT;;GAEC,GACDC,YAAY;IACZ;;GAEC,GACDC,YAAY;IACZ;;GAEC,GACDC,WAAW;IACX;;GAEC,GACDC,iBAAiB;IACjB;;GAEC,GACDC,iBAAiB;IACjB;;GAEC,GACDC,cAAc;IACd;;GAEC,GACDC,cAAc;AAChB;AAKA,MAAMlB,iBAAiB;IACrB,GAAGK,oBAAoB;IACvBc,OAAO;QACLC,cAAc;YACZf,qBAAqBE,qBAAqB;YAC1CF,qBAAqBI,aAAa;SACnC;QACDY,YAAY;YACVhB,qBAAqBE,qBAAqB;YAC1CF,qBAAqBI,aAAa;YAClCJ,qBAAqBQ,UAAU;YAC/BR,qBAAqBO,UAAU;SAChC;QACDU,eAAe;YACb,YAAY;YACZjB,qBAAqBK,OAAO;YAC5BL,qBAAqBM,OAAO;SAC7B;QACDY,YAAY;YACVlB,qBAAqBG,mBAAmB;YACxCH,qBAAqBU,eAAe;SACrC;QACDS,SAAS;YACPnB,qBAAqBE,qBAAqB;YAC1CF,qBAAqBI,aAAa;YAClCJ,qBAAqBG,mBAAmB;YACxCH,qBAAqBU,eAAe;YACpCV,qBAAqBC,MAAM;YAC3BD,qBAAqBQ,UAAU;YAC/BR,qBAAqBO,UAAU;SAChC;QACDa,UAAU;YACR,+BAA+B;YAC/BpB,qBAAqBE,qBAAqB;YAC1CF,qBAAqBG,mBAAmB;YACxCH,qBAAqBU,eAAe;YACpCV,qBAAqBI,aAAa;SACnC;IACH;AACF;AAEA,MAAMR,2BAA2B;IAC/ByB,cAAc;IACdC,UAAU;IACVC,eAAe;IACfC,mBAAmB;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3692, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/route-regex.ts"], "sourcesContent": ["import {\n  NEXT_INTERCEPTION_MARKER_PREFIX,\n  NEXT_QUERY_PARAM_PREFIX,\n} from '../../../../lib/constants'\nimport { INTERCEPTION_ROUTE_MARKERS } from './interception-routes'\nimport { escapeStringRegexp } from '../../escape-regexp'\nimport { removeTrailingSlash } from './remove-trailing-slash'\n\nexport interface Group {\n  pos: number\n  repeat: boolean\n  optional: boolean\n}\n\nexport interface RouteRegex {\n  groups: { [groupName: string]: Group }\n  re: RegExp\n}\n\ntype GetNamedRouteRegexOptions = {\n  /**\n   * Whether to prefix the route keys with the NEXT_INTERCEPTION_MARKER_PREFIX\n   * or NEXT_QUERY_PARAM_PREFIX. This is only relevant when creating the\n   * routes-manifest during the build.\n   */\n  prefixRouteKeys: boolean\n\n  /**\n   * Whether to include the suffix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `.json` part will be included\n   * in the regex, yielding `/(.*).json` as the regex.\n   */\n  includeSuffix?: boolean\n\n  /**\n   * Whether to include the prefix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `/` part will be included\n   * in the regex, yielding `^/(.*).json$` as the regex.\n   *\n   * Note that interception markers will already be included without the need\n   */\n  includePrefix?: boolean\n\n  /**\n   * Whether to exclude the optional trailing slash from the route regex.\n   */\n  excludeOptionalTrailingSlash?: boolean\n\n  /**\n   * Whether to backtrack duplicate keys. This is only relevant when creating\n   * the routes-manifest during the build.\n   */\n  backreferenceDuplicateKeys?: boolean\n}\n\ntype GetRouteRegexOptions = {\n  /**\n   * Whether to include extra parts in the route regex. This means that when you\n   * have something like `/[...slug].json` the `.json` part will be included\n   * in the regex, yielding `/(.*).json` as the regex.\n   */\n  includeSuffix?: boolean\n\n  /**\n   * Whether to include the prefix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `/` part will be included\n   * in the regex, yielding `^/(.*).json$` as the regex.\n   *\n   * Note that interception markers will already be included without the need\n   * of adding this option.\n   */\n  includePrefix?: boolean\n\n  /**\n   * Whether to exclude the optional trailing slash from the route regex.\n   */\n  excludeOptionalTrailingSlash?: boolean\n}\n\n/**\n * Regular expression pattern used to match route parameters.\n * Matches both single parameters and parameter groups.\n * Examples:\n *   - `[[...slug]]` matches parameter group with key 'slug', repeat: true, optional: true\n *   - `[...slug]` matches parameter group with key 'slug', repeat: true, optional: false\n *   - `[[foo]]` matches parameter with key 'foo', repeat: false, optional: true\n *   - `[bar]` matches parameter with key 'bar', repeat: false, optional: false\n */\nconst PARAMETER_PATTERN = /^([^[]*)\\[((?:\\[[^\\]]*\\])|[^\\]]+)\\](.*)$/\n\n/**\n * Parses a given parameter from a route to a data structure that can be used\n * to generate the parametrized route.\n * Examples:\n *   - `[[...slug]]` -> `{ key: 'slug', repeat: true, optional: true }`\n *   - `[...slug]` -> `{ key: 'slug', repeat: true, optional: false }`\n *   - `[[foo]]` -> `{ key: 'foo', repeat: false, optional: true }`\n *   - `[bar]` -> `{ key: 'bar', repeat: false, optional: false }`\n *   - `fizz` -> `{ key: 'fizz', repeat: false, optional: false }`\n * @param param - The parameter to parse.\n * @returns The parsed parameter as a data structure.\n */\nexport function parseParameter(param: string) {\n  const match = param.match(PARAMETER_PATTERN)\n\n  if (!match) {\n    return parseMatchedParameter(param)\n  }\n\n  return parseMatchedParameter(match[2])\n}\n\n/**\n * Parses a matched parameter from the PARAMETER_PATTERN regex to a data structure that can be used\n * to generate the parametrized route.\n * Examples:\n *   - `[...slug]` -> `{ key: 'slug', repeat: true, optional: true }`\n *   - `...slug` -> `{ key: 'slug', repeat: true, optional: false }`\n *   - `[foo]` -> `{ key: 'foo', repeat: false, optional: true }`\n *   - `bar` -> `{ key: 'bar', repeat: false, optional: false }`\n * @param param - The matched parameter to parse.\n * @returns The parsed parameter as a data structure.\n */\nfunction parseMatchedParameter(param: string) {\n  const optional = param.startsWith('[') && param.endsWith(']')\n  if (optional) {\n    param = param.slice(1, -1)\n  }\n  const repeat = param.startsWith('...')\n  if (repeat) {\n    param = param.slice(3)\n  }\n  return { key: param, repeat, optional }\n}\n\nfunction getParametrizedRoute(\n  route: string,\n  includeSuffix: boolean,\n  includePrefix: boolean\n) {\n  const groups: { [groupName: string]: Group } = {}\n  let groupIndex = 1\n\n  const segments: string[] = []\n  for (const segment of removeTrailingSlash(route).slice(1).split('/')) {\n    const markerMatch = INTERCEPTION_ROUTE_MARKERS.find((m) =>\n      segment.startsWith(m)\n    )\n    const paramMatches = segment.match(PARAMETER_PATTERN) // Check for parameters\n\n    if (markerMatch && paramMatches && paramMatches[2]) {\n      const { key, optional, repeat } = parseMatchedParameter(paramMatches[2])\n      groups[key] = { pos: groupIndex++, repeat, optional }\n      segments.push(`/${escapeStringRegexp(markerMatch)}([^/]+?)`)\n    } else if (paramMatches && paramMatches[2]) {\n      const { key, repeat, optional } = parseMatchedParameter(paramMatches[2])\n      groups[key] = { pos: groupIndex++, repeat, optional }\n\n      if (includePrefix && paramMatches[1]) {\n        segments.push(`/${escapeStringRegexp(paramMatches[1])}`)\n      }\n\n      let s = repeat ? (optional ? '(?:/(.+?))?' : '/(.+?)') : '/([^/]+?)'\n\n      // Remove the leading slash if includePrefix already added it.\n      if (includePrefix && paramMatches[1]) {\n        s = s.substring(1)\n      }\n\n      segments.push(s)\n    } else {\n      segments.push(`/${escapeStringRegexp(segment)}`)\n    }\n\n    // If there's a suffix, add it to the segments if it's enabled.\n    if (includeSuffix && paramMatches && paramMatches[3]) {\n      segments.push(escapeStringRegexp(paramMatches[3]))\n    }\n  }\n\n  return {\n    parameterizedRoute: segments.join(''),\n    groups,\n  }\n}\n\n/**\n * From a normalized route this function generates a regular expression and\n * a corresponding groups object intended to be used to store matching groups\n * from the regular expression.\n */\nexport function getRouteRegex(\n  normalizedRoute: string,\n  {\n    includeSuffix = false,\n    includePrefix = false,\n    excludeOptionalTrailingSlash = false,\n  }: GetRouteRegexOptions = {}\n): RouteRegex {\n  const { parameterizedRoute, groups } = getParametrizedRoute(\n    normalizedRoute,\n    includeSuffix,\n    includePrefix\n  )\n\n  let re = parameterizedRoute\n  if (!excludeOptionalTrailingSlash) {\n    re += '(?:/)?'\n  }\n\n  return {\n    re: new RegExp(`^${re}$`),\n    groups: groups,\n  }\n}\n\n/**\n * Builds a function to generate a minimal routeKey using only a-z and minimal\n * number of characters.\n */\nfunction buildGetSafeRouteKey() {\n  let i = 0\n\n  return () => {\n    let routeKey = ''\n    let j = ++i\n    while (j > 0) {\n      routeKey += String.fromCharCode(97 + ((j - 1) % 26))\n      j = Math.floor((j - 1) / 26)\n    }\n    return routeKey\n  }\n}\n\nfunction getSafeKeyFromSegment({\n  interceptionMarker,\n  getSafeRouteKey,\n  segment,\n  routeKeys,\n  keyPrefix,\n  backreferenceDuplicateKeys,\n}: {\n  interceptionMarker?: string\n  getSafeRouteKey: () => string\n  segment: string\n  routeKeys: Record<string, string>\n  keyPrefix?: string\n  backreferenceDuplicateKeys: boolean\n}) {\n  const { key, optional, repeat } = parseMatchedParameter(segment)\n\n  // replace any non-word characters since they can break\n  // the named regex\n  let cleanedKey = key.replace(/\\W/g, '')\n\n  if (keyPrefix) {\n    cleanedKey = `${keyPrefix}${cleanedKey}`\n  }\n  let invalidKey = false\n\n  // check if the key is still invalid and fallback to using a known\n  // safe key\n  if (cleanedKey.length === 0 || cleanedKey.length > 30) {\n    invalidKey = true\n  }\n  if (!isNaN(parseInt(cleanedKey.slice(0, 1)))) {\n    invalidKey = true\n  }\n\n  if (invalidKey) {\n    cleanedKey = getSafeRouteKey()\n  }\n\n  const duplicateKey = cleanedKey in routeKeys\n\n  if (keyPrefix) {\n    routeKeys[cleanedKey] = `${keyPrefix}${key}`\n  } else {\n    routeKeys[cleanedKey] = key\n  }\n\n  // if the segment has an interception marker, make sure that's part of the regex pattern\n  // this is to ensure that the route with the interception marker doesn't incorrectly match\n  // the non-intercepted route (ie /app/(.)[username] should not match /app/[username])\n  const interceptionPrefix = interceptionMarker\n    ? escapeStringRegexp(interceptionMarker)\n    : ''\n\n  let pattern: string\n  if (duplicateKey && backreferenceDuplicateKeys) {\n    // Use a backreference to the key to ensure that the key is the same value\n    // in each of the placeholders.\n    pattern = `\\\\k<${cleanedKey}>`\n  } else if (repeat) {\n    pattern = `(?<${cleanedKey}>.+?)`\n  } else {\n    pattern = `(?<${cleanedKey}>[^/]+?)`\n  }\n\n  return optional\n    ? `(?:/${interceptionPrefix}${pattern})?`\n    : `/${interceptionPrefix}${pattern}`\n}\n\nfunction getNamedParametrizedRoute(\n  route: string,\n  prefixRouteKeys: boolean,\n  includeSuffix: boolean,\n  includePrefix: boolean,\n  backreferenceDuplicateKeys: boolean\n) {\n  const getSafeRouteKey = buildGetSafeRouteKey()\n  const routeKeys: { [named: string]: string } = {}\n\n  const segments: string[] = []\n  for (const segment of removeTrailingSlash(route).slice(1).split('/')) {\n    const hasInterceptionMarker = INTERCEPTION_ROUTE_MARKERS.some((m) =>\n      segment.startsWith(m)\n    )\n\n    const paramMatches = segment.match(PARAMETER_PATTERN) // Check for parameters\n\n    if (hasInterceptionMarker && paramMatches && paramMatches[2]) {\n      // If there's an interception marker, add it to the segments.\n      segments.push(\n        getSafeKeyFromSegment({\n          getSafeRouteKey,\n          interceptionMarker: paramMatches[1],\n          segment: paramMatches[2],\n          routeKeys,\n          keyPrefix: prefixRouteKeys\n            ? NEXT_INTERCEPTION_MARKER_PREFIX\n            : undefined,\n          backreferenceDuplicateKeys,\n        })\n      )\n    } else if (paramMatches && paramMatches[2]) {\n      // If there's a prefix, add it to the segments if it's enabled.\n      if (includePrefix && paramMatches[1]) {\n        segments.push(`/${escapeStringRegexp(paramMatches[1])}`)\n      }\n\n      let s = getSafeKeyFromSegment({\n        getSafeRouteKey,\n        segment: paramMatches[2],\n        routeKeys,\n        keyPrefix: prefixRouteKeys ? NEXT_QUERY_PARAM_PREFIX : undefined,\n        backreferenceDuplicateKeys,\n      })\n\n      // Remove the leading slash if includePrefix already added it.\n      if (includePrefix && paramMatches[1]) {\n        s = s.substring(1)\n      }\n\n      segments.push(s)\n    } else {\n      segments.push(`/${escapeStringRegexp(segment)}`)\n    }\n\n    // If there's a suffix, add it to the segments if it's enabled.\n    if (includeSuffix && paramMatches && paramMatches[3]) {\n      segments.push(escapeStringRegexp(paramMatches[3]))\n    }\n  }\n\n  return {\n    namedParameterizedRoute: segments.join(''),\n    routeKeys,\n  }\n}\n\n/**\n * This function extends `getRouteRegex` generating also a named regexp where\n * each group is named along with a routeKeys object that indexes the assigned\n * named group with its corresponding key. When the routeKeys need to be\n * prefixed to uniquely identify internally the \"prefixRouteKey\" arg should\n * be \"true\" currently this is only the case when creating the routes-manifest\n * during the build\n */\nexport function getNamedRouteRegex(\n  normalizedRoute: string,\n  options: GetNamedRouteRegexOptions\n) {\n  const result = getNamedParametrizedRoute(\n    normalizedRoute,\n    options.prefixRouteKeys,\n    options.includeSuffix ?? false,\n    options.includePrefix ?? false,\n    options.backreferenceDuplicateKeys ?? false\n  )\n\n  let namedRegex = result.namedParameterizedRoute\n  if (!options.excludeOptionalTrailingSlash) {\n    namedRegex += '(?:/)?'\n  }\n\n  return {\n    ...getRouteRegex(normalizedRoute, options),\n    namedRegex: `^${namedRegex}$`,\n    routeKeys: result.routeKeys,\n  }\n}\n\n/**\n * Generates a named regexp.\n * This is intended to be using for build time only.\n */\nexport function getNamedMiddlewareRegex(\n  normalizedRoute: string,\n  options: {\n    catchAll?: boolean\n  }\n) {\n  const { parameterizedRoute } = getParametrizedRoute(\n    normalizedRoute,\n    false,\n    false\n  )\n  const { catchAll = true } = options\n  if (parameterizedRoute === '/') {\n    let catchAllRegex = catchAll ? '.*' : ''\n    return {\n      namedRegex: `^/${catchAllRegex}$`,\n    }\n  }\n\n  const { namedParameterizedRoute } = getNamedParametrizedRoute(\n    normalizedRoute,\n    false,\n    false,\n    false,\n    false\n  )\n  let catchAllGroupedRegex = catchAll ? '(?:(/.*)?)' : ''\n  return {\n    namedRegex: `^${namedParameterizedRoute}${catchAllGroupedRegex}$`,\n  }\n}\n"], "names": ["getNamedMiddlewareRegex", "getNamedRouteRegex", "getRouteRegex", "parseParameter", "PARAMETER_PATTERN", "param", "match", "parseMatchedParameter", "optional", "startsWith", "endsWith", "slice", "repeat", "key", "getParametrizedRoute", "route", "includeSuffix", "includePrefix", "groups", "groupIndex", "segments", "segment", "removeTrailingSlash", "split", "markerMatch", "INTERCEPTION_ROUTE_MARKERS", "find", "m", "paramMatch<PERSON>", "pos", "push", "escapeStringRegexp", "s", "substring", "parameterizedRoute", "join", "normalizedRoute", "excludeOptionalTrailingSlash", "re", "RegExp", "buildGetSafeRouteKey", "i", "routeKey", "j", "String", "fromCharCode", "Math", "floor", "getSafeKeyFromSegment", "<PERSON><PERSON><PERSON><PERSON>", "getSafeRouteKey", "routeKeys", "keyPrefix", "backreferenceDuplicateKeys", "<PERSON><PERSON><PERSON>", "replace", "<PERSON><PERSON><PERSON>", "length", "isNaN", "parseInt", "duplicate<PERSON>ey", "interceptionPrefix", "pattern", "getNamedParametrizedRoute", "prefixRouteKeys", "hasInterceptionMarker", "some", "NEXT_INTERCEPTION_MARKER_PREFIX", "undefined", "NEXT_QUERY_PARAM_PREFIX", "namedParameterizedRoute", "options", "result", "namedRegex", "catchAll", "catchAllRegex", "catchAllGroupedRegex"], "mappings": ";;;;;;;;;;;;;;;;;IAwZgBA,uBAAuB,EAAA;eAAvBA;;IA5BAC,kBAAkB,EAAA;eAAlBA;;IA7LAC,aAAa,EAAA;eAAbA;;IAzFAC,cAAc,EAAA;eAAdA;;;2BAnGT;oCACoC;8BACR;qCACC;AAyEpC;;;;;;;;CAQC,GACD,MAAMC,oBAAoB;AAcnB,SAASD,eAAeE,KAAa;IAC1C,MAAMC,QAAQD,MAAMC,KAAK,CAACF;IAE1B,IAAI,CAACE,OAAO;QACV,OAAOC,sBAAsBF;IAC/B;IAEA,OAAOE,sBAAsBD,KAAK,CAAC,EAAE;AACvC;AAEA;;;;;;;;;;CAUC,GACD,SAASC,sBAAsBF,KAAa;IAC1C,MAAMG,WAAWH,MAAMI,UAAU,CAAC,QAAQJ,MAAMK,QAAQ,CAAC;IACzD,IAAIF,UAAU;QACZH,QAAQA,MAAMM,KAAK,CAAC,GAAG,CAAC;IAC1B;IACA,MAAMC,SAASP,MAAMI,UAAU,CAAC;IAChC,IAAIG,QAAQ;QACVP,QAAQA,MAAMM,KAAK,CAAC;IACtB;IACA,OAAO;QAAEE,KAAKR;QAAOO;QAAQJ;IAAS;AACxC;AAEA,SAASM,qBACPC,KAAa,EACbC,aAAsB,EACtBC,aAAsB;IAEtB,MAAMC,SAAyC,CAAC;IAChD,IAAIC,aAAa;IAEjB,MAAMC,WAAqB,EAAE;IAC7B,KAAK,MAAMC,WAAWC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACP,OAAOJ,KAAK,CAAC,GAAGY,KAAK,CAAC,KAAM;QACpE,MAAMC,cAAcC,oBAAAA,0BAA0B,CAACC,IAAI,CAAC,CAACC,IACnDN,QAAQZ,UAAU,CAACkB;QAErB,MAAMC,eAAeP,QAAQf,KAAK,CAACF,mBAAmB,uBAAuB;;QAE7E,IAAIoB,eAAeI,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAClD,MAAM,EAAEf,GAAG,EAAEL,QAAQ,EAAEI,MAAM,EAAE,GAAGL,sBAAsBqB,YAAY,CAAC,EAAE;YACvEV,MAAM,CAACL,IAAI,GAAG;gBAAEgB,KAAKV;gBAAcP;gBAAQJ;YAAS;YACpDY,SAASU,IAAI,CAAE,MAAGC,CAAAA,GAAAA,cAAAA,kBAAkB,EAACP,eAAa;QACpD,OAAO,IAAII,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAC1C,MAAM,EAAEf,GAAG,EAAED,MAAM,EAAEJ,QAAQ,EAAE,GAAGD,sBAAsBqB,YAAY,CAAC,EAAE;YACvEV,MAAM,CAACL,IAAI,GAAG;gBAAEgB,KAAKV;gBAAcP;gBAAQJ;YAAS;YAEpD,IAAIS,iBAAiBW,YAAY,CAAC,EAAE,EAAE;gBACpCR,SAASU,IAAI,CAAE,MAAGC,CAAAA,GAAAA,cAAAA,kBAAkB,EAACH,YAAY,CAAC,EAAE;YACtD;YAEA,IAAII,IAAIpB,SAAUJ,WAAW,gBAAgB,WAAY;YAEzD,8DAA8D;YAC9D,IAAIS,iBAAiBW,YAAY,CAAC,EAAE,EAAE;gBACpCI,IAAIA,EAAEC,SAAS,CAAC;YAClB;YAEAb,SAASU,IAAI,CAACE;QAChB,OAAO;YACLZ,SAASU,IAAI,CAAE,MAAGC,CAAAA,GAAAA,cAAAA,kBAAkB,EAACV;QACvC;QAEA,+DAA+D;QAC/D,IAAIL,iBAAiBY,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YACpDR,SAASU,IAAI,CAACC,CAAAA,GAAAA,cAAAA,kBAAkB,EAACH,YAAY,CAAC,EAAE;QAClD;IACF;IAEA,OAAO;QACLM,oBAAoBd,SAASe,IAAI,CAAC;QAClCjB;IACF;AACF;AAOO,SAAShB,cACdkC,eAAuB,EACvB,KAAA;IAAA,IAAA,EACEpB,gBAAgB,KAAK,EACrBC,gBAAgB,KAAK,EACrBoB,+BAA+B,KAAK,EACf,GAJvB,UAAA,KAAA,IAI0B,CAAC,IAJ3B;IAMA,MAAM,EAAEH,kBAAkB,EAAEhB,MAAM,EAAE,GAAGJ,qBACrCsB,iBACApB,eACAC;IAGF,IAAIqB,KAAKJ;IACT,IAAI,CAACG,8BAA8B;QACjCC,MAAM;IACR;IAEA,OAAO;QACLA,IAAI,IAAIC,OAAQ,MAAGD,KAAG;QACtBpB,QAAQA;IACV;AACF;AAEA;;;CAGC,GACD,SAASsB;IACP,IAAIC,IAAI;IAER,OAAO;QACL,IAAIC,WAAW;QACf,IAAIC,IAAI,EAAEF;QACV,MAAOE,IAAI,EAAG;YACZD,YAAYE,OAAOC,YAAY,CAAC,KAAOF,CAAAA,IAAI,CAAA,IAAK;YAChDA,IAAIG,KAAKC,KAAK,CAAEJ,CAAAA,IAAI,CAAA,IAAK;QAC3B;QACA,OAAOD;IACT;AACF;AAEA,SAASM,sBAAsB,KAc9B;IAd8B,IAAA,EAC7BC,kBAAkB,EAClBC,eAAe,EACf7B,OAAO,EACP8B,SAAS,EACTC,SAAS,EACTC,0BAA0B,EAQ3B,GAd8B;IAe7B,MAAM,EAAExC,GAAG,EAAEL,QAAQ,EAAEI,MAAM,EAAE,GAAGL,sBAAsBc;IAExD,uDAAuD;IACvD,kBAAkB;IAClB,IAAIiC,aAAazC,IAAI0C,OAAO,CAAC,OAAO;IAEpC,IAAIH,WAAW;QACbE,aAAc,KAAEF,YAAYE;IAC9B;IACA,IAAIE,aAAa;IAEjB,kEAAkE;IAClE,WAAW;IACX,IAAIF,WAAWG,MAAM,KAAK,KAAKH,WAAWG,MAAM,GAAG,IAAI;QACrDD,aAAa;IACf;IACA,IAAI,CAACE,MAAMC,SAASL,WAAW3C,KAAK,CAAC,GAAG,MAAM;QAC5C6C,aAAa;IACf;IAEA,IAAIA,YAAY;QACdF,aAAaJ;IACf;IAEA,MAAMU,eAAeN,cAAcH;IAEnC,IAAIC,WAAW;QACbD,SAAS,CAACG,WAAW,GAAI,KAAEF,YAAYvC;IACzC,OAAO;QACLsC,SAAS,CAACG,WAAW,GAAGzC;IAC1B;IAEA,wFAAwF;IACxF,0FAA0F;IAC1F,qFAAqF;IACrF,MAAMgD,qBAAqBZ,qBACvBlB,CAAAA,GAAAA,cAAAA,kBAAkB,EAACkB,sBACnB;IAEJ,IAAIa;IACJ,IAAIF,gBAAgBP,4BAA4B;QAC9C,0EAA0E;QAC1E,+BAA+B;QAC/BS,UAAW,SAAMR,aAAW;IAC9B,OAAO,IAAI1C,QAAQ;QACjBkD,UAAW,QAAKR,aAAW;IAC7B,OAAO;QACLQ,UAAW,QAAKR,aAAW;IAC7B;IAEA,OAAO9C,WACF,SAAMqD,qBAAqBC,UAAQ,OACnC,MAAGD,qBAAqBC;AAC/B;AAEA,SAASC,0BACPhD,KAAa,EACbiD,eAAwB,EACxBhD,aAAsB,EACtBC,aAAsB,EACtBoC,0BAAmC;IAEnC,MAAMH,kBAAkBV;IACxB,MAAMW,YAAyC,CAAC;IAEhD,MAAM/B,WAAqB,EAAE;IAC7B,KAAK,MAAMC,WAAWC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACP,OAAOJ,KAAK,CAAC,GAAGY,KAAK,CAAC,KAAM;QACpE,MAAM0C,wBAAwBxC,oBAAAA,0BAA0B,CAACyC,IAAI,CAAC,CAACvC,IAC7DN,QAAQZ,UAAU,CAACkB;QAGrB,MAAMC,eAAeP,QAAQf,KAAK,CAACF,mBAAmB,uBAAuB;;QAE7E,IAAI6D,yBAAyBrC,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAC5D,6DAA6D;YAC7DR,SAASU,IAAI,CACXkB,sBAAsB;gBACpBE;gBACAD,oBAAoBrB,YAAY,CAAC,EAAE;gBACnCP,SAASO,YAAY,CAAC,EAAE;gBACxBuB;gBACAC,WAAWY,kBACPG,WAAAA,+BAA+B,GAC/BC;gBACJf;YACF;QAEJ,OAAO,IAAIzB,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAC1C,+DAA+D;YAC/D,IAAIX,iBAAiBW,YAAY,CAAC,EAAE,EAAE;gBACpCR,SAASU,IAAI,CAAE,MAAGC,CAAAA,GAAAA,cAAAA,kBAAkB,EAACH,YAAY,CAAC,EAAE;YACtD;YAEA,IAAII,IAAIgB,sBAAsB;gBAC5BE;gBACA7B,SAASO,YAAY,CAAC,EAAE;gBACxBuB;gBACAC,WAAWY,kBAAkBK,WAAAA,uBAAuB,GAAGD;gBACvDf;YACF;YAEA,8DAA8D;YAC9D,IAAIpC,iBAAiBW,YAAY,CAAC,EAAE,EAAE;gBACpCI,IAAIA,EAAEC,SAAS,CAAC;YAClB;YAEAb,SAASU,IAAI,CAACE;QAChB,OAAO;YACLZ,SAASU,IAAI,CAAE,MAAGC,CAAAA,GAAAA,cAAAA,kBAAkB,EAACV;QACvC;QAEA,+DAA+D;QAC/D,IAAIL,iBAAiBY,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YACpDR,SAASU,IAAI,CAACC,CAAAA,GAAAA,cAAAA,kBAAkB,EAACH,YAAY,CAAC,EAAE;QAClD;IACF;IAEA,OAAO;QACL0C,yBAAyBlD,SAASe,IAAI,CAAC;QACvCgB;IACF;AACF;AAUO,SAASlD,mBACdmC,eAAuB,EACvBmC,OAAkC;QAKhCA,wBACAA,wBACAA;IALF,MAAMC,SAAST,0BACb3B,iBACAmC,QAAQP,eAAe,EACvBO,CAAAA,yBAAAA,QAAQvD,aAAa,KAAA,OAArBuD,yBAAyB,OACzBA,CAAAA,yBAAAA,QAAQtD,aAAa,KAAA,OAArBsD,yBAAyB,OACzBA,CAAAA,sCAAAA,QAAQlB,0BAA0B,KAAA,OAAlCkB,sCAAsC;IAGxC,IAAIE,aAAaD,OAAOF,uBAAuB;IAC/C,IAAI,CAACC,QAAQlC,4BAA4B,EAAE;QACzCoC,cAAc;IAChB;IAEA,OAAO;QACL,GAAGvE,cAAckC,iBAAiBmC,QAAQ;QAC1CE,YAAa,MAAGA,aAAW;QAC3BtB,WAAWqB,OAAOrB,SAAS;IAC7B;AACF;AAMO,SAASnD,wBACdoC,eAAuB,EACvBmC,OAEC;IAED,MAAM,EAAErC,kBAAkB,EAAE,GAAGpB,qBAC7BsB,iBACA,OACA;IAEF,MAAM,EAAEsC,WAAW,IAAI,EAAE,GAAGH;IAC5B,IAAIrC,uBAAuB,KAAK;QAC9B,IAAIyC,gBAAgBD,WAAW,OAAO;QACtC,OAAO;YACLD,YAAa,OAAIE,gBAAc;QACjC;IACF;IAEA,MAAM,EAAEL,uBAAuB,EAAE,GAAGP,0BAClC3B,iBACA,OACA,OACA,OACA;IAEF,IAAIwC,uBAAuBF,WAAW,eAAe;IACrD,OAAO;QACLD,YAAa,MAAGH,0BAA0BM,uBAAqB;IACjE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3963, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/format-url.ts"], "sourcesContent": ["// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nimport type { UrlObject } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport * as querystring from './querystring'\n\nconst slashedProtocols = /https?|ftp|gopher|file/\n\nexport function formatUrl(urlObj: UrlObject) {\n  let { auth, hostname } = urlObj\n  let protocol = urlObj.protocol || ''\n  let pathname = urlObj.pathname || ''\n  let hash = urlObj.hash || ''\n  let query = urlObj.query || ''\n  let host: string | false = false\n\n  auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : ''\n\n  if (urlObj.host) {\n    host = auth + urlObj.host\n  } else if (hostname) {\n    host = auth + (~hostname.indexOf(':') ? `[${hostname}]` : hostname)\n    if (urlObj.port) {\n      host += ':' + urlObj.port\n    }\n  }\n\n  if (query && typeof query === 'object') {\n    query = String(querystring.urlQueryToSearchParams(query as ParsedUrlQuery))\n  }\n\n  let search = urlObj.search || (query && `?${query}`) || ''\n\n  if (protocol && !protocol.endsWith(':')) protocol += ':'\n\n  if (\n    urlObj.slashes ||\n    ((!protocol || slashedProtocols.test(protocol)) && host !== false)\n  ) {\n    host = '//' + (host || '')\n    if (pathname && pathname[0] !== '/') pathname = '/' + pathname\n  } else if (!host) {\n    host = ''\n  }\n\n  if (hash && hash[0] !== '#') hash = '#' + hash\n  if (search && search[0] !== '?') search = '?' + search\n\n  pathname = pathname.replace(/[?#]/g, encodeURIComponent)\n  search = search.replace('#', '%23')\n\n  return `${protocol}${host}${pathname}${search}${hash}`\n}\n\nexport const urlObjectKeys = [\n  'auth',\n  'hash',\n  'host',\n  'hostname',\n  'href',\n  'path',\n  'pathname',\n  'port',\n  'protocol',\n  'query',\n  'search',\n  'slashes',\n]\n\nexport function formatWithValidation(url: UrlObject): string {\n  if (process.env.NODE_ENV === 'development') {\n    if (url !== null && typeof url === 'object') {\n      Object.keys(url).forEach((key) => {\n        if (!urlObjectKeys.includes(key)) {\n          console.warn(\n            `Unknown key passed via urlObject into url.format: ${key}`\n          )\n        }\n      })\n    }\n  }\n\n  return formatUrl(url)\n}\n"], "names": ["formatUrl", "formatWithValidation", "urlObjectKeys", "slashedProtocols", "url<PERSON>bj", "auth", "hostname", "protocol", "pathname", "hash", "query", "host", "encodeURIComponent", "replace", "indexOf", "port", "String", "querystring", "urlQueryToSearchParams", "search", "endsWith", "slashes", "test", "url", "process", "env", "NODE_ENV", "Object", "keys", "for<PERSON>ach", "key", "includes", "console", "warn"], "mappings": "AAAA,uCAAuC;AACvC,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;;;;;;;;;;;;;;;;;IAQzBA,SAAS,EAAA;eAATA;;IA6DAC,oBAAoB,EAAA;eAApBA;;IAfHC,aAAa,EAAA;eAAbA;;;;uEAlDgB;AAE7B,MAAMC,mBAAmB;AAElB,SAASH,UAAUI,MAAiB;IACzC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAE,GAAGF;IACzB,IAAIG,WAAWH,OAAOG,QAAQ,IAAI;IAClC,IAAIC,WAAWJ,OAAOI,QAAQ,IAAI;IAClC,IAAIC,OAAOL,OAAOK,IAAI,IAAI;IAC1B,IAAIC,QAAQN,OAAOM,KAAK,IAAI;IAC5B,IAAIC,OAAuB;IAE3BN,OAAOA,OAAOO,mBAAmBP,MAAMQ,OAAO,CAAC,QAAQ,OAAO,MAAM;IAEpE,IAAIT,OAAOO,IAAI,EAAE;QACfA,OAAON,OAAOD,OAAOO,IAAI;IAC3B,OAAO,IAAIL,UAAU;QACnBK,OAAON,OAAQ,CAAA,CAACC,SAASQ,OAAO,CAAC,OAAQ,MAAGR,WAAS,MAAKA,QAAO;QACjE,IAAIF,OAAOW,IAAI,EAAE;YACfJ,QAAQ,MAAMP,OAAOW,IAAI;QAC3B;IACF;IAEA,IAAIL,SAAS,OAAOA,UAAU,UAAU;QACtCA,QAAQM,OAAOC,aAAYC,sBAAsB,CAACR;IACpD;IAEA,IAAIS,SAASf,OAAOe,MAAM,IAAKT,SAAU,MAAGA,SAAY;IAExD,IAAIH,YAAY,CAACA,SAASa,QAAQ,CAAC,MAAMb,YAAY;IAErD,IACEH,OAAOiB,OAAO,IACZ,CAAA,CAACd,YAAYJ,iBAAiBmB,IAAI,CAACf,SAAQ,KAAMI,SAAS,OAC5D;QACAA,OAAO,OAAQA,CAAAA,QAAQ,EAAC;QACxB,IAAIH,YAAYA,QAAQ,CAAC,EAAE,KAAK,KAAKA,WAAW,MAAMA;IACxD,OAAO,IAAI,CAACG,MAAM;QAChBA,OAAO;IACT;IAEA,IAAIF,QAAQA,IAAI,CAAC,EAAE,KAAK,KAAKA,OAAO,MAAMA;IAC1C,IAAIU,UAAUA,MAAM,CAAC,EAAE,KAAK,KAAKA,SAAS,MAAMA;IAEhDX,WAAWA,SAASK,OAAO,CAAC,SAASD;IACrCO,SAASA,OAAON,OAAO,CAAC,KAAK;IAE7B,OAAQ,KAAEN,WAAWI,OAAOH,WAAWW,SAASV;AAClD;AAEO,MAAMP,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAASD,qBAAqBsB,GAAc;IACjD,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;QAC1C,IAAIH,QAAQ,QAAQ,OAAOA,QAAQ,UAAU;YAC3CI,OAAOC,IAAI,CAACL,KAAKM,OAAO,CAAC,CAACC;gBACxB,IAAI,CAAC5B,cAAc6B,QAAQ,CAACD,MAAM;oBAChCE,QAAQC,IAAI,CACT,uDAAoDH;gBAEzD;YACF;QACF;IACF;IAEA,OAAO9B,UAAUuB;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4077, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/client/detect-domain-locale.ts"], "sourcesContent": ["import type { detectDomainLocale as Fn } from '../shared/lib/i18n/detect-domain-locale'\n\nexport const detectDomainLocale: typeof Fn = (...args) => {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    return require('../shared/lib/i18n/detect-domain-locale').detectDomainLocale(\n      ...args\n    )\n  }\n}\n"], "names": ["detectDomainLocale", "args", "process", "env", "__NEXT_I18N_SUPPORT", "require"], "mappings": ";;;;+BAEaA,sBAAAA;;;eAAAA;;;AAAN,MAAMA,qBAAgC;qCAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;QAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;IAC/C,IAAIC,QAAQC,GAAG,CAACC,mBAAmB,IAAE;;IAIrC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4107, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/client/normalize-trailing-slash.ts"], "sourcesContent": ["import { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { parsePath } from '../shared/lib/router/utils/parse-path'\n\n/**\n * Normalizes the trailing slash of a path according to the `trailingSlash` option\n * in `next.config.js`.\n */\nexport const normalizePathTrailingSlash = (path: string) => {\n  if (!path.startsWith('/') || process.env.__NEXT_MANUAL_TRAILING_SLASH) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  if (process.env.__NEXT_TRAILING_SLASH) {\n    if (/\\.[^/]+\\/?$/.test(pathname)) {\n      return `${removeTrailingSlash(pathname)}${query}${hash}`\n    } else if (pathname.endsWith('/')) {\n      return `${pathname}${query}${hash}`\n    } else {\n      return `${pathname}/${query}${hash}`\n    }\n  }\n\n  return `${removeTrailingSlash(pathname)}${query}${hash}`\n}\n"], "names": ["normalizePathTrailingSlash", "path", "startsWith", "process", "env", "__NEXT_MANUAL_TRAILING_SLASH", "pathname", "query", "hash", "parsePath", "__NEXT_TRAILING_SLASH", "test", "removeTrailingSlash", "endsWith"], "mappings": ";;;;+BAOaA,8BAAAA;;;eAAAA;;;qCAPuB;2BACV;AAMnB,MAAMA,6BAA6B,CAACC;IACzC,IAAI,CAACA,KAAKC,UAAU,CAAC,QAAQC,QAAQC,GAAG,CAACC,4BAA4B,EAAE;QACrE,OAAOJ;IACT;IAEA,MAAM,EAAEK,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,GAAGC,CAAAA,GAAAA,WAAAA,SAAS,EAACR;IAC5C,IAAIE,QAAQC,GAAG,CAACM,qBAAqB,EAAE;;IAQvC;IAEA,OAAQ,KAAEE,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACN,YAAYC,QAAQC;AACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4141, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/client/add-locale.ts"], "sourcesContent": ["import type { addLocale as Fn } from '../shared/lib/router/utils/add-locale'\nimport { normalizePathTrailingSlash } from './normalize-trailing-slash'\n\nexport const addLocale: typeof Fn = (path, ...args) => {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    return normalizePathTrailingSlash(\n      require('../shared/lib/router/utils/add-locale').addLocale(path, ...args)\n    )\n  }\n  return path\n}\n"], "names": ["addLocale", "path", "args", "process", "env", "__NEXT_I18N_SUPPORT", "normalizePathTrailingSlash", "require"], "mappings": ";;;;+BAGaA,aAAAA;;;eAAAA;;;wCAF8B;AAEpC,MAAMA,YAAuB,SAACC,IAAAA;qCAASC,OAAAA,IAAAA,MAAAA,OAAAA,IAAAA,OAAAA,IAAAA,IAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;QAAAA,IAAAA,CAAAA,OAAAA,EAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;IAC5C,IAAIC,QAAQC,GAAG,CAACC,mBAAmB,IAAE;;IAIrC;IACA,OAAOJ;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4173, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/client/remove-locale.ts"], "sourcesContent": ["import { parsePath } from '../shared/lib/router/utils/parse-path'\n\nexport function removeLocale(path: string, locale?: string) {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    const { pathname } = parsePath(path)\n    const pathLower = pathname.toLowerCase()\n    const localeLower = locale?.toLowerCase()\n\n    return locale &&\n      (pathLower.startsWith(`/${localeLower}/`) ||\n        pathLower === `/${localeLower}`)\n      ? `${pathname.length === locale.length + 1 ? `/` : ``}${path.slice(\n          locale.length + 1\n        )}`\n      : path\n  }\n  return path\n}\n"], "names": ["removeLocale", "path", "locale", "process", "env", "__NEXT_I18N_SUPPORT", "pathname", "parsePath", "pathLower", "toLowerCase", "localeLower", "startsWith", "length", "slice"], "mappings": ";;;;+BAEgBA,gBAAAA;;;eAAAA;;;2BAFU;AAEnB,SAASA,aAAaC,IAAY,EAAEC,MAAe;IACxD,IAAIC,QAAQC,GAAG,CAACC,mBAAmB,IAAE;;IAYrC;IACA,OAAOJ;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4202, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/add-path-prefix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Adds the provided prefix to the given path. It first ensures that the path\n * is indeed starting with a slash.\n */\nexport function addPathPrefix(path: string, prefix?: string) {\n  if (!path.startsWith('/') || !prefix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${prefix}${pathname}${query}${hash}`\n}\n"], "names": ["addPathPrefix", "path", "prefix", "startsWith", "pathname", "query", "hash", "parsePath"], "mappings": ";;;;+BAMgBA,iBAAAA;;;eAAAA;;;2BANU;AAMnB,SAASA,cAAcC,IAAY,EAAEC,MAAe;IACzD,IAAI,CAACD,KAAKE,UAAU,CAAC,QAAQ,CAACD,QAAQ;QACpC,OAAOD;IACT;IAEA,MAAM,EAAEG,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,GAAGC,CAAAA,GAAAA,WAAAA,SAAS,EAACN;IAC5C,OAAQ,KAAEC,SAASE,WAAWC,QAAQC;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4225, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/client/add-base-path.ts"], "sourcesContent": ["import { addPathPrefix } from '../shared/lib/router/utils/add-path-prefix'\nimport { normalizePathTrailingSlash } from './normalize-trailing-slash'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function addBasePath(path: string, required?: boolean): string {\n  return normalizePathTrailingSlash(\n    process.env.__NEXT_MANUAL_CLIENT_BASE_PATH && !required\n      ? path\n      : addPathPrefix(path, basePath)\n  )\n}\n"], "names": ["addBasePath", "basePath", "process", "env", "__NEXT_ROUTER_BASEPATH", "path", "required", "normalizePathTrailingSlash", "__NEXT_MANUAL_CLIENT_BASE_PATH", "addPathPrefix"], "mappings": ";;;;+BAKg<PERSON>,eAAAA;;;eAAAA;;;+BALc;wCACa;AAE3C,MAAMC,WAAYC,QAAQC,GAAG,CAACC,sBAAsB,MAAe;AAE5D,SAASJ,YAAYK,IAAY,EAAEC,QAAkB;IAC1D,OAAOC,CAAAA,GAAAA,wBAAAA,0BAA0B,EAC/BL,QAAQC,GAAG,CAACK,8BAA8B,IAAI,CAACF,WAC3CD,mBACAI,CAAAA,GAAAA,eAAAA,aAAa,EAACJ,MAAMJ;AAE5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4253, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/omit.ts"], "sourcesContent": ["export function omit<T extends { [key: string]: unknown }, K extends keyof T>(\n  object: T,\n  keys: K[]\n): Omit<T, K> {\n  const omitted: { [key: string]: unknown } = {}\n  Object.keys(object).forEach((key) => {\n    if (!keys.includes(key as K)) {\n      omitted[key] = object[key]\n    }\n  })\n  return omitted as Omit<T, K>\n}\n"], "names": ["omit", "object", "keys", "omitted", "Object", "for<PERSON>ach", "key", "includes"], "mappings": ";;;;+BAAgBA,QAAAA;;;eAAAA;;;AAAT,SAASA,KACdC,MAAS,EACTC,IAAS;IAET,MAAMC,UAAsC,CAAC;IAC7CC,OAAOF,IAAI,CAACD,QAAQI,OAAO,CAAC,CAACC;QAC3B,IAAI,CAACJ,KAAKK,QAAQ,CAACD,MAAW;YAC5BH,OAAO,CAACG,IAAI,GAAGL,MAAM,CAACK,IAAI;QAC5B;IACF;IACA,OAAOH;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4277, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/is-local-url.ts"], "sourcesContent": ["import { isAbsoluteUrl, getLocationOrigin } from '../../utils'\nimport { hasBasePath } from '../../../../client/has-base-path'\n\n/**\n * Detects whether a given url is routable by the Next.js router (browser only).\n */\nexport function isLocalURL(url: string): boolean {\n  // prevent a hydration mismatch on href for url with anchor refs\n  if (!isAbsoluteUrl(url)) return true\n  try {\n    // absolute urls can be local if they are on the same origin\n    const locationOrigin = getLocationOrigin()\n    const resolved = new URL(url, locationOrigin)\n    return resolved.origin === locationOrigin && hasBasePath(resolved.pathname)\n  } catch (_) {\n    return false\n  }\n}\n"], "names": ["isLocalURL", "url", "isAbsoluteUrl", "locationOrigin", "getLocationOrigin", "resolved", "URL", "origin", "has<PERSON>ase<PERSON><PERSON>", "pathname", "_"], "mappings": ";;;;+BAMgBA,cAAAA;;;eAAAA;;;uBANiC;6BACrB;AAKrB,SAASA,WAAWC,GAAW;IACpC,gEAAgE;IAChE,IAAI,CAACC,CAAAA,GAAAA,OAAAA,aAAa,EAACD,MAAM,OAAO;IAChC,IAAI;QACF,4DAA4D;QAC5D,MAAME,iBAAiBC,CAAAA,GAAAA,OAAAA,iBAAiB;QACxC,MAAMC,WAAW,IAAIC,IAAIL,KAAKE;QAC9B,OAAOE,SAASE,MAAM,KAAKJ,kBAAkBK,CAAAA,GAAAA,aAAAA,WAAW,EAACH,SAASI,QAAQ;IAC5E,EAAE,OAAOC,GAAG;QACV,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4306, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/interpolate-as.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nimport { getRouteMatcher } from './route-matcher'\nimport { getRouteRegex } from './route-regex'\n\nexport function interpolateAs(\n  route: string,\n  asPathname: string,\n  query: ParsedUrlQuery\n) {\n  let interpolatedRoute = ''\n\n  const dynamicRegex = getRouteRegex(route)\n  const dynamicGroups = dynamicRegex.groups\n  const dynamicMatches =\n    // Try to match the dynamic route against the asPath\n    (asPathname !== route ? getRouteMatcher(dynamicRegex)(asPathname) : '') ||\n    // Fall back to reading the values from the href\n    // TODO: should this take priority; also need to change in the router.\n    query\n\n  interpolatedRoute = route\n  const params = Object.keys(dynamicGroups)\n\n  if (\n    !params.every((param) => {\n      let value = dynamicMatches[param] || ''\n      const { repeat, optional } = dynamicGroups[param]\n\n      // support single-level catch-all\n      // TODO: more robust handling for user-error (passing `/`)\n      let replaced = `[${repeat ? '...' : ''}${param}]`\n      if (optional) {\n        replaced = `${!value ? '/' : ''}[${replaced}]`\n      }\n      if (repeat && !Array.isArray(value)) value = [value]\n\n      return (\n        (optional || param in dynamicMatches) &&\n        // Interpolate group into data URL if present\n        (interpolatedRoute =\n          interpolatedRoute!.replace(\n            replaced,\n            repeat\n              ? (value as string[])\n                  .map(\n                    // these values should be fully encoded instead of just\n                    // path delimiter escaped since they are being inserted\n                    // into the URL and we expect URL encoded segments\n                    // when parsing dynamic route params\n                    (segment) => encodeURIComponent(segment)\n                  )\n                  .join('/')\n              : encodeURIComponent(value as string)\n          ) || '/')\n      )\n    })\n  ) {\n    interpolatedRoute = '' // did not satisfy all requirements\n\n    // n.b. We ignore this error because we handle warning for this case in\n    // development in the `<Link>` component directly.\n  }\n  return {\n    params,\n    result: interpolatedRoute,\n  }\n}\n"], "names": ["interpolateAs", "route", "asPathname", "query", "interpolatedRoute", "dynamicRegex", "getRouteRegex", "dynamicGroups", "groups", "dynamicMatches", "getRouteMatcher", "params", "Object", "keys", "every", "param", "value", "repeat", "optional", "replaced", "Array", "isArray", "replace", "map", "segment", "encodeURIComponent", "join", "result"], "mappings": ";;;;+BAKgBA,iBAAAA;;;eAAAA;;;8BAHgB;4BACF;AAEvB,SAASA,cACdC,KAAa,EACbC,UAAkB,EAClBC,KAAqB;IAErB,IAAIC,oBAAoB;IAExB,MAAMC,eAAeC,CAAAA,GAAAA,YAAAA,aAAa,EAACL;IACnC,MAAMM,gBAAgBF,aAAaG,MAAM;IACzC,MAAMC,iBAEJ,AADA,AACCP,CAAAA,eAAeD,QAAQS,CAAAA,GAAAA,cAAAA,UAD4B,KACb,EAACL,cAAcH,cAAc,EAAC,KACrE,gDAAgD;IAChD,sEAAsE;IACtEC;IAEFC,oBAAoBH;IACpB,MAAMU,SAASC,OAAOC,IAAI,CAACN;IAE3B,IACE,CAACI,OAAOG,KAAK,CAAC,CAACC;QACb,IAAIC,QAAQP,cAAc,CAACM,MAAM,IAAI;QACrC,MAAM,EAAEE,MAAM,EAAEC,QAAQ,EAAE,GAAGX,aAAa,CAACQ,MAAM;QAEjD,iCAAiC;QACjC,0DAA0D;QAC1D,IAAII,WAAY,MAAGF,CAAAA,SAAS,QAAQ,EAAC,IAAIF,QAAM;QAC/C,IAAIG,UAAU;YACZC,WAAc,CAAA,CAACH,QAAQ,MAAM,EAAC,IAAE,MAAGG,WAAS;QAC9C;QACA,IAAIF,UAAU,CAACG,MAAMC,OAAO,CAACL,QAAQA,QAAQ;YAACA;SAAM;QAEpD,OACGE,CAAAA,YAAYH,SAASN,cAAa,KACnC,6CAA6C;QAC5CL,CAAAA,oBACCA,kBAAmBkB,OAAO,CACxBH,UACAF,SACKD,MACEO,GAAG,CACF,AACA,uDAAuD,AADA;QAEvD,kDAAkD;QAClD,oCAAoC;QACpC,CAACC,UAAYC,mBAAmBD,UAEjCE,IAAI,CAAC,OACRD,mBAAmBT,WACpB,GAAE;IAEb,IACA;QACAZ,oBAAoB,GAAG,mCAAmC;;IAE1D,uEAAuE;IACvE,kDAAkD;IACpD;IACA,OAAO;QACLO;QACAgB,QAAQvB;IACV;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4360, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/client/resolve-href.ts"], "sourcesContent": ["import type { NextRouter, Url } from '../shared/lib/router/router'\n\nimport { searchParamsToUrlQuery } from '../shared/lib/router/utils/querystring'\nimport { formatWithValidation } from '../shared/lib/router/utils/format-url'\nimport { omit } from '../shared/lib/router/utils/omit'\nimport { normalizeRepeatedSlashes } from '../shared/lib/utils'\nimport { normalizePathTrailingSlash } from './normalize-trailing-slash'\nimport { isLocalURL } from '../shared/lib/router/utils/is-local-url'\nimport { isDynamicRoute } from '../shared/lib/router/utils'\nimport { interpolateAs } from '../shared/lib/router/utils/interpolate-as'\n\n/**\n * Resolves a given hyperlink with a certain router state (basePath not included).\n * Preserves absolute urls.\n */\nexport function resolveHref(\n  router: NextRouter,\n  href: Url,\n  resolveAs: true\n): [string, string] | [string]\nexport function resolveHref(\n  router: NextRouter,\n  href: Url,\n  resolveAs?: false\n): string\nexport function resolveHref(\n  router: NextRouter,\n  href: Url,\n  resolveAs?: boolean\n): [string, string] | [string] | string {\n  // we use a dummy base url for relative urls\n  let base: URL\n  let urlAsString = typeof href === 'string' ? href : formatWithValidation(href)\n\n  // repeated slashes and backslashes in the URL are considered\n  // invalid and will never match a Next.js page/file\n  const urlProtoMatch = urlAsString.match(/^[a-zA-Z]{1,}:\\/\\//)\n  const urlAsStringNoProto = urlProtoMatch\n    ? urlAsString.slice(urlProtoMatch[0].length)\n    : urlAsString\n\n  const urlParts = urlAsStringNoProto.split('?', 1)\n\n  if ((urlParts[0] || '').match(/(\\/\\/|\\\\)/)) {\n    console.error(\n      `Invalid href '${urlAsString}' passed to next/router in page: '${router.pathname}'. Repeated forward-slashes (//) or backslashes \\\\ are not valid in the href.`\n    )\n    const normalizedUrl = normalizeRepeatedSlashes(urlAsStringNoProto)\n    urlAsString = (urlProtoMatch ? urlProtoMatch[0] : '') + normalizedUrl\n  }\n\n  // Return because it cannot be routed by the Next.js router\n  if (!isLocalURL(urlAsString)) {\n    return (resolveAs ? [urlAsString] : urlAsString) as string\n  }\n\n  try {\n    base = new URL(\n      urlAsString.startsWith('#') ? router.asPath : router.pathname,\n      'http://n'\n    )\n  } catch (_) {\n    // fallback to / for invalid asPath values e.g. //\n    base = new URL('/', 'http://n')\n  }\n\n  try {\n    const finalUrl = new URL(urlAsString, base)\n    finalUrl.pathname = normalizePathTrailingSlash(finalUrl.pathname)\n    let interpolatedAs = ''\n\n    if (\n      isDynamicRoute(finalUrl.pathname) &&\n      finalUrl.searchParams &&\n      resolveAs\n    ) {\n      const query = searchParamsToUrlQuery(finalUrl.searchParams)\n\n      const { result, params } = interpolateAs(\n        finalUrl.pathname,\n        finalUrl.pathname,\n        query\n      )\n\n      if (result) {\n        interpolatedAs = formatWithValidation({\n          pathname: result,\n          hash: finalUrl.hash,\n          query: omit(query, params),\n        })\n      }\n    }\n\n    // if the origin didn't change, it means we received a relative href\n    const resolvedHref =\n      finalUrl.origin === base.origin\n        ? finalUrl.href.slice(finalUrl.origin.length)\n        : finalUrl.href\n\n    return resolveAs\n      ? [resolvedHref, interpolatedAs || resolvedHref]\n      : resolvedHref\n  } catch (_) {\n    return resolveAs ? [urlAsString] : urlAsString\n  }\n}\n"], "names": ["resolveHref", "router", "href", "resolveAs", "base", "urlAsString", "formatWithValidation", "urlProtoMatch", "match", "urlAsStringNoProto", "slice", "length", "urlParts", "split", "console", "error", "pathname", "normalizedUrl", "normalizeRepeatedSlashes", "isLocalURL", "URL", "startsWith", "<PERSON><PERSON><PERSON>", "_", "finalUrl", "normalizePathTrailingSlash", "interpolatedAs", "isDynamicRoute", "searchParams", "query", "searchParamsToUrlQuery", "result", "params", "interpolateAs", "hash", "omit", "resolvedHref", "origin"], "mappings": ";;;;+BAyBg<PERSON>,eAAAA;;;eAAAA;;;6BAvBuB;2BACF;sBAChB;uBACoB;wCACE;4BAChB;wBACI;+BACD;AAgBvB,SAASA,YACdC,MAAkB,EAClBC,IAAS,EACTC,SAAmB;IAEnB,4CAA4C;IAC5C,IAAIC;IACJ,IAAIC,cAAc,OAAOH,SAAS,WAAWA,OAAOI,CAAAA,GAAAA,WAAAA,oBAAoB,EAACJ;IAEzE,6DAA6D;IAC7D,mDAAmD;IACnD,MAAMK,gBAAgBF,YAAYG,KAAK,CAAC;IACxC,MAAMC,qBAAqBF,gBACvBF,YAAYK,KAAK,CAACH,aAAa,CAAC,EAAE,CAACI,MAAM,IACzCN;IAEJ,MAAMO,WAAWH,mBAAmBI,KAAK,CAAC,KAAK;IAE/C,IAAKD,CAAAA,QAAQ,CAAC,EAAE,IAAI,EAAC,EAAGJ,KAAK,CAAC,cAAc;QAC1CM,QAAQC,KAAK,CACV,mBAAgBV,cAAY,uCAAoCJ,OAAOe,QAAQ,GAAC;QAEnF,MAAMC,gBAAgBC,CAAAA,GAAAA,OAAAA,wBAAwB,EAACT;QAC/CJ,cAAeE,CAAAA,gBAAgBA,aAAa,CAAC,EAAE,GAAG,EAAC,IAAKU;IAC1D;IAEA,2DAA2D;IAC3D,IAAI,CAACE,CAAAA,GAAAA,YAAAA,UAAU,EAACd,cAAc;QAC5B,OAAQF,YAAY;YAACE;SAAY,GAAGA;IACtC;IAEA,IAAI;QACFD,OAAO,IAAIgB,IACTf,YAAYgB,UAAU,CAAC,OAAOpB,OAAOqB,MAAM,GAAGrB,OAAOe,QAAQ,EAC7D;IAEJ,EAAE,OAAOO,GAAG;QACV,kDAAkD;QAClDnB,OAAO,IAAIgB,IAAI,KAAK;IACtB;IAEA,IAAI;QACF,MAAMI,WAAW,IAAIJ,IAAIf,aAAaD;QACtCoB,SAASR,QAAQ,GAAGS,CAAAA,GAAAA,wBAAAA,0BAA0B,EAACD,SAASR,QAAQ;QAChE,IAAIU,iBAAiB;QAErB,IACEC,CAAAA,GAAAA,QAAAA,cAAc,EAACH,SAASR,QAAQ,KAChCQ,SAASI,YAAY,IACrBzB,WACA;YACA,MAAM0B,QAAQC,CAAAA,GAAAA,aAAAA,sBAAsB,EAACN,SAASI,YAAY;YAE1D,MAAM,EAAEG,MAAM,EAAEC,MAAM,EAAE,GAAGC,CAAAA,GAAAA,eAAAA,aAAa,EACtCT,SAASR,QAAQ,EACjBQ,SAASR,QAAQ,EACjBa;YAGF,IAAIE,QAAQ;gBACVL,iBAAiBpB,CAAAA,GAAAA,WAAAA,oBAAoB,EAAC;oBACpCU,UAAUe;oBACVG,MAAMV,SAASU,IAAI;oBACnBL,OAAOM,CAAAA,GAAAA,MAAAA,IAAI,EAACN,OAAOG;gBACrB;YACF;QACF;QAEA,oEAAoE;QACpE,MAAMI,eACJZ,SAASa,MAAM,KAAKjC,KAAKiC,MAAM,GAC3Bb,SAAStB,IAAI,CAACQ,KAAK,CAACc,SAASa,MAAM,CAAC1B,MAAM,IAC1Ca,SAAStB,IAAI;QAEnB,OAAOC,YACH;YAACiC;YAAcV,kBAAkBU;SAAa,GAC9CA;IACN,EAAE,OAAOb,GAAG;QACV,OAAOpB,YAAY;YAACE;SAAY,GAAGA;IACrC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4443, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/lib/is-api-route.ts"], "sourcesContent": ["export function isAPIRoute(value?: string) {\n  return value === '/api' || Boolean(value?.startsWith('/api/'))\n}\n"], "names": ["isAPIRoute", "value", "Boolean", "startsWith"], "mappings": ";;;;+BAAgBA,cAAAA;;;eAAAA;;;AAAT,SAASA,WAAWC,KAAc;IACvC,OAAOA,UAAU,UAAUC,QAAQD,SAAAA,OAAAA,KAAAA,IAAAA,MAAOE,UAAU,CAAC;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4461, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/remove-path-prefix.ts"], "sourcesContent": ["import { pathHasPrefix } from './path-has-prefix'\n\n/**\n * Given a path and a prefix it will remove the prefix when it exists in the\n * given path. It ensures it matches exactly without containing extra chars\n * and if the prefix is not there it will be noop.\n *\n * @param path The path to remove the prefix from.\n * @param prefix The prefix to be removed.\n */\nexport function removePathPrefix(path: string, prefix: string): string {\n  // If the path doesn't start with the prefix we can return it as is. This\n  // protects us from situations where the prefix is a substring of the path\n  // prefix such as:\n  //\n  // For prefix: /blog\n  //\n  //   /blog -> true\n  //   /blog/ -> true\n  //   /blog/1 -> true\n  //   /blogging -> false\n  //   /blogging/ -> false\n  //   /blogging/1 -> false\n  if (!pathHasPrefix(path, prefix)) {\n    return path\n  }\n\n  // Remove the prefix from the path via slicing.\n  const withoutPrefix = path.slice(prefix.length)\n\n  // If the path without the prefix starts with a `/` we can return it as is.\n  if (withoutPrefix.startsWith('/')) {\n    return withoutPrefix\n  }\n\n  // If the path without the prefix doesn't start with a `/` we need to add it\n  // back to the path to make sure it's a valid path.\n  return `/${withoutPrefix}`\n}\n"], "names": ["removePathPrefix", "path", "prefix", "pathHasPrefix", "withoutPrefix", "slice", "length", "startsWith"], "mappings": ";;;;+BAUgBA,oBAAAA;;;eAAAA;;;+BAVc;AAUvB,SAASA,iBAAiBC,IAAY,EAAEC,MAAc;IAC3D,yEAAyE;IACzE,0EAA0E;IAC1E,kBAAkB;IAClB,EAAE;IACF,oBAAoB;IACpB,EAAE;IACF,kBAAkB;IAClB,mBAAmB;IACnB,oBAAoB;IACpB,uBAAuB;IACvB,wBAAwB;IACxB,yBAAyB;IACzB,IAAI,CAACC,CAAAA,GAAAA,eAAAA,aAAa,EAACF,MAAMC,SAAS;QAChC,OAAOD;IACT;IAEA,+CAA+C;IAC/C,MAAMG,gBAAgBH,KAAKI,KAAK,CAACH,OAAOI,MAAM;IAE9C,2EAA2E;IAC3E,IAAIF,cAAcG,UAAU,CAAC,MAAM;QACjC,OAAOH;IACT;IAEA,4EAA4E;IAC5E,mDAAmD;IACnD,OAAQ,MAAGA;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4503, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/get-next-pathname-info.ts"], "sourcesContent": ["import { normalizeLocalePath } from '../../i18n/normalize-locale-path'\nimport { removePathPrefix } from './remove-path-prefix'\nimport { pathHasPrefix } from './path-has-prefix'\nimport type { I18NProvider } from '../../../../server/lib/i18n-provider'\n\nexport interface NextPathnameInfo {\n  /**\n   * The base path in case the pathname included it.\n   */\n  basePath?: string\n  /**\n   * The buildId for when the parsed URL is a data URL. Parsing it can be\n   * disabled with the `parseData` option.\n   */\n  buildId?: string\n  /**\n   * If there was a locale in the pathname, this will hold its value.\n   */\n  locale?: string\n  /**\n   * The processed pathname without a base path, locale, or data URL elements\n   * when parsing it is enabled.\n   */\n  pathname: string\n  /**\n   * A boolean telling if the pathname had a trailingSlash. This can be only\n   * true if trailingSlash is enabled.\n   */\n  trailingSlash?: boolean\n}\n\ninterface Options {\n  /**\n   * When passed to true, this function will also parse Nextjs data URLs.\n   */\n  parseData?: boolean\n  /**\n   * A partial of the Next.js configuration to parse the URL.\n   */\n  nextConfig?: {\n    basePath?: string\n    i18n?: { locales?: readonly string[] } | null\n    trailingSlash?: boolean\n  }\n\n  /**\n   * If provided, this normalizer will be used to detect the locale instead of\n   * the default locale detection.\n   */\n  i18nProvider?: I18NProvider\n}\n\nexport function getNextPathnameInfo(\n  pathname: string,\n  options: Options\n): NextPathnameInfo {\n  const { basePath, i18n, trailingSlash } = options.nextConfig ?? {}\n  const info: NextPathnameInfo = {\n    pathname,\n    trailingSlash: pathname !== '/' ? pathname.endsWith('/') : trailingSlash,\n  }\n\n  if (basePath && pathHasPrefix(info.pathname, basePath)) {\n    info.pathname = removePathPrefix(info.pathname, basePath)\n    info.basePath = basePath\n  }\n  let pathnameNoDataPrefix = info.pathname\n\n  if (\n    info.pathname.startsWith('/_next/data/') &&\n    info.pathname.endsWith('.json')\n  ) {\n    const paths = info.pathname\n      .replace(/^\\/_next\\/data\\//, '')\n      .replace(/\\.json$/, '')\n      .split('/')\n\n    const buildId = paths[0]\n    info.buildId = buildId\n    pathnameNoDataPrefix =\n      paths[1] !== 'index' ? `/${paths.slice(1).join('/')}` : '/'\n\n    // update pathname with normalized if enabled although\n    // we use normalized to populate locale info still\n    if (options.parseData === true) {\n      info.pathname = pathnameNoDataPrefix\n    }\n  }\n\n  // If provided, use the locale route normalizer to detect the locale instead\n  // of the function below.\n  if (i18n) {\n    let result = options.i18nProvider\n      ? options.i18nProvider.analyze(info.pathname)\n      : normalizeLocalePath(info.pathname, i18n.locales)\n\n    info.locale = result.detectedLocale\n    info.pathname = result.pathname ?? info.pathname\n\n    if (!result.detectedLocale && info.buildId) {\n      result = options.i18nProvider\n        ? options.i18nProvider.analyze(pathnameNoDataPrefix)\n        : normalizeLocalePath(pathnameNoDataPrefix, i18n.locales)\n\n      if (result.detectedLocale) {\n        info.locale = result.detectedLocale\n      }\n    }\n  }\n  return info\n}\n"], "names": ["getNextPathnameInfo", "pathname", "options", "basePath", "i18n", "trailingSlash", "nextConfig", "info", "endsWith", "pathHasPrefix", "removePathPrefix", "pathnameNoDataPrefix", "startsWith", "paths", "replace", "split", "buildId", "slice", "join", "parseData", "result", "i18nProvider", "analyze", "normalizeLocalePath", "locales", "locale", "detectedLocale"], "mappings": ";;;;+BAoDgBA,uBAAAA;;;eAAAA;;;qCApDoB;kCACH;+BACH;AAkDvB,SAASA,oBACdC,QAAgB,EAChBC,OAAgB;QAE0BA;IAA1C,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,aAAa,EAAE,GAAGH,CAAAA,sBAAAA,QAAQI,UAAU,KAAA,OAAlBJ,sBAAsB,CAAC;IACjE,MAAMK,OAAyB;QAC7BN;QACAI,eAAeJ,aAAa,MAAMA,SAASO,QAAQ,CAAC,OAAOH;IAC7D;IAEA,IAAIF,YAAYM,CAAAA,GAAAA,eAAAA,aAAa,EAACF,KAAKN,QAAQ,EAAEE,WAAW;QACtDI,KAAKN,QAAQ,GAAGS,CAAAA,GAAAA,kBAAAA,gBAAgB,EAACH,KAAKN,QAAQ,EAAEE;QAChDI,KAAKJ,QAAQ,GAAGA;IAClB;IACA,IAAIQ,uBAAuBJ,KAAKN,QAAQ;IAExC,IACEM,KAAKN,QAAQ,CAACW,UAAU,CAAC,mBACzBL,KAAKN,QAAQ,CAACO,QAAQ,CAAC,UACvB;QACA,MAAMK,QAAQN,KAAKN,QAAQ,CACxBa,OAAO,CAAC,oBAAoB,IAC5BA,OAAO,CAAC,WAAW,IACnBC,KAAK,CAAC;QAET,MAAMC,UAAUH,KAAK,CAAC,EAAE;QACxBN,KAAKS,OAAO,GAAGA;QACfL,uBACEE,KAAK,CAAC,EAAE,KAAK,UAAW,MAAGA,MAAMI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS;QAE1D,sDAAsD;QACtD,kDAAkD;QAClD,IAAIhB,QAAQiB,SAAS,KAAK,MAAM;YAC9BZ,KAAKN,QAAQ,GAAGU;QAClB;IACF;IAEA,4EAA4E;IAC5E,yBAAyB;IACzB,IAAIP,MAAM;QACR,IAAIgB,SAASlB,QAAQmB,YAAY,GAC7BnB,QAAQmB,YAAY,CAACC,OAAO,CAACf,KAAKN,QAAQ,IAC1CsB,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAChB,KAAKN,QAAQ,EAAEG,KAAKoB,OAAO;QAEnDjB,KAAKkB,MAAM,GAAGL,OAAOM,cAAc;YACnBN;QAAhBb,KAAKN,QAAQ,GAAGmB,CAAAA,mBAAAA,OAAOnB,QAAQ,KAAA,OAAfmB,mBAAmBb,KAAKN,QAAQ;QAEhD,IAAI,CAACmB,OAAOM,cAAc,IAAInB,KAAKS,OAAO,EAAE;YAC1CI,SAASlB,QAAQmB,YAAY,GACzBnB,QAAQmB,YAAY,CAACC,OAAO,CAACX,wBAC7BY,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACZ,sBAAsBP,KAAKoB,OAAO;YAE1D,IAAIJ,OAAOM,cAAc,EAAE;gBACzBnB,KAAKkB,MAAM,GAAGL,OAAOM,cAAc;YACrC;QACF;IACF;IACA,OAAOnB;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4560, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/add-path-suffix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Similarly to `addPathPrefix`, this function adds a suffix at the end on the\n * provided path. It also works only for paths ensuring the argument starts\n * with a slash.\n */\nexport function addPathSuffix(path: string, suffix?: string) {\n  if (!path.startsWith('/') || !suffix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${pathname}${suffix}${query}${hash}`\n}\n"], "names": ["addPathSuffix", "path", "suffix", "startsWith", "pathname", "query", "hash", "parsePath"], "mappings": ";;;;+BAOgBA,iBAAAA;;;eAAAA;;;2BAPU;AAOnB,SAASA,cAAcC,IAAY,EAAEC,MAAe;IACzD,IAAI,CAACD,KAAKE,UAAU,CAAC,QAAQ,CAACD,QAAQ;QACpC,OAAOD;IACT;IAEA,MAAM,EAAEG,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,GAAGC,CAAAA,GAAAA,WAAAA,SAAS,EAACN;IAC5C,OAAQ,KAAEG,WAAWF,SAASG,QAAQC;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4583, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/add-locale.ts"], "sourcesContent": ["import { addPathPrefix } from './add-path-prefix'\nimport { pathHasPrefix } from './path-has-prefix'\n\n/**\n * For a given path and a locale, if the locale is given, it will prefix the\n * locale. The path shouldn't be an API path. If a default locale is given the\n * prefix will be omitted if the locale is already the default locale.\n */\nexport function addLocale(\n  path: string,\n  locale?: string | false,\n  defaultLocale?: string,\n  ignorePrefix?: boolean\n) {\n  // If no locale was given or the locale is the default locale, we don't need\n  // to prefix the path.\n  if (!locale || locale === defaultLocale) return path\n\n  const lower = path.toLowerCase()\n\n  // If the path is an API path or the path already has the locale prefix, we\n  // don't need to prefix the path.\n  if (!ignorePrefix) {\n    if (pathHasPrefix(lower, '/api')) return path\n    if (pathHasPrefix(lower, `/${locale.toLowerCase()}`)) return path\n  }\n\n  // Add the locale prefix to the path.\n  return addPathPrefix(path, `/${locale}`)\n}\n"], "names": ["addLocale", "path", "locale", "defaultLocale", "ignorePrefix", "lower", "toLowerCase", "pathHasPrefix", "addPathPrefix"], "mappings": ";;;;+BAQg<PERSON>,aAAAA;;;eAAAA;;;+BARc;+BACA;AAOvB,SAASA,UACdC,IAAY,EACZC,MAAuB,EACvBC,aAAsB,EACtBC,YAAsB;IAEtB,4EAA4E;IAC5E,sBAAsB;IACtB,IAAI,CAACF,UAAUA,WAAWC,eAAe,OAAOF;IAEhD,MAAMI,QAAQJ,KAAKK,WAAW;IAE9B,2EAA2E;IAC3E,iCAAiC;IACjC,IAAI,CAACF,cAAc;QACjB,IAAIG,CAAAA,GAAAA,eAAAA,aAAa,EAACF,OAAO,SAAS,OAAOJ;QACzC,IAAIM,CAAAA,GAAAA,eAAAA,aAAa,EAACF,OAAQ,MAAGH,OAAOI,WAAW,KAAO,OAAOL;IAC/D;IAEA,qCAAqC;IACrC,OAAOO,CAAAA,GAAAA,eAAAA,aAAa,EAACP,MAAO,MAAGC;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4614, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/format-next-pathname-info.ts"], "sourcesContent": ["import type { NextPathnameInfo } from './get-next-pathname-info'\nimport { removeTrailingSlash } from './remove-trailing-slash'\nimport { addPathPrefix } from './add-path-prefix'\nimport { addPathSuffix } from './add-path-suffix'\nimport { addLocale } from './add-locale'\n\ninterface ExtendedInfo extends NextPathnameInfo {\n  defaultLocale?: string\n  ignorePrefix?: boolean\n}\n\nexport function formatNextPathnameInfo(info: ExtendedInfo) {\n  let pathname = addLocale(\n    info.pathname,\n    info.locale,\n    info.buildId ? undefined : info.defaultLocale,\n    info.ignorePrefix\n  )\n\n  if (info.buildId || !info.trailingSlash) {\n    pathname = removeTrailingSlash(pathname)\n  }\n\n  if (info.buildId) {\n    pathname = addPathSuffix(\n      addPathPrefix(pathname, `/_next/data/${info.buildId}`),\n      info.pathname === '/' ? 'index.json' : '.json'\n    )\n  }\n\n  pathname = addPathPrefix(pathname, info.basePath)\n  return !info.buildId && info.trailingSlash\n    ? !pathname.endsWith('/')\n      ? addPathSuffix(pathname, '/')\n      : pathname\n    : removeTrailingSlash(pathname)\n}\n"], "names": ["formatNextPathnameInfo", "info", "pathname", "addLocale", "locale", "buildId", "undefined", "defaultLocale", "ignorePrefix", "trailingSlash", "removeTrailingSlash", "addPathSuffix", "addPathPrefix", "basePath", "endsWith"], "mappings": ";;;;+BAWg<PERSON>,0BAAAA;;;eAAAA;;;qCAVoB;+BACN;+BACA;2BACJ;AAOnB,SAASA,uBAAuBC,IAAkB;IACvD,IAAIC,WAAWC,CAAAA,GAAAA,WAAAA,SAAS,EACtBF,KAAKC,QAAQ,EACbD,KAAKG,MAAM,EACXH,KAAKI,OAAO,GAAGC,YAAYL,KAAKM,aAAa,EAC7CN,KAAKO,YAAY;IAGnB,IAAIP,KAAKI,OAAO,IAAI,CAACJ,KAAKQ,aAAa,EAAE;QACvCP,WAAWQ,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACR;IACjC;IAEA,IAAID,KAAKI,OAAO,EAAE;QAChBH,WAAWS,CAAAA,GAAAA,eAAAA,aAAa,EACtBC,CAAAA,GAAAA,eAAAA,aAAa,EAACV,UAAW,iBAAcD,KAAKI,OAAO,GACnDJ,KAAKC,QAAQ,KAAK,MAAM,eAAe;IAE3C;IAEAA,WAAWU,CAAAA,GAAAA,eAAAA,aAAa,EAACV,UAAUD,KAAKY,QAAQ;IAChD,OAAO,CAACZ,KAAKI,OAAO,IAAIJ,KAAKQ,aAAa,GACtC,CAACP,SAASY,QAAQ,CAAC,OACjBH,CAAAA,GAAAA,eAAAA,aAAa,EAACT,UAAU,OACxBA,WACFQ,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACR;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4644, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/compare-states.ts"], "sourcesContent": ["import type { default as Router } from '../router'\n\nexport function compareRouterStates(a: Router['state'], b: Router['state']) {\n  const stateKeys = Object.keys(a)\n  if (stateKeys.length !== Object.keys(b).length) return false\n\n  for (let i = stateKeys.length; i--; ) {\n    const key = stateKeys[i]\n    if (key === 'query') {\n      const queryKeys = Object.keys(a.query)\n      if (queryKeys.length !== Object.keys(b.query).length) {\n        return false\n      }\n      for (let j = queryKeys.length; j--; ) {\n        const queryKey = queryKeys[j]\n        if (\n          !b.query.hasOwnProperty(queryKey) ||\n          a.query[queryKey] !== b.query[queryKey]\n        ) {\n          return false\n        }\n      }\n    } else if (\n      !b.hasOwnProperty(key) ||\n      a[key as keyof Router['state']] !== b[key as keyof Router['state']]\n    ) {\n      return false\n    }\n  }\n\n  return true\n}\n"], "names": ["compareRouterStates", "a", "b", "stateKeys", "Object", "keys", "length", "i", "key", "query<PERSON>eys", "query", "j", "query<PERSON><PERSON>", "hasOwnProperty"], "mappings": ";;;;+BAEg<PERSON>,uBAAAA;;;eAAAA;;;AAAT,SAASA,oBAAoBC,CAAkB,EAAEC,CAAkB;IACxE,MAAMC,YAAYC,OAAOC,IAAI,CAACJ;IAC9B,IAAIE,UAAUG,MAAM,KAAKF,OAAOC,IAAI,CAACH,GAAGI,MAAM,EAAE,OAAO;IAEvD,IAAK,IAAIC,IAAIJ,UAAUG,MAAM,EAAEC,KAAO;QACpC,MAAMC,MAAML,SAAS,CAACI,EAAE;QACxB,IAAIC,QAAQ,SAAS;YACnB,MAAMC,YAAYL,OAAOC,IAAI,CAACJ,EAAES,KAAK;YACrC,IAAID,UAAUH,MAAM,KAAKF,OAAOC,IAAI,CAACH,EAAEQ,KAAK,EAAEJ,MAAM,EAAE;gBACpD,OAAO;YACT;YACA,IAAK,IAAIK,IAAIF,UAAUH,MAAM,EAAEK,KAAO;gBACpC,MAAMC,WAAWH,SAAS,CAACE,EAAE;gBAC7B,IACE,CAACT,EAAEQ,KAAK,CAACG,cAAc,CAACD,aACxBX,EAAES,KAAK,CAACE,SAAS,KAAKV,EAAEQ,KAAK,CAACE,SAAS,EACvC;oBACA,OAAO;gBACT;YACF;QACF,OAAO,IACL,CAACV,EAAEW,cAAc,CAACL,QAClBP,CAAC,CAACO,IAA6B,KAAKN,CAAC,CAACM,IAA6B,EACnE;YACA,OAAO;QACT;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4681, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/html-bots.ts"], "sourcesContent": ["// This regex contains the bots that we need to do a blocking render for and can't safely stream the response\n// due to how they parse the DOM. For example, they might explicitly check for metadata in the `head` tag, so we can't stream metadata tags after the `head` was sent.\nexport const HTML_LIMITED_BOT_UA_RE =\n  /Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i\n"], "names": ["HTML_LIMITED_BOT_UA_RE"], "mappings": "AAAA,6GAA6G;AAC7G,sKAAsK;;;;;+BACzJA,0BAAAA;;;eAAAA;;;AAAN,MAAMA,yBACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4699, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/is-bot.ts"], "sourcesContent": ["import { HTML_LIMITED_BOT_UA_RE } from './html-bots'\n\n// Bot crawler that will spin up a headless browser and execute JS\nconst HEADLESS_BROWSER_BOT_UA_RE =\n  /Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i\n\nexport const HTML_LIMITED_BOT_UA_RE_STRING = HTML_LIMITED_BOT_UA_RE.source\n\nexport { HTML_LIMITED_BOT_UA_RE }\n\nfunction isDomBotUA(userAgent: string) {\n  return HEADLESS_BROWSER_BOT_UA_RE.test(userAgent)\n}\n\nfunction isHtmlLimitedBotUA(userAgent: string) {\n  return HTML_LIMITED_BOT_UA_RE.test(userAgent)\n}\n\nexport function isBot(userAgent: string): boolean {\n  return isDomBotUA(userAgent) || isHtmlLimitedBotUA(userAgent)\n}\n\nexport function getBotType(userAgent: string): 'dom' | 'html' | undefined {\n  if (isDomBotUA(userAgent)) {\n    return 'dom'\n  }\n  if (isHtmlLimitedBotUA(userAgent)) {\n    return 'html'\n  }\n  return undefined\n}\n"], "names": ["HTML_LIMITED_BOT_UA_RE", "HTML_LIMITED_BOT_UA_RE_STRING", "getBotType", "isBot", "HEADLESS_BROWSER_BOT_UA_RE", "source", "isDomBotUA", "userAgent", "test", "isHtmlLimitedBotUA", "undefined"], "mappings": ";;;;;;;;;;;;;;;;;IAQSA,sBAAsB,EAAA;eAAtBA,UAAAA,sBAAsB;;IAFlBC,6BAA6B,EAAA;eAA7BA;;IAgBGC,UAAU,EAAA;eAAVA;;IAJAC,KAAK,EAAA;eAALA;;;0BAlBuB;AAEvC,kEAAkE;AAClE,MAAMC,6BACJ;AAEK,MAAMH,gCAAgCD,UAAAA,sBAAsB,CAACK,MAAM;AAI1E,SAASC,WAAWC,SAAiB;IACnC,OAAOH,2BAA2BI,IAAI,CAACD;AACzC;AAEA,SAASE,mBAAmBF,SAAiB;IAC3C,OAAOP,UAAAA,sBAAsB,CAACQ,IAAI,CAACD;AACrC;AAEO,SAASJ,MAAMI,SAAiB;IACrC,OAAOD,WAAWC,cAAcE,mBAAmBF;AACrD;AAEO,SAASL,WAAWK,SAAiB;IAC1C,IAAID,WAAWC,YAAY;QACzB,OAAO;IACT;IACA,IAAIE,mBAAmBF,YAAY;QACjC,OAAO;IACT;IACA,OAAOG;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4756, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/utils/handle-smooth-scroll.ts"], "sourcesContent": ["/**\n * Run function with `scroll-behavior: auto` applied to `<html/>`.\n * This css change will be reverted after the function finishes.\n */\nexport function handleSmoothScroll(\n  fn: () => void,\n  options: { dontForceLayout?: boolean; onlyHashChange?: boolean } = {}\n) {\n  // if only the hash is changed, we don't need to disable smooth scrolling\n  // we only care to prevent smooth scrolling when navigating to a new page to avoid jarring UX\n  if (options.onlyHashChange) {\n    fn()\n    return\n  }\n  const htmlElement = document.documentElement\n  const existing = htmlElement.style.scrollBehavior\n  htmlElement.style.scrollBehavior = 'auto'\n  if (!options.dontForceLayout) {\n    // In Chrome-based browsers we need to force reflow before calling `scrollTo`.\n    // Otherwise it will not pickup the change in scrollBehavior\n    // More info here: https://github.com/vercel/next.js/issues/40719#issuecomment-1336248042\n    htmlElement.getClientRects()\n  }\n  fn()\n  htmlElement.style.scrollBehavior = existing\n}\n"], "names": ["handleSmoothScroll", "fn", "options", "onlyHashChange", "htmlElement", "document", "documentElement", "existing", "style", "scroll<PERSON>eh<PERSON>or", "dontForceLayout", "getClientRects"], "mappings": "AAAA;;;CAGC,GAAA;;;;+BACeA,sBAAAA;;;eAAAA;;;AAAT,SAASA,mBACdC,EAAc,EACdC,OAAqE;IAArEA,IAAAA,YAAAA,KAAAA,GAAAA,UAAmE,CAAC;IAEpE,yEAAyE;IACzE,6FAA6F;IAC7F,IAAIA,QAAQC,cAAc,EAAE;QAC1BF;QACA;IACF;IACA,MAAMG,cAAcC,SAASC,eAAe;IAC5C,MAAMC,WAAWH,YAAYI,KAAK,CAACC,cAAc;IACjDL,YAAYI,KAAK,CAACC,cAAc,GAAG;IACnC,IAAI,CAACP,QAAQQ,eAAe,EAAE;QAC5B,8EAA8E;QAC9E,4DAA4D;QAC5D,yFAAyF;QACzFN,YAAYO,cAAc;IAC5B;IACAV;IACAG,YAAYI,KAAK,CAACC,cAAc,GAAGF;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4794, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/shared/lib/router/router.ts"], "sourcesContent": ["// tslint:disable:no-console\nimport type { ComponentType } from 'react'\nimport type { DomainLocale } from '../../../server/config'\nimport type { MittEmitter } from '../mitt'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { RouterEvent } from '../../../client/router'\nimport type { StyleSheetTuple } from '../../../client/page-loader'\nimport type { UrlObject } from 'url'\nimport type PageLoader from '../../../client/page-loader'\nimport type { AppContextType, NextPageContext, NEXT_DATA } from '../utils'\nimport { removeTrailingSlash } from './utils/remove-trailing-slash'\nimport {\n  getClientBuildManifest,\n  isAssetError,\n  markAssetError,\n} from '../../../client/route-loader'\nimport { handleClientScriptLoad } from '../../../client/script'\nimport isError, { getProperError } from '../../../lib/is-error'\nimport { denormalizePagePath } from '../page-path/denormalize-page-path'\nimport { normalizeLocalePath } from '../i18n/normalize-locale-path'\nimport mitt from '../mitt'\nimport { getLocationOrigin, getURL, loadGetInitialProps, ST } from '../utils'\nimport { isDynamicRoute } from './utils/is-dynamic'\nimport { parseRelativeUrl } from './utils/parse-relative-url'\nimport resolveRewrites from './utils/resolve-rewrites'\nimport { getRouteMatcher } from './utils/route-matcher'\nimport { getRouteRegex } from './utils/route-regex'\nimport { formatWithValidation } from './utils/format-url'\nimport { detectDomainLocale } from '../../../client/detect-domain-locale'\nimport { parsePath } from './utils/parse-path'\nimport { addLocale } from '../../../client/add-locale'\nimport { removeLocale } from '../../../client/remove-locale'\nimport { removeBasePath } from '../../../client/remove-base-path'\nimport { addBasePath } from '../../../client/add-base-path'\nimport { hasBasePath } from '../../../client/has-base-path'\nimport { resolveHref } from '../../../client/resolve-href'\nimport { isAPIRoute } from '../../../lib/is-api-route'\nimport { getNextPathnameInfo } from './utils/get-next-pathname-info'\nimport { formatNextPathnameInfo } from './utils/format-next-pathname-info'\nimport { compareRouterStates } from './utils/compare-states'\nimport { isLocalURL } from './utils/is-local-url'\nimport { isBot } from './utils/is-bot'\nimport { omit } from './utils/omit'\nimport { interpolateAs } from './utils/interpolate-as'\nimport { handleSmoothScroll } from './utils/handle-smooth-scroll'\nimport type { Params } from '../../../server/request/params'\nimport { MATCHED_PATH_HEADER } from '../../../lib/constants'\n\ndeclare global {\n  interface Window {\n    /* prod */\n    __NEXT_DATA__: NEXT_DATA\n  }\n}\n\ninterface RouteProperties {\n  shallow: boolean\n}\n\ninterface TransitionOptions {\n  shallow?: boolean\n  locale?: string | false\n  scroll?: boolean\n  unstable_skipClientCache?: boolean\n}\n\ninterface NextHistoryState {\n  url: string\n  as: string\n  options: TransitionOptions\n}\n\nexport type HistoryState =\n  | null\n  | { __NA: true; __N?: false }\n  | { __N: false; __NA?: false }\n  | ({ __NA?: false; __N: true; key: string } & NextHistoryState)\n\nfunction buildCancellationError() {\n  return Object.assign(new Error('Route Cancelled'), {\n    cancelled: true,\n  })\n}\n\ninterface MiddlewareEffectParams<T extends FetchDataOutput> {\n  fetchData?: () => Promise<T>\n  locale?: string\n  asPath: string\n  router: Router\n}\n\nexport async function matchesMiddleware<T extends FetchDataOutput>(\n  options: MiddlewareEffectParams<T>\n): Promise<boolean> {\n  const matchers = await Promise.resolve(\n    options.router.pageLoader.getMiddleware()\n  )\n  if (!matchers) return false\n\n  const { pathname: asPathname } = parsePath(options.asPath)\n  // remove basePath first since path prefix has to be in the order of `/${basePath}/${locale}`\n  const cleanedAs = hasBasePath(asPathname)\n    ? removeBasePath(asPathname)\n    : asPathname\n  const asWithBasePathAndLocale = addBasePath(\n    addLocale(cleanedAs, options.locale)\n  )\n\n  // Check only path match on client. Matching \"has\" should be done on server\n  // where we can access more info such as headers, HttpOnly cookie, etc.\n  return matchers.some((m) =>\n    new RegExp(m.regexp).test(asWithBasePathAndLocale)\n  )\n}\n\nfunction stripOrigin(url: string) {\n  const origin = getLocationOrigin()\n\n  return url.startsWith(origin) ? url.substring(origin.length) : url\n}\n\nfunction prepareUrlAs(router: NextRouter, url: Url, as?: Url) {\n  // If url and as provided as an object representation,\n  // we'll format them into the string version here.\n  let [resolvedHref, resolvedAs] = resolveHref(router, url, true)\n  const origin = getLocationOrigin()\n  const hrefWasAbsolute = resolvedHref.startsWith(origin)\n  const asWasAbsolute = resolvedAs && resolvedAs.startsWith(origin)\n\n  resolvedHref = stripOrigin(resolvedHref)\n  resolvedAs = resolvedAs ? stripOrigin(resolvedAs) : resolvedAs\n\n  const preparedUrl = hrefWasAbsolute ? resolvedHref : addBasePath(resolvedHref)\n  const preparedAs = as\n    ? stripOrigin(resolveHref(router, as))\n    : resolvedAs || resolvedHref\n\n  return {\n    url: preparedUrl,\n    as: asWasAbsolute ? preparedAs : addBasePath(preparedAs),\n  }\n}\n\nfunction resolveDynamicRoute(pathname: string, pages: string[]) {\n  const cleanPathname = removeTrailingSlash(denormalizePagePath(pathname))\n  if (cleanPathname === '/404' || cleanPathname === '/_error') {\n    return pathname\n  }\n\n  // handle resolving href for dynamic routes\n  if (!pages.includes(cleanPathname)) {\n    // eslint-disable-next-line array-callback-return\n    pages.some((page) => {\n      if (isDynamicRoute(page) && getRouteRegex(page).re.test(cleanPathname)) {\n        pathname = page\n        return true\n      }\n    })\n  }\n  return removeTrailingSlash(pathname)\n}\n\nfunction getMiddlewareData<T extends FetchDataOutput>(\n  source: string,\n  response: Response,\n  options: MiddlewareEffectParams<T>\n) {\n  const nextConfig = {\n    basePath: options.router.basePath,\n    i18n: { locales: options.router.locales },\n    trailingSlash: Boolean(process.env.__NEXT_TRAILING_SLASH),\n  }\n  const rewriteHeader = response.headers.get('x-nextjs-rewrite')\n\n  let rewriteTarget =\n    rewriteHeader || response.headers.get('x-nextjs-matched-path')\n\n  const matchedPath = response.headers.get(MATCHED_PATH_HEADER)\n\n  if (\n    matchedPath &&\n    !rewriteTarget &&\n    !matchedPath.includes('__next_data_catchall') &&\n    !matchedPath.includes('/_error') &&\n    !matchedPath.includes('/404')\n  ) {\n    // leverage x-matched-path to detect next.config.js rewrites\n    rewriteTarget = matchedPath\n  }\n\n  if (rewriteTarget) {\n    if (\n      rewriteTarget.startsWith('/') ||\n      process.env.__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE\n    ) {\n      const parsedRewriteTarget = parseRelativeUrl(rewriteTarget)\n      const pathnameInfo = getNextPathnameInfo(parsedRewriteTarget.pathname, {\n        nextConfig,\n        parseData: true,\n      })\n\n      let fsPathname = removeTrailingSlash(pathnameInfo.pathname)\n      return Promise.all([\n        options.router.pageLoader.getPageList(),\n        getClientBuildManifest(),\n      ]).then(([pages, { __rewrites: rewrites }]: any) => {\n        let as = addLocale(pathnameInfo.pathname, pathnameInfo.locale)\n\n        if (\n          isDynamicRoute(as) ||\n          (!rewriteHeader &&\n            pages.includes(\n              normalizeLocalePath(removeBasePath(as), options.router.locales)\n                .pathname\n            ))\n        ) {\n          const parsedSource = getNextPathnameInfo(\n            parseRelativeUrl(source).pathname,\n            {\n              nextConfig: process.env.__NEXT_HAS_REWRITES\n                ? undefined\n                : nextConfig,\n              parseData: true,\n            }\n          )\n\n          as = addBasePath(parsedSource.pathname)\n          parsedRewriteTarget.pathname = as\n        }\n\n        if (process.env.__NEXT_HAS_REWRITES) {\n          const result = resolveRewrites(\n            as,\n            pages,\n            rewrites,\n            parsedRewriteTarget.query,\n            (path: string) => resolveDynamicRoute(path, pages),\n            options.router.locales\n          )\n\n          if (result.matchedPage) {\n            parsedRewriteTarget.pathname = result.parsedAs.pathname\n            as = parsedRewriteTarget.pathname\n            Object.assign(parsedRewriteTarget.query, result.parsedAs.query)\n          }\n        } else if (!pages.includes(fsPathname)) {\n          const resolvedPathname = resolveDynamicRoute(fsPathname, pages)\n\n          if (resolvedPathname !== fsPathname) {\n            fsPathname = resolvedPathname\n          }\n        }\n\n        const resolvedHref = !pages.includes(fsPathname)\n          ? resolveDynamicRoute(\n              normalizeLocalePath(\n                removeBasePath(parsedRewriteTarget.pathname),\n                options.router.locales\n              ).pathname,\n              pages\n            )\n          : fsPathname\n\n        if (isDynamicRoute(resolvedHref)) {\n          const matches = getRouteMatcher(getRouteRegex(resolvedHref))(as)\n          Object.assign(parsedRewriteTarget.query, matches || {})\n        }\n\n        return {\n          type: 'rewrite' as const,\n          parsedAs: parsedRewriteTarget,\n          resolvedHref,\n        }\n      })\n    }\n    const src = parsePath(source)\n    const pathname = formatNextPathnameInfo({\n      ...getNextPathnameInfo(src.pathname, { nextConfig, parseData: true }),\n      defaultLocale: options.router.defaultLocale,\n      buildId: '',\n    })\n\n    return Promise.resolve({\n      type: 'redirect-external' as const,\n      destination: `${pathname}${src.query}${src.hash}`,\n    })\n  }\n\n  const redirectTarget = response.headers.get('x-nextjs-redirect')\n\n  if (redirectTarget) {\n    if (redirectTarget.startsWith('/')) {\n      const src = parsePath(redirectTarget)\n      const pathname = formatNextPathnameInfo({\n        ...getNextPathnameInfo(src.pathname, { nextConfig, parseData: true }),\n        defaultLocale: options.router.defaultLocale,\n        buildId: '',\n      })\n\n      return Promise.resolve({\n        type: 'redirect-internal' as const,\n        newAs: `${pathname}${src.query}${src.hash}`,\n        newUrl: `${pathname}${src.query}${src.hash}`,\n      })\n    }\n\n    return Promise.resolve({\n      type: 'redirect-external' as const,\n      destination: redirectTarget,\n    })\n  }\n\n  return Promise.resolve({ type: 'next' as const })\n}\n\ninterface WithMiddlewareEffectsOutput extends FetchDataOutput {\n  effect: Awaited<ReturnType<typeof getMiddlewareData>>\n}\n\nasync function withMiddlewareEffects<T extends FetchDataOutput>(\n  options: MiddlewareEffectParams<T>\n): Promise<WithMiddlewareEffectsOutput | null> {\n  const matches = await matchesMiddleware(options)\n  if (!matches || !options.fetchData) {\n    return null\n  }\n\n  const data = await options.fetchData()\n\n  const effect = await getMiddlewareData(data.dataHref, data.response, options)\n\n  return {\n    dataHref: data.dataHref,\n    json: data.json,\n    response: data.response,\n    text: data.text,\n    cacheKey: data.cacheKey,\n    effect,\n  }\n}\n\nexport type Url = UrlObject | string\n\nexport type BaseRouter = {\n  route: string\n  pathname: string\n  query: ParsedUrlQuery\n  asPath: string\n  basePath: string\n  locale?: string | undefined\n  locales?: readonly string[] | undefined\n  defaultLocale?: string | undefined\n  domainLocales?: readonly DomainLocale[] | undefined\n  isLocaleDomain: boolean\n}\n\nexport type NextRouter = BaseRouter &\n  Pick<\n    Router,\n    | 'push'\n    | 'replace'\n    | 'reload'\n    | 'back'\n    | 'forward'\n    | 'prefetch'\n    | 'beforePopState'\n    | 'events'\n    | 'isFallback'\n    | 'isReady'\n    | 'isPreview'\n  >\n\nexport type PrefetchOptions = {\n  priority?: boolean\n  locale?: string | false\n  unstable_skipClientCache?: boolean\n}\n\nexport type PrivateRouteInfo =\n  | (Omit<CompletePrivateRouteInfo, 'styleSheets'> & { initial: true })\n  | CompletePrivateRouteInfo\n\nexport type CompletePrivateRouteInfo = {\n  Component: ComponentType\n  styleSheets: StyleSheetTuple[]\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n  props?: Record<string, any>\n  err?: Error\n  error?: any\n  route?: string\n  resolvedAs?: string\n  query?: ParsedUrlQuery\n}\n\nexport type AppProps = Pick<CompletePrivateRouteInfo, 'Component' | 'err'> & {\n  router: Router\n} & Record<string, any>\nexport type AppComponent = ComponentType<AppProps>\n\ntype Subscription = (\n  data: PrivateRouteInfo,\n  App: AppComponent,\n  resetScroll: { x: number; y: number } | null\n) => Promise<void>\n\ntype BeforePopStateCallback = (state: NextHistoryState) => boolean\n\ntype ComponentLoadCancel = (() => void) | null\n\ntype HistoryMethod = 'replaceState' | 'pushState'\n\nconst manualScrollRestoration =\n  process.env.__NEXT_SCROLL_RESTORATION &&\n  typeof window !== 'undefined' &&\n  'scrollRestoration' in window.history &&\n  !!(function () {\n    try {\n      let v = '__next'\n      // eslint-disable-next-line no-sequences\n      return sessionStorage.setItem(v, v), sessionStorage.removeItem(v), true\n    } catch (n) {}\n  })()\n\nconst SSG_DATA_NOT_FOUND = Symbol('SSG_DATA_NOT_FOUND')\n\nfunction fetchRetry(\n  url: string,\n  attempts: number,\n  options: Pick<RequestInit, 'method' | 'headers'>\n): Promise<Response> {\n  return fetch(url, {\n    // Cookies are required to be present for Next.js' SSG \"Preview Mode\".\n    // Cookies may also be required for `getServerSideProps`.\n    //\n    // > `fetch` won’t send cookies, unless you set the credentials init\n    // > option.\n    // https://developer.mozilla.org/docs/Web/API/Fetch_API/Using_Fetch\n    //\n    // > For maximum browser compatibility when it comes to sending &\n    // > receiving cookies, always supply the `credentials: 'same-origin'`\n    // > option instead of relying on the default.\n    // https://github.com/github/fetch#caveats\n    credentials: 'same-origin',\n    method: options.method || 'GET',\n    headers: Object.assign({}, options.headers, {\n      'x-nextjs-data': '1',\n    }),\n  }).then((response) => {\n    return !response.ok && attempts > 1 && response.status >= 500\n      ? fetchRetry(url, attempts - 1, options)\n      : response\n  })\n}\n\ninterface FetchDataOutput {\n  dataHref: string\n  json: Record<string, any> | null\n  response: Response\n  text: string\n  cacheKey: string\n}\n\ninterface FetchNextDataParams {\n  dataHref: string\n  isServerRender: boolean\n  parseJSON: boolean | undefined\n  hasMiddleware?: boolean\n  inflightCache: NextDataCache\n  persistCache: boolean\n  isPrefetch: boolean\n  isBackground?: boolean\n  unstable_skipClientCache?: boolean\n}\n\nfunction tryToParseAsJSON(text: string) {\n  try {\n    return JSON.parse(text)\n  } catch (error) {\n    return null\n  }\n}\n\nfunction fetchNextData({\n  dataHref,\n  inflightCache,\n  isPrefetch,\n  hasMiddleware,\n  isServerRender,\n  parseJSON,\n  persistCache,\n  isBackground,\n  unstable_skipClientCache,\n}: FetchNextDataParams): Promise<FetchDataOutput> {\n  const { href: cacheKey } = new URL(dataHref, window.location.href)\n  const getData = (params?: { method?: 'HEAD' | 'GET' }) =>\n    fetchRetry(dataHref, isServerRender ? 3 : 1, {\n      headers: Object.assign(\n        {} as HeadersInit,\n        isPrefetch ? { purpose: 'prefetch' } : {},\n        isPrefetch && hasMiddleware ? { 'x-middleware-prefetch': '1' } : {},\n        process.env.NEXT_DEPLOYMENT_ID\n          ? { 'x-deployment-id': process.env.NEXT_DEPLOYMENT_ID }\n          : {}\n      ),\n      method: params?.method ?? 'GET',\n    })\n      .then((response) => {\n        if (response.ok && params?.method === 'HEAD') {\n          return { dataHref, response, text: '', json: {}, cacheKey }\n        }\n\n        return response.text().then((text) => {\n          if (!response.ok) {\n            /**\n             * When the data response is a redirect because of a middleware\n             * we do not consider it an error. The headers must bring the\n             * mapped location.\n             * TODO: Change the status code in the handler.\n             */\n            if (\n              hasMiddleware &&\n              [301, 302, 307, 308].includes(response.status)\n            ) {\n              return { dataHref, response, text, json: {}, cacheKey }\n            }\n\n            if (response.status === 404) {\n              if (tryToParseAsJSON(text)?.notFound) {\n                return {\n                  dataHref,\n                  json: { notFound: SSG_DATA_NOT_FOUND },\n                  response,\n                  text,\n                  cacheKey,\n                }\n              }\n            }\n\n            const error = new Error(`Failed to load static props`)\n\n            /**\n             * We should only trigger a server-side transition if this was\n             * caused on a client-side transition. Otherwise, we'd get into\n             * an infinite loop.\n             */\n            if (!isServerRender) {\n              markAssetError(error)\n            }\n\n            throw error\n          }\n\n          return {\n            dataHref,\n            json: parseJSON ? tryToParseAsJSON(text) : null,\n            response,\n            text,\n            cacheKey,\n          }\n        })\n      })\n      .then((data) => {\n        if (\n          !persistCache ||\n          process.env.NODE_ENV !== 'production' ||\n          data.response.headers.get('x-middleware-cache') === 'no-cache'\n        ) {\n          delete inflightCache[cacheKey]\n        }\n        return data\n      })\n      .catch((err) => {\n        if (!unstable_skipClientCache) {\n          delete inflightCache[cacheKey]\n        }\n        if (\n          // chrome\n          err.message === 'Failed to fetch' ||\n          // firefox\n          err.message === 'NetworkError when attempting to fetch resource.' ||\n          // safari\n          err.message === 'Load failed'\n        ) {\n          markAssetError(err)\n        }\n        throw err\n      })\n\n  // when skipping client cache we wait to update\n  // inflight cache until successful data response\n  // this allows racing click event with fetching newer data\n  // without blocking navigation when stale data is available\n  if (unstable_skipClientCache && persistCache) {\n    return getData({}).then((data) => {\n      if (data.response.headers.get('x-middleware-cache') !== 'no-cache') {\n        // only update cache if not marked as no-cache\n        inflightCache[cacheKey] = Promise.resolve(data)\n      }\n\n      return data\n    })\n  }\n\n  if (inflightCache[cacheKey] !== undefined) {\n    return inflightCache[cacheKey]\n  }\n  return (inflightCache[cacheKey] = getData(\n    isBackground ? { method: 'HEAD' } : {}\n  ))\n}\n\ninterface NextDataCache {\n  [asPath: string]: Promise<FetchDataOutput>\n}\n\nexport function createKey() {\n  return Math.random().toString(36).slice(2, 10)\n}\n\nfunction handleHardNavigation({\n  url,\n  router,\n}: {\n  url: string\n  router: Router\n}) {\n  // ensure we don't trigger a hard navigation to the same\n  // URL as this can end up with an infinite refresh\n  if (url === addBasePath(addLocale(router.asPath, router.locale))) {\n    throw new Error(\n      `Invariant: attempted to hard navigate to the same URL ${url} ${location.href}`\n    )\n  }\n  window.location.href = url\n}\n\nconst getCancelledHandler = ({\n  route,\n  router,\n}: {\n  route: string\n  router: Router\n}) => {\n  let cancelled = false\n  const cancel = (router.clc = () => {\n    cancelled = true\n  })\n\n  const handleCancelled = () => {\n    if (cancelled) {\n      const error: any = new Error(\n        `Abort fetching component for route: \"${route}\"`\n      )\n      error.cancelled = true\n      throw error\n    }\n\n    if (cancel === router.clc) {\n      router.clc = null\n    }\n  }\n  return handleCancelled\n}\n\nexport default class Router implements BaseRouter {\n  basePath: string\n\n  /**\n   * Map of all components loaded in `Router`\n   */\n  components: { [pathname: string]: PrivateRouteInfo }\n  // Server Data Cache (full data requests)\n  sdc: NextDataCache = {}\n  // Server Background Cache (HEAD requests)\n  sbc: NextDataCache = {}\n\n  sub: Subscription\n  clc: ComponentLoadCancel\n  pageLoader: PageLoader\n  _bps: BeforePopStateCallback | undefined\n  events: MittEmitter<RouterEvent>\n  _wrapApp: (App: AppComponent) => any\n  isSsr: boolean\n  _inFlightRoute?: string | undefined\n  _shallow?: boolean | undefined\n  locales?: readonly string[] | undefined\n  defaultLocale?: string | undefined\n  domainLocales?: readonly DomainLocale[] | undefined\n  isReady: boolean\n  isLocaleDomain: boolean\n  isFirstPopStateEvent = true\n  _initialMatchesMiddlewarePromise: Promise<boolean>\n  // static entries filter\n  _bfl_s?: import('../../lib/bloom-filter').BloomFilter\n  // dynamic entires filter\n  _bfl_d?: import('../../lib/bloom-filter').BloomFilter\n\n  private state: Readonly<{\n    route: string\n    pathname: string\n    query: ParsedUrlQuery\n    asPath: string\n    locale: string | undefined\n    isFallback: boolean\n    isPreview: boolean\n  }>\n\n  private _key: string = createKey()\n\n  static events: MittEmitter<RouterEvent> = mitt()\n\n  constructor(\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    {\n      initialProps,\n      pageLoader,\n      App,\n      wrapApp,\n      Component,\n      err,\n      subscription,\n      isFallback,\n      locale,\n      locales,\n      defaultLocale,\n      domainLocales,\n      isPreview,\n    }: {\n      subscription: Subscription\n      initialProps: any\n      pageLoader: any\n      Component: ComponentType\n      App: AppComponent\n      wrapApp: (WrapAppComponent: AppComponent) => any\n      err?: Error\n      isFallback: boolean\n      locale?: string\n      locales?: readonly string[]\n      defaultLocale?: string\n      domainLocales?: readonly DomainLocale[]\n      isPreview?: boolean\n    }\n  ) {\n    // represents the current component key\n    const route = removeTrailingSlash(pathname)\n\n    // set up the component cache (by route keys)\n    this.components = {}\n    // We should not keep the cache, if there's an error\n    // Otherwise, this cause issues when when going back and\n    // come again to the errored page.\n    if (pathname !== '/_error') {\n      this.components[route] = {\n        Component,\n        initial: true,\n        props: initialProps,\n        err,\n        __N_SSG: initialProps && initialProps.__N_SSG,\n        __N_SSP: initialProps && initialProps.__N_SSP,\n      }\n    }\n\n    this.components['/_app'] = {\n      Component: App as ComponentType,\n      styleSheets: [\n        /* /_app does not need its stylesheets managed */\n      ],\n    }\n\n    // Backwards compat for Router.router.events\n    // TODO: Should be remove the following major version as it was never documented\n    this.events = Router.events\n\n    this.pageLoader = pageLoader\n    // if auto prerendered and dynamic route wait to update asPath\n    // until after mount to prevent hydration mismatch\n    const autoExportDynamic =\n      isDynamicRoute(pathname) && self.__NEXT_DATA__.autoExport\n\n    this.basePath = process.env.__NEXT_ROUTER_BASEPATH || ''\n    this.sub = subscription\n    this.clc = null\n    this._wrapApp = wrapApp\n    // make sure to ignore extra popState in safari on navigating\n    // back from external site\n    this.isSsr = true\n    this.isLocaleDomain = false\n    this.isReady = !!(\n      self.__NEXT_DATA__.gssp ||\n      self.__NEXT_DATA__.gip ||\n      self.__NEXT_DATA__.isExperimentalCompile ||\n      (self.__NEXT_DATA__.appGip && !self.__NEXT_DATA__.gsp) ||\n      (!autoExportDynamic &&\n        !self.location.search &&\n        !process.env.__NEXT_HAS_REWRITES)\n    )\n\n    if (process.env.__NEXT_I18N_SUPPORT) {\n      this.locales = locales\n      this.defaultLocale = defaultLocale\n      this.domainLocales = domainLocales\n      this.isLocaleDomain = !!detectDomainLocale(\n        domainLocales,\n        self.location.hostname\n      )\n    }\n\n    this.state = {\n      route,\n      pathname,\n      query,\n      asPath: autoExportDynamic ? pathname : as,\n      isPreview: !!isPreview,\n      locale: process.env.__NEXT_I18N_SUPPORT ? locale : undefined,\n      isFallback,\n    }\n\n    this._initialMatchesMiddlewarePromise = Promise.resolve(false)\n\n    if (typeof window !== 'undefined') {\n      // make sure \"as\" doesn't start with double slashes or else it can\n      // throw an error as it's considered invalid\n      if (!as.startsWith('//')) {\n        // in order for `e.state` to work on the `onpopstate` event\n        // we have to register the initial route upon initialization\n        const options: TransitionOptions = { locale }\n        const asPath = getURL()\n\n        this._initialMatchesMiddlewarePromise = matchesMiddleware({\n          router: this,\n          locale,\n          asPath,\n        }).then((matches) => {\n          // if middleware matches we leave resolving to the change function\n          // as the server needs to resolve for correct priority\n          ;(options as any)._shouldResolveHref = as !== pathname\n\n          this.changeState(\n            'replaceState',\n            matches\n              ? asPath\n              : formatWithValidation({\n                  pathname: addBasePath(pathname),\n                  query,\n                }),\n            asPath,\n            options\n          )\n          return matches\n        })\n      }\n\n      window.addEventListener('popstate', this.onPopState)\n\n      // enable custom scroll restoration handling when available\n      // otherwise fallback to browser's default handling\n      if (process.env.__NEXT_SCROLL_RESTORATION) {\n        if (manualScrollRestoration) {\n          window.history.scrollRestoration = 'manual'\n        }\n      }\n    }\n  }\n\n  onPopState = (e: PopStateEvent): void => {\n    const { isFirstPopStateEvent } = this\n    this.isFirstPopStateEvent = false\n\n    const state = e.state as HistoryState\n\n    if (!state) {\n      // We get state as undefined for two reasons.\n      //  1. With older safari (< 8) and older chrome (< 34)\n      //  2. When the URL changed with #\n      //\n      // In the both cases, we don't need to proceed and change the route.\n      // (as it's already changed)\n      // But we can simply replace the state with the new changes.\n      // Actually, for (1) we don't need to nothing. But it's hard to detect that event.\n      // So, doing the following for (1) does no harm.\n      const { pathname, query } = this\n      this.changeState(\n        'replaceState',\n        formatWithValidation({ pathname: addBasePath(pathname), query }),\n        getURL()\n      )\n      return\n    }\n\n    // __NA is used to identify if the history entry can be handled by the app-router.\n    if (state.__NA) {\n      window.location.reload()\n      return\n    }\n\n    if (!state.__N) {\n      return\n    }\n\n    // Safari fires popstateevent when reopening the browser.\n    if (\n      isFirstPopStateEvent &&\n      this.locale === state.options.locale &&\n      state.as === this.asPath\n    ) {\n      return\n    }\n\n    let forcedScroll: { x: number; y: number } | undefined\n    const { url, as, options, key } = state\n    if (process.env.__NEXT_SCROLL_RESTORATION) {\n      if (manualScrollRestoration) {\n        if (this._key !== key) {\n          // Snapshot current scroll position:\n          try {\n            sessionStorage.setItem(\n              '__next_scroll_' + this._key,\n              JSON.stringify({ x: self.pageXOffset, y: self.pageYOffset })\n            )\n          } catch {}\n\n          // Restore old scroll position:\n          try {\n            const v = sessionStorage.getItem('__next_scroll_' + key)\n            forcedScroll = JSON.parse(v!)\n          } catch {\n            forcedScroll = { x: 0, y: 0 }\n          }\n        }\n      }\n    }\n    this._key = key\n\n    const { pathname } = parseRelativeUrl(url)\n\n    // Make sure we don't re-render on initial load,\n    // can be caused by navigating back from an external site\n    if (\n      this.isSsr &&\n      as === addBasePath(this.asPath) &&\n      pathname === addBasePath(this.pathname)\n    ) {\n      return\n    }\n\n    // If the downstream application returns falsy, return.\n    // They will then be responsible for handling the event.\n    if (this._bps && !this._bps(state)) {\n      return\n    }\n\n    this.change(\n      'replaceState',\n      url,\n      as,\n      Object.assign<{}, TransitionOptions, TransitionOptions>({}, options, {\n        shallow: options.shallow && this._shallow,\n        locale: options.locale || this.defaultLocale,\n        // @ts-ignore internal value not exposed on types\n        _h: 0,\n      }),\n      forcedScroll\n    )\n  }\n\n  reload(): void {\n    window.location.reload()\n  }\n\n  /**\n   * Go back in history\n   */\n  back() {\n    window.history.back()\n  }\n\n  /**\n   * Go forward in history\n   */\n  forward() {\n    window.history.forward()\n  }\n\n  /**\n   * Performs a `pushState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */\n  push(url: Url, as?: Url, options: TransitionOptions = {}) {\n    if (process.env.__NEXT_SCROLL_RESTORATION) {\n      // TODO: remove in the future when we update history before route change\n      // is complete, as the popstate event should handle this capture.\n      if (manualScrollRestoration) {\n        try {\n          // Snapshot scroll position right before navigating to a new page:\n          sessionStorage.setItem(\n            '__next_scroll_' + this._key,\n            JSON.stringify({ x: self.pageXOffset, y: self.pageYOffset })\n          )\n        } catch {}\n      }\n    }\n    ;({ url, as } = prepareUrlAs(this, url, as))\n    return this.change('pushState', url, as, options)\n  }\n\n  /**\n   * Performs a `replaceState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */\n  replace(url: Url, as?: Url, options: TransitionOptions = {}) {\n    ;({ url, as } = prepareUrlAs(this, url, as))\n    return this.change('replaceState', url, as, options)\n  }\n\n  async _bfl(\n    as: string,\n    resolvedAs?: string,\n    locale?: string | false,\n    skipNavigate?: boolean\n  ) {\n    if (process.env.__NEXT_CLIENT_ROUTER_FILTER_ENABLED) {\n      if (!this._bfl_s && !this._bfl_d) {\n        const { BloomFilter } =\n          require('../../lib/bloom-filter') as typeof import('../../lib/bloom-filter')\n\n        type Filter = ReturnType<\n          import('../../lib/bloom-filter').BloomFilter['export']\n        >\n        let staticFilterData: Filter | undefined\n        let dynamicFilterData: Filter | undefined\n\n        try {\n          ;({\n            __routerFilterStatic: staticFilterData,\n            __routerFilterDynamic: dynamicFilterData,\n          } = (await getClientBuildManifest()) as any as {\n            __routerFilterStatic?: Filter\n            __routerFilterDynamic?: Filter\n          })\n        } catch (err) {\n          // failed to load build manifest hard navigate\n          // to be safe\n          console.error(err)\n          if (skipNavigate) {\n            return true\n          }\n          handleHardNavigation({\n            url: addBasePath(\n              addLocale(as, locale || this.locale, this.defaultLocale)\n            ),\n            router: this,\n          })\n          return new Promise(() => {})\n        }\n\n        const routerFilterSValue: Filter | false = process.env\n          .__NEXT_CLIENT_ROUTER_S_FILTER as any\n\n        if (!staticFilterData && routerFilterSValue) {\n          staticFilterData = routerFilterSValue ? routerFilterSValue : undefined\n        }\n\n        const routerFilterDValue: Filter | false = process.env\n          .__NEXT_CLIENT_ROUTER_D_FILTER as any\n\n        if (!dynamicFilterData && routerFilterDValue) {\n          dynamicFilterData = routerFilterDValue\n            ? routerFilterDValue\n            : undefined\n        }\n\n        if (staticFilterData?.numHashes) {\n          this._bfl_s = new BloomFilter(\n            staticFilterData.numItems,\n            staticFilterData.errorRate\n          )\n          this._bfl_s.import(staticFilterData)\n        }\n\n        if (dynamicFilterData?.numHashes) {\n          this._bfl_d = new BloomFilter(\n            dynamicFilterData.numItems,\n            dynamicFilterData.errorRate\n          )\n          this._bfl_d.import(dynamicFilterData)\n        }\n      }\n\n      let matchesBflStatic = false\n      let matchesBflDynamic = false\n      const pathsToCheck: Array<{ as?: string; allowMatchCurrent?: boolean }> =\n        [{ as }, { as: resolvedAs }]\n\n      for (const { as: curAs, allowMatchCurrent } of pathsToCheck) {\n        if (curAs) {\n          const asNoSlash = removeTrailingSlash(\n            new URL(curAs, 'http://n').pathname\n          )\n          const asNoSlashLocale = addBasePath(\n            addLocale(asNoSlash, locale || this.locale)\n          )\n\n          if (\n            allowMatchCurrent ||\n            asNoSlash !==\n              removeTrailingSlash(new URL(this.asPath, 'http://n').pathname)\n          ) {\n            matchesBflStatic =\n              matchesBflStatic ||\n              !!this._bfl_s?.contains(asNoSlash) ||\n              !!this._bfl_s?.contains(asNoSlashLocale)\n\n            for (const normalizedAS of [asNoSlash, asNoSlashLocale]) {\n              // if any sub-path of as matches a dynamic filter path\n              // it should be hard navigated\n              const curAsParts = normalizedAS.split('/')\n              for (\n                let i = 0;\n                !matchesBflDynamic && i < curAsParts.length + 1;\n                i++\n              ) {\n                const currentPart = curAsParts.slice(0, i).join('/')\n                if (currentPart && this._bfl_d?.contains(currentPart)) {\n                  matchesBflDynamic = true\n                  break\n                }\n              }\n            }\n\n            // if the client router filter is matched then we trigger\n            // a hard navigation\n            if (matchesBflStatic || matchesBflDynamic) {\n              if (skipNavigate) {\n                return true\n              }\n              handleHardNavigation({\n                url: addBasePath(\n                  addLocale(as, locale || this.locale, this.defaultLocale)\n                ),\n                router: this,\n              })\n              return new Promise(() => {})\n            }\n          }\n        }\n      }\n    }\n    return false\n  }\n\n  private async change(\n    method: HistoryMethod,\n    url: string,\n    as: string,\n    options: TransitionOptions,\n    forcedScroll?: { x: number; y: number }\n  ): Promise<boolean> {\n    if (!isLocalURL(url)) {\n      handleHardNavigation({ url, router: this })\n      return false\n    }\n    // WARNING: `_h` is an internal option for handing Next.js client-side\n    // hydration. Your app should _never_ use this property. It may change at\n    // any time without notice.\n    const isQueryUpdating = (options as any)._h === 1\n\n    if (!isQueryUpdating && !options.shallow) {\n      await this._bfl(as, undefined, options.locale)\n    }\n\n    let shouldResolveHref =\n      isQueryUpdating ||\n      (options as any)._shouldResolveHref ||\n      parsePath(url).pathname === parsePath(as).pathname\n\n    const nextState = {\n      ...this.state,\n    }\n\n    // for static pages with query params in the URL we delay\n    // marking the router ready until after the query is updated\n    // or a navigation has occurred\n    const readyStateChange = this.isReady !== true\n    this.isReady = true\n    const isSsr = this.isSsr\n\n    if (!isQueryUpdating) {\n      this.isSsr = false\n    }\n\n    // if a route transition is already in progress before\n    // the query updating is triggered ignore query updating\n    if (isQueryUpdating && this.clc) {\n      return false\n    }\n\n    const prevLocale = nextState.locale\n\n    if (process.env.__NEXT_I18N_SUPPORT) {\n      nextState.locale =\n        options.locale === false\n          ? this.defaultLocale\n          : options.locale || nextState.locale\n\n      if (typeof options.locale === 'undefined') {\n        options.locale = nextState.locale\n      }\n\n      const parsedAs = parseRelativeUrl(\n        hasBasePath(as) ? removeBasePath(as) : as\n      )\n      const localePathResult = normalizeLocalePath(\n        parsedAs.pathname,\n        this.locales\n      )\n\n      if (localePathResult.detectedLocale) {\n        nextState.locale = localePathResult.detectedLocale\n        parsedAs.pathname = addBasePath(parsedAs.pathname)\n        as = formatWithValidation(parsedAs)\n        url = addBasePath(\n          normalizeLocalePath(\n            hasBasePath(url) ? removeBasePath(url) : url,\n            this.locales\n          ).pathname\n        )\n      }\n      let didNavigate = false\n\n      // we need to wrap this in the env check again since regenerator runtime\n      // moves this on its own due to the return\n      if (process.env.__NEXT_I18N_SUPPORT) {\n        // if the locale isn't configured hard navigate to show 404 page\n        if (!this.locales?.includes(nextState.locale!)) {\n          parsedAs.pathname = addLocale(parsedAs.pathname, nextState.locale)\n          handleHardNavigation({\n            url: formatWithValidation(parsedAs),\n            router: this,\n          })\n          // this was previously a return but was removed in favor\n          // of better dead code elimination with regenerator runtime\n          didNavigate = true\n        }\n      }\n\n      const detectedDomain = detectDomainLocale(\n        this.domainLocales,\n        undefined,\n        nextState.locale\n      )\n\n      // we need to wrap this in the env check again since regenerator runtime\n      // moves this on its own due to the return\n      if (process.env.__NEXT_I18N_SUPPORT) {\n        // if we are navigating to a domain locale ensure we redirect to the\n        // correct domain\n        if (\n          !didNavigate &&\n          detectedDomain &&\n          this.isLocaleDomain &&\n          self.location.hostname !== detectedDomain.domain\n        ) {\n          const asNoBasePath = removeBasePath(as)\n          handleHardNavigation({\n            url: `http${detectedDomain.http ? '' : 's'}://${\n              detectedDomain.domain\n            }${addBasePath(\n              `${\n                nextState.locale === detectedDomain.defaultLocale\n                  ? ''\n                  : `/${nextState.locale}`\n              }${asNoBasePath === '/' ? '' : asNoBasePath}` || '/'\n            )}`,\n            router: this,\n          })\n          // this was previously a return but was removed in favor\n          // of better dead code elimination with regenerator runtime\n          didNavigate = true\n        }\n      }\n\n      if (didNavigate) {\n        return new Promise(() => {})\n      }\n    }\n\n    // marking route changes as a navigation start entry\n    if (ST) {\n      performance.mark('routeChange')\n    }\n\n    const { shallow = false, scroll = true } = options\n    const routeProps = { shallow }\n\n    if (this._inFlightRoute && this.clc) {\n      if (!isSsr) {\n        Router.events.emit(\n          'routeChangeError',\n          buildCancellationError(),\n          this._inFlightRoute,\n          routeProps\n        )\n      }\n      this.clc()\n      this.clc = null\n    }\n\n    as = addBasePath(\n      addLocale(\n        hasBasePath(as) ? removeBasePath(as) : as,\n        options.locale,\n        this.defaultLocale\n      )\n    )\n    const cleanedAs = removeLocale(\n      hasBasePath(as) ? removeBasePath(as) : as,\n      nextState.locale\n    )\n    this._inFlightRoute = as\n\n    const localeChange = prevLocale !== nextState.locale\n\n    // If the url change is only related to a hash change\n    // We should not proceed. We should only change the state.\n\n    if (!isQueryUpdating && this.onlyAHashChange(cleanedAs) && !localeChange) {\n      nextState.asPath = cleanedAs\n      Router.events.emit('hashChangeStart', as, routeProps)\n      // TODO: do we need the resolved href when only a hash change?\n      this.changeState(method, url, as, {\n        ...options,\n        scroll: false,\n      })\n      if (scroll) {\n        this.scrollToHash(cleanedAs)\n      }\n      try {\n        await this.set(nextState, this.components[nextState.route], null)\n      } catch (err) {\n        if (isError(err) && err.cancelled) {\n          Router.events.emit('routeChangeError', err, cleanedAs, routeProps)\n        }\n        throw err\n      }\n\n      Router.events.emit('hashChangeComplete', as, routeProps)\n      return true\n    }\n\n    let parsed = parseRelativeUrl(url)\n    let { pathname, query } = parsed\n\n    // The build manifest needs to be loaded before auto-static dynamic pages\n    // get their query parameters to allow ensuring they can be parsed properly\n    // when rewritten to\n    let pages: string[], rewrites: any\n    try {\n      ;[pages, { __rewrites: rewrites }] = await Promise.all([\n        this.pageLoader.getPageList(),\n        getClientBuildManifest(),\n        this.pageLoader.getMiddleware(),\n      ])\n    } catch (err) {\n      // If we fail to resolve the page list or client-build manifest, we must\n      // do a server-side transition:\n      handleHardNavigation({ url: as, router: this })\n      return false\n    }\n\n    // If asked to change the current URL we should reload the current page\n    // (not location.reload() but reload getInitialProps and other Next.js stuffs)\n    // We also need to set the method = replaceState always\n    // as this should not go into the history (That's how browsers work)\n    // We should compare the new asPath to the current asPath, not the url\n    if (!this.urlIsNew(cleanedAs) && !localeChange) {\n      method = 'replaceState'\n    }\n\n    // we need to resolve the as value using rewrites for dynamic SSG\n    // pages to allow building the data URL correctly\n    let resolvedAs = as\n\n    // url and as should always be prefixed with basePath by this\n    // point by either next/link or router.push/replace so strip the\n    // basePath from the pathname to match the pages dir 1-to-1\n    pathname = pathname\n      ? removeTrailingSlash(removeBasePath(pathname))\n      : pathname\n\n    let route = removeTrailingSlash(pathname)\n    const parsedAsPathname = as.startsWith('/') && parseRelativeUrl(as).pathname\n\n    // if we detected the path as app route during prefetching\n    // trigger hard navigation\n    if ((this.components[pathname] as any)?.__appRouter) {\n      handleHardNavigation({ url: as, router: this })\n      return new Promise(() => {})\n    }\n\n    const isMiddlewareRewrite = !!(\n      parsedAsPathname &&\n      route !== parsedAsPathname &&\n      (!isDynamicRoute(route) ||\n        !getRouteMatcher(getRouteRegex(route))(parsedAsPathname))\n    )\n\n    // we don't attempt resolve asPath when we need to execute\n    // middleware as the resolving will occur server-side\n    const isMiddlewareMatch =\n      !options.shallow &&\n      (await matchesMiddleware({\n        asPath: as,\n        locale: nextState.locale,\n        router: this,\n      }))\n\n    if (isQueryUpdating && isMiddlewareMatch) {\n      shouldResolveHref = false\n    }\n\n    if (shouldResolveHref && pathname !== '/_error') {\n      ;(options as any)._shouldResolveHref = true\n\n      if (process.env.__NEXT_HAS_REWRITES && as.startsWith('/')) {\n        const rewritesResult = resolveRewrites(\n          addBasePath(addLocale(cleanedAs, nextState.locale), true),\n          pages,\n          rewrites,\n          query,\n          (p: string) => resolveDynamicRoute(p, pages),\n          this.locales\n        )\n\n        if (rewritesResult.externalDest) {\n          handleHardNavigation({ url: as, router: this })\n          return true\n        }\n        if (!isMiddlewareMatch) {\n          resolvedAs = rewritesResult.asPath\n        }\n\n        if (rewritesResult.matchedPage && rewritesResult.resolvedHref) {\n          // if this directly matches a page we need to update the href to\n          // allow the correct page chunk to be loaded\n          pathname = rewritesResult.resolvedHref\n          parsed.pathname = addBasePath(pathname)\n\n          if (!isMiddlewareMatch) {\n            url = formatWithValidation(parsed)\n          }\n        }\n      } else {\n        parsed.pathname = resolveDynamicRoute(pathname, pages)\n\n        if (parsed.pathname !== pathname) {\n          pathname = parsed.pathname\n          parsed.pathname = addBasePath(pathname)\n\n          if (!isMiddlewareMatch) {\n            url = formatWithValidation(parsed)\n          }\n        }\n      }\n    }\n\n    if (!isLocalURL(as)) {\n      if (process.env.NODE_ENV !== 'production') {\n        throw new Error(\n          `Invalid href: \"${url}\" and as: \"${as}\", received relative href and external as` +\n            `\\nSee more info: https://nextjs.org/docs/messages/invalid-relative-url-external-as`\n        )\n      }\n      handleHardNavigation({ url: as, router: this })\n      return false\n    }\n\n    resolvedAs = removeLocale(removeBasePath(resolvedAs), nextState.locale)\n\n    route = removeTrailingSlash(pathname)\n    let routeMatch: Params | false = false\n\n    if (isDynamicRoute(route)) {\n      const parsedAs = parseRelativeUrl(resolvedAs)\n      const asPathname = parsedAs.pathname\n\n      const routeRegex = getRouteRegex(route)\n      routeMatch = getRouteMatcher(routeRegex)(asPathname)\n      const shouldInterpolate = route === asPathname\n      const interpolatedAs = shouldInterpolate\n        ? interpolateAs(route, asPathname, query)\n        : ({} as { result: undefined; params: undefined })\n\n      if (!routeMatch || (shouldInterpolate && !interpolatedAs.result)) {\n        const missingParams = Object.keys(routeRegex.groups).filter(\n          (param) => !query[param] && !routeRegex.groups[param].optional\n        )\n\n        if (missingParams.length > 0 && !isMiddlewareMatch) {\n          if (process.env.NODE_ENV !== 'production') {\n            console.warn(\n              `${\n                shouldInterpolate\n                  ? `Interpolating href`\n                  : `Mismatching \\`as\\` and \\`href\\``\n              } failed to manually provide ` +\n                `the params: ${missingParams.join(\n                  ', '\n                )} in the \\`href\\`'s \\`query\\``\n            )\n          }\n\n          throw new Error(\n            (shouldInterpolate\n              ? `The provided \\`href\\` (${url}) value is missing query values (${missingParams.join(\n                  ', '\n                )}) to be interpolated properly. `\n              : `The provided \\`as\\` value (${asPathname}) is incompatible with the \\`href\\` value (${route}). `) +\n              `Read more: https://nextjs.org/docs/messages/${\n                shouldInterpolate\n                  ? 'href-interpolation-failed'\n                  : 'incompatible-href-as'\n              }`\n          )\n        }\n      } else if (shouldInterpolate) {\n        as = formatWithValidation(\n          Object.assign({}, parsedAs, {\n            pathname: interpolatedAs.result,\n            query: omit(query, interpolatedAs.params!),\n          })\n        )\n      } else {\n        // Merge params into `query`, overwriting any specified in search\n        Object.assign(query, routeMatch)\n      }\n    }\n\n    if (!isQueryUpdating) {\n      Router.events.emit('routeChangeStart', as, routeProps)\n    }\n\n    const isErrorRoute = this.pathname === '/404' || this.pathname === '/_error'\n\n    try {\n      let routeInfo = await this.getRouteInfo({\n        route,\n        pathname,\n        query,\n        as,\n        resolvedAs,\n        routeProps,\n        locale: nextState.locale,\n        isPreview: nextState.isPreview,\n        hasMiddleware: isMiddlewareMatch,\n        unstable_skipClientCache: options.unstable_skipClientCache,\n        isQueryUpdating: isQueryUpdating && !this.isFallback,\n        isMiddlewareRewrite,\n      })\n\n      if (!isQueryUpdating && !options.shallow) {\n        await this._bfl(\n          as,\n          'resolvedAs' in routeInfo ? routeInfo.resolvedAs : undefined,\n          nextState.locale\n        )\n      }\n\n      if ('route' in routeInfo && isMiddlewareMatch) {\n        pathname = routeInfo.route || route\n        route = pathname\n\n        if (!routeProps.shallow) {\n          query = Object.assign({}, routeInfo.query || {}, query)\n        }\n\n        const cleanedParsedPathname = hasBasePath(parsed.pathname)\n          ? removeBasePath(parsed.pathname)\n          : parsed.pathname\n\n        if (routeMatch && pathname !== cleanedParsedPathname) {\n          Object.keys(routeMatch).forEach((key) => {\n            if (routeMatch && query[key] === routeMatch[key]) {\n              delete query[key]\n            }\n          })\n        }\n\n        if (isDynamicRoute(pathname)) {\n          const prefixedAs =\n            !routeProps.shallow && routeInfo.resolvedAs\n              ? routeInfo.resolvedAs\n              : addBasePath(\n                  addLocale(\n                    new URL(as, location.href).pathname,\n                    nextState.locale\n                  ),\n                  true\n                )\n\n          let rewriteAs = prefixedAs\n\n          if (hasBasePath(rewriteAs)) {\n            rewriteAs = removeBasePath(rewriteAs)\n          }\n\n          if (process.env.__NEXT_I18N_SUPPORT) {\n            const localeResult = normalizeLocalePath(rewriteAs, this.locales)\n            nextState.locale = localeResult.detectedLocale || nextState.locale\n            rewriteAs = localeResult.pathname\n          }\n          const routeRegex = getRouteRegex(pathname)\n          const curRouteMatch = getRouteMatcher(routeRegex)(\n            new URL(rewriteAs, location.href).pathname\n          )\n\n          if (curRouteMatch) {\n            Object.assign(query, curRouteMatch)\n          }\n        }\n      }\n\n      // If the routeInfo brings a redirect we simply apply it.\n      if ('type' in routeInfo) {\n        if (routeInfo.type === 'redirect-internal') {\n          return this.change(method, routeInfo.newUrl, routeInfo.newAs, options)\n        } else {\n          handleHardNavigation({ url: routeInfo.destination, router: this })\n          return new Promise(() => {})\n        }\n      }\n\n      const component: any = routeInfo.Component\n      if (component && component.unstable_scriptLoader) {\n        const scripts = [].concat(component.unstable_scriptLoader())\n\n        scripts.forEach((script: any) => {\n          handleClientScriptLoad(script.props)\n        })\n      }\n\n      // handle redirect on client-transition\n      if ((routeInfo.__N_SSG || routeInfo.__N_SSP) && routeInfo.props) {\n        if (\n          routeInfo.props.pageProps &&\n          routeInfo.props.pageProps.__N_REDIRECT\n        ) {\n          // Use the destination from redirect without adding locale\n          options.locale = false\n\n          const destination = routeInfo.props.pageProps.__N_REDIRECT\n\n          // check if destination is internal (resolves to a page) and attempt\n          // client-navigation if it is falling back to hard navigation if\n          // it's not\n          if (\n            destination.startsWith('/') &&\n            routeInfo.props.pageProps.__N_REDIRECT_BASE_PATH !== false\n          ) {\n            const parsedHref = parseRelativeUrl(destination)\n            parsedHref.pathname = resolveDynamicRoute(\n              parsedHref.pathname,\n              pages\n            )\n\n            const { url: newUrl, as: newAs } = prepareUrlAs(\n              this,\n              destination,\n              destination\n            )\n            return this.change(method, newUrl, newAs, options)\n          }\n          handleHardNavigation({ url: destination, router: this })\n          return new Promise(() => {})\n        }\n\n        nextState.isPreview = !!routeInfo.props.__N_PREVIEW\n\n        // handle SSG data 404\n        if (routeInfo.props.notFound === SSG_DATA_NOT_FOUND) {\n          let notFoundRoute\n\n          try {\n            await this.fetchComponent('/404')\n            notFoundRoute = '/404'\n          } catch (_) {\n            notFoundRoute = '/_error'\n          }\n\n          routeInfo = await this.getRouteInfo({\n            route: notFoundRoute,\n            pathname: notFoundRoute,\n            query,\n            as,\n            resolvedAs,\n            routeProps: { shallow: false },\n            locale: nextState.locale,\n            isPreview: nextState.isPreview,\n            isNotFound: true,\n          })\n\n          if ('type' in routeInfo) {\n            throw new Error(`Unexpected middleware effect on /404`)\n          }\n        }\n      }\n\n      if (\n        isQueryUpdating &&\n        this.pathname === '/_error' &&\n        self.__NEXT_DATA__.props?.pageProps?.statusCode === 500 &&\n        routeInfo.props?.pageProps\n      ) {\n        // ensure statusCode is still correct for static 500 page\n        // when updating query information\n        routeInfo.props.pageProps.statusCode = 500\n      }\n\n      // shallow routing is only allowed for same page URL changes.\n      const isValidShallowRoute =\n        options.shallow && nextState.route === (routeInfo.route ?? route)\n\n      const shouldScroll =\n        options.scroll ?? (!isQueryUpdating && !isValidShallowRoute)\n      const resetScroll = shouldScroll ? { x: 0, y: 0 } : null\n      const upcomingScrollState = forcedScroll ?? resetScroll\n\n      // the new state that the router gonna set\n      const upcomingRouterState = {\n        ...nextState,\n        route,\n        pathname,\n        query,\n        asPath: cleanedAs,\n        isFallback: false,\n      }\n\n      // When the page being rendered is the 404 page, we should only update the\n      // query parameters. Route changes here might add the basePath when it\n      // wasn't originally present. This is also why this block is before the\n      // below `changeState` call which updates the browser's history (changing\n      // the URL).\n      if (isQueryUpdating && isErrorRoute) {\n        routeInfo = await this.getRouteInfo({\n          route: this.pathname,\n          pathname: this.pathname,\n          query,\n          as,\n          resolvedAs,\n          routeProps: { shallow: false },\n          locale: nextState.locale,\n          isPreview: nextState.isPreview,\n          isQueryUpdating: isQueryUpdating && !this.isFallback,\n        })\n\n        if ('type' in routeInfo) {\n          throw new Error(`Unexpected middleware effect on ${this.pathname}`)\n        }\n\n        if (\n          this.pathname === '/_error' &&\n          self.__NEXT_DATA__.props?.pageProps?.statusCode === 500 &&\n          routeInfo.props?.pageProps\n        ) {\n          // ensure statusCode is still correct for static 500 page\n          // when updating query information\n          routeInfo.props.pageProps.statusCode = 500\n        }\n\n        try {\n          await this.set(upcomingRouterState, routeInfo, upcomingScrollState)\n        } catch (err) {\n          if (isError(err) && err.cancelled) {\n            Router.events.emit('routeChangeError', err, cleanedAs, routeProps)\n          }\n          throw err\n        }\n\n        return true\n      }\n\n      Router.events.emit('beforeHistoryChange', as, routeProps)\n      this.changeState(method, url, as, options)\n\n      // for query updates we can skip it if the state is unchanged and we don't\n      // need to scroll\n      // https://github.com/vercel/next.js/issues/37139\n      const canSkipUpdating =\n        isQueryUpdating &&\n        !upcomingScrollState &&\n        !readyStateChange &&\n        !localeChange &&\n        compareRouterStates(upcomingRouterState, this.state)\n\n      if (!canSkipUpdating) {\n        try {\n          await this.set(upcomingRouterState, routeInfo, upcomingScrollState)\n        } catch (e: any) {\n          if (e.cancelled) routeInfo.error = routeInfo.error || e\n          else throw e\n        }\n\n        if (routeInfo.error) {\n          if (!isQueryUpdating) {\n            Router.events.emit(\n              'routeChangeError',\n              routeInfo.error,\n              cleanedAs,\n              routeProps\n            )\n          }\n\n          throw routeInfo.error\n        }\n\n        if (process.env.__NEXT_I18N_SUPPORT) {\n          if (nextState.locale) {\n            document.documentElement.lang = nextState.locale\n          }\n        }\n\n        if (!isQueryUpdating) {\n          Router.events.emit('routeChangeComplete', as, routeProps)\n        }\n\n        // A hash mark # is the optional last part of a URL\n        const hashRegex = /#.+$/\n        if (shouldScroll && hashRegex.test(as)) {\n          this.scrollToHash(as)\n        }\n      }\n\n      return true\n    } catch (err) {\n      if (isError(err) && err.cancelled) {\n        return false\n      }\n      throw err\n    }\n  }\n\n  changeState(\n    method: HistoryMethod,\n    url: string,\n    as: string,\n    options: TransitionOptions = {}\n  ): void {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof window.history === 'undefined') {\n        console.error(`Warning: window.history is not available.`)\n        return\n      }\n\n      if (typeof window.history[method] === 'undefined') {\n        console.error(`Warning: window.history.${method} is not available`)\n        return\n      }\n    }\n\n    if (method !== 'pushState' || getURL() !== as) {\n      this._shallow = options.shallow\n      window.history[method](\n        {\n          url,\n          as,\n          options,\n          __N: true,\n          key: (this._key = method !== 'pushState' ? this._key : createKey()),\n        } as HistoryState,\n        // Most browsers currently ignores this parameter, although they may use it in the future.\n        // Passing the empty string here should be safe against future changes to the method.\n        // https://developer.mozilla.org/docs/Web/API/History/replaceState\n        '',\n        as\n      )\n    }\n  }\n\n  async handleRouteInfoError(\n    err: Error & { code?: any; cancelled?: boolean },\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    routeProps: RouteProperties,\n    loadErrorFail?: boolean\n  ): Promise<CompletePrivateRouteInfo> {\n    if (err.cancelled) {\n      // bubble up cancellation errors\n      throw err\n    }\n\n    if (isAssetError(err) || loadErrorFail) {\n      Router.events.emit('routeChangeError', err, as, routeProps)\n\n      // If we can't load the page it could be one of following reasons\n      //  1. Page doesn't exists\n      //  2. Page does exist in a different zone\n      //  3. Internal error while loading the page\n\n      // So, doing a hard reload is the proper way to deal with this.\n      handleHardNavigation({\n        url: as,\n        router: this,\n      })\n\n      // Changing the URL doesn't block executing the current code path.\n      // So let's throw a cancellation error stop the routing logic.\n      throw buildCancellationError()\n    }\n\n    console.error(err)\n\n    try {\n      let props: Record<string, any> | undefined\n      const { page: Component, styleSheets } =\n        await this.fetchComponent('/_error')\n\n      const routeInfo: CompletePrivateRouteInfo = {\n        props,\n        Component,\n        styleSheets,\n        err,\n        error: err,\n      }\n\n      if (!routeInfo.props) {\n        try {\n          routeInfo.props = await this.getInitialProps(Component, {\n            err,\n            pathname,\n            query,\n          } as any)\n        } catch (gipErr) {\n          console.error('Error in error page `getInitialProps`: ', gipErr)\n          routeInfo.props = {}\n        }\n      }\n\n      return routeInfo\n    } catch (routeInfoErr) {\n      return this.handleRouteInfoError(\n        isError(routeInfoErr) ? routeInfoErr : new Error(routeInfoErr + ''),\n        pathname,\n        query,\n        as,\n        routeProps,\n        true\n      )\n    }\n  }\n\n  async getRouteInfo({\n    route: requestedRoute,\n    pathname,\n    query,\n    as,\n    resolvedAs,\n    routeProps,\n    locale,\n    hasMiddleware,\n    isPreview,\n    unstable_skipClientCache,\n    isQueryUpdating,\n    isMiddlewareRewrite,\n    isNotFound,\n  }: {\n    route: string\n    pathname: string\n    query: ParsedUrlQuery\n    as: string\n    resolvedAs: string\n    hasMiddleware?: boolean\n    routeProps: RouteProperties\n    locale: string | undefined\n    isPreview: boolean\n    unstable_skipClientCache?: boolean\n    isQueryUpdating?: boolean\n    isMiddlewareRewrite?: boolean\n    isNotFound?: boolean\n  }) {\n    /**\n     * This `route` binding can change if there's a rewrite\n     * so we keep a reference to the original requested route\n     * so we can store the cache for it and avoid re-requesting every time\n     * for shallow routing purposes.\n     */\n    let route = requestedRoute\n\n    try {\n      let existingInfo: PrivateRouteInfo | undefined = this.components[route]\n      if (routeProps.shallow && existingInfo && this.route === route) {\n        return existingInfo\n      }\n\n      const handleCancelled = getCancelledHandler({ route, router: this })\n\n      if (hasMiddleware) {\n        existingInfo = undefined\n      }\n\n      let cachedRouteInfo =\n        existingInfo &&\n        !('initial' in existingInfo) &&\n        process.env.NODE_ENV !== 'development'\n          ? existingInfo\n          : undefined\n\n      const isBackground = isQueryUpdating\n      const fetchNextDataParams: FetchNextDataParams = {\n        dataHref: this.pageLoader.getDataHref({\n          href: formatWithValidation({ pathname, query }),\n          skipInterpolation: true,\n          asPath: isNotFound ? '/404' : resolvedAs,\n          locale,\n        }),\n        hasMiddleware: true,\n        isServerRender: this.isSsr,\n        parseJSON: true,\n        inflightCache: isBackground ? this.sbc : this.sdc,\n        persistCache: !isPreview,\n        isPrefetch: false,\n        unstable_skipClientCache,\n        isBackground,\n      }\n\n      let data:\n        | WithMiddlewareEffectsOutput\n        | (Pick<WithMiddlewareEffectsOutput, 'json'> &\n            Omit<Partial<WithMiddlewareEffectsOutput>, 'json'>)\n        | null =\n        isQueryUpdating && !isMiddlewareRewrite\n          ? null\n          : await withMiddlewareEffects({\n              fetchData: () => fetchNextData(fetchNextDataParams),\n              asPath: isNotFound ? '/404' : resolvedAs,\n              locale: locale,\n              router: this,\n            }).catch((err) => {\n              // we don't hard error during query updating\n              // as it's un-necessary and doesn't need to be fatal\n              // unless it is a fallback route and the props can't\n              // be loaded\n              if (isQueryUpdating) {\n                return null\n              }\n              throw err\n            })\n\n      // when rendering error routes we don't apply middleware\n      // effects\n      if (data && (pathname === '/_error' || pathname === '/404')) {\n        data.effect = undefined\n      }\n\n      if (isQueryUpdating) {\n        if (!data) {\n          data = { json: self.__NEXT_DATA__.props }\n        } else {\n          data.json = self.__NEXT_DATA__.props\n        }\n      }\n\n      handleCancelled()\n\n      if (\n        data?.effect?.type === 'redirect-internal' ||\n        data?.effect?.type === 'redirect-external'\n      ) {\n        return data.effect\n      }\n\n      if (data?.effect?.type === 'rewrite') {\n        const resolvedRoute = removeTrailingSlash(data.effect.resolvedHref)\n        const pages = await this.pageLoader.getPageList()\n\n        // during query updating the page must match although during\n        // client-transition a redirect that doesn't match a page\n        // can be returned and this should trigger a hard navigation\n        // which is valid for incremental migration\n        if (!isQueryUpdating || pages.includes(resolvedRoute)) {\n          route = resolvedRoute\n          pathname = data.effect.resolvedHref\n          query = { ...query, ...data.effect.parsedAs.query }\n          resolvedAs = removeBasePath(\n            normalizeLocalePath(data.effect.parsedAs.pathname, this.locales)\n              .pathname\n          )\n\n          // Check again the cache with the new destination.\n          existingInfo = this.components[route]\n          if (\n            routeProps.shallow &&\n            existingInfo &&\n            this.route === route &&\n            !hasMiddleware\n          ) {\n            // If we have a match with the current route due to rewrite,\n            // we can copy the existing information to the rewritten one.\n            // Then, we return the information along with the matched route.\n            return { ...existingInfo, route }\n          }\n        }\n      }\n\n      if (isAPIRoute(route)) {\n        handleHardNavigation({ url: as, router: this })\n        return new Promise<never>(() => {})\n      }\n\n      const routeInfo =\n        cachedRouteInfo ||\n        (await this.fetchComponent(route).then<CompletePrivateRouteInfo>(\n          (res) => ({\n            Component: res.page,\n            styleSheets: res.styleSheets,\n            __N_SSG: res.mod.__N_SSG,\n            __N_SSP: res.mod.__N_SSP,\n          })\n        ))\n\n      if (process.env.NODE_ENV !== 'production') {\n        const { isValidElementType } = require('next/dist/compiled/react-is')\n        if (!isValidElementType(routeInfo.Component)) {\n          throw new Error(\n            `The default export is not a React Component in page: \"${pathname}\"`\n          )\n        }\n      }\n      const wasBailedPrefetch = data?.response?.headers.get('x-middleware-skip')\n\n      const shouldFetchData = routeInfo.__N_SSG || routeInfo.__N_SSP\n\n      // For non-SSG prefetches that bailed before sending data\n      // we clear the cache to fetch full response\n      if (wasBailedPrefetch && data?.dataHref) {\n        delete this.sdc[data.dataHref]\n      }\n\n      const { props, cacheKey } = await this._getData(async () => {\n        if (shouldFetchData) {\n          if (data?.json && !wasBailedPrefetch) {\n            return { cacheKey: data.cacheKey, props: data.json }\n          }\n\n          const dataHref = data?.dataHref\n            ? data.dataHref\n            : this.pageLoader.getDataHref({\n                href: formatWithValidation({ pathname, query }),\n                asPath: resolvedAs,\n                locale,\n              })\n\n          const fetched = await fetchNextData({\n            dataHref,\n            isServerRender: this.isSsr,\n            parseJSON: true,\n            inflightCache: wasBailedPrefetch ? {} : this.sdc,\n            persistCache: !isPreview,\n            isPrefetch: false,\n            unstable_skipClientCache,\n          })\n\n          return {\n            cacheKey: fetched.cacheKey,\n            props: fetched.json || {},\n          }\n        }\n\n        return {\n          headers: {},\n          props: await this.getInitialProps(\n            routeInfo.Component,\n            // we provide AppTree later so this needs to be `any`\n            {\n              pathname,\n              query,\n              asPath: as,\n              locale,\n              locales: this.locales,\n              defaultLocale: this.defaultLocale,\n            } as any\n          ),\n        }\n      })\n\n      // Only bust the data cache for SSP routes although\n      // middleware can skip cache per request with\n      // x-middleware-cache: no-cache as well\n      if (routeInfo.__N_SSP && fetchNextDataParams.dataHref && cacheKey) {\n        delete this.sdc[cacheKey]\n      }\n\n      // we kick off a HEAD request in the background\n      // when a non-prefetch request is made to signal revalidation\n      if (\n        !this.isPreview &&\n        routeInfo.__N_SSG &&\n        process.env.NODE_ENV !== 'development' &&\n        !isQueryUpdating\n      ) {\n        fetchNextData(\n          Object.assign({}, fetchNextDataParams, {\n            isBackground: true,\n            persistCache: false,\n            inflightCache: this.sbc,\n          })\n        ).catch(() => {})\n      }\n\n      props.pageProps = Object.assign({}, props.pageProps)\n      routeInfo.props = props\n      routeInfo.route = route\n      routeInfo.query = query\n      routeInfo.resolvedAs = resolvedAs\n      this.components[route] = routeInfo\n\n      return routeInfo\n    } catch (err) {\n      return this.handleRouteInfoError(\n        getProperError(err),\n        pathname,\n        query,\n        as,\n        routeProps\n      )\n    }\n  }\n\n  private set(\n    state: typeof this.state,\n    data: PrivateRouteInfo,\n    resetScroll: { x: number; y: number } | null\n  ): Promise<void> {\n    this.state = state\n\n    return this.sub(\n      data,\n      this.components['/_app'].Component as AppComponent,\n      resetScroll\n    )\n  }\n\n  /**\n   * Callback to execute before replacing router state\n   * @param cb callback to be executed\n   */\n  beforePopState(cb: BeforePopStateCallback) {\n    this._bps = cb\n  }\n\n  onlyAHashChange(as: string): boolean {\n    if (!this.asPath) return false\n    const [oldUrlNoHash, oldHash] = this.asPath.split('#', 2)\n    const [newUrlNoHash, newHash] = as.split('#', 2)\n\n    // Makes sure we scroll to the provided hash if the url/hash are the same\n    if (newHash && oldUrlNoHash === newUrlNoHash && oldHash === newHash) {\n      return true\n    }\n\n    // If the urls are change, there's more than a hash change\n    if (oldUrlNoHash !== newUrlNoHash) {\n      return false\n    }\n\n    // If the hash has changed, then it's a hash only change.\n    // This check is necessary to handle both the enter and\n    // leave hash === '' cases. The identity case falls through\n    // and is treated as a next reload.\n    return oldHash !== newHash\n  }\n\n  scrollToHash(as: string): void {\n    const [, hash = ''] = as.split('#', 2)\n\n    handleSmoothScroll(\n      () => {\n        // Scroll to top if the hash is just `#` with no value or `#top`\n        // To mirror browsers\n        if (hash === '' || hash === 'top') {\n          window.scrollTo(0, 0)\n          return\n        }\n\n        // Decode hash to make non-latin anchor works.\n        const rawHash = decodeURIComponent(hash)\n        // First we check if the element by id is found\n        const idEl = document.getElementById(rawHash)\n        if (idEl) {\n          idEl.scrollIntoView()\n          return\n        }\n        // If there's no element with the id, we check the `name` property\n        // To mirror browsers\n        const nameEl = document.getElementsByName(rawHash)[0]\n        if (nameEl) {\n          nameEl.scrollIntoView()\n        }\n      },\n      {\n        onlyHashChange: this.onlyAHashChange(as),\n      }\n    )\n  }\n\n  urlIsNew(asPath: string): boolean {\n    return this.asPath !== asPath\n  }\n\n  /**\n   * Prefetch page code, you may wait for the data during page rendering.\n   * This feature only works in production!\n   * @param url the href of prefetched page\n   * @param asPath the as path of the prefetched page\n   */\n  async prefetch(\n    url: string,\n    asPath: string = url,\n    options: PrefetchOptions = {}\n  ): Promise<void> {\n    // Prefetch is not supported in development mode because it would trigger on-demand-entries\n    if (process.env.NODE_ENV !== 'production') {\n      return\n    }\n\n    if (typeof window !== 'undefined' && isBot(window.navigator.userAgent)) {\n      // No prefetches for bots that render the link since they are typically navigating\n      // links via the equivalent of a hard navigation and hence never utilize these\n      // prefetches.\n      return\n    }\n    let parsed = parseRelativeUrl(url)\n    const urlPathname = parsed.pathname\n\n    let { pathname, query } = parsed\n    const originalPathname = pathname\n\n    if (process.env.__NEXT_I18N_SUPPORT) {\n      if (options.locale === false) {\n        pathname = normalizeLocalePath!(pathname, this.locales).pathname\n        parsed.pathname = pathname\n        url = formatWithValidation(parsed)\n\n        let parsedAs = parseRelativeUrl(asPath)\n        const localePathResult = normalizeLocalePath!(\n          parsedAs.pathname,\n          this.locales\n        )\n        parsedAs.pathname = localePathResult.pathname\n        options.locale = localePathResult.detectedLocale || this.defaultLocale\n        asPath = formatWithValidation(parsedAs)\n      }\n    }\n\n    const pages = await this.pageLoader.getPageList()\n    let resolvedAs = asPath\n\n    const locale =\n      typeof options.locale !== 'undefined'\n        ? options.locale || undefined\n        : this.locale\n\n    const isMiddlewareMatch = await matchesMiddleware({\n      asPath: asPath,\n      locale: locale,\n      router: this,\n    })\n\n    if (process.env.__NEXT_HAS_REWRITES && asPath.startsWith('/')) {\n      let rewrites: any\n      ;({ __rewrites: rewrites } = await getClientBuildManifest())\n\n      const rewritesResult = resolveRewrites(\n        addBasePath(addLocale(asPath, this.locale), true),\n        pages,\n        rewrites,\n        parsed.query,\n        (p: string) => resolveDynamicRoute(p, pages),\n        this.locales\n      )\n\n      if (rewritesResult.externalDest) {\n        return\n      }\n\n      if (!isMiddlewareMatch) {\n        resolvedAs = removeLocale(\n          removeBasePath(rewritesResult.asPath),\n          this.locale\n        )\n      }\n\n      if (rewritesResult.matchedPage && rewritesResult.resolvedHref) {\n        // if this directly matches a page we need to update the href to\n        // allow the correct page chunk to be loaded\n        pathname = rewritesResult.resolvedHref\n        parsed.pathname = pathname\n\n        if (!isMiddlewareMatch) {\n          url = formatWithValidation(parsed)\n        }\n      }\n    }\n    parsed.pathname = resolveDynamicRoute(parsed.pathname, pages)\n\n    if (isDynamicRoute(parsed.pathname)) {\n      pathname = parsed.pathname\n      parsed.pathname = pathname\n      Object.assign(\n        query,\n        getRouteMatcher(getRouteRegex(parsed.pathname))(\n          parsePath(asPath).pathname\n        ) || {}\n      )\n\n      if (!isMiddlewareMatch) {\n        url = formatWithValidation(parsed)\n      }\n    }\n\n    const data =\n      process.env.__NEXT_MIDDLEWARE_PREFETCH === 'strict'\n        ? null\n        : await withMiddlewareEffects({\n            fetchData: () =>\n              fetchNextData({\n                dataHref: this.pageLoader.getDataHref({\n                  href: formatWithValidation({\n                    pathname: originalPathname,\n                    query,\n                  }),\n                  skipInterpolation: true,\n                  asPath: resolvedAs,\n                  locale,\n                }),\n                hasMiddleware: true,\n                isServerRender: false,\n                parseJSON: true,\n                inflightCache: this.sdc,\n                persistCache: !this.isPreview,\n                isPrefetch: true,\n              }),\n            asPath: asPath,\n            locale: locale,\n            router: this,\n          })\n\n    /**\n     * If there was a rewrite we apply the effects of the rewrite on the\n     * current parameters for the prefetch.\n     */\n    if (data?.effect.type === 'rewrite') {\n      parsed.pathname = data.effect.resolvedHref\n      pathname = data.effect.resolvedHref\n      query = { ...query, ...data.effect.parsedAs.query }\n      resolvedAs = data.effect.parsedAs.pathname\n      url = formatWithValidation(parsed)\n    }\n\n    /**\n     * If there is a redirect to an external destination then we don't have\n     * to prefetch content as it will be unused.\n     */\n    if (data?.effect.type === 'redirect-external') {\n      return\n    }\n\n    const route = removeTrailingSlash(pathname)\n\n    if (await this._bfl(asPath, resolvedAs, options.locale, true)) {\n      this.components[urlPathname] = { __appRouter: true } as any\n    }\n\n    await Promise.all([\n      this.pageLoader._isSsg(route).then((isSsg) => {\n        return isSsg\n          ? fetchNextData({\n              dataHref: data?.json\n                ? data?.dataHref\n                : this.pageLoader.getDataHref({\n                    href: url,\n                    asPath: resolvedAs,\n                    locale: locale,\n                  }),\n              isServerRender: false,\n              parseJSON: true,\n              inflightCache: this.sdc,\n              persistCache: !this.isPreview,\n              isPrefetch: true,\n              unstable_skipClientCache:\n                options.unstable_skipClientCache ||\n                (options.priority &&\n                  !!process.env.__NEXT_OPTIMISTIC_CLIENT_CACHE),\n            })\n              .then(() => false)\n              .catch(() => false)\n          : false\n      }),\n      this.pageLoader[options.priority ? 'loadPage' : 'prefetch'](route),\n    ])\n  }\n\n  async fetchComponent(route: string) {\n    const handleCancelled = getCancelledHandler({ route, router: this })\n\n    try {\n      const componentResult = await this.pageLoader.loadPage(route)\n      handleCancelled()\n\n      return componentResult\n    } catch (err) {\n      handleCancelled()\n      throw err\n    }\n  }\n\n  _getData<T>(fn: () => Promise<T>): Promise<T> {\n    let cancelled = false\n    const cancel = () => {\n      cancelled = true\n    }\n    this.clc = cancel\n    return fn().then((data) => {\n      if (cancel === this.clc) {\n        this.clc = null\n      }\n\n      if (cancelled) {\n        const err: any = new Error('Loading initial props cancelled')\n        err.cancelled = true\n        throw err\n      }\n\n      return data\n    })\n  }\n\n  getInitialProps(\n    Component: ComponentType,\n    ctx: NextPageContext\n  ): Promise<Record<string, any>> {\n    const { Component: App } = this.components['/_app']\n    const AppTree = this._wrapApp(App as AppComponent)\n    ctx.AppTree = AppTree\n    return loadGetInitialProps<AppContextType<Router>>(App, {\n      AppTree,\n      Component,\n      router: this,\n      ctx,\n    })\n  }\n\n  get route(): string {\n    return this.state.route\n  }\n\n  get pathname(): string {\n    return this.state.pathname\n  }\n\n  get query(): ParsedUrlQuery {\n    return this.state.query\n  }\n\n  get asPath(): string {\n    return this.state.asPath\n  }\n\n  get locale(): string | undefined {\n    return this.state.locale\n  }\n\n  get isFallback(): boolean {\n    return this.state.isFallback\n  }\n\n  get isPreview(): boolean {\n    return this.state.isPreview\n  }\n}\n"], "names": ["create<PERSON><PERSON>", "Router", "matchesMiddleware", "buildCancellationError", "Object", "assign", "Error", "cancelled", "options", "matchers", "Promise", "resolve", "router", "page<PERSON><PERSON>der", "getMiddleware", "pathname", "asPathname", "parsePath", "<PERSON><PERSON><PERSON>", "cleanedAs", "has<PERSON>ase<PERSON><PERSON>", "removeBasePath", "asWithBasePathAndLocale", "addBasePath", "addLocale", "locale", "some", "m", "RegExp", "regexp", "test", "strip<PERSON><PERSON>in", "url", "origin", "getLocationOrigin", "startsWith", "substring", "length", "prepareUrlAs", "as", "resolvedHref", "resolvedAs", "resolveHref", "hrefWasAbsolute", "asWasAbsolute", "preparedUrl", "preparedAs", "resolveDynamicRoute", "pages", "cleanPathname", "removeTrailingSlash", "denormalizePagePath", "includes", "page", "isDynamicRoute", "getRouteRegex", "re", "getMiddlewareData", "source", "response", "nextConfig", "basePath", "i18n", "locales", "trailingSlash", "Boolean", "process", "env", "__NEXT_TRAILING_SLASH", "rewriteHeader", "headers", "get", "rewriteTarget", "<PERSON><PERSON><PERSON>", "MATCHED_PATH_HEADER", "__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE", "parsedRewriteTarget", "parseRelativeUrl", "pathnameInfo", "getNextPathnameInfo", "parseData", "fsPathname", "all", "getPageList", "getClientBuildManifest", "then", "__rewrites", "rewrites", "normalizeLocalePath", "parsedSource", "__NEXT_HAS_REWRITES", "undefined", "result", "resolveRewrites", "query", "path", "matchedPage", "parsedAs", "resolvedPathname", "matches", "getRouteMatcher", "type", "src", "formatNextPathnameInfo", "defaultLocale", "buildId", "destination", "hash", "redirectTarget", "newAs", "newUrl", "withMiddlewareEffects", "fetchData", "data", "effect", "dataHref", "json", "text", "cache<PERSON>ey", "manualScrollRestoration", "__NEXT_SCROLL_RESTORATION", "window", "history", "v", "sessionStorage", "setItem", "removeItem", "n", "SSG_DATA_NOT_FOUND", "Symbol", "fetchRetry", "attempts", "fetch", "credentials", "method", "ok", "status", "tryToParseAsJSON", "JSON", "parse", "error", "fetchNextData", "inflightCache", "isPrefetch", "hasMiddleware", "isServerRender", "parseJSON", "persistCache", "isBackground", "unstable_skipClientCache", "href", "URL", "location", "getData", "params", "purpose", "NEXT_DEPLOYMENT_ID", "notFound", "<PERSON><PERSON><PERSON><PERSON>", "NODE_ENV", "catch", "err", "message", "Math", "random", "toString", "slice", "handleHardNavigation", "getCancelledHandler", "route", "cancel", "clc", "handleCancelled", "reload", "back", "forward", "push", "_key", "stringify", "x", "self", "pageXOffset", "y", "pageYOffset", "change", "replace", "_bfl", "skipNavigate", "__NEXT_CLIENT_ROUTER_FILTER_ENABLED", "_bfl_s", "_bfl_d", "<PERSON><PERSON><PERSON><PERSON>", "require", "staticFilterData", "dynamicFilterData", "__routerFilterStatic", "__routerFilterDynamic", "console", "routerFilterSValue", "__NEXT_CLIENT_ROUTER_S_FILTER", "routerFilterDValue", "__NEXT_CLIENT_ROUTER_D_FILTER", "numHashes", "numItems", "errorRate", "import", "matchesBflStatic", "matchesBflDynamic", "pathsToCheck", "curAs", "allowMatchCurrent", "asNoSlash", "asNoSlashLocale", "contains", "normalizedAS", "curAs<PERSON><PERSON>s", "split", "i", "currentPart", "join", "forcedScroll", "isLocalURL", "isQueryUpdating", "_h", "shallow", "shouldResolveHref", "_shouldResolveHref", "nextState", "state", "readyStateChange", "isReady", "isSsr", "prevLocale", "__NEXT_I18N_SUPPORT", "localePathResult", "detectedLocale", "formatWithValidation", "didNavigate", "detectedDomain", "detectDomainLocale", "domainLocales", "isLocaleDomain", "hostname", "domain", "asNoBasePath", "http", "ST", "performance", "mark", "scroll", "routeProps", "_inFlightRoute", "events", "emit", "removeLocale", "localeChange", "onlyAHashChange", "changeState", "scrollToHash", "set", "components", "isError", "parsed", "urlIsNew", "parsedAsPathname", "__appRouter", "isMiddlewareRewrite", "isMiddlewareMatch", "rewritesResult", "p", "externalDest", "routeMatch", "routeRegex", "shouldInterpolate", "interpolatedAs", "interpolateAs", "missingParams", "keys", "groups", "filter", "param", "optional", "warn", "omit", "isErrorRoute", "routeInfo", "getRouteInfo", "isPreview", "<PERSON><PERSON><PERSON><PERSON>", "cleanedParsedPathname", "for<PERSON>ach", "key", "prefixedAs", "rewriteAs", "localeResult", "cur<PERSON><PERSON>eMatch", "component", "Component", "unstable_scriptLoader", "scripts", "concat", "script", "handleClientScriptLoad", "props", "__N_SSG", "__N_SSP", "pageProps", "__N_REDIRECT", "__N_REDIRECT_BASE_PATH", "parsedHref", "__N_PREVIEW", "notFoundRoute", "fetchComponent", "_", "isNotFound", "__NEXT_DATA__", "statusCode", "isValidShallowRoute", "shouldScroll", "resetScroll", "upcomingScrollState", "upcomingRouterState", "canSkipUpdating", "compareRouterStates", "e", "document", "documentElement", "lang", "hashRegex", "getURL", "_shallow", "__N", "handleRouteInfoError", "loadErrorFail", "isAssetError", "styleSheets", "getInitialProps", "gipErr", "routeInfoErr", "requestedRoute", "existingInfo", "cachedRouteInfo", "fetchNextDataParams", "getDataHref", "skipInterpolation", "sbc", "sdc", "resolvedRoute", "isAPIRoute", "res", "mod", "isValidElementType", "wasBailedPrefetch", "shouldFetchData", "_getData", "fetched", "getProperError", "sub", "beforePopState", "cb", "_bps", "oldUrlNoHash", "oldHash", "newUrlNoHash", "newHash", "handleSmoothScroll", "scrollTo", "rawHash", "decodeURIComponent", "idEl", "getElementById", "scrollIntoView", "nameEl", "getElementsByName", "onlyHashChange", "prefetch", "isBot", "navigator", "userAgent", "urlPathname", "originalPathname", "__NEXT_MIDDLEWARE_PREFETCH", "_isSsg", "isSsg", "priority", "__NEXT_OPTIMISTIC_CLIENT_CACHE", "componentResult", "loadPage", "fn", "ctx", "App", "AppTree", "_wrapApp", "loadGetInitialProps", "constructor", "initialProps", "wrapApp", "subscription", "isFirstPopStateEvent", "onPopState", "__NA", "getItem", "initial", "autoExportDynamic", "autoExport", "__NEXT_ROUTER_BASEPATH", "gssp", "gip", "isExperimentalCompile", "appGip", "gsp", "search", "_initialMatchesMiddlewarePromise", "addEventListener", "scrollRestoration", "mitt"], "mappings": "AAAA,4BAA4B;;;;;;;;;;;;;;;;;IAwmBZA,SAAS,EAAA;eAATA;;;eAiDKC;;IA9jBCC,iBAAiB,EAAA;eAAjBA;;;;;qCAjFc;6BAK7B;wBACgC;mEACC;qCACJ;qCACA;+DACnB;uBACkD;2BACpC;kCACE;0EACL;8BACI;4BACF;2BACO;oCACF;2BACT;2BACA;8BACG;gCACE;6BACH;6BACA;6BACA;4BACD;qCACS;wCACG;+BACH;4BACT;uBACL;sBACD;+BACS;oCACK;2BAEC;AAgCpC,SAASC;IACP,OAAOC,OAAOC,MAAM,CAAC,OAAA,cAA4B,CAA5B,IAAIC,MAAM,oBAAV,qBAAA;eAAA;oBAAA;sBAAA;IAA2B,IAAG;QACjDC,WAAW;IACb;AACF;AASO,eAAeL,kBACpBM,OAAkC;IAElC,MAAMC,WAAW,MAAMC,QAAQC,OAAO,CACpCH,QAAQI,MAAM,CAACC,UAAU,CAACC,aAAa;IAEzC,IAAI,CAACL,UAAU,OAAO;IAEtB,MAAM,EAAEM,UAAUC,UAAU,EAAE,GAAGC,CAAAA,GAAAA,WAAAA,SAAS,EAACT,QAAQU,MAAM;IACzD,6FAA6F;IAC7F,MAAMC,YAAYC,CAAAA,GAAAA,aAAAA,WAAW,EAACJ,cAC1BK,CAAAA,GAAAA,gBAAAA,cAAc,EAACL,cACfA;IACJ,MAAMM,0BAA0BC,CAAAA,GAAAA,aAAAA,WAAW,EACzCC,CAAAA,GAAAA,WAAAA,SAAS,EAACL,WAAWX,QAAQiB,MAAM;IAGrC,2EAA2E;IAC3E,uEAAuE;IACvE,OAAOhB,SAASiB,IAAI,CAAC,CAACC,IACpB,IAAIC,OAAOD,EAAEE,MAAM,EAAEC,IAAI,CAACR;AAE9B;AAEA,SAASS,YAAYC,GAAW;IAC9B,MAAMC,SAASC,CAAAA,GAAAA,OAAAA,iBAAiB;IAEhC,OAAOF,IAAIG,UAAU,CAACF,UAAUD,IAAII,SAAS,CAACH,OAAOI,MAAM,IAAIL;AACjE;AAEA,SAASM,aAAa1B,MAAkB,EAAEoB,GAAQ,EAAEO,EAAQ;IAC1D,sDAAsD;IACtD,kDAAkD;IAClD,IAAI,CAACC,cAAcC,WAAW,GAAGC,CAAAA,GAAAA,aAAAA,WAAW,EAAC9B,QAAQoB,KAAK;IAC1D,MAAMC,SAASC,CAAAA,GAAAA,OAAAA,iBAAiB;IAChC,MAAMS,kBAAkBH,aAAaL,UAAU,CAACF;IAChD,MAAMW,gBAAgBH,cAAcA,WAAWN,UAAU,CAACF;IAE1DO,eAAeT,YAAYS;IAC3BC,aAAaA,aAAaV,YAAYU,cAAcA;IAEpD,MAAMI,cAAcF,kBAAkBH,eAAejB,CAAAA,GAAAA,aAAAA,WAAW,EAACiB;IACjE,MAAMM,aAAaP,KACfR,YAAYW,CAAAA,GAAAA,aAAAA,WAAW,EAAC9B,QAAQ2B,OAChCE,cAAcD;IAElB,OAAO;QACLR,KAAKa;QACLN,IAAIK,gBAAgBE,aAAavB,CAAAA,GAAAA,aAAAA,WAAW,EAACuB;IAC/C;AACF;AAEA,SAASC,oBAAoBhC,QAAgB,EAAEiC,KAAe;IAC5D,MAAMC,gBAAgBC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACpC;IAC9D,IAAIkC,kBAAkB,UAAUA,kBAAkB,WAAW;QAC3D,OAAOlC;IACT;IAEA,2CAA2C;IAC3C,IAAI,CAACiC,MAAMI,QAAQ,CAACH,gBAAgB;QAClC,iDAAiD;QACjDD,MAAMtB,IAAI,CAAC,CAAC2B;YACV,IAAIC,CAAAA,GAAAA,WAAAA,cAAc,EAACD,SAASE,CAAAA,GAAAA,YAAAA,aAAa,EAACF,MAAMG,EAAE,CAAC1B,IAAI,CAACmB,gBAAgB;gBACtElC,WAAWsC;gBACX,OAAO;YACT;QACF;IACF;IACA,OAAOH,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACnC;AAC7B;AAEA,SAAS0C,kBACPC,MAAc,EACdC,QAAkB,EAClBnD,OAAkC;IAElC,MAAMoD,aAAa;QACjBC,UAAUrD,QAAQI,MAAM,CAACiD,QAAQ;QACjCC,MAAM;YAAEC,SAASvD,QAAQI,MAAM,CAACmD,OAAO;QAAC;QACxCC,eAAeC,QAAQC,QAAQC,GAAG,CAACC,qBAAqB;IAC1D;IACA,MAAMC,gBAAgBV,SAASW,OAAO,CAACC,GAAG,CAAC;IAE3C,IAAIC,gBACFH,iBAAiBV,SAASW,OAAO,CAACC,GAAG,CAAC;IAExC,MAAME,cAAcd,SAASW,OAAO,CAACC,GAAG,CAACG,WAAAA,mBAAmB;IAE5D,IACED,eACA,CAACD,iBACD,CAACC,YAAYrB,QAAQ,CAAC,2BACtB,CAACqB,YAAYrB,QAAQ,CAAC,cACtB,CAACqB,YAAYrB,QAAQ,CAAC,SACtB;QACA,4DAA4D;QAC5DoB,gBAAgBC;IAClB;IAEA,IAAID,eAAe;QACjB,IACEA,cAAcrC,UAAU,CAAC,QACzB+B,QAAQC,GAAG,CAACQ,6BACZ,aADsD;YAEtD,MAAMC,sBAAsBC,CAAAA,GAAAA,kBAAAA,gBAAgB,EAACL;YAC7C,MAAMM,eAAeC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACH,oBAAoB7D,QAAQ,EAAE;gBACrE6C;gBACAoB,WAAW;YACb;YAEA,IAAIC,aAAa/B,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC4B,aAAa/D,QAAQ;YAC1D,OAAOL,QAAQwE,GAAG,CAAC;gBACjB1E,QAAQI,MAAM,CAACC,UAAU,CAACsE,WAAW;gBACrCC,CAAAA,GAAAA,aAAAA,sBAAsB;aACvB,EAAEC,IAAI,CAAC,CAAA;oBAAC,CAACrC,OAAO,EAAEsC,YAAYC,QAAQ,EAAE,CAAM,GAAA;gBAC7C,IAAIhD,KAAKf,CAAAA,GAAAA,WAAAA,SAAS,EAACsD,aAAa/D,QAAQ,EAAE+D,aAAarD,MAAM;gBAE7D,IACE6B,CAAAA,GAAAA,WAAAA,cAAc,EAACf,OACd,CAAC8B,iBACArB,MAAMI,QAAQ,CACZoC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACnE,CAAAA,GAAAA,gBAAAA,cAAc,EAACkB,KAAK/B,QAAQI,MAAM,CAACmD,OAAO,EAC3DhD,QAAQ,GAEf;oBACA,MAAM0E,eAAeV,CAAAA,GAAAA,qBAAAA,mBAAmB,EACtCF,CAAAA,GAAAA,kBAAAA,gBAAgB,EAACnB,QAAQ3C,QAAQ,EACjC;wBACE6C,YAAYM,QAAQC,GAAG,CAACuB,mBAAmB,GACvCC,2CACA/B;wBACJoB,WAAW;oBACb;oBAGFzC,KAAKhB,CAAAA,GAAAA,aAAAA,WAAW,EAACkE,aAAa1E,QAAQ;oBACtC6D,oBAAoB7D,QAAQ,GAAGwB;gBACjC;gBAEA,IAAI2B,QAAQC,GAAG,CAACuB,mBAAmB,IAAE;;gBAerC,OAAO,IAAI,CAAC1C,MAAMI,QAAQ,CAAC6B,aAAa;oBACtC,MAAMiB,mBAAmBnD,oBAAoBkC,YAAYjC;oBAEzD,IAAIkD,qBAAqBjB,YAAY;wBACnCA,aAAaiB;oBACf;gBACF;gBAEA,MAAM1D,eAAe,CAACQ,MAAMI,QAAQ,CAAC6B,cACjClC,oBACEyC,CAAAA,GAAAA,qBAAAA,mBAAmB,EACjBnE,CAAAA,GAAAA,gBAAAA,cAAc,EAACuD,oBAAoB7D,QAAQ,GAC3CP,QAAQI,MAAM,CAACmD,OAAO,EACtBhD,QAAQ,EACViC,SAEFiC;gBAEJ,IAAI3B,CAAAA,GAAAA,WAAAA,cAAc,EAACd,eAAe;oBAChC,MAAM2D,UAAUC,CAAAA,GAAAA,cAAAA,eAAe,EAAC7C,CAAAA,GAAAA,YAAAA,aAAa,EAACf,eAAeD;oBAC7DnC,OAAOC,MAAM,CAACuE,oBAAoBkB,KAAK,EAAEK,WAAW,CAAC;gBACvD;gBAEA,OAAO;oBACLE,MAAM;oBACNJ,UAAUrB;oBACVpC;gBACF;YACF;QACF;QACA,MAAM8D,MAAMrF,CAAAA,GAAAA,WAAAA,SAAS,EAACyC;QACtB,MAAM3C,WAAWwF,CAAAA,GAAAA,wBAAAA,sBAAsB,EAAC;YACtC,GAAGxB,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACuB,IAAIvF,QAAQ,EAAE;gBAAE6C;gBAAYoB,WAAW;YAAK,EAAE;YACrEwB,eAAehG,QAAQI,MAAM,CAAC4F,aAAa;YAC3CC,SAAS;QACX;QAEA,OAAO/F,QAAQC,OAAO,CAAC;YACrB0F,MAAM;YACNK,aAAc,KAAE3F,WAAWuF,IAAIR,KAAK,GAAGQ,IAAIK,IAAI;QACjD;IACF;IAEA,MAAMC,iBAAiBjD,SAASW,OAAO,CAACC,GAAG,CAAC;IAE5C,IAAIqC,gBAAgB;QAClB,IAAIA,eAAezE,UAAU,CAAC,MAAM;YAClC,MAAMmE,MAAMrF,CAAAA,GAAAA,WAAAA,SAAS,EAAC2F;YACtB,MAAM7F,WAAWwF,CAAAA,GAAAA,wBAAAA,sBAAsB,EAAC;gBACtC,GAAGxB,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACuB,IAAIvF,QAAQ,EAAE;oBAAE6C;oBAAYoB,WAAW;gBAAK,EAAE;gBACrEwB,eAAehG,QAAQI,MAAM,CAAC4F,aAAa;gBAC3CC,SAAS;YACX;YAEA,OAAO/F,QAAQC,OAAO,CAAC;gBACrB0F,MAAM;gBACNQ,OAAQ,KAAE9F,WAAWuF,IAAIR,KAAK,GAAGQ,IAAIK,IAAI;gBACzCG,QAAS,KAAE/F,WAAWuF,IAAIR,KAAK,GAAGQ,IAAIK,IAAI;YAC5C;QACF;QAEA,OAAOjG,QAAQC,OAAO,CAAC;YACrB0F,MAAM;YACNK,aAAaE;QACf;IACF;IAEA,OAAOlG,QAAQC,OAAO,CAAC;QAAE0F,MAAM;IAAgB;AACjD;AAMA,eAAeU,sBACbvG,OAAkC;IAElC,MAAM2F,UAAU,MAAMjG,kBAAkBM;IACxC,IAAI,CAAC2F,WAAW,CAAC3F,QAAQwG,SAAS,EAAE;QAClC,OAAO;IACT;IAEA,MAAMC,OAAO,MAAMzG,QAAQwG,SAAS;IAEpC,MAAME,SAAS,MAAMzD,kBAAkBwD,KAAKE,QAAQ,EAAEF,KAAKtD,QAAQ,EAAEnD;IAErE,OAAO;QACL2G,UAAUF,KAAKE,QAAQ;QACvBC,MAAMH,KAAKG,IAAI;QACfzD,UAAUsD,KAAKtD,QAAQ;QACvB0D,MAAMJ,KAAKI,IAAI;QACfC,UAAUL,KAAKK,QAAQ;QACvBJ;IACF;AACF;AAyEA,MAAMK,0BACJrD,QAAQC,GAAG,CAACqD,yBAAyB,MACrC,OAAOC,WAAW,eAClB,uBAAuBA,OAAOC,OAAO,IACrC,CAAC,CAAE;IACD,IAAI;QACF,IAAIC,IAAI;QACR,wCAAwC;QACxC,OAAOC,eAAeC,OAAO,CAACF,GAAGA,IAAIC,eAAeE,UAAU,CAACH,IAAI;IACrE,EAAE,OAAOI,GAAG,CAAC;AACf;AAEF,MAAMC,qBAAqBC,OAAO;AAElC,SAASC,WACPlG,GAAW,EACXmG,QAAgB,EAChB3H,OAAgD;IAEhD,OAAO4H,MAAMpG,KAAK;QAChB,sEAAsE;QACtE,yDAAyD;QACzD,EAAE;QACF,oEAAoE;QACpE,YAAY;QACZ,mEAAmE;QACnE,EAAE;QACF,iEAAiE;QACjE,sEAAsE;QACtE,8CAA8C;QAC9C,0CAA0C;QAC1CqG,aAAa;QACbC,QAAQ9H,QAAQ8H,MAAM,IAAI;QAC1BhE,SAASlE,OAAOC,MAAM,CAAC,CAAC,GAAGG,QAAQ8D,OAAO,EAAE;YAC1C,iBAAiB;QACnB;IACF,GAAGe,IAAI,CAAC,CAAC1B;QACP,OAAO,CAACA,SAAS4E,EAAE,IAAIJ,WAAW,KAAKxE,SAAS6E,MAAM,IAAI,MACtDN,WAAWlG,KAAKmG,WAAW,GAAG3H,WAC9BmD;IACN;AACF;AAsBA,SAAS8E,iBAAiBpB,IAAY;IACpC,IAAI;QACF,OAAOqB,KAAKC,KAAK,CAACtB;IACpB,EAAE,OAAOuB,OAAO;QACd,OAAO;IACT;AACF;AAEA,SAASC,cAAc,KAUD;IAVC,IAAA,EACrB1B,QAAQ,EACR2B,aAAa,EACbC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,SAAS,EACTC,YAAY,EACZC,YAAY,EACZC,wBAAwB,EACJ,GAVC;IAWrB,MAAM,EAAEC,MAAMhC,QAAQ,EAAE,GAAG,IAAIiC,IAAIpC,UAAUM,OAAO+B,QAAQ,CAACF,IAAI;IACjE,MAAMG,UAAU,CAACC;YAULA;eATVxB,WAAWf,UAAU8B,iBAAiB,IAAI,GAAG;YAC3C3E,SAASlE,OAAOC,MAAM,CACpB,CAAC,GACD0I,aAAa;gBAAEY,SAAS;YAAW,IAAI,CAAC,GACxCZ,cAAcC,gBAAgB;gBAAE,yBAAyB;YAAI,IAAI,CAAC,GAClE9E,QAAQC,GAAG,CAACyF,kBAAkB,GAC1B,4CACA,CAAC;YAEPtB,QAAQoB,CAAAA,iBAAAA,UAAAA,OAAAA,KAAAA,IAAAA,OAAQpB,MAAM,KAAA,OAAdoB,iBAAkB;QAC5B,GACGrE,IAAI,CAAC,CAAC1B;YACL,IAAIA,SAAS4E,EAAE,IAAImB,CAAAA,UAAAA,OAAAA,KAAAA,IAAAA,OAAQpB,MAAM,MAAK,QAAQ;gBAC5C,OAAO;oBAAEnB;oBAAUxD;oBAAU0D,MAAM;oBAAID,MAAM,CAAC;oBAAGE;gBAAS;YAC5D;YAEA,OAAO3D,SAAS0D,IAAI,GAAGhC,IAAI,CAAC,CAACgC;gBAC3B,IAAI,CAAC1D,SAAS4E,EAAE,EAAE;oBAChB;;;;;aAKC,GACD,IACES,iBACA;wBAAC;wBAAK;wBAAK;wBAAK;qBAAI,CAAC5F,QAAQ,CAACO,SAAS6E,MAAM,GAC7C;wBACA,OAAO;4BAAErB;4BAAUxD;4BAAU0D;4BAAMD,MAAM,CAAC;4BAAGE;wBAAS;oBACxD;oBAEA,IAAI3D,SAAS6E,MAAM,KAAK,KAAK;4BACvBC;wBAAJ,IAAA,CAAIA,oBAAAA,iBAAiBpB,KAAAA,KAAAA,OAAAA,KAAAA,IAAjBoB,kBAAwBoB,QAAQ,EAAE;4BACpC,OAAO;gCACL1C;gCACAC,MAAM;oCAAEyC,UAAU7B;gCAAmB;gCACrCrE;gCACA0D;gCACAC;4BACF;wBACF;oBACF;oBAEA,MAAMsB,QAAQ,OAAA,cAAwC,CAAxC,IAAItI,MAAO,gCAAX,qBAAA;+BAAA;oCAAA;sCAAA;oBAAuC;oBAErD;;;;aAIC,GACD,IAAI,CAAC2I,gBAAgB;wBACnBa,CAAAA,GAAAA,aAAAA,cAAc,EAAClB;oBACjB;oBAEA,MAAMA;gBACR;gBAEA,OAAO;oBACLzB;oBACAC,MAAM8B,YAAYT,iBAAiBpB,QAAQ;oBAC3C1D;oBACA0D;oBACAC;gBACF;YACF;QACF,GACCjC,IAAI,CAAC,CAAC4B;YACL,IACE,CAACkC,gBACDjF,QAAQC,GAAG,CAAC4F,QAAQ,gCAAK,gBACzB9C,KAAKtD,QAAQ,CAACW,OAAO,CAACC,GAAG,CAAC,0BAA0B,YACpD;gBACA,OAAOuE,aAAa,CAACxB,SAAS;YAChC;YACA,OAAOL;QACT,GACC+C,KAAK,CAAC,CAACC;YACN,IAAI,CAACZ,0BAA0B;gBAC7B,OAAOP,aAAa,CAACxB,SAAS;YAChC;YACA,IACE,AACA2C,IAAIC,KADK,EACE,KAAK,qBAChB,UAAU;YACVD,IAAIC,OAAO,KAAK,qDAChB,SAAS;YACTD,IAAIC,OAAO,KAAK,eAChB;gBACAJ,CAAAA,GAAAA,aAAAA,cAAc,EAACG;YACjB;YACA,MAAMA;QACR;;IAEJ,+CAA+C;IAC/C,gDAAgD;IAChD,0DAA0D;IAC1D,2DAA2D;IAC3D,IAAIZ,4BAA4BF,cAAc;QAC5C,OAAOM,QAAQ,CAAC,GAAGpE,IAAI,CAAC,CAAC4B;YACvB,IAAIA,KAAKtD,QAAQ,CAACW,OAAO,CAACC,GAAG,CAAC,0BAA0B,YAAY;gBAClE,8CAA8C;gBAC9CuE,aAAa,CAACxB,SAAS,GAAG5G,QAAQC,OAAO,CAACsG;YAC5C;YAEA,OAAOA;QACT;IACF;IAEA,IAAI6B,aAAa,CAACxB,SAAS,KAAK3B,WAAW;QACzC,OAAOmD,aAAa,CAACxB,SAAS;IAChC;IACA,OAAQwB,aAAa,CAACxB,SAAS,GAAGmC,QAChCL,eAAe;QAAEd,QAAQ;IAAO,IAAI,CAAC;AAEzC;AAMO,SAAStI;IACd,OAAOmK,KAAKC,MAAM,GAAGC,QAAQ,CAAC,IAAIC,KAAK,CAAC,GAAG;AAC7C;AAEA,SAASC,qBAAqB,KAM7B;IAN6B,IAAA,EAC5BvI,GAAG,EACHpB,MAAM,EAIP,GAN6B;IAO5B,wDAAwD;IACxD,kDAAkD;IAClD,IAAIoB,QAAQT,CAAAA,GAAAA,aAAAA,WAAW,EAACC,CAAAA,GAAAA,WAAAA,SAAS,EAACZ,OAAOM,MAAM,EAAEN,OAAOa,MAAM,IAAI;QAChE,MAAM,OAAA,cAEL,CAFK,IAAInB,MACP,2DAAwD0B,MAAI,MAAGwH,SAASF,IAAI,GADzE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACA7B,OAAO+B,QAAQ,CAACF,IAAI,GAAGtH;AACzB;AAEA,MAAMwI,sBAAsB,CAAA;QAAC,EAC3BC,KAAK,EACL7J,MAAM,EAIP,GAAA;IACC,IAAIL,YAAY;IAChB,MAAMmK,SAAU9J,OAAO+J,GAAG,GAAG;QAC3BpK,YAAY;IACd;IAEA,MAAMqK,kBAAkB;QACtB,IAAIrK,WAAW;YACb,MAAMqI,QAAa,OAAA,cAElB,CAFkB,IAAItI,MACpB,0CAAuCmK,QAAM,MAD7B,qBAAA;uBAAA;4BAAA;8BAAA;YAEnB;YACA7B,MAAMrI,SAAS,GAAG;YAClB,MAAMqI;QACR;QAEA,IAAI8B,WAAW9J,OAAO+J,GAAG,EAAE;YACzB/J,OAAO+J,GAAG,GAAG;QACf;IACF;IACA,OAAOC;AACT;AAEe,MAAM3K;IA+SnB4K,SAAe;QACbpD,OAAO+B,QAAQ,CAACqB,MAAM;IACxB;IAEA;;GAEC,GACDC,OAAO;QACLrD,OAAOC,OAAO,CAACoD,IAAI;IACrB;IAEA;;GAEC,GACDC,UAAU;QACRtD,OAAOC,OAAO,CAACqD,OAAO;IACxB;IAEA;;;;;GAKC,GACDC,KAAKhJ,GAAQ,EAAEO,EAAQ,EAAE/B,OAA+B,EAAE;QAAjCA,IAAAA,YAAAA,KAAAA,GAAAA,UAA6B,CAAC;QACrD,IAAI0D,QAAQC,GAAG,CAACqD,uBAA2B,EAAF;;QAYzC;;QACE,CAAA,EAAExF,GAAG,EAAEO,EAAE,EAAE,GAAGD,aAAa,IAAI,EAAEN,KAAKO,GAAE;QAC1C,OAAO,IAAI,CAACiJ,MAAM,CAAC,aAAaxJ,KAAKO,IAAI/B;IAC3C;IAEA;;;;;GAKC,GACDiL,QAAQzJ,GAAQ,EAAEO,EAAQ,EAAE/B,OAA+B,EAAE;QAAjCA,IAAAA,YAAAA,KAAAA,GAAAA,UAA6B,CAAC;;QACtD,CAAA,EAAEwB,GAAG,EAAEO,EAAE,EAAE,GAAGD,aAAa,IAAI,EAAEN,KAAKO,GAAE;QAC1C,OAAO,IAAI,CAACiJ,MAAM,CAAC,gBAAgBxJ,KAAKO,IAAI/B;IAC9C;IAEA,MAAMkL,KACJnJ,EAAU,EACVE,UAAmB,EACnBhB,MAAuB,EACvBkK,YAAsB,EACtB;QACA,IAAIzH,QAAQC,GAAG,CAACyH,wBAAqC,WAAF;YACjD,IAAI,CAAC,IAAI,CAACC,MAAM,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;gBAChC,MAAM,EAAEC,WAAW,EAAE,GACnBC,QAAQ;gBAKV,IAAIC;gBACJ,IAAIC;gBAEJ,IAAI;;oBACA,CAAA,EACAC,sBAAsBF,gBAAgB,EACtCG,uBAAuBF,iBAAiB,EACzC,GAAI,MAAM9G,CAAAA,GAAAA,aAAAA,sBAAsB,GAGjC;gBACF,EAAE,OAAO6E,KAAK;oBACZ,8CAA8C;oBAC9C,aAAa;oBACboC,QAAQzD,KAAK,CAACqB;oBACd,IAAI0B,cAAc;wBAChB,OAAO;oBACT;oBACApB,qBAAqB;wBACnBvI,KAAKT,CAAAA,GAAAA,aAAAA,WAAW,EACdC,CAAAA,GAAAA,WAAAA,SAAS,EAACe,IAAId,UAAU,IAAI,CAACA,MAAM,EAAE,IAAI,CAAC+E,aAAa;wBAEzD5F,QAAQ,IAAI;oBACd;oBACA,OAAO,IAAIF,QAAQ,KAAO;gBAC5B;gBAEA,MAAM4L,qBAAqCpI,QAAQC,GAAG,CACnDoI,6BAA6B;gBAEhC,IAAI,CAACN,oBAAoBK,oBAAoB;oBAC3CL,mBAAmBK,qBAAqBA,qBAAqB3G;gBAC/D;gBAEA,MAAM6G,qBAAqCtI,QAAQC,GAAG,CACnDsI,6BAA6B;gBAEhC,IAAI,CAACP,qBAAqBM,oBAAoB;oBAC5CN,oBAAoBM,qBAChBA,qBACA7G;gBACN;gBAEA,IAAIsG,oBAAAA,OAAAA,KAAAA,IAAAA,iBAAkBS,SAAS,EAAE;oBAC/B,IAAI,CAACb,MAAM,GAAG,IAAIE,YAChBE,iBAAiBU,QAAQ,EACzBV,iBAAiBW,SAAS;oBAE5B,IAAI,CAACf,MAAM,CAACgB,MAAM,CAACZ;gBACrB;gBAEA,IAAIC,qBAAAA,OAAAA,KAAAA,IAAAA,kBAAmBQ,SAAS,EAAE;oBAChC,IAAI,CAACZ,MAAM,GAAG,IAAIC,YAChBG,kBAAkBS,QAAQ,EAC1BT,kBAAkBU,SAAS;oBAE7B,IAAI,CAACd,MAAM,CAACe,MAAM,CAACX;gBACrB;YACF;YAEA,IAAIY,mBAAmB;YACvB,IAAIC,oBAAoB;YACxB,MAAMC,eACJ;gBAAC;oBAAEzK;gBAAG;gBAAG;oBAAEA,IAAIE;gBAAW;aAAE;YAE9B,KAAK,MAAM,EAAEF,IAAI0K,KAAK,EAAEC,iBAAiB,EAAE,IAAIF,aAAc;gBAC3D,IAAIC,OAAO;oBACT,MAAME,YAAYjK,CAAAA,GAAAA,qBAAAA,mBAAmB,EACnC,IAAIqG,IAAI0D,OAAO,YAAYlM,QAAQ;oBAErC,MAAMqM,kBAAkB7L,CAAAA,GAAAA,aAAAA,WAAW,EACjCC,CAAAA,GAAAA,WAAAA,SAAS,EAAC2L,WAAW1L,UAAU,IAAI,CAACA,MAAM;oBAG5C,IACEyL,qBACAC,cACEjK,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC,IAAIqG,IAAI,IAAI,CAACrI,MAAM,EAAE,YAAYH,QAAQ,GAC/D;4BAGI,cACA;wBAHJ+L,mBACEA,oBACA,CAAC,CAAA,CAAA,CAAC,eAAA,IAAI,CAACjB,MAAM,KAAA,OAAA,KAAA,IAAX,aAAawB,QAAQ,CAACF,UAAAA,KACxB,CAAC,CAAA,CAAA,CAAC,gBAAA,IAAI,CAACtB,MAAM,KAAA,OAAA,KAAA,IAAX,cAAawB,QAAQ,CAACD,gBAAAA;wBAE1B,KAAK,MAAME,gBAAgB;4BAACH;4BAAWC;yBAAgB,CAAE;4BACvD,sDAAsD;4BACtD,8BAA8B;4BAC9B,MAAMG,aAAaD,aAAaE,KAAK,CAAC;4BACtC,IACE,IAAIC,IAAI,GACR,CAACV,qBAAqBU,IAAIF,WAAWlL,MAAM,GAAG,GAC9CoL,IACA;oCAEmB;gCADnB,MAAMC,cAAcH,WAAWjD,KAAK,CAAC,GAAGmD,GAAGE,IAAI,CAAC;gCAChD,IAAID,eAAAA,CAAAA,CAAe,eAAA,IAAI,CAAC5B,MAAM,KAAA,OAAA,KAAA,IAAX,aAAauB,QAAQ,CAACK,YAAAA,GAAc;oCACrDX,oBAAoB;oCACpB;gCACF;4BACF;wBACF;wBAEA,yDAAyD;wBACzD,oBAAoB;wBACpB,IAAID,oBAAoBC,mBAAmB;4BACzC,IAAIpB,cAAc;gCAChB,OAAO;4BACT;4BACApB,qBAAqB;gCACnBvI,KAAKT,CAAAA,GAAAA,aAAAA,WAAW,EACdC,CAAAA,GAAAA,WAAAA,SAAS,EAACe,IAAId,UAAU,IAAI,CAACA,MAAM,EAAE,IAAI,CAAC+E,aAAa;gCAEzD5F,QAAQ,IAAI;4BACd;4BACA,OAAO,IAAIF,QAAQ,KAAO;wBAC5B;oBACF;gBACF;YACF;QACF;QACA,OAAO;IACT;IAEA,MAAc8K,OACZlD,MAAqB,EACrBtG,GAAW,EACXO,EAAU,EACV/B,OAA0B,EAC1BoN,YAAuC,EACrB;YA8Ob;QA7OL,IAAI,CAACC,CAAAA,GAAAA,YAAAA,UAAU,EAAC7L,MAAM;YACpBuI,qBAAqB;gBAAEvI;gBAAKpB,QAAQ,IAAI;YAAC;YACzC,OAAO;QACT;QACA,sEAAsE;QACtE,yEAAyE;QACzE,2BAA2B;QAC3B,MAAMkN,kBAAmBtN,QAAgBuN,EAAE,KAAK;QAEhD,IAAI,CAACD,mBAAmB,CAACtN,QAAQwN,OAAO,EAAE;YACxC,MAAM,IAAI,CAACtC,IAAI,CAACnJ,IAAIoD,WAAWnF,QAAQiB,MAAM;QAC/C;QAEA,IAAIwM,oBACFH,mBACCtN,QAAgB0N,kBAAkB,IACnCjN,CAAAA,GAAAA,WAAAA,SAAS,EAACe,KAAKjB,QAAQ,KAAKE,CAAAA,GAAAA,WAAAA,SAAS,EAACsB,IAAIxB,QAAQ;QAEpD,MAAMoN,YAAY;YAChB,GAAG,IAAI,CAACC,KAAK;QACf;QAEA,yDAAyD;QACzD,4DAA4D;QAC5D,+BAA+B;QAC/B,MAAMC,mBAAmB,IAAI,CAACC,OAAO,KAAK;QAC1C,IAAI,CAACA,OAAO,GAAG;QACf,MAAMC,QAAQ,IAAI,CAACA,KAAK;QAExB,IAAI,CAACT,iBAAiB;YACpB,IAAI,CAACS,KAAK,GAAG;QACf;QAEA,sDAAsD;QACtD,wDAAwD;QACxD,IAAIT,mBAAmB,IAAI,CAACnD,GAAG,EAAE;YAC/B,OAAO;QACT;QAEA,MAAM6D,aAAaL,UAAU1M,MAAM;QAEnC,IAAIyC,QAAQC,GAAG,CAACsK,mBAAmB,IAAE;;gBAmC5B;QAmDT;QAEA,oDAAoD;QACpD,IAAIa,OAAAA,EAAE,EAAE;YACNC,YAAYC,IAAI,CAAC;QACnB;QAEA,MAAM,EAAExB,UAAU,KAAK,EAAEyB,SAAS,IAAI,EAAE,GAAGjP;QAC3C,MAAMkP,aAAa;YAAE1B;QAAQ;QAE7B,IAAI,IAAI,CAAC2B,cAAc,IAAI,IAAI,CAAChF,GAAG,EAAE;YACnC,IAAI,CAAC4D,OAAO;gBACVtO,OAAO2P,MAAM,CAACC,IAAI,CAChB,oBACA1P,0BACA,IAAI,CAACwP,cAAc,EACnBD;YAEJ;YACA,IAAI,CAAC/E,GAAG;YACR,IAAI,CAACA,GAAG,GAAG;QACb;QAEApI,KAAKhB,CAAAA,GAAAA,aAAAA,WAAW,EACdC,CAAAA,GAAAA,WAAAA,SAAS,EACPJ,CAAAA,GAAAA,aAAAA,WAAW,EAACmB,MAAMlB,CAAAA,GAAAA,gBAAAA,cAAc,EAACkB,MAAMA,IACvC/B,QAAQiB,MAAM,EACd,IAAI,CAAC+E,aAAa;QAGtB,MAAMrF,YAAY2O,CAAAA,GAAAA,cAAAA,YAAY,EAC5B1O,CAAAA,GAAAA,aAAAA,WAAW,EAACmB,MAAMlB,CAAAA,GAAAA,gBAAAA,cAAc,EAACkB,MAAMA,IACvC4L,UAAU1M,MAAM;QAElB,IAAI,CAACkO,cAAc,GAAGpN;QAEtB,MAAMwN,eAAevB,eAAeL,UAAU1M,MAAM;QAEpD,qDAAqD;QACrD,0DAA0D;QAE1D,IAAI,CAACqM,mBAAmB,IAAI,CAACkC,eAAe,CAAC7O,cAAc,CAAC4O,cAAc;YACxE5B,UAAUjN,MAAM,GAAGC;YACnBlB,OAAO2P,MAAM,CAACC,IAAI,CAAC,mBAAmBtN,IAAImN;YAC1C,8DAA8D;YAC9D,IAAI,CAACO,WAAW,CAAC3H,QAAQtG,KAAKO,IAAI;gBAChC,GAAG/B,OAAO;gBACViP,QAAQ;YACV;YACA,IAAIA,QAAQ;gBACV,IAAI,CAACS,YAAY,CAAC/O;YACpB;YACA,IAAI;gBACF,MAAM,IAAI,CAACgP,GAAG,CAAChC,WAAW,IAAI,CAACiC,UAAU,CAACjC,UAAU1D,KAAK,CAAC,EAAE;YAC9D,EAAE,OAAOR,KAAK;gBACZ,IAAIoG,CAAAA,GAAAA,SAAAA,OAAO,EAACpG,QAAQA,IAAI1J,SAAS,EAAE;oBACjCN,OAAO2P,MAAM,CAACC,IAAI,CAAC,oBAAoB5F,KAAK9I,WAAWuO;gBACzD;gBACA,MAAMzF;YACR;YAEAhK,OAAO2P,MAAM,CAACC,IAAI,CAAC,sBAAsBtN,IAAImN;YAC7C,OAAO;QACT;QAEA,IAAIY,SAASzL,CAAAA,GAAAA,kBAAAA,gBAAgB,EAAC7C;QAC9B,IAAI,EAAEjB,QAAQ,EAAE+E,KAAK,EAAE,GAAGwK;QAE1B,yEAAyE;QACzE,2EAA2E;QAC3E,oBAAoB;QACpB,IAAItN,OAAiBuC;QACrB,IAAI;;YACD,CAACvC,OAAO,EAAEsC,YAAYC,QAAQ,EAAE,CAAC,GAAG,MAAM7E,QAAQwE,GAAG,CAAC;gBACrD,IAAI,CAACrE,UAAU,CAACsE,WAAW;gBAC3BC,CAAAA,GAAAA,aAAAA,sBAAsB;gBACtB,IAAI,CAACvE,UAAU,CAACC,aAAa;aAC9B;QACH,EAAE,OAAOmJ,KAAK;YACZ,wEAAwE;YACxE,+BAA+B;YAC/BM,qBAAqB;gBAAEvI,KAAKO;gBAAI3B,QAAQ,IAAI;YAAC;YAC7C,OAAO;QACT;QAEA,uEAAuE;QACvE,8EAA8E;QAC9E,uDAAuD;QACvD,oEAAoE;QACpE,sEAAsE;QACtE,IAAI,CAAC,IAAI,CAAC2P,QAAQ,CAACpP,cAAc,CAAC4O,cAAc;YAC9CzH,SAAS;QACX;QAEA,iEAAiE;QACjE,iDAAiD;QACjD,IAAI7F,aAAaF;QAEjB,6DAA6D;QAC7D,gEAAgE;QAChE,2DAA2D;QAC3DxB,WAAWA,WACPmC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC7B,CAAAA,GAAAA,gBAAAA,cAAc,EAACN,aACnCA;QAEJ,IAAI0J,QAAQvH,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACnC;QAChC,MAAMyP,mBAAmBjO,GAAGJ,UAAU,CAAC,QAAQ0C,CAAAA,GAAAA,kBAAAA,gBAAgB,EAACtC,IAAIxB,QAAQ;QAE5E,0DAA0D;QAC1D,0BAA0B;QAC1B,IAAA,CAAK,4BAAA,IAAI,CAACqP,UAAU,CAACrP,SAAS,KAAA,OAAA,KAAA,IAAzB,0BAAmC0P,WAAW,EAAE;YACnDlG,qBAAqB;gBAAEvI,KAAKO;gBAAI3B,QAAQ,IAAI;YAAC;YAC7C,OAAO,IAAIF,QAAQ,KAAO;QAC5B;QAEA,MAAMgQ,sBAAsB,CAAC,CAC3BF,CAAAA,oBACA/F,UAAU+F,oBACT,CAAA,CAAClN,CAAAA,GAAAA,WAAAA,cAAc,EAACmH,UACf,CAACrE,CAAAA,GAAAA,cAAAA,eAAe,EAAC7C,CAAAA,GAAAA,YAAAA,aAAa,EAACkH,QAAQ+F,iBAAgB,CAAC;QAG5D,0DAA0D;QAC1D,qDAAqD;QACrD,MAAMG,oBACJ,CAACnQ,QAAQwN,OAAO,IACf,MAAM9N,kBAAkB;YACvBgB,QAAQqB;YACRd,QAAQ0M,UAAU1M,MAAM;YACxBb,QAAQ,IAAI;QACd;QAEF,IAAIkN,mBAAmB6C,mBAAmB;YACxC1C,oBAAoB;QACtB;QAEA,IAAIA,qBAAqBlN,aAAa,WAAW;;YAC7CP,QAAgB0N,kBAAkB,GAAG;YAEvC,IAAIhK,QAAQC,GAAG,CAACuB,mBAAmB,YAAInD,GAAGJ,UAAU,CAAC,MAAM;;YA4B3D,OAAO;gBACLmO,OAAOvP,QAAQ,GAAGgC,oBAAoBhC,UAAUiC;gBAEhD,IAAIsN,OAAOvP,QAAQ,KAAKA,UAAU;oBAChCA,WAAWuP,OAAOvP,QAAQ;oBAC1BuP,OAAOvP,QAAQ,GAAGQ,CAAAA,GAAAA,aAAAA,WAAW,EAACR;oBAE9B,IAAI,CAAC4P,mBAAmB;wBACtB3O,MAAM4M,CAAAA,GAAAA,WAAAA,oBAAoB,EAAC0B;oBAC7B;gBACF;YACF;QACF;QAEA,IAAI,CAACzC,CAAAA,GAAAA,YAAAA,UAAU,EAACtL,KAAK;YACnB,IAAI2B,QAAQC,GAAG,CAAC4F,QAAQ,KAAK,WAAc;gBACzC,MAAM,OAAA,cAGL,CAHK,IAAIzJ,MACP,oBAAiB0B,MAAI,gBAAaO,KAAG,8CACnC,uFAFC,qBAAA;2BAAA;gCAAA;kCAAA;gBAGN;YACF;YACAgI,qBAAqB;gBAAEvI,KAAKO;gBAAI3B,QAAQ,IAAI;YAAC;YAC7C,OAAO;QACT;QAEA6B,aAAaqN,CAAAA,GAAAA,cAAAA,YAAY,EAACzO,CAAAA,GAAAA,gBAAAA,cAAc,EAACoB,aAAa0L,UAAU1M,MAAM;QAEtEgJ,QAAQvH,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACnC;QAC5B,IAAIgQ,aAA6B;QAEjC,IAAIzN,CAAAA,GAAAA,WAAAA,cAAc,EAACmH,QAAQ;YACzB,MAAMxE,WAAWpB,CAAAA,GAAAA,kBAAAA,gBAAgB,EAACpC;YAClC,MAAMzB,aAAaiF,SAASlF,QAAQ;YAEpC,MAAMiQ,aAAazN,CAAAA,GAAAA,YAAAA,aAAa,EAACkH;YACjCsG,aAAa3K,CAAAA,GAAAA,cAAAA,eAAe,EAAC4K,YAAYhQ;YACzC,MAAMiQ,oBAAoBxG,UAAUzJ;YACpC,MAAMkQ,iBAAiBD,oBACnBE,CAAAA,GAAAA,eAAAA,aAAa,EAAC1G,OAAOzJ,YAAY8E,SAChC,CAAC;YAEN,IAAI,CAACiL,cAAeE,qBAAqB,CAACC,eAAetL,MAAM,EAAG;gBAChE,MAAMwL,gBAAgBhR,OAAOiR,IAAI,CAACL,WAAWM,MAAM,EAAEC,MAAM,CACzD,CAACC,QAAU,CAAC1L,KAAK,CAAC0L,MAAM,IAAI,CAACR,WAAWM,MAAM,CAACE,MAAM,CAACC,QAAQ;gBAGhE,IAAIL,cAAc/O,MAAM,GAAG,KAAK,CAACsO,mBAAmB;oBAClD,IAAIzM,QAAQC,GAAG,CAAC4F,QAAQ,KAAK,WAAc;wBACzCsC,QAAQqF,IAAI,CACT,KACCT,CAAAA,oBACK,uBACA,6BAA+B,IACrC,iCACC,CAAC,iBAAcG,cAAczD,IAAI,CAC/B,QACA,0BAA4B;oBAEpC;oBAEA,MAAM,OAAA,cAWL,CAXK,IAAIrN,MACP2Q,CAAAA,oBACI,0BAAyBjP,MAAI,sCAAmCoP,cAAczD,IAAI,CACjF,QACA,oCACD,8BAA6B3M,aAAW,8CAA6CyJ,QAAM,KAAG,IACjG,CAAC,iDACCwG,CAAAA,oBACI,8BACA,sBAAqB,CAC1B,IAVC,qBAAA;+BAAA;oCAAA;sCAAA;oBAWN;gBACF;YACF,OAAO,IAAIA,mBAAmB;gBAC5B1O,KAAKqM,CAAAA,GAAAA,WAAAA,oBAAoB,EACvBxO,OAAOC,MAAM,CAAC,CAAC,GAAG4F,UAAU;oBAC1BlF,UAAUmQ,eAAetL,MAAM;oBAC/BE,OAAO6L,CAAAA,GAAAA,MAAAA,IAAI,EAAC7L,OAAOoL,eAAexH,MAAM;gBAC1C;YAEJ,OAAO;gBACL,iEAAiE;gBACjEtJ,OAAOC,MAAM,CAACyF,OAAOiL;YACvB;QACF;QAEA,IAAI,CAACjD,iBAAiB;YACpB7N,OAAO2P,MAAM,CAACC,IAAI,CAAC,oBAAoBtN,IAAImN;QAC7C;QAEA,MAAMkC,eAAe,IAAI,CAAC7Q,QAAQ,KAAK,UAAU,IAAI,CAACA,QAAQ,KAAK;QAEnE,IAAI;gBAsKAqK,qCAAAA,2BACAyG;YAtKF,IAAIA,YAAY,MAAM,IAAI,CAACC,YAAY,CAAC;gBACtCrH;gBACA1J;gBACA+E;gBACAvD;gBACAE;gBACAiN;gBACAjO,QAAQ0M,UAAU1M,MAAM;gBACxBsQ,WAAW5D,UAAU4D,SAAS;gBAC9B/I,eAAe2H;gBACftH,0BAA0B7I,QAAQ6I,wBAAwB;gBAC1DyE,iBAAiBA,mBAAmB,CAAC,IAAI,CAACkE,UAAU;gBACpDtB;YACF;YAEA,IAAI,CAAC5C,mBAAmB,CAACtN,QAAQwN,OAAO,EAAE;gBACxC,MAAM,IAAI,CAACtC,IAAI,CACbnJ,IACA,gBAAgBsP,YAAYA,UAAUpP,UAAU,GAAGkD,WACnDwI,UAAU1M,MAAM;YAEpB;YAEA,IAAI,WAAWoQ,aAAalB,mBAAmB;gBAC7C5P,WAAW8Q,UAAUpH,KAAK,IAAIA;gBAC9BA,QAAQ1J;gBAER,IAAI,CAAC2O,WAAW1B,OAAO,EAAE;oBACvBlI,QAAQ1F,OAAOC,MAAM,CAAC,CAAC,GAAGwR,UAAU/L,KAAK,IAAI,CAAC,GAAGA;gBACnD;gBAEA,MAAMmM,wBAAwB7Q,CAAAA,GAAAA,aAAAA,WAAW,EAACkP,OAAOvP,QAAQ,IACrDM,CAAAA,GAAAA,gBAAAA,cAAc,EAACiP,OAAOvP,QAAQ,IAC9BuP,OAAOvP,QAAQ;gBAEnB,IAAIgQ,cAAchQ,aAAakR,uBAAuB;oBACpD7R,OAAOiR,IAAI,CAACN,YAAYmB,OAAO,CAAC,CAACC;wBAC/B,IAAIpB,cAAcjL,KAAK,CAACqM,IAAI,KAAKpB,UAAU,CAACoB,IAAI,EAAE;4BAChD,OAAOrM,KAAK,CAACqM,IAAI;wBACnB;oBACF;gBACF;gBAEA,IAAI7O,CAAAA,GAAAA,WAAAA,cAAc,EAACvC,WAAW;oBAC5B,MAAMqR,aACJ,CAAC1C,WAAW1B,OAAO,IAAI6D,UAAUpP,UAAU,GACvCoP,UAAUpP,UAAU,GACpBlB,CAAAA,GAAAA,aAAAA,WAAW,EACTC,CAAAA,GAAAA,WAAAA,SAAS,EACP,IAAI+H,IAAIhH,IAAIiH,SAASF,IAAI,EAAEvI,QAAQ,EACnCoN,UAAU1M,MAAM,GAElB;oBAGR,IAAI4Q,YAAYD;oBAEhB,IAAIhR,CAAAA,GAAAA,aAAAA,WAAW,EAACiR,YAAY;wBAC1BA,YAAYhR,CAAAA,GAAAA,gBAAAA,cAAc,EAACgR;oBAC7B;oBAEA,IAAInO,QAAQC,GAAG,CAACsK,mBAAmB,IAAE;;oBAIrC;oBACA,MAAMuC,aAAazN,CAAAA,GAAAA,YAAAA,aAAa,EAACxC;oBACjC,MAAMwR,gBAAgBnM,CAAAA,GAAAA,cAAAA,eAAe,EAAC4K,YACpC,IAAIzH,IAAI8I,WAAW7I,SAASF,IAAI,EAAEvI,QAAQ;oBAG5C,IAAIwR,eAAe;wBACjBnS,OAAOC,MAAM,CAACyF,OAAOyM;oBACvB;gBACF;YACF;YAEA,yDAAyD;YACzD,IAAI,UAAUV,WAAW;gBACvB,IAAIA,UAAUxL,IAAI,KAAK,qBAAqB;oBAC1C,OAAO,IAAI,CAACmF,MAAM,CAAClD,QAAQuJ,UAAU/K,MAAM,EAAE+K,UAAUhL,KAAK,EAAErG;gBAChE,OAAO;oBACL+J,qBAAqB;wBAAEvI,KAAK6P,UAAUnL,WAAW;wBAAE9F,QAAQ,IAAI;oBAAC;oBAChE,OAAO,IAAIF,QAAQ,KAAO;gBAC5B;YACF;YAEA,MAAM8R,YAAiBX,UAAUY,SAAS;YAC1C,IAAID,aAAaA,UAAUE,qBAAqB,EAAE;gBAChD,MAAMC,UAAU,EAAE,CAACC,MAAM,CAACJ,UAAUE,qBAAqB;gBAEzDC,QAAQT,OAAO,CAAC,CAACW;oBACfC,CAAAA,GAAAA,QAAAA,sBAAsB,EAACD,OAAOE,KAAK;gBACrC;YACF;YAEA,uCAAuC;YACvC,IAAKlB,CAAAA,UAAUmB,OAAO,IAAInB,UAAUoB,OAAM,KAAMpB,UAAUkB,KAAK,EAAE;gBAC/D,IACElB,UAAUkB,KAAK,CAACG,SAAS,IACzBrB,UAAUkB,KAAK,CAACG,SAAS,CAACC,YAAY,EACtC;oBACA,0DAA0D;oBAC1D3S,QAAQiB,MAAM,GAAG;oBAEjB,MAAMiF,cAAcmL,UAAUkB,KAAK,CAACG,SAAS,CAACC,YAAY;oBAE1D,oEAAoE;oBACpE,gEAAgE;oBAChE,WAAW;oBACX,IACEzM,YAAYvE,UAAU,CAAC,QACvB0P,UAAUkB,KAAK,CAACG,SAAS,CAACE,sBAAsB,KAAK,OACrD;wBACA,MAAMC,aAAaxO,CAAAA,GAAAA,kBAAAA,gBAAgB,EAAC6B;wBACpC2M,WAAWtS,QAAQ,GAAGgC,oBACpBsQ,WAAWtS,QAAQ,EACnBiC;wBAGF,MAAM,EAAEhB,KAAK8E,MAAM,EAAEvE,IAAIsE,KAAK,EAAE,GAAGvE,aACjC,IAAI,EACJoE,aACAA;wBAEF,OAAO,IAAI,CAAC8E,MAAM,CAAClD,QAAQxB,QAAQD,OAAOrG;oBAC5C;oBACA+J,qBAAqB;wBAAEvI,KAAK0E;wBAAa9F,QAAQ,IAAI;oBAAC;oBACtD,OAAO,IAAIF,QAAQ,KAAO;gBAC5B;gBAEAyN,UAAU4D,SAAS,GAAG,CAAC,CAACF,UAAUkB,KAAK,CAACO,WAAW;gBAEnD,sBAAsB;gBACtB,IAAIzB,UAAUkB,KAAK,CAAClJ,QAAQ,KAAK7B,oBAAoB;oBACnD,IAAIuL;oBAEJ,IAAI;wBACF,MAAM,IAAI,CAACC,cAAc,CAAC;wBAC1BD,gBAAgB;oBAClB,EAAE,OAAOE,GAAG;wBACVF,gBAAgB;oBAClB;oBAEA1B,YAAY,MAAM,IAAI,CAACC,YAAY,CAAC;wBAClCrH,OAAO8I;wBACPxS,UAAUwS;wBACVzN;wBACAvD;wBACAE;wBACAiN,YAAY;4BAAE1B,SAAS;wBAAM;wBAC7BvM,QAAQ0M,UAAU1M,MAAM;wBACxBsQ,WAAW5D,UAAU4D,SAAS;wBAC9B2B,YAAY;oBACd;oBAEA,IAAI,UAAU7B,WAAW;wBACvB,MAAM,OAAA,cAAiD,CAAjD,IAAIvR,MAAO,yCAAX,qBAAA;mCAAA;wCAAA;0CAAA;wBAAgD;oBACxD;gBACF;YACF;YAEA,IACEwN,mBACA,IAAI,CAAC/M,QAAQ,KAAK,aAClBqK,CAAAA,CAAAA,4BAAAA,KAAKuI,aAAa,CAACZ,KAAK,KAAA,OAAA,KAAA,IAAA,CAAxB3H,sCAAAA,0BAA0B8H,SAAS,KAAA,OAAA,KAAA,IAAnC9H,oCAAqCwI,UAAU,MAAK,OAAA,CAAA,CACpD/B,mBAAAA,UAAUkB,KAAK,KAAA,OAAA,KAAA,IAAflB,iBAAiBqB,SAAS,GAC1B;gBACA,yDAAyD;gBACzD,kCAAkC;gBAClCrB,UAAUkB,KAAK,CAACG,SAAS,CAACU,UAAU,GAAG;YACzC;gBAI0C/B;YAF1C,6DAA6D;YAC7D,MAAMgC,sBACJrT,QAAQwN,OAAO,IAAIG,UAAU1D,KAAK,KAAMoH,CAAAA,CAAAA,mBAAAA,UAAUpH,KAAK,KAAA,OAAfoH,mBAAmBpH,KAAI;gBAG/DjK;YADF,MAAMsT,eACJtT,CAAAA,kBAAAA,QAAQiP,MAAM,KAAA,OAAdjP,kBAAmB,CAACsN,mBAAmB,CAAC+F;YAC1C,MAAME,cAAcD,eAAe;gBAAE3I,GAAG;gBAAGG,GAAG;YAAE,IAAI;YACpD,MAAM0I,sBAAsBpG,gBAAAA,OAAAA,eAAgBmG;YAE5C,0CAA0C;YAC1C,MAAME,sBAAsB;gBAC1B,GAAG9F,SAAS;gBACZ1D;gBACA1J;gBACA+E;gBACA5E,QAAQC;gBACR6Q,YAAY;YACd;YAEA,0EAA0E;YAC1E,sEAAsE;YACtE,uEAAuE;YACvE,yEAAyE;YACzE,YAAY;YACZ,IAAIlE,mBAAmB8D,cAAc;oBAmBjCxG,sCAAAA,4BACAyG;gBAnBFA,YAAY,MAAM,IAAI,CAACC,YAAY,CAAC;oBAClCrH,OAAO,IAAI,CAAC1J,QAAQ;oBACpBA,UAAU,IAAI,CAACA,QAAQ;oBACvB+E;oBACAvD;oBACAE;oBACAiN,YAAY;wBAAE1B,SAAS;oBAAM;oBAC7BvM,QAAQ0M,UAAU1M,MAAM;oBACxBsQ,WAAW5D,UAAU4D,SAAS;oBAC9BjE,iBAAiBA,mBAAmB,CAAC,IAAI,CAACkE,UAAU;gBACtD;gBAEA,IAAI,UAAUH,WAAW;oBACvB,MAAM,OAAA,cAA6D,CAA7D,IAAIvR,MAAO,qCAAkC,IAAI,CAACS,QAAQ,GAA1D,qBAAA;+BAAA;oCAAA;sCAAA;oBAA4D;gBACpE;gBAEA,IACE,IAAI,CAACA,QAAQ,KAAK,aAClBqK,CAAAA,CAAAA,6BAAAA,KAAKuI,aAAa,CAACZ,KAAK,KAAA,OAAA,KAAA,IAAA,CAAxB3H,uCAAAA,2BAA0B8H,SAAS,KAAA,OAAA,KAAA,IAAnC9H,qCAAqCwI,UAAU,MAAK,OAAA,CAAA,CACpD/B,oBAAAA,UAAUkB,KAAK,KAAA,OAAA,KAAA,IAAflB,kBAAiBqB,SAAS,GAC1B;oBACA,yDAAyD;oBACzD,kCAAkC;oBAClCrB,UAAUkB,KAAK,CAACG,SAAS,CAACU,UAAU,GAAG;gBACzC;gBAEA,IAAI;oBACF,MAAM,IAAI,CAACzD,GAAG,CAAC8D,qBAAqBpC,WAAWmC;gBACjD,EAAE,OAAO/J,KAAK;oBACZ,IAAIoG,CAAAA,GAAAA,SAAAA,OAAO,EAACpG,QAAQA,IAAI1J,SAAS,EAAE;wBACjCN,OAAO2P,MAAM,CAACC,IAAI,CAAC,oBAAoB5F,KAAK9I,WAAWuO;oBACzD;oBACA,MAAMzF;gBACR;gBAEA,OAAO;YACT;YAEAhK,OAAO2P,MAAM,CAACC,IAAI,CAAC,uBAAuBtN,IAAImN;YAC9C,IAAI,CAACO,WAAW,CAAC3H,QAAQtG,KAAKO,IAAI/B;YAElC,0EAA0E;YAC1E,iBAAiB;YACjB,iDAAiD;YACjD,MAAM0T,kBACJpG,mBACA,CAACkG,uBACD,CAAC3F,oBACD,CAAC0B,gBACDoE,CAAAA,GAAAA,eAAAA,mBAAmB,EAACF,qBAAqB,IAAI,CAAC7F,KAAK;YAErD,IAAI,CAAC8F,iBAAiB;gBACpB,IAAI;oBACF,MAAM,IAAI,CAAC/D,GAAG,CAAC8D,qBAAqBpC,WAAWmC;gBACjD,EAAE,OAAOI,GAAQ;oBACf,IAAIA,EAAE7T,SAAS,EAAEsR,UAAUjJ,KAAK,GAAGiJ,UAAUjJ,KAAK,IAAIwL;yBACjD,MAAMA;gBACb;gBAEA,IAAIvC,UAAUjJ,KAAK,EAAE;oBACnB,IAAI,CAACkF,iBAAiB;wBACpB7N,OAAO2P,MAAM,CAACC,IAAI,CAChB,oBACAgC,UAAUjJ,KAAK,EACfzH,WACAuO;oBAEJ;oBAEA,MAAMmC,UAAUjJ,KAAK;gBACvB;gBAEA,IAAI1E,QAAQC,GAAG,CAACsK,mBAAmB,IAAE;;gBAIrC;gBAEA,IAAI,CAACX,iBAAiB;oBACpB7N,OAAO2P,MAAM,CAACC,IAAI,CAAC,uBAAuBtN,IAAImN;gBAChD;gBAEA,mDAAmD;gBACnD,MAAM8E,YAAY;gBAClB,IAAIV,gBAAgBU,UAAU1S,IAAI,CAACS,KAAK;oBACtC,IAAI,CAAC2N,YAAY,CAAC3N;gBACpB;YACF;YAEA,OAAO;QACT,EAAE,OAAO0H,KAAK;YACZ,IAAIoG,CAAAA,GAAAA,SAAAA,OAAO,EAACpG,QAAQA,IAAI1J,SAAS,EAAE;gBACjC,OAAO;YACT;YACA,MAAM0J;QACR;IACF;IAEAgG,YACE3H,MAAqB,EACrBtG,GAAW,EACXO,EAAU,EACV/B,OAA+B,EACzB;QADNA,IAAAA,YAAAA,KAAAA,GAAAA,UAA6B,CAAC;QAE9B,IAAI0D,QAAQC,GAAG,CAAC4F,QAAQ,KAAK,WAAc;YACzC,IAAI,OAAOtC,OAAOC,OAAO,KAAK,aAAa;gBACzC2E,QAAQzD,KAAK,CAAE;gBACf;YACF;YAEA,IAAI,OAAOnB,OAAOC,OAAO,CAACY,OAAO,KAAK,aAAa;gBACjD+D,QAAQzD,KAAK,CAAE,6BAA0BN,SAAO;gBAChD;YACF;QACF;QAEA,IAAIA,WAAW,eAAemM,CAAAA,GAAAA,OAAAA,MAAM,QAAOlS,IAAI;YAC7C,IAAI,CAACmS,QAAQ,GAAGlU,QAAQwN,OAAO;YAC/BvG,OAAOC,OAAO,CAACY,OAAO,CACpB;gBACEtG;gBACAO;gBACA/B;gBACAmU,KAAK;gBACLxC,KAAM,IAAI,CAAClH,IAAI,GAAG3C,WAAW,cAAc,IAAI,CAAC2C,IAAI,GAAGjL;YACzD,GACA,AACA,qFAAqF,KADK;YAE1F,kEAAkE;YAClE,IACAuC;QAEJ;IACF;IAEA,MAAMqS,qBACJ3K,GAAgD,EAChDlJ,QAAgB,EAChB+E,KAAqB,EACrBvD,EAAU,EACVmN,UAA2B,EAC3BmF,aAAuB,EACY;QACnC,IAAI5K,IAAI1J,SAAS,EAAE;YACjB,gCAAgC;YAChC,MAAM0J;QACR;QAEA,IAAI6K,CAAAA,GAAAA,aAAAA,YAAY,EAAC7K,QAAQ4K,eAAe;YACtC5U,OAAO2P,MAAM,CAACC,IAAI,CAAC,oBAAoB5F,KAAK1H,IAAImN;YAEhD,iEAAiE;YACjE,0BAA0B;YAC1B,0CAA0C;YAC1C,4CAA4C;YAE5C,+DAA+D;YAC/DnF,qBAAqB;gBACnBvI,KAAKO;gBACL3B,QAAQ,IAAI;YACd;YAEA,kEAAkE;YAClE,8DAA8D;YAC9D,MAAMT;QACR;QAEAkM,QAAQzD,KAAK,CAACqB;QAEd,IAAI;YACF,IAAI8I;YACJ,MAAM,EAAE1P,MAAMoP,SAAS,EAAEsC,WAAW,EAAE,GACpC,MAAM,IAAI,CAACvB,cAAc,CAAC;YAE5B,MAAM3B,YAAsC;gBAC1CkB;gBACAN;gBACAsC;gBACA9K;gBACArB,OAAOqB;YACT;YAEA,IAAI,CAAC4H,UAAUkB,KAAK,EAAE;gBACpB,IAAI;oBACFlB,UAAUkB,KAAK,GAAG,MAAM,IAAI,CAACiC,eAAe,CAACvC,WAAW;wBACtDxI;wBACAlJ;wBACA+E;oBACF;gBACF,EAAE,OAAOmP,QAAQ;oBACf5I,QAAQzD,KAAK,CAAC,2CAA2CqM;oBACzDpD,UAAUkB,KAAK,GAAG,CAAC;gBACrB;YACF;YAEA,OAAOlB;QACT,EAAE,OAAOqD,cAAc;YACrB,OAAO,IAAI,CAACN,oBAAoB,CAC9BvE,CAAAA,GAAAA,SAAAA,OAAO,EAAC6E,gBAAgBA,eAAe,OAAA,cAA4B,CAA5B,IAAI5U,MAAM4U,eAAe,KAAzB,qBAAA;uBAAA;4BAAA;8BAAA;YAA2B,IAClEnU,UACA+E,OACAvD,IACAmN,YACA;QAEJ;IACF;IAEA,MAAMoC,aAAa,KA4BlB,EAAE;QA5BgB,IAAA,EACjBrH,OAAO0K,cAAc,EACrBpU,QAAQ,EACR+E,KAAK,EACLvD,EAAE,EACFE,UAAU,EACViN,UAAU,EACVjO,MAAM,EACNuH,aAAa,EACb+I,SAAS,EACT1I,wBAAwB,EACxByE,eAAe,EACf4C,mBAAmB,EACnBgD,UAAU,EAeX,GA5BkB;QA6BjB;;;;;KAKC,GACD,IAAIjJ,QAAQ0K;QAEZ,IAAI;gBA6EAlO,cACAA,eAKEA,eAyDsBA;YA3I1B,IAAImO,eAA6C,IAAI,CAAChF,UAAU,CAAC3F,MAAM;YACvE,IAAIiF,WAAW1B,OAAO,IAAIoH,gBAAgB,IAAI,CAAC3K,KAAK,KAAKA,OAAO;gBAC9D,OAAO2K;YACT;YAEA,MAAMxK,kBAAkBJ,oBAAoB;gBAAEC;gBAAO7J,QAAQ,IAAI;YAAC;YAElE,IAAIoI,eAAe;gBACjBoM,eAAezP;YACjB;YAEA,IAAI0P,kBACFD,gBACA,CAAE,CAAA,aAAaA,YAAW,KAC1BlR,QAAQC,GAAG,CAAC4F,QAAQ,gCAAK,gBACrBqL,uCACAzP;YAEN,MAAMyD,eAAe0E;YACrB,MAAMwH,sBAA2C;gBAC/CnO,UAAU,IAAI,CAACtG,UAAU,CAAC0U,WAAW,CAAC;oBACpCjM,MAAMsF,CAAAA,GAAAA,WAAAA,oBAAoB,EAAC;wBAAE7N;wBAAU+E;oBAAM;oBAC7C0P,mBAAmB;oBACnBtU,QAAQwS,aAAa,SAASjR;oBAC9BhB;gBACF;gBACAuH,eAAe;gBACfC,gBAAgB,IAAI,CAACsF,KAAK;gBAC1BrF,WAAW;gBACXJ,eAAeM,eAAe,IAAI,CAACqM,GAAG,GAAG,IAAI,CAACC,GAAG;gBACjDvM,cAAc,CAAC4I;gBACfhJ,YAAY;gBACZM;gBACAD;YACF;YAEA,IAAInC,OAKF6G,mBAAmB,CAAC4C,sBAChB,OACA,MAAM3J,sBAAsB;gBAC1BC,WAAW,IAAM6B,cAAcyM;gBAC/BpU,QAAQwS,aAAa,SAASjR;gBAC9BhB,QAAQA;gBACRb,QAAQ,IAAI;YACd,GAAGoJ,KAAK,CAAC,CAACC;gBACR,4CAA4C;gBAC5C,oDAAoD;gBACpD,oDAAoD;gBACpD,YAAY;gBACZ,IAAI6D,iBAAiB;oBACnB,OAAO;gBACT;gBACA,MAAM7D;YACR;YAEN,wDAAwD;YACxD,UAAU;YACV,IAAIhD,QAASlG,CAAAA,aAAa,aAAaA,aAAa,MAAK,GAAI;gBAC3DkG,KAAKC,MAAM,GAAGvB;YAChB;YAEA,IAAImI,iBAAiB;gBACnB,IAAI,CAAC7G,MAAM;oBACTA,OAAO;wBAAEG,MAAMgE,KAAKuI,aAAa,CAACZ,KAAK;oBAAC;gBAC1C,OAAO;oBACL9L,KAAKG,IAAI,GAAGgE,KAAKuI,aAAa,CAACZ,KAAK;gBACtC;YACF;YAEAnI;YAEA,IACE3D,CAAAA,QAAAA,OAAAA,KAAAA,IAAAA,CAAAA,eAAAA,KAAMC,MAAM,KAAA,OAAA,KAAA,IAAZD,aAAcZ,IAAI,MAAK,uBACvBY,CAAAA,QAAAA,OAAAA,KAAAA,IAAAA,CAAAA,gBAAAA,KAAMC,MAAM,KAAA,OAAA,KAAA,IAAZD,cAAcZ,IAAI,MAAK,qBACvB;gBACA,OAAOY,KAAKC,MAAM;YACpB;YAEA,IAAID,CAAAA,QAAAA,OAAAA,KAAAA,IAAAA,CAAAA,gBAAAA,KAAMC,MAAM,KAAA,OAAA,KAAA,IAAZD,cAAcZ,IAAI,MAAK,WAAW;gBACpC,MAAMsP,gBAAgBzS,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC+D,KAAKC,MAAM,CAAC1E,YAAY;gBAClE,MAAMQ,QAAQ,MAAM,IAAI,CAACnC,UAAU,CAACsE,WAAW;gBAE/C,4DAA4D;gBAC5D,yDAAyD;gBACzD,4DAA4D;gBAC5D,2CAA2C;gBAC3C,IAAI,CAAC2I,mBAAmB9K,MAAMI,QAAQ,CAACuS,gBAAgB;oBACrDlL,QAAQkL;oBACR5U,WAAWkG,KAAKC,MAAM,CAAC1E,YAAY;oBACnCsD,QAAQ;wBAAE,GAAGA,KAAK;wBAAE,GAAGmB,KAAKC,MAAM,CAACjB,QAAQ,CAACH,KAAK;oBAAC;oBAClDrD,aAAapB,CAAAA,GAAAA,gBAAAA,cAAc,EACzBmE,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACyB,KAAKC,MAAM,CAACjB,QAAQ,CAAClF,QAAQ,EAAE,IAAI,CAACgD,OAAO,EAC5DhD,QAAQ;oBAGb,kDAAkD;oBAClDqU,eAAe,IAAI,CAAChF,UAAU,CAAC3F,MAAM;oBACrC,IACEiF,WAAW1B,OAAO,IAClBoH,gBACA,IAAI,CAAC3K,KAAK,KAAKA,SACf,CAACzB,eACD;wBACA,4DAA4D;wBAC5D,6DAA6D;wBAC7D,gEAAgE;wBAChE,OAAO;4BAAE,GAAGoM,YAAY;4BAAE3K;wBAAM;oBAClC;gBACF;YACF;YAEA,IAAImL,CAAAA,GAAAA,YAAAA,UAAU,EAACnL,QAAQ;gBACrBF,qBAAqB;oBAAEvI,KAAKO;oBAAI3B,QAAQ,IAAI;gBAAC;gBAC7C,OAAO,IAAIF,QAAe,KAAO;YACnC;YAEA,MAAMmR,YACJwD,mBACC,MAAM,IAAI,CAAC7B,cAAc,CAAC/I,OAAOpF,IAAI,CACpC,CAACwQ,MAAS,CAAA;oBACRpD,WAAWoD,IAAIxS,IAAI;oBACnB0R,aAAac,IAAId,WAAW;oBAC5B/B,SAAS6C,IAAIC,GAAG,CAAC9C,OAAO;oBACxBC,SAAS4C,IAAIC,GAAG,CAAC7C,OAAO;gBAC1B,CAAA;YAGJ,IAAI/O,QAAQC,GAAG,CAAC4F,QAAQ,KAAK,WAAc;gBACzC,MAAM,EAAEgM,kBAAkB,EAAE,GAAG/J,QAAQ;gBACvC,IAAI,CAAC+J,mBAAmBlE,UAAUY,SAAS,GAAG;oBAC5C,MAAM,OAAA,cAEL,CAFK,IAAInS,MACP,2DAAwDS,WAAS,MAD9D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YACA,MAAMiV,oBAAoB/O,QAAAA,OAAAA,KAAAA,IAAAA,CAAAA,iBAAAA,KAAMtD,QAAQ,KAAA,OAAA,KAAA,IAAdsD,eAAgB3C,OAAO,CAACC,GAAG,CAAC;YAEtD,MAAM0R,kBAAkBpE,UAAUmB,OAAO,IAAInB,UAAUoB,OAAO;YAE9D,yDAAyD;YACzD,4CAA4C;YAC5C,IAAI+C,qBAAAA,CAAqB/O,QAAAA,OAAAA,KAAAA,IAAAA,KAAME,QAAQ,GAAE;gBACvC,OAAO,IAAI,CAACuO,GAAG,CAACzO,KAAKE,QAAQ,CAAC;YAChC;YAEA,MAAM,EAAE4L,KAAK,EAAEzL,QAAQ,EAAE,GAAG,MAAM,IAAI,CAAC4O,QAAQ,CAAC;gBAC9C,IAAID,iBAAiB;oBACnB,IAAIhP,CAAAA,QAAAA,OAAAA,KAAAA,IAAAA,KAAMG,IAAI,KAAI,CAAC4O,mBAAmB;wBACpC,OAAO;4BAAE1O,UAAUL,KAAKK,QAAQ;4BAAEyL,OAAO9L,KAAKG,IAAI;wBAAC;oBACrD;oBAEA,MAAMD,WAAWF,CAAAA,QAAAA,OAAAA,KAAAA,IAAAA,KAAME,QAAQ,IAC3BF,KAAKE,QAAQ,GACb,IAAI,CAACtG,UAAU,CAAC0U,WAAW,CAAC;wBAC1BjM,MAAMsF,CAAAA,GAAAA,WAAAA,oBAAoB,EAAC;4BAAE7N;4BAAU+E;wBAAM;wBAC7C5E,QAAQuB;wBACRhB;oBACF;oBAEJ,MAAM0U,UAAU,MAAMtN,cAAc;wBAClC1B;wBACA8B,gBAAgB,IAAI,CAACsF,KAAK;wBAC1BrF,WAAW;wBACXJ,eAAekN,oBAAoB,CAAC,IAAI,IAAI,CAACN,GAAG;wBAChDvM,cAAc,CAAC4I;wBACfhJ,YAAY;wBACZM;oBACF;oBAEA,OAAO;wBACL/B,UAAU6O,QAAQ7O,QAAQ;wBAC1ByL,OAAOoD,QAAQ/O,IAAI,IAAI,CAAC;oBAC1B;gBACF;gBAEA,OAAO;oBACL9C,SAAS,CAAC;oBACVyO,OAAO,MAAM,IAAI,CAACiC,eAAe,CAC/BnD,UAAUY,SAAS,EACnB,AACA,qDADqD;wBAEnD1R;wBACA+E;wBACA5E,QAAQqB;wBACRd;wBACAsC,SAAS,IAAI,CAACA,OAAO;wBACrByC,eAAe,IAAI,CAACA,aAAa;oBACnC;gBAEJ;YACF;YAEA,mDAAmD;YACnD,6CAA6C;YAC7C,uCAAuC;YACvC,IAAIqL,UAAUoB,OAAO,IAAIqC,oBAAoBnO,QAAQ,IAAIG,UAAU;gBACjE,OAAO,IAAI,CAACoO,GAAG,CAACpO,SAAS;YAC3B;YAEA,+CAA+C;YAC/C,6DAA6D;YAC7D,IACE,CAAC,IAAI,CAACyK,SAAS,IACfF,UAAUmB,OAAO,IACjB9O,QAAQC,GAAG,CAAC4F,QAAQ,gCAAK,iBACzB,CAAC+D,iBACD;;YAQF;YAEAiF,MAAMG,SAAS,GAAG9S,OAAOC,MAAM,CAAC,CAAC,GAAG0S,MAAMG,SAAS;YACnDrB,UAAUkB,KAAK,GAAGA;YAClBlB,UAAUpH,KAAK,GAAGA;YAClBoH,UAAU/L,KAAK,GAAGA;YAClB+L,UAAUpP,UAAU,GAAGA;YACvB,IAAI,CAAC2N,UAAU,CAAC3F,MAAM,GAAGoH;YAEzB,OAAOA;QACT,EAAE,OAAO5H,KAAK;YACZ,OAAO,IAAI,CAAC2K,oBAAoB,CAC9BwB,CAAAA,GAAAA,SAAAA,cAAc,EAACnM,MACflJ,UACA+E,OACAvD,IACAmN;QAEJ;IACF;IAEQS,IACN/B,KAAwB,EACxBnH,IAAsB,EACtB8M,WAA4C,EAC7B;QACf,IAAI,CAAC3F,KAAK,GAAGA;QAEb,OAAO,IAAI,CAACiI,GAAG,CACbpP,MACA,IAAI,CAACmJ,UAAU,CAAC,QAAQ,CAACqC,SAAS,EAClCsB;IAEJ;IAEA;;;GAGC,GACDuC,eAAeC,EAA0B,EAAE;QACzC,IAAI,CAACC,IAAI,GAAGD;IACd;IAEAvG,gBAAgBzN,EAAU,EAAW;QACnC,IAAI,CAAC,IAAI,CAACrB,MAAM,EAAE,OAAO;QACzB,MAAM,CAACuV,cAAcC,QAAQ,GAAG,IAAI,CAACxV,MAAM,CAACsM,KAAK,CAAC,KAAK;QACvD,MAAM,CAACmJ,cAAcC,QAAQ,GAAGrU,GAAGiL,KAAK,CAAC,KAAK;QAE9C,yEAAyE;QACzE,IAAIoJ,WAAWH,iBAAiBE,gBAAgBD,YAAYE,SAAS;YACnE,OAAO;QACT;QAEA,0DAA0D;QAC1D,IAAIH,iBAAiBE,cAAc;YACjC,OAAO;QACT;QAEA,yDAAyD;QACzD,uDAAuD;QACvD,2DAA2D;QAC3D,mCAAmC;QACnC,OAAOD,YAAYE;IACrB;IAEA1G,aAAa3N,EAAU,EAAQ;QAC7B,MAAM,GAAGoE,OAAO,EAAE,CAAC,GAAGpE,GAAGiL,KAAK,CAAC,KAAK;QAEpCqJ,CAAAA,GAAAA,oBAAAA,kBAAkB,EAChB;YACE,gEAAgE;YAChE,qBAAqB;YACrB,IAAIlQ,SAAS,MAAMA,SAAS,OAAO;gBACjCc,OAAOqP,QAAQ,CAAC,GAAG;gBACnB;YACF;YAEA,8CAA8C;YAC9C,MAAMC,UAAUC,mBAAmBrQ;YACnC,+CAA+C;YAC/C,MAAMsQ,OAAO5C,SAAS6C,cAAc,CAACH;YACrC,IAAIE,MAAM;gBACRA,KAAKE,cAAc;gBACnB;YACF;YACA,kEAAkE;YAClE,qBAAqB;YACrB,MAAMC,SAAS/C,SAASgD,iBAAiB,CAACN,QAAQ,CAAC,EAAE;YACrD,IAAIK,QAAQ;gBACVA,OAAOD,cAAc;YACvB;QACF,GACA;YACEG,gBAAgB,IAAI,CAACtH,eAAe,CAACzN;QACvC;IAEJ;IAEAgO,SAASrP,MAAc,EAAW;QAChC,OAAO,IAAI,CAACA,MAAM,KAAKA;IACzB;IAEA;;;;;GAKC,GACD,MAAMqW,SACJvV,GAAW,EACXd,MAAoB,EACpBV,OAA6B,EACd;QAFfU,IAAAA,WAAAA,KAAAA,GAAAA,SAAiBc;QACjBxB,IAAAA,YAAAA,KAAAA,GAAAA,UAA2B,CAAC;QAE5B,2FAA2F;QAC3F,IAAI0D,QAAQC,GAAG,CAAC4F,QAAQ,KAAK,WAAc;YACzC;QACF;;QAQA,IAAIuG,SAASzL,IAAAA,kCAAgB,EAAC7C;QAC9B,MAAM2V,cAAcrH,OAAOvP,QAAQ;QAEnC,IAAI,AAAEA,QAAQ,EAAE+E,KAAK,EAAE,GAAGwK;QAC1B,MAAMsH,mBAAmB7W;QAmBzB,MAAMiC,QAAQ,MAAM,IAAI,CAACnC,UAAU,CAACsE,WAAW;QAC/C,IAAI1C,aAAavB;QAEjB,MAAMO,SACJ,OAAOjB,QAAQiB,MAAM,KAAK,cACtBjB,QAAQiB,MAAM,IAAIkE,YAClB,IAAI,CAAClE,MAAM;QAEjB,MAAMkP,oBAAoB,MAAMzQ,kBAAkB;QA0DlD,MAAM+G,OACJ/C,QAAQC,GAAG,CAAC0T,0BAA0B,KAAK,WACvC,OACA,MAAM9Q,sBAAsB;QA4ClC,MAAM0D,QAAQvH,IAAAA,wCAAmB,EAACnC;IAiCpC;IAEA,MAAMyS,eAAe/I,KAAa,EAAE;QAClC,MAAMG,kBAAkBJ,oBAAoB;YAAEC;YAAO7J,QAAQ,IAAI;QAAC;QAElE,IAAI;YACF,MAAMsX,kBAAkB,MAAM,IAAI,CAACrX,UAAU,CAACsX,QAAQ,CAAC1N;YACvDG;YAEA,OAAOsN;QACT,EAAE,OAAOjO,KAAK;YACZW;YACA,MAAMX;QACR;IACF;IAEAiM,SAAYkC,EAAoB,EAAc;QAC5C,IAAI7X,YAAY;QAChB,MAAMmK,SAAS;YACbnK,YAAY;QACd;QACA,IAAI,CAACoK,GAAG,GAAGD;QACX,OAAO0N,KAAK/S,IAAI,CAAC,CAAC4B;YAChB,IAAIyD,WAAW,IAAI,CAACC,GAAG,EAAE;gBACvB,IAAI,CAACA,GAAG,GAAG;YACb;YAEA,IAAIpK,WAAW;gBACb,MAAM0J,MAAW,OAAA,cAA4C,CAA5C,IAAI3J,MAAM,oCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA2C;gBAC5D2J,IAAI1J,SAAS,GAAG;gBAChB,MAAM0J;YACR;YAEA,OAAOhD;QACT;IACF;IAEA+N,gBACEvC,SAAwB,EACxB4F,GAAoB,EACU;QAC9B,MAAM,EAAE5F,WAAW6F,GAAG,EAAE,GAAG,IAAI,CAAClI,UAAU,CAAC,QAAQ;QACnD,MAAMmI,UAAU,IAAI,CAACC,QAAQ,CAACF;QAC9BD,IAAIE,OAAO,GAAGA;QACd,OAAOE,CAAAA,GAAAA,OAAAA,mBAAmB,EAAyBH,KAAK;YACtDC;YACA9F;YACA7R,QAAQ,IAAI;YACZyX;QACF;IACF;IAEA,IAAI5N,QAAgB;QAClB,OAAO,IAAI,CAAC2D,KAAK,CAAC3D,KAAK;IACzB;IAEA,IAAI1J,WAAmB;QACrB,OAAO,IAAI,CAACqN,KAAK,CAACrN,QAAQ;IAC5B;IAEA,IAAI+E,QAAwB;QAC1B,OAAO,IAAI,CAACsI,KAAK,CAACtI,KAAK;IACzB;IAEA,IAAI5E,SAAiB;QACnB,OAAO,IAAI,CAACkN,KAAK,CAAClN,MAAM;IAC1B;IAEA,IAAIO,SAA6B;QAC/B,OAAO,IAAI,CAAC2M,KAAK,CAAC3M,MAAM;IAC1B;IAEA,IAAIuQ,aAAsB;QACxB,OAAO,IAAI,CAAC5D,KAAK,CAAC4D,UAAU;IAC9B;IAEA,IAAID,YAAqB;QACvB,OAAO,IAAI,CAAC3D,KAAK,CAAC2D,SAAS;IAC7B;IAh1DA2G,YACE3X,QAAgB,EAChB+E,KAAqB,EACrBvD,EAAU,EACV,EACEoW,YAAY,EACZ9X,UAAU,EACVyX,GAAG,EACHM,OAAO,EACPnG,SAAS,EACTxI,GAAG,EACH4O,YAAY,EACZ7G,UAAU,EACVvQ,MAAM,EACNsC,OAAO,EACPyC,aAAa,EACbwI,aAAa,EACb+C,SAAS,EAeV,CACD;QAzEF,yCAAyC;aACzC2D,GAAAA,GAAqB,CAAC;QACtB,0CAA0C;aAC1CD,GAAAA,GAAqB,CAAC;aAgBtBqD,oBAAAA,GAAuB;aAiBf7N,IAAAA,GAAejL;aA+JvB+Y,UAAAA,GAAa,CAAC3E;YACZ,MAAM,EAAE0E,oBAAoB,EAAE,GAAG,IAAI;YACrC,IAAI,CAACA,oBAAoB,GAAG;YAE5B,MAAM1K,QAAQgG,EAAEhG,KAAK;YAErB,IAAI,CAACA,OAAO;gBACV,6CAA6C;gBAC7C,sDAAsD;gBACtD,kCAAkC;gBAClC,EAAE;gBACF,oEAAoE;gBACpE,4BAA4B;gBAC5B,4DAA4D;gBAC5D,kFAAkF;gBAClF,gDAAgD;gBAChD,MAAM,EAAErN,QAAQ,EAAE+E,KAAK,EAAE,GAAG,IAAI;gBAChC,IAAI,CAACmK,WAAW,CACd,gBACArB,CAAAA,GAAAA,WAAAA,oBAAoB,EAAC;oBAAE7N,UAAUQ,CAAAA,GAAAA,aAAAA,WAAW,EAACR;oBAAW+E;gBAAM,IAC9D2O,CAAAA,GAAAA,OAAAA,MAAM;gBAER;YACF;YAEA,kFAAkF;YAClF,IAAIrG,MAAM4K,IAAI,EAAE;gBACdvR,OAAO+B,QAAQ,CAACqB,MAAM;gBACtB;YACF;YAEA,IAAI,CAACuD,MAAMuG,GAAG,EAAE;gBACd;YACF;YAEA,yDAAyD;YACzD,IACEmE,wBACA,IAAI,CAACrX,MAAM,KAAK2M,MAAM5N,OAAO,CAACiB,MAAM,IACpC2M,MAAM7L,EAAE,KAAK,IAAI,CAACrB,MAAM,EACxB;gBACA;YACF;YAEA,IAAI0M;YACJ,MAAM,EAAE5L,GAAG,EAAEO,EAAE,EAAE/B,OAAO,EAAE2R,GAAG,EAAE,GAAG/D;YAClC,IAAIlK,QAAQC,GAAG,CAACqD,uBAA2B,EAAF;;YAoBzC;YACA,IAAI,CAACyD,IAAI,GAAGkH;YAEZ,MAAM,EAAEpR,QAAQ,EAAE,GAAG8D,CAAAA,GAAAA,kBAAAA,gBAAgB,EAAC7C;YAEtC,gDAAgD;YAChD,yDAAyD;YACzD,IACE,IAAI,CAACuM,KAAK,IACVhM,OAAOhB,CAAAA,GAAAA,aAAAA,WAAW,EAAC,IAAI,CAACL,MAAM,KAC9BH,aAAaQ,CAAAA,GAAAA,aAAAA,WAAW,EAAC,IAAI,CAACR,QAAQ,GACtC;gBACA;YACF;YAEA,uDAAuD;YACvD,wDAAwD;YACxD,IAAI,IAAI,CAACyV,IAAI,IAAI,CAAC,IAAI,CAACA,IAAI,CAACpI,QAAQ;gBAClC;YACF;YAEA,IAAI,CAAC5C,MAAM,CACT,gBACAxJ,KACAO,IACAnC,OAAOC,MAAM,CAA2C,CAAC,GAAGG,SAAS;gBACnEwN,SAASxN,QAAQwN,OAAO,IAAI,IAAI,CAAC0G,QAAQ;gBACzCjT,QAAQjB,QAAQiB,MAAM,IAAI,IAAI,CAAC+E,aAAa;gBAC5C,iDAAiD;gBACjDuH,IAAI;YACN,IACAH;QAEJ;QA5NE,uCAAuC;QACvC,MAAMnD,QAAQvH,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACnC;QAElC,6CAA6C;QAC7C,IAAI,CAACqP,UAAU,GAAG,CAAC;QACnB,oDAAoD;QACpD,wDAAwD;QACxD,kCAAkC;QAClC,IAAIrP,aAAa,WAAW;YAC1B,IAAI,CAACqP,UAAU,CAAC3F,MAAM,GAAG;gBACvBgI;gBACAyG,SAAS;gBACTnG,OAAO4F;gBACP1O;gBACA+I,SAAS2F,gBAAgBA,aAAa3F,OAAO;gBAC7CC,SAAS0F,gBAAgBA,aAAa1F,OAAO;YAC/C;QACF;QAEA,IAAI,CAAC7C,UAAU,CAAC,QAAQ,GAAG;YACzBqC,WAAW6F;YACXvD,aAAa,EAEZ;QACH;QAEA,4CAA4C;QAC5C,gFAAgF;QAChF,IAAI,CAACnF,MAAM,GAAG3P,OAAO2P,MAAM;QAE3B,IAAI,CAAC/O,UAAU,GAAGA;QAClB,8DAA8D;QAC9D,kDAAkD;QAClD,MAAMsY,oBACJ7V,CAAAA,GAAAA,WAAAA,cAAc,EAACvC,aAAaqK,KAAKuI,aAAa,CAACyF,UAAU;QAE3D,IAAI,CAACvV,QAAQ,GAAGK,QAAQC,GAAG,CAACkV,sBAAsB,MAAI;QACtD,IAAI,CAAChD,GAAG,GAAGwC;QACX,IAAI,CAAClO,GAAG,GAAG;QACX,IAAI,CAAC6N,QAAQ,GAAGI;QAChB,6DAA6D;QAC7D,0BAA0B;QAC1B,IAAI,CAACrK,KAAK,GAAG;QACb,IAAI,CAACU,cAAc,GAAG;QACtB,IAAI,CAACX,OAAO,GAAG,CAAC,CACdlD,CAAAA,KAAKuI,aAAa,CAAC2F,IAAI,IACvBlO,KAAKuI,aAAa,CAAC4F,GAAG,IACtBnO,KAAKuI,aAAa,CAAC6F,qBAAqB,IACvCpO,KAAKuI,aAAa,CAAC8F,MAAM,IAAI,CAACrO,KAAKuI,aAAa,CAAC+F,GAAG,IACpD,CAACP,qBACA,CAAC/N,KAAK5B,QAAQ,CAACmQ,MAAM,IACrB,CAACzV,QAAQC,GAAG,CAACuB,2BAAmB;QAGpC,IAAIxB,QAAQC,GAAG,CAACsK,mBAAmB,IAAE;;QAQrC;QAEA,IAAI,CAACL,KAAK,GAAG;YACX3D;YACA1J;YACA+E;YACA5E,QAAQiY,oBAAoBpY,WAAWwB;YACvCwP,WAAW,CAAC,CAACA;YACbtQ,QAAQyC,QAAQC,GAAG,CAACsK,mBAAmB,GAAGhN,2CAASkE;YACnDqM;QACF;QAEA,IAAI,CAAC4H,gCAAgC,GAAGlZ,QAAQC,OAAO,CAAC;QAExD,IAAI,OAAO8G,WAAW,aAAa;YACjC,kEAAkE;YAClE,4CAA4C;YAC5C,IAAI,CAAClF,GAAGJ,UAAU,CAAC,OAAO;gBACxB,2DAA2D;gBAC3D,4DAA4D;gBAC5D,MAAM3B,UAA6B;oBAAEiB;gBAAO;gBAC5C,MAAMP,SAASuT,CAAAA,GAAAA,OAAAA,MAAM;gBAErB,IAAI,CAACmF,gCAAgC,GAAG1Z,kBAAkB;oBACxDU,QAAQ,IAAI;oBACZa;oBACAP;gBACF,GAAGmE,IAAI,CAAC,CAACc;oBACP,kEAAkE;oBAClE,sDAAsD;;oBACpD3F,QAAgB0N,kBAAkB,GAAG3L,OAAOxB;oBAE9C,IAAI,CAACkP,WAAW,CACd,gBACA9J,UACIjF,SACA0N,CAAAA,GAAAA,WAAAA,oBAAoB,EAAC;wBACnB7N,UAAUQ,CAAAA,GAAAA,aAAAA,WAAW,EAACR;wBACtB+E;oBACF,IACJ5E,QACAV;oBAEF,OAAO2F;gBACT;YACF;YAEAsB,OAAOoS,gBAAgB,CAAC,YAAY,IAAI,CAACd,UAAU;YAEnD,2DAA2D;YAC3D,mDAAmD;YACnD,IAAI7U,QAAQC,GAAG,CAACqD,uBAA2B,EAAF;;YAIzC;QACF;IACF;AAwrDF;AAh4DqBvH,OA6CZ2P,MAAAA,GAAmCmK,CAAAA,GAAAA,MAAAA,OAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6358, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/client/with-router.tsx"], "sourcesContent": ["import React, { type JSX } from 'react'\nimport type {\n  BaseContext,\n  NextComponentType,\n  NextPageContext,\n} from '../shared/lib/utils'\nimport type { NextRouter } from './router'\nimport { useRouter } from './router'\n\nexport type WithRouterProps = {\n  router: NextRouter\n}\n\nexport type ExcludeRouterProps<P> = Pick<\n  P,\n  Exclude<keyof P, keyof WithRouterProps>\n>\n\nexport default function withRouter<\n  P extends WithRouterProps,\n  C extends BaseContext = NextPageContext,\n>(\n  ComposedComponent: NextComponentType<C, any, P>\n): React.ComponentType<ExcludeRouterProps<P>> {\n  function WithRouterWrapper(props: any): JSX.Element {\n    return <ComposedComponent router={useRouter()} {...props} />\n  }\n\n  WithRouterWrapper.getInitialProps = ComposedComponent.getInitialProps\n  // This is needed to allow checking for custom getInitialProps in _app\n  ;(WithRouterWrapper as any).origGetInitialProps = (\n    ComposedComponent as any\n  ).origGetInitialProps\n  if (process.env.NODE_ENV !== 'production') {\n    const name =\n      ComposedComponent.displayName || ComposedComponent.name || 'Unknown'\n    WithRouterWrapper.displayName = `withRouter(${name})`\n  }\n\n  return WithRouterWrapper\n}\n"], "names": ["with<PERSON><PERSON><PERSON>", "ComposedComponent", "WithRouterWrapper", "props", "router", "useRouter", "getInitialProps", "origGetInitialProps", "process", "env", "NODE_ENV", "name", "displayName"], "mappings": ";;;;+BAk<PERSON>,WAAA;;;eAAwBA;;;;;gEAlBQ;wBAON;AAWX,SAASA,WAItBC,iBAA+C;IAE/C,SAASC,kBAAkBC,KAAU;QACnC,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACF,mBAAAA;YAAkBG,QAAQC,CAAAA,GAAAA,QAAAA,SAAS;YAAK,GAAGF,KAAK;;IAC1D;IAEAD,kBAAkBI,eAAe,GAAGL,kBAAkBK,eAAe;IAEnEJ,kBAA0BK,mBAAmB,GAC7CN,kBACAM,mBAAmB;IACrB,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,MAAMC,OACJV,kBAAkBW,WAAW,IAAIX,kBAAkBU,IAAI,IAAI;QAC7DT,kBAAkBU,WAAW,GAAI,gBAAaD,OAAK;IACrD;IAEA,OAAOT;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6399, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/client/router.ts"], "sourcesContent": ["/* global window */\nimport React from 'react'\nimport Router from '../shared/lib/router/router'\nimport type { NextRouter } from '../shared/lib/router/router'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\nimport isError from '../lib/is-error'\n\ntype SingletonRouterBase = {\n  router: Router | null\n  readyCallbacks: Array<() => any>\n  ready(cb: () => any): void\n}\n\nexport { Router }\n\nexport type { NextRouter }\n\nexport type SingletonRouter = SingletonRouterBase & NextRouter\n\nconst singletonRouter: SingletonRouterBase = {\n  router: null, // holds the actual router instance\n  readyCallbacks: [],\n  ready(callback: () => void) {\n    if (this.router) return callback()\n    if (typeof window !== 'undefined') {\n      this.readyCallbacks.push(callback)\n    }\n  },\n}\n\n// Create public properties and methods of the router in the singletonRouter\nconst urlPropertyFields = [\n  'pathname',\n  'route',\n  'query',\n  'asPath',\n  'components',\n  'isFallback',\n  'basePath',\n  'locale',\n  'locales',\n  'defaultLocale',\n  'isReady',\n  'isPreview',\n  'isLocaleDomain',\n  'domainLocales',\n] as const\nconst routerEvents = [\n  'routeChangeStart',\n  'beforeHistoryChange',\n  'routeChangeComplete',\n  'routeChangeError',\n  'hashChangeStart',\n  'hashChangeComplete',\n] as const\nexport type RouterEvent = (typeof routerEvents)[number]\n\nconst coreMethodFields = [\n  'push',\n  'replace',\n  'reload',\n  'back',\n  'prefetch',\n  'beforePopState',\n] as const\n\n// Events is a static property on the router, the router doesn't have to be initialized to use it\nObject.defineProperty(singletonRouter, 'events', {\n  get() {\n    return Router.events\n  },\n})\n\nfunction getRouter(): Router {\n  if (!singletonRouter.router) {\n    const message =\n      'No router instance found.\\n' +\n      'You should only use \"next/router\" on the client side of your app.\\n'\n    throw new Error(message)\n  }\n  return singletonRouter.router\n}\n\nurlPropertyFields.forEach((field) => {\n  // Here we need to use Object.defineProperty because we need to return\n  // the property assigned to the actual router\n  // The value might get changed as we change routes and this is the\n  // proper way to access it\n  Object.defineProperty(singletonRouter, field, {\n    get() {\n      const router = getRouter()\n      return router[field] as string\n    },\n  })\n})\n\ncoreMethodFields.forEach((field) => {\n  // We don't really know the types here, so we add them later instead\n  ;(singletonRouter as any)[field] = (...args: any[]) => {\n    const router = getRouter() as any\n    return router[field](...args)\n  }\n})\n\nrouterEvents.forEach((event) => {\n  singletonRouter.ready(() => {\n    Router.events.on(event, (...args) => {\n      const eventField = `on${event.charAt(0).toUpperCase()}${event.substring(\n        1\n      )}`\n      const _singletonRouter = singletonRouter as any\n      if (_singletonRouter[eventField]) {\n        try {\n          _singletonRouter[eventField](...args)\n        } catch (err) {\n          console.error(`Error when running the Router event: ${eventField}`)\n          console.error(\n            isError(err) ? `${err.message}\\n${err.stack}` : err + ''\n          )\n        }\n      }\n    })\n  })\n})\n\n// Export the singletonRouter and this is the public API.\nexport default singletonRouter as SingletonRouter\n\n// Reexport the withRouter HOC\nexport { default as withRouter } from './with-router'\n\n/**\n * This hook gives access the [router object](https://nextjs.org/docs/pages/api-reference/functions/use-router#router-object)\n * inside the [Pages Router](https://nextjs.org/docs/pages/building-your-application).\n *\n * Read more: [Next.js Docs: `useRouter`](https://nextjs.org/docs/pages/api-reference/functions/use-router)\n */\nexport function useRouter(): NextRouter {\n  const router = React.useContext(RouterContext)\n  if (!router) {\n    throw new Error(\n      'NextRouter was not mounted. https://nextjs.org/docs/messages/next-router-not-mounted'\n    )\n  }\n\n  return router\n}\n\n/**\n * Create a router and assign it as the singleton instance.\n * This is used in client side when we are initializing the app.\n * This should **not** be used inside the server.\n * @internal\n */\nexport function createRouter(\n  ...args: ConstructorParameters<typeof Router>\n): Router {\n  singletonRouter.router = new Router(...args)\n  singletonRouter.readyCallbacks.forEach((cb) => cb())\n  singletonRouter.readyCallbacks = []\n\n  return singletonRouter.router\n}\n\n/**\n * This function is used to create the `withRouter` router instance\n * @internal\n */\nexport function makePublicRouterInstance(router: Router): NextRouter {\n  const scopedRouter = router as any\n  const instance = {} as any\n\n  for (const property of urlPropertyFields) {\n    if (typeof scopedRouter[property] === 'object') {\n      instance[property] = Object.assign(\n        Array.isArray(scopedRouter[property]) ? [] : {},\n        scopedRouter[property]\n      ) // makes sure query is not stateful\n      continue\n    }\n\n    instance[property] = scopedRouter[property]\n  }\n\n  // Events is a static property on the router, the router doesn't have to be initialized to use it\n  instance.events = Router.events\n\n  coreMethodFields.forEach((field) => {\n    instance[field] = (...args: any[]) => {\n      return scopedRouter[field](...args)\n    }\n  })\n\n  return instance\n}\n"], "names": ["Router", "createRouter", "makePublicRouterInstance", "useRouter", "with<PERSON><PERSON><PERSON>", "singletonRouter", "router", "readyCallbacks", "ready", "callback", "window", "push", "url<PERSON><PERSON><PERSON><PERSON>ields", "routerEvents", "core<PERSON><PERSON><PERSON><PERSON><PERSON>s", "Object", "defineProperty", "get", "events", "getRouter", "message", "Error", "for<PERSON>ach", "field", "args", "event", "on", "eventField", "char<PERSON>t", "toUpperCase", "substring", "_singletonRouter", "err", "console", "error", "isError", "stack", "React", "useContext", "RouterContext", "cb", "scopedRouter", "instance", "property", "assign", "Array", "isArray"], "mappings": "AAAA,iBAAiB,GAAA;;;;;;;;;;;;;;;;;;;IAaRA,MAAM,EAAA;eAANA,QAAAA,OAAM;;IA6ICC,YAAY,EAAA;eAAZA;;IA7BhB,yDAAyD;IACzD,OAAiD,EAAA;eAAjD;;IA0CgBC,wBAAwB,EAAA;eAAxBA;;IA/BAC,SAAS,EAAA;eAATA;;IARIC,UAAU,EAAA;eAAVA,YAAAA,OAAU;;;;gEAhIZ;iEACC;4CAEW;kEACV;qEA4HkB;AA9GtC,MAAMC,kBAAuC;IAC3CC,QAAQ;IACRC,gBAAgB,EAAE;IAClBC,OAAMC,QAAoB;QACxB,IAAI,IAAI,CAACH,MAAM,EAAE,OAAOG;QACxB,IAAI,OAAOC,WAAW,aAAa;YACjC,IAAI,CAACH,cAAc,CAACI,IAAI,CAACF;QAC3B;IACF;AACF;AAEA,4EAA4E;AAC5E,MAAMG,oBAAoB;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,MAAMC,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;CACD;AAGD,MAAMC,mBAAmB;IACvB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,iGAAiG;AACjGC,OAAOC,cAAc,CAACX,iBAAiB,UAAU;IAC/CY;QACE,OAAOjB,QAAAA,OAAM,CAACkB,MAAM;IACtB;AACF;AAEA,SAASC;IACP,IAAI,CAACd,gBAAgBC,MAAM,EAAE;QAC3B,MAAMc,UACJ,gCACA;QACF,MAAM,OAAA,cAAkB,CAAlB,IAAIC,MAAMD,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;IACA,OAAOf,gBAAgBC,MAAM;AAC/B;AAEAM,kBAAkBU,OAAO,CAAC,CAACC;IACzB,sEAAsE;IACtE,6CAA6C;IAC7C,kEAAkE;IAClE,0BAA0B;IAC1BR,OAAOC,cAAc,CAACX,iBAAiBkB,OAAO;QAC5CN;YACE,MAAMX,SAASa;YACf,OAAOb,MAAM,CAACiB,MAAM;QACtB;IACF;AACF;AAEAT,iBAAiBQ,OAAO,CAAC,CAACC;IACxB,oEAAoE;;IAClElB,eAAuB,CAACkB,MAAM,GAAG;yCAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QACrC,MAAMlB,SAASa;QACf,OAAOb,MAAM,CAACiB,MAAM,IAAIC;IAC1B;AACF;AAEAX,aAAaS,OAAO,CAAC,CAACG;IACpBpB,gBAAgBG,KAAK,CAAC;QACpBR,QAAAA,OAAM,CAACkB,MAAM,CAACQ,EAAE,CAACD,OAAO;6CAAID,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;gBAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;YAC1B,MAAMG,aAAc,OAAIF,MAAMG,MAAM,CAAC,GAAGC,WAAW,KAAKJ,MAAMK,SAAS,CACrE;YAEF,MAAMC,mBAAmB1B;YACzB,IAAI0B,gBAAgB,CAACJ,WAAW,EAAE;gBAChC,IAAI;oBACFI,gBAAgB,CAACJ,WAAW,IAAIH;gBAClC,EAAE,OAAOQ,KAAK;oBACZC,QAAQC,KAAK,CAAE,0CAAuCP;oBACtDM,QAAQC,KAAK,CACXC,CAAAA,GAAAA,SAAAA,OAAO,EAACH,OAAUA,IAAIZ,OAAO,GAAC,OAAIY,IAAII,KAAK,GAAKJ,MAAM;gBAE1D;YACF;QACF;IACF;AACF;MAGA,WAAe3B;AAWR,SAASF;IACd,MAAMG,SAAS+B,OAAAA,OAAK,CAACC,UAAU,CAACC,4BAAAA,aAAa;IAC7C,IAAI,CAACjC,QAAQ;QACX,MAAM,OAAA,cAEL,CAFK,IAAIe,MACR,yFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAOf;AACT;AAQO,SAASL;IACd,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGuB,OAAH,IAAA,MAAA,OAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,IAAAA,CAAH,KAAA,GAAA,SAAA,CAAA,KAA6C;;IAE7CnB,gBAAgBC,MAAM,GAAG,IAAIN,QAAAA,OAAM,IAAIwB;IACvCnB,gBAAgBE,cAAc,CAACe,OAAO,CAAC,CAACkB,KAAOA;IAC/CnC,gBAAgBE,cAAc,GAAG,EAAE;IAEnC,OAAOF,gBAAgBC,MAAM;AAC/B;AAMO,SAASJ,yBAAyBI,MAAc;IACrD,MAAMmC,eAAenC;IACrB,MAAMoC,WAAW,CAAC;IAElB,KAAK,MAAMC,YAAY/B,kBAAmB;QACxC,IAAI,OAAO6B,YAAY,CAACE,SAAS,KAAK,UAAU;YAC9CD,QAAQ,CAACC,SAAS,GAAG5B,OAAO6B,MAAM,CAChCC,MAAMC,OAAO,CAACL,YAAY,CAACE,SAAS,IAAI,EAAE,GAAG,CAAC,GAC9CF,YAAY,CAACE,SAAS,EACtB,mCAAmC;;YACrC;QACF;QAEAD,QAAQ,CAACC,SAAS,GAAGF,YAAY,CAACE,SAAS;IAC7C;IAEA,iGAAiG;IACjGD,SAASxB,MAAM,GAAGlB,QAAAA,OAAM,CAACkB,MAAM;IAE/BJ,iBAAiBQ,OAAO,CAAC,CAACC;QACxBmB,QAAQ,CAACnB,MAAM,GAAG;6CAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;gBAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;YACpB,OAAOiB,YAAY,CAAClB,MAAM,IAAIC;QAChC;IACF;IAEA,OAAOkB;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6602, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/next%4015.3.0_%40babel%2Bcore%407.27.4_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/router.js"], "sourcesContent": ["module.exports = require('./dist/client/router')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6609, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/utils/invalidateNextRouterCache.ts"], "sourcesContent": ["// interface FetchDataOutput {\n//   dataHref: string;\n//   json: Record<string, any> | null;\n//   response: Response;\n//   text: string;\n//   cacheKey: string;\n// }\n//\n// interface NextDataCache {\n//   [asPath: string]: Promise<FetchDataOutput>;\n// }\n\n/**\n * A placeholder for the actual next types.\n * The types above are not exported from the next package currently,\n * we only include them here for documentation purposes.\n * see: https://github.com/vercel/next.js/blob/018208fb15c9b969e173684668cea89588f4c536/packages/next/src/shared/lib/router/router.ts#L655\n */\ntype NextDataCache = any;\n\n/**\n * Only for /pages router\n *\n * Next currently prefetches the page of every visible Link on the page.\n * For every prefetch request, the middleware runs and the response is cached in\n * window.next.router.sdc or window.next.router.sdc\n *\n * Imagine a scenario with a /protected page requiring the user to be signed in using middleware.\n * If we don't invalidate the cache, we end up in the following redirect flow:\n * home -> /protected -> middleware redirects to /sign-in -> perform sign-in\n *            -> try to navigate to /protected but the cached 307 response is used\n *                   -> redirect to /sign-in instead -> withRedirectToHome -> home\n * When the auth state changes and the middleware runs again, the client-side router\n * does not automatically invalidate the cache so the browser follows the cached response\n *\n * This helper invalidates both known caches help prevent the scenario described above.\n */\nexport const invalidateNextRouterCache = () => {\n  if (typeof window === 'undefined') {\n    return;\n  }\n\n  const invalidate = (cache: NextDataCache) => {\n    // deleting the keys without nuking the cache by reassigning the variable to an empty object,\n    // in case next holds a reference to it\n    Object.keys(cache).forEach(key => {\n      delete cache[key];\n    });\n  };\n\n  try {\n    invalidate((window as any).next.router.sdc);\n    invalidate((window as any).next.router.sbc);\n  } catch {\n    return;\n  }\n};\n"], "names": [], "mappings": ";;;;AAqCO,MAAM,4BAA4B,MAAM;IAC7C,IAAI,OAAO,WAAW,aAAa;QACjC;IACF;IAEA,MAAM,aAAa,CAAC,UAAyB;QAG3C,OAAO,IAAA,CAAK,KAAK,EAAE,OAAA,CAAQ,CAAA,QAAO;YAChC,OAAO,KAAA,CAAM,GAAG,CAAA;QAClB,CAAC;IACH;IAEA,IAAI;QACF,WAAY,OAAe,IAAA,CAAK,MAAA,CAAO,GAAG;QAC1C,WAAY,OAAe,IAAA,CAAK,MAAA,CAAO,GAAG;IAC5C,EAAA,OAAQ;QACN;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6637, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/pages/ClerkProvider.tsx"], "sourcesContent": ["import { Clerk<PERSON>rovider as ReactClerkProvider } from '@clerk/clerk-react';\n// Override Clerk React error thrower to show that errors come from @clerk/nextjs\nimport { setClerkJsLoadingErrorPackageName, setErrorThrowerOptions } from '@clerk/clerk-react/internal';\nimport { useRouter } from 'next/router';\nimport React from 'react';\n\nimport { useSafeLayoutEffect } from '../client-boundary/hooks/useSafeLayoutEffect';\nimport { ClerkNextOptionsProvider } from '../client-boundary/NextOptionsContext';\nimport type { NextClerkProviderProps } from '../types';\nimport { ClerkJSScript } from '../utils/clerk-js-script';\nimport { invalidateNextRouterCache } from '../utils/invalidateNextRouterCache';\nimport { mergeNextClerkPropsWithEnv } from '../utils/mergeNextClerkPropsWithEnv';\nimport { removeBasePath } from '../utils/removeBasePath';\nimport { RouterTelemetry } from '../utils/router-telemetry';\n\nsetErrorThrowerOptions({ packageName: PACKAGE_NAME });\nsetClerkJsLoadingErrorPackageName(PACKAGE_NAME);\n\nexport function ClerkProvider({ children, ...props }: NextClerkProviderProps): JSX.Element {\n  const { __unstable_invokeMiddlewareOnAuthStateChange = true } = props;\n  const { push, replace } = useRouter();\n  ReactClerkProvider.displayName = 'ReactClerkProvider';\n\n  useSafeLayoutEffect(() => {\n    window.__unstable__onBeforeSetActive = invalidateNextRouterCache;\n  }, []);\n\n  useSafeLayoutEffect(() => {\n    window.__unstable__onAfterSetActive = () => {\n      // Re-run the middleware every time there auth state changes.\n      // This enables complete control from a centralised place (NextJS middleware),\n      // as we will invoke it every time the client-side auth state changes, eg: signing-out, switching orgs, etc.\\\n      if (__unstable_invokeMiddlewareOnAuthStateChange) {\n        void push(window.location.href);\n      }\n    };\n  }, []);\n\n  const navigate = (to: string) => push(removeBasePath(to));\n  const replaceNavigate = (to: string) => replace(removeBasePath(to));\n  const mergedProps = mergeNextClerkPropsWithEnv({\n    ...props,\n    routerPush: navigate,\n    routerReplace: replaceNavigate,\n  });\n  // ClerkProvider automatically injects __clerk_ssr_state\n  // getAuth returns a user-facing authServerSideProps that hides __clerk_ssr_state\n  // @ts-expect-error initialState is hidden from the types as it's a private prop\n  const initialState = props.authServerSideProps?.__clerk_ssr_state || props.__clerk_ssr_state;\n\n  return (\n    <ClerkNextOptionsProvider options={mergedProps}>\n      <ReactClerkProvider\n        {...mergedProps}\n        initialState={initialState}\n      >\n        <RouterTelemetry />\n        <ClerkJSScript router='pages' />\n        {children}\n      </ReactClerkProvider>\n    </ClerkNextOptionsProvider>\n  );\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,iBAAiB,0BAA0B;;AAEpD,SAAS,mCAAmC,8BAA8B;;;AAC1E,SAAS,iBAAiB;AAC1B,OAAO,WAAW;AAElB,SAAS,2BAA2B;AACpC,SAAS,gCAAgC;AAEzC,SAAS,qBAAqB;AAC9B,SAAS,iCAAiC;AAC1C,SAAS,kCAAkC;AAC3C,SAAS,sBAAsB;AAC/B,SAAS,uBAAuB;;;;;;;;;;;;;gVAEhC,yBAAA,EAAuB;IAAE,aAAa;AAAa,CAAC;+SACpD,oCAAA,EAAkC,eAAY;AAEvC,SAAS,cAAc,EAAE,QAAA,EAAU,GAAG,MAAM,CAAA,EAAwC;IAlB3F,IAAA;IAmBE,MAAM,EAAE,+CAA+C,IAAA,CAAK,CAAA,GAAI;IAChE,MAAM,EAAE,IAAA,EAAM,OAAA,CAAQ,CAAA,+UAAI,YAAA,CAAU;IACpC,+TAAA,CAAA,gBAAA,CAAmB,WAAA,GAAc;IAEjC,CAAA,GAAA,sZAAA,CAAA,sBAAA,EAAoB,MAAM;QACxB,OAAO,6BAAA,0YAAgC,4BAAA;IACzC,GAAG,CAAC,CAAC;IAEL,CAAA,GAAA,sZAAA,CAAA,sBAAA,EAAoB,MAAM;QACxB,OAAO,4BAAA,GAA+B,MAAM;YAI1C,IAAI,8CAA8C;gBAChD,KAAK,KAAK,OAAO,QAAA,CAAS,IAAI;YAChC;QACF;IACF,GAAG,CAAC,CAAC;IAEL,MAAM,WAAW,CAAC,KAAe,qYAAK,iBAAA,EAAe,EAAE,CAAC;IACxD,MAAM,kBAAkB,CAAC,KAAe,QAAQ,iZAAA,EAAe,EAAE,CAAC;IAClE,MAAM,0ZAAc,6BAAA,EAA2B;QAC7C,GAAG,KAAA;QACH,YAAY;QACZ,eAAe;IACjB,CAAC;IAID,MAAM,eAAA,CAAA,CAAe,KAAA,MAAM,mBAAA,KAAN,OAAA,KAAA,IAAA,GAA2B,iBAAA,KAAqB,MAAM,iBAAA;IAE3E,OACE,aAAA,GAAA,8YAAA,CAAA,UAAA,CAAA,aAAA,8YAAC,2BAAA,EAAA;QAAyB,SAAS;IAAA,GACjC,aAAA,GAAA,8YAAA,CAAA,UAAA,CAAA,aAAA,iUAAC,gBAAA,EAAA;QACE,GAAG,WAAA;QACJ;IAAA,GAEA,aAAA,GAAA,8YAAA,CAAA,UAAA,CAAA,aAAA,kYAAC,kBAAA,EAAA,IAAgB,GACjB,aAAA,GAAA,8YAAA,CAAA,UAAA,CAAA,aAAA,oYAAC,gBAAA,EAAA;QAAc,QAAO;IAAA,CAAQ,GAC7B;AAIT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6710, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/client-boundary/ClerkProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/compat/router';\nimport React from 'react';\n\nimport { ClientClerkProvider } from '../app-router/client/ClerkProvider';\nimport { ClerkProvider as PageClerkProvider } from '../pages/ClerkProvider';\nimport { type NextClerkProviderProps } from '../types';\n\n/**\n * This is a compatibility layer to support a single ClerkProvider component in both the app and pages routers.\n */\nexport function ClerkProvider(props: NextClerkProviderProps) {\n  const router = useRouter();\n\n  const Provider = router ? PageClerkProvider : ClientClerkProvider;\n\n  return <Provider {...props} />;\n}\n"], "names": [], "mappings": ";;;AAEA,SAAS,iBAAiB;AAC1B,OAAO,WAAW;AAElB,SAAS,2BAA2B;AACpC,SAAS,iBAAiB,yBAAyB;;;;;;;AAM5C,SAAS,cAAc,KAAA,EAA+B;IAC3D,MAAM,+VAAS,YAAA,CAAU;IAEzB,MAAM,WAAW,oYAAS,gBAAA,gZAAoB,sBAAA;IAE9C,OAAO,aAAA,GAAA,8YAAA,CAAA,UAAA,CAAA,aAAA,CAAC,UAAA;QAAU,GAAG,KAAA;IAAA,CAAO;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6738, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/index.ts"], "sourcesContent": ["/**\n * These need to be explicitly listed. Do not use an * here.\n * If you do, app router will break.\n */\nexport {\n  AuthenticateWithRedirect<PERSON><PERSON>back,\n  <PERSON>Loa<PERSON>,\n  <PERSON><PERSON>oa<PERSON>,\n  <PERSON><PERSON><PERSON>raded,\n  ClerkFailed,\n  RedirectToCreateOrganization,\n  RedirectToOrganizationProfile,\n  RedirectToSignIn,\n  RedirectToSignUp,\n  RedirectToUserProfile,\n} from './client-boundary/controlComponents';\n\n/**\n * These need to be explicitly listed. Do not use an * here.\n * If you do, app router will break.\n */\nexport {\n  CreateOrganization,\n  OrganizationList,\n  OrganizationProfile,\n  OrganizationSwitcher,\n  SignIn,\n  SignInButton,\n  SignInWithMetamaskButton,\n  SignOutButton,\n  SignUp,\n  SignUpButton,\n  UserButton,\n  UserProfile,\n  GoogleOneTap,\n  Waitlist,\n  PricingTable,\n  APIKeys,\n} from './client-boundary/uiComponents';\n\n/**\n * These need to be explicitly listed. Do not use an * here.\n * If you do, app router will break.\n */\nexport {\n  useAuth,\n  useClerk,\n  useEmailLink,\n  useOrganization,\n  useOrganizationList,\n  useSession,\n  useSessionList,\n  useSignIn,\n  useSignUp,\n  useUser,\n  useReverification,\n} from './client-boundary/hooks';\n\n/**\n * Conditionally export components that exhibit different behavior\n * when used in /app vs /pages.\n * We defined the runtime and the type values explicitly,\n * because TS will not recognize the subpath import unless the HOST\n * application sets moduleResolution to 'NodeNext'.\n */\n// @ts-ignore\nimport * as ComponentsModule from '#components';\n\nimport type { ServerComponentsServerModuleTypes } from './components.server';\n\nexport const ClerkProvider = ComponentsModule.ClerkProvider as ServerComponentsServerModuleTypes['ClerkProvider'];\nexport const SignedIn = ComponentsModule.SignedIn as ServerComponentsServerModuleTypes['SignedIn'];\nexport const SignedOut = ComponentsModule.SignedOut as ServerComponentsServerModuleTypes['SignedOut'];\nexport const Protect = ComponentsModule.Protect as ServerComponentsServerModuleTypes['Protect'];\n"], "names": [], "mappings": ";;;;;;;AAkEA,YAAY,sBAAsB;;;;;;AAI3B,MAAM,wZAAgB,gBAAiB,CAAA;AACvC,MAAM,uVAAW,WAAiB,MAAA;AAClC,MAAM,wVAAY,YAAiB,KAAA;AACnC,MAAM,sVAAU,UAAiB,OAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6771, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/client-boundary/hooks/useEnforceCatchAllRoute.tsx"], "sourcesContent": ["import { isProductionEnvironment } from '@clerk/shared/utils';\nimport type { RoutingStrategy } from '@clerk/types';\nimport React from 'react';\n\nimport { useSession } from '../hooks';\nimport { usePagesRouter } from './usePagesRouter';\n\n/**\n * This ugly hook  enforces that the Clerk components are mounted in a catch-all route\n * For pages router, we can parse the pathname we get from the useRouter hook\n * For app router, there is no reliable way to do the same check right now, so we\n * fire a request to a path under window.location.href and we check whether the path\n * exists or not\n */\nexport const useEnforceCatchAllRoute = (\n  component: string,\n  path: string,\n  routing?: RoutingStrategy,\n  requireSessionBeforeCheck = true,\n) => {\n  const ref = React.useRef(0);\n  const { pagesRouter } = usePagesRouter();\n  const { session, isLoaded } = useSession();\n\n  // This check does not break the rules of hooks\n  // as the condition will remain the same for the whole app lifecycle\n  if (isProductionEnvironment()) {\n    return;\n  }\n\n  React.useEffect(() => {\n    if (!isLoaded || (routing && routing !== 'path')) {\n      return;\n    }\n\n    // For components that require an active session, like UserProfile\n    // we should not enforce the catch-all route if there is no session\n    // because these components are usually protected by the middleware\n    // and if the check runs before the session is available, it will fail\n    // even if the route is a catch-all route, as the check request will result\n    // in a 404 because of auth.protect();\n    if (requireSessionBeforeCheck && !session) {\n      return;\n    }\n\n    const ac = new AbortController();\n    const error = () => {\n      const correctPath = pagesRouter ? `${path}/[[...index]].tsx` : `${path}/[[...rest]]/page.tsx`;\n      throw new Error(\n        `\nClerk: The <${component}/> component is not configured correctly. The most likely reasons for this error are:\n\n1. The \"${path}\" route is not a catch-all route.\nIt is recommended to convert this route to a catch-all route, eg: \"${correctPath}\". Alternatively, you can update the <${component}/> component to use hash-based routing by setting the \"routing\" prop to \"hash\".\n\n2. The <${component}/> component is mounted in a catch-all route, but all routes under \"${path}\" are protected by the middleware.\nTo resolve this, ensure that the middleware does not protect the catch-all route or any of its children. If you are using the \"createRouteMatcher\" helper, consider adding \"(.*)\" to the end of the route pattern, eg: \"${path}(.*)\". For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#create-route-matcher\n`,\n      );\n    };\n\n    if (pagesRouter) {\n      if (!pagesRouter.pathname.match(/\\[\\[\\.\\.\\..+]]/)) {\n        error();\n      }\n    } else {\n      const check = async () => {\n        // make sure to run this as soon as possible\n        // but don't run again when strict mode is enabled\n        ref.current++;\n        if (ref.current > 1) {\n          return;\n        }\n        let res;\n        try {\n          const url = `${window.location.origin}${\n            window.location.pathname\n          }/${component}_clerk_catchall_check_${Date.now()}`;\n          res = await fetch(url, { signal: ac.signal });\n        } catch {\n          // no op\n        }\n        if (res?.status === 404) {\n          error();\n        }\n      };\n      void check();\n    }\n\n    return () => {\n      // make sure to run this as soon as possible\n      // but don't run again when strict mode is enabled\n      if (ref.current > 1) {\n        ac.abort();\n      }\n    };\n  }, [isLoaded]);\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,+BAA+B;;AAExC,OAAO,WAAW;AAElB,SAAS,kBAAkB;AAC3B,SAAS,sBAAsB;;;;;;AASxB,MAAM,0BAA0B,CACrC,WACA,MACA,SACA,4BAA4B,IAAA,KACzB;IACH,MAAM,qZAAM,UAAA,CAAM,MAAA,CAAO,CAAC;IAC1B,MAAM,EAAE,WAAA,CAAY,CAAA,yZAAI,iBAAA,CAAe;IACvC,MAAM,EAAE,OAAA,EAAS,QAAA,CAAS,CAAA,OAAI,qTAAA,CAAW;IAIzC,mTAAI,0BAAA,CAAwB,IAAG;QAC7B;IACF;IAEA,8YAAA,CAAA,UAAA,CAAM,SAAA,CAAU,MAAM;QACpB,IAAI,CAAC,YAAa,WAAW,YAAY,QAAS;YAChD;QACF;QAQA,IAAI,6BAA6B,CAAC,SAAS;YACzC;QACF;QAEA,MAAM,KAAK,IAAI,gBAAgB;QAC/B,MAAM,QAAQ,MAAM;YAClB,MAAM,cAAc,cAAc,GAAG,IAAI,CAAA,iBAAA,CAAA,GAAsB,GAAG,IAAI,CAAA,qBAAA,CAAA;YACtE,MAAM,IAAI,MACR,CAAA;YAAA,EACM,SAAS,CAAA;;QAAA,EAEb,IAAI,CAAA;mEAAA,EACuD,WAAW,CAAA,sCAAA,EAAyC,SAAS,CAAA;;QAAA,EAExH,SAAS,CAAA,oEAAA,EAAuE,IAAI,CAAA;wNAAA,EAC4H,IAAI,CAAA;AAAA,CAAA;QAG1N;QAEA,IAAI,aAAa;YACf,IAAI,CAAC,YAAY,QAAA,CAAS,KAAA,CAAM,gBAAgB,GAAG;gBACjD,MAAM;YACR;QACF,OAAO;YACL,MAAM,QAAQ,YAAY;gBAGxB,IAAI,OAAA;gBACJ,IAAI,IAAI,OAAA,GAAU,GAAG;oBACnB;gBACF;gBACA,IAAI;gBACJ,IAAI;oBACF,MAAM,MAAM,GAAG,OAAO,QAAA,CAAS,MAAM,GACnC,OAAO,QAAA,CAAS,QAClB,CAAA,CAAA,EAAI,SAAS,CAAA,sBAAA,EAAyB,KAAK,GAAA,CAAI,CAAC,EAAA;oBAChD,MAAM,MAAM,MAAM,KAAK;wBAAE,QAAQ,GAAG,MAAA;oBAAO,CAAC;gBAC9C,EAAA,OAAQ,CAER;gBACA,IAAA,CAAI,OAAA,OAAA,KAAA,IAAA,IAAK,MAAA,MAAW,KAAK;oBACvB,MAAM;gBACR;YACF;YACA,KAAK,MAAM;QACb;QAEA,OAAO,MAAM;YAGX,IAAI,IAAI,OAAA,GAAU,GAAG;gBACnB,GAAG,KAAA,CAAM;YACX;QACF;IACF,GAAG;QAAC,QAAQ;KAAC;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6851, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/client-boundary/hooks/usePathnameWithoutCatchAll.tsx"], "sourcesContent": ["import React from 'react';\n\nimport { usePagesRouter } from './usePagesRouter';\n\nexport const usePathnameWithoutCatchAll = () => {\n  const pathRef = React.useRef<string>();\n\n  const { pagesRouter } = usePagesRouter();\n\n  if (pagesRouter) {\n    if (pathRef.current) {\n      return pathRef.current;\n    } else {\n      // in pages router things are simpler as the pathname includes the catch all route\n      // which starts with [[... and we can just remove it\n      pathRef.current = pagesRouter.pathname.replace(/\\/\\[\\[\\.\\.\\..*/, '');\n      return pathRef.current;\n    }\n  }\n\n  // require is used to avoid importing next/navigation when the pages router is used,\n  // as it will throw an error. We cannot use dynamic import as it is async\n  // and we need the hook to be sync\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  const usePathname = require('next/navigation').usePathname;\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  const useParams = require('next/navigation').useParams;\n\n  // Get the pathname that includes any named or catch all params\n  // eg:\n  // the filesystem route /user/[id]/profile/[[...rest]]/page.tsx\n  // could give us the following pathname /user/123/profile/security\n  // if the user navigates to the security section of the user profile\n  const pathname = usePathname() || '';\n  const pathParts = pathname.split('/').filter(Boolean);\n  // the useParams hook returns an object with all named and catch all params\n  // for named params, the key in the returned object always contains a single value\n  // for catch all params, the key in the returned object contains an array of values\n  // we find the catch all params by checking if the value is an array\n  // and then we remove one path part for each catch all param\n  const catchAllParams = Object.values(useParams() || {})\n    .filter(v => Array.isArray(v))\n    .flat(Infinity);\n  // so we end up with the pathname where the components are mounted at\n  // eg /user/123/profile/security will return /user/123/profile as the path\n  if (pathRef.current) {\n    return pathRef.current;\n  } else {\n    pathRef.current = `/${pathParts.slice(0, pathParts.length - catchAllParams.length).join('/')}`;\n    return pathRef.current;\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,OAAO,WAAW;AAElB,SAAS,sBAAsB;;;;AAExB,MAAM,6BAA6B,MAAM;IAC9C,MAAM,yZAAU,UAAA,CAAM,MAAA,CAAe;IAErC,MAAM,EAAE,WAAA,CAAY,CAAA,OAAI,maAAA,CAAe;IAEvC,IAAI,aAAa;QACf,IAAI,QAAQ,OAAA,EAAS;YACnB,OAAO,QAAQ,OAAA;QACjB,OAAO;YAGL,QAAQ,OAAA,GAAU,YAAY,QAAA,CAAS,OAAA,CAAQ,kBAAkB,EAAE;YACnE,OAAO,QAAQ,OAAA;QACjB;IACF;IAMA,MAAM,cAAc,QAAQ,iBAAiB,0LAAE,WAAA;IAE/C,MAAM,YAAY,QAAQ,iBAAiB,0LAAE,SAAA;IAO7C,MAAM,WAAW,YAAY,KAAK;IAClC,MAAM,YAAY,SAAS,KAAA,CAAM,GAAG,EAAE,MAAA,CAAO,OAAO;IAMpD,MAAM,iBAAiB,OAAO,MAAA,CAAO,UAAU,KAAK,CAAC,CAAC,EACnD,MAAA,CAAO,CAAA,IAAK,MAAM,OAAA,CAAQ,CAAC,CAAC,EAC5B,IAAA,CAAK,QAAQ;IAGhB,IAAI,QAAQ,OAAA,EAAS;QACnB,OAAO,QAAQ,OAAA;IACjB,OAAO;QACL,QAAQ,OAAA,GAAU,CAAA,CAAA,EAAI,UAAU,KAAA,CAAM,GAAG,UAAU,MAAA,GAAS,eAAe,MAAM,EAAE,IAAA,CAAK,GAAG,CAAC,EAAA;QAC5F,OAAO,QAAQ,OAAA;IACjB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6890, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/client-boundary/hooks/useEnforceRoutingProps.tsx"], "sourcesContent": ["import { useRoutingProps } from '@clerk/clerk-react/internal';\nimport type { RoutingOptions } from '@clerk/types';\n\nimport { useEnforceCatchAllRoute } from './useEnforceCatchAllRoute';\nimport { usePathnameWithoutCatchAll } from './usePathnameWithoutCatchAll';\n\nexport function useEnforceCorrectRoutingProps<T extends RoutingOptions>(\n  componentName: string,\n  props: T,\n  requireSessionBeforeCheck = true,\n): T {\n  const path = usePathnameWithoutCatchAll();\n  const routingProps = useRoutingProps(componentName, props, { path });\n  useEnforceCatchAllRoute(componentName, path, routingProps.routing, requireSessionBeforeCheck);\n  return routingProps;\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,uBAAuB;;AAGhC,SAAS,+BAA+B;AACxC,SAAS,kCAAkC;;;;;AAEpC,SAAS,8BACd,aAAA,EACA,KAAA,EACA,4BAA4B,IAAA,EACzB;IACH,MAAM,yaAAO,6BAAA,CAA2B;IACxC,MAAM,sVAAe,kBAAA,EAAgB,eAAe,OAAO;QAAE;IAAK,CAAC;IACnE,CAAA,GAAA,0ZAAA,CAAA,0BAAA,EAAwB,eAAe,MAAM,aAAa,OAAA,EAAS,yBAAyB;IAC5F,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6917, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bnextjs%406.22.0_next%4015.3.0_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0_42d1807e423217c6736c669ff09b1861/node_modules/%40clerk/nextjs/src/client-boundary/uiComponents.tsx"], "sourcesContent": ["'use client';\n\nimport {\n  OrganizationProfile as BaseOrganizationProfile,\n  SignIn as BaseSignIn,\n  SignUp as BaseSignUp,\n  UserProfile as BaseUserProfile,\n} from '@clerk/clerk-react';\nimport type { ComponentProps } from 'react';\nimport React from 'react';\n\nimport { useEnforceCorrectRoutingProps } from './hooks/useEnforceRoutingProps';\n\nexport {\n  CreateOrganization,\n  OrganizationList,\n  OrganizationSwitcher,\n  SignInButton,\n  SignInWithMetamaskButton,\n  SignOutButton,\n  SignUpButton,\n  UserButton,\n  GoogleOneTap,\n  Waitlist,\n  PricingTable,\n  APIKeys,\n} from '@clerk/clerk-react';\n\n// The assignment of UserProfile with BaseUserProfile props is used\n// to support the CustomPage functionality (eg UserProfile.Page)\n// Also the `typeof BaseUserProfile` is used to resolve the following error:\n// \"The inferred type of 'UserProfile' cannot be named without a reference to ...\"\nexport const UserProfile: typeof BaseUserProfile = Object.assign(\n  (props: ComponentProps<typeof BaseUserProfile>) => {\n    return <BaseUserProfile {...useEnforceCorrectRoutingProps('UserProfile', props)} />;\n  },\n  { ...BaseUserProfile },\n);\n\n// The assignment of OrganizationProfile with BaseOrganizationProfile props is used\n// to support the CustomPage functionality (eg OrganizationProfile.Page)\n// Also the `typeof BaseOrganizationProfile` is used to resolved the following error:\n// \"The inferred type of 'OrganizationProfile' cannot be named without a reference to ...\"\nexport const OrganizationProfile: typeof BaseOrganizationProfile = Object.assign(\n  (props: ComponentProps<typeof BaseOrganizationProfile>) => {\n    return <BaseOrganizationProfile {...useEnforceCorrectRoutingProps('OrganizationProfile', props)} />;\n  },\n  { ...BaseOrganizationProfile },\n);\n\nexport const SignIn = (props: ComponentProps<typeof BaseSignIn>) => {\n  return <BaseSignIn {...useEnforceCorrectRoutingProps('SignIn', props, false)} />;\n};\n\nexport const SignUp = (props: ComponentProps<typeof BaseSignUp>) => {\n  return <BaseSignUp {...useEnforceCorrectRoutingProps('SignUp', props, false)} />;\n};\n"], "names": [], "mappings": ";;;;;;AAEA;;AAOA,OAAO,WAAW;AAElB,SAAS,qCAAqC;;;;;;;AAqBvC,MAAM,cAAsC,OAAO,MAAA,CACxD,CAAC,UAAkD;IACjD,OAAO,aAAA,GAAA,8YAAA,CAAA,UAAA,CAAA,aAAA,iUAAC,cAAA,EAAA;QAAiB,iaAAG,gCAAA,EAA8B,eAAe,KAAK,CAAA;IAAA,CAAG;AACnF,GACA;IAAE,mUAAG,cAAA;AAAgB;AAOhB,MAAM,sBAAsD,OAAO,MAAA,CACxE,CAAC,UAA0D;IACzD,OAAO,aAAA,GAAA,8YAAA,CAAA,UAAA,CAAA,aAAA,CAAC,sVAAA,EAAA;QAAyB,iaAAG,gCAAA,EAA8B,uBAAuB,KAAK,CAAA;IAAA,CAAG;AACnG,GACA;IAAE,kUAAG,uBAAA;AAAwB;AAGxB,MAAM,SAAS,CAAC,UAA6C;IAClE,OAAO,aAAA,GAAA,8YAAA,CAAA,UAAA,CAAA,aAAA,gUAAC,UAAA,EAAA;QAAY,iaAAG,gCAAA,EAA8B,UAAU,OAAO,KAAK,CAAA;IAAA,CAAG;AAChF;AAEO,MAAM,SAAS,CAAC,UAA6C;IAClE,OAAO,aAAA,GAAA,8YAAA,CAAA,UAAA,CAAA,aAAA,iUAAC,SAAA,EAAA;QAAY,iaAAG,gCAAA,EAA8B,UAAU,OAAO,KAAK,CAAA;IAAA,CAAG;AAChF", "ignoreList": [0], "debugId": null}}]}