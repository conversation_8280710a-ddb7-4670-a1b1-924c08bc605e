{"version": 3, "sources": [], "sections": [{"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/app/sign-in/%5B%5B...sign-in%5D%5D/page.tsx"], "sourcesContent": ["import { SignIn } from '@clerk/nextjs'\n\nexport default function SignInPage() {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-app-background\">\n      <div className=\"w-full max-w-md\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-app-headline mb-2\">Welcome Back</h1>\n          <p className=\"text-app-sub-headline\">Sign in to your BuddyChip account</p>\n        </div>\n        \n        <div className=\"bg-app-card rounded-lg border border-app-stroke shadow-lg p-6\">\n          <SignIn \n            appearance={{\n              elements: {\n                formButtonPrimary: 'bg-app-main hover:bg-app-highlight text-app-secondary',\n                card: 'bg-transparent shadow-none',\n                headerTitle: 'hidden',\n                headerSubtitle: 'hidden',\n                socialButtonsBlockButton: 'border-app-stroke hover:bg-app-background',\n                dividerText: 'text-app-headline',\n                formFieldLabel: 'text-app-headline',\n                formFieldInput: 'border-app-stroke focus:border-app-main',\n                footerActionLink: 'text-app-main hover:text-app-highlight',\n              },\n            }}\n            redirectUrl=\"/dashboard\"\n            signUpUrl=\"/sign-up\"\n          />\n        </div>\n        \n        <div className=\"text-center mt-6\">\n          <p className=\"text-sm text-app-headline opacity-70\">\n            Don't have an account?{' '}\n            <a href=\"/sign-up\" className=\"text-app-main hover:text-app-highlight font-medium\">\n              Sign up here\n            </a>\n          </p>\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,ubAAC;QAAI,WAAU;kBACb,cAAA,ubAAC;YAAI,WAAU;;8BACb,ubAAC;oBAAI,WAAU;;sCACb,ubAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAC1D,ubAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAGvC,ubAAC;oBAAI,WAAU;8BACb,cAAA,ubAAC,sYAAA,CAAA,SAAM;wBACL,YAAY;4BACV,UAAU;gCACR,mBAAmB;gCACnB,MAAM;gCACN,aAAa;gCACb,gBAAgB;gCAChB,0BAA0B;gCAC1B,aAAa;gCACb,gBAAgB;gCAChB,gBAAgB;gCAChB,kBAAkB;4BACpB;wBACF;wBACA,aAAY;wBACZ,WAAU;;;;;;;;;;;8BAId,ubAAC;oBAAI,WAAU;8BACb,cAAA,ubAAC;wBAAE,WAAU;;4BAAuC;4BAC3B;0CACvB,ubAAC;gCAAE,MAAK;gCAAW,WAAU;0CAAqD;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9F", "debugId": null}}]}