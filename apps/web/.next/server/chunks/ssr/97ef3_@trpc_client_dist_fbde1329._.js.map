{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "objectSpread2-BvkFp-_Y.mjs", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/typeof.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/toPrimitive.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/toPropertyKey.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/defineProperty.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/objectSpread2.js"], "sourcesContent": ["function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _typeof(o);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nmodule.exports = toPrimitive, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nvar toPrimitive = require(\"./toPrimitive.js\");\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nmodule.exports = toPropertyKey, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var defineProperty = require(\"./defineProperty.js\");\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nmodule.exports = _objectSpread2, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": ["_typeof", "o", "_typeof", "toPrimitive", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "r"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAAA,SAASA,UAAQ,CAAA,EAAG;YAClB;YAEA,OAAO,OAAO,OAAA,GAAUA,YAAU,cAAA,OAAqB,UAAU,YAAA,OAAmB,OAAO,QAAA,GAAW,SAAUC,GAAAA,EAAG;gBACjH,OAAA,OAAcA;YACf,IAAG,SAAUA,GAAAA,EAAG;gBACf,OAAOA,OAAK,cAAA,OAAqB,UAAUA,IAAE,WAAA,KAAgB,UAAUA,QAAM,OAAO,SAAA,GAAY,WAAA,OAAkBA;YACnH,GAAE,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA,EAAS,UAAQ,EAAE;QAC5F;QACD,OAAO,OAAA,GAAUD,WAAS,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCT/F,IAAIE,YAAAA,gBAAAA,CAAiC,UAAA;QACrC,SAASC,cAAY,CAAA,EAAG,CAAA,EAAG;YACzB,IAAI,YAAY,UAAQ,EAAE,IAAA,CAAK,EAAG,CAAA,OAAO;YACzC,IAAI,IAAI,CAAA,CAAE,OAAO,WAAA,CAAA;YACjB,IAAA,KAAS,MAAM,GAAG;gBAChB,IAAI,IAAI,EAAE,IAAA,CAAK,GAAG,KAAK,UAAU;gBACjC,IAAI,YAAY,UAAQ,EAAE,CAAE,CAAA,OAAO;gBACnC,MAAM,IAAI,UAAU;YACrB;YACD,OAAO,CAAC,aAAa,IAAI,SAAS,MAAA,EAAQ,EAAE;QAC7C;QACD,OAAO,OAAA,GAAUA,eAAa,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCXnG,IAAI,UAAA,gBAAA,CAAiC,UAAA;QACrC,IAAI,cAAA;QACJ,SAASC,gBAAc,CAAA,EAAG;YACxB,IAAI,IAAI,YAAY,GAAG,SAAS;YAChC,OAAO,YAAY,QAAQ,EAAE,GAAG,IAAI,IAAI;QACzC;QACD,OAAO,OAAA,GAAUA,iBAAe,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCNrG,IAAI,gBAAA;QACJ,SAAS,gBAAgB,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;YAChC,OAAA,CAAQ,IAAI,cAAc,EAAE,KAAK,IAAI,OAAO,cAAA,CAAe,GAAG,GAAG;gBAC/D,OAAO;gBACP,YAAA,CAAa;gBACb,cAAA,CAAe;gBACf,UAAA,CAAW;YACZ,EAAC,GAAG,CAAA,CAAE,EAAA,GAAK,GAAG;QAChB;QACD,OAAO,OAAA,GAAU,iBAAiB,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCTvG,IAAI,iBAAA;QACJ,SAAS,QAAQ,CAAA,EAAG,CAAA,EAAG;YACrB,IAAI,IAAI,OAAO,IAAA,CAAK,EAAE;YACtB,IAAI,OAAO,qBAAA,EAAuB;gBAChC,IAAI,IAAI,OAAO,qBAAA,CAAsB,EAAE;gBACvC,KAAA,CAAM,IAAI,EAAE,MAAA,CAAO,SAAUC,GAAAA,EAAG;oBAC9B,OAAO,OAAO,wBAAA,CAAyB,GAAGA,IAAE,CAAC,UAAA;gBAC9C,EAAC,GAAG,EAAE,IAAA,CAAK,KAAA,CAAM,GAAG,EAAE;YACxB;YACD,OAAO;QACR;QACD,SAAS,eAAe,CAAA,EAAG;YACzB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAA,EAAQ,IAAK;gBACzC,IAAI,IAAI,QAAQ,SAAA,CAAU,EAAA,GAAK,SAAA,CAAU,EAAA,GAAK,CAAE;gBAChD,IAAI,IAAI,QAAQ,OAAO,EAAE,EAAA,CAAG,EAAE,CAAC,OAAA,CAAQ,SAAUA,GAAAA,EAAG;oBAClD,eAAe,GAAGA,KAAG,CAAA,CAAEA,IAAAA,CAAG;gBAC3B,EAAC,GAAG,OAAO,yBAAA,GAA4B,OAAO,gBAAA,CAAiB,GAAG,OAAO,yBAAA,CAA0B,EAAE,CAAC,GAAG,QAAQ,OAAO,EAAE,CAAC,CAAC,OAAA,CAAQ,SAAUA,GAAAA,EAAG;oBAChJ,OAAO,cAAA,CAAe,GAAGA,KAAG,OAAO,wBAAA,CAAyB,GAAGA,IAAE,CAAC;gBACnE,EAAC;YACH;YACD,OAAO;QACR;QACD,OAAO,OAAA,GAAU,gBAAgB,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA", "ignoreList": [0, 1, 2, 3, 4], "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "file": "splitLink-B7Cuf2c_.mjs", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/links/internals/createChain.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/links/splitLink.ts"], "sourcesContent": ["import { observable } from '@trpc/server/observable';\nimport type { AnyRouter } from '@trpc/server/unstable-core-do-not-import';\nimport type {\n  Operation,\n  OperationLink,\n  OperationResultObservable,\n} from '../types';\n\n/** @internal */\nexport function create<PERSON>hain<\n  TRouter extends AnyRouter,\n  TInput = unknown,\n  TOutput = unknown,\n>(opts: {\n  links: OperationLink<TRouter, TInput, TOutput>[];\n  op: Operation<TInput>;\n}): OperationResultObservable<TRouter, TOutput> {\n  return observable((observer) => {\n    function execute(index = 0, op = opts.op) {\n      const next = opts.links[index];\n      if (!next) {\n        throw new Error(\n          'No more links to execute - did you forget to add an ending link?',\n        );\n      }\n      const subscription = next({\n        op,\n        next(nextOp) {\n          const nextObserver = execute(index + 1, nextOp);\n\n          return nextObserver;\n        },\n      });\n      return subscription;\n    }\n\n    const obs$ = execute();\n    return obs$.subscribe(observer);\n  });\n}\n", "import { observable } from '@trpc/server/observable';\nimport type { AnyRouter } from '@trpc/server/unstable-core-do-not-import';\nimport { create<PERSON>hain } from './internals/createChain';\nimport type { Operation, TRPCLink } from './types';\n\nfunction asArray<TType>(value: TType | TType[]) {\n  return Array.isArray(value) ? value : [value];\n}\nexport function splitLink<TRouter extends AnyRouter = AnyRouter>(opts: {\n  condition: (op: Operation) => boolean;\n  /**\n   * The link to execute next if the test function returns `true`.\n   */\n  true: TRPCLink<TRouter> | TRPCLink<TRouter>[];\n  /**\n   * The link to execute next if the test function returns `false`.\n   */\n  false: TRPCLink<TRouter> | TRPCLink<TRouter>[];\n}): TRPCLink<TRouter> {\n  return (runtime) => {\n    const yes = asArray(opts.true).map((link) => link(runtime));\n    const no = asArray(opts.false).map((link) => link(runtime));\n    return (props) => {\n      return observable((observer) => {\n        const links = opts.condition(props.op) ? yes : no;\n        return createChain({ op: props.op, links }).subscribe(observer);\n      });\n    };\n  };\n}\n"], "names": ["opts: {\n  links: OperationLink<TRouter, TInput, TOutput>[];\n  op: Operation<TInput>;\n}", "value: TType | TType[]", "opts: {\n  condition: (op: Operation) => boolean;\n  /**\n   * The link to execute next if the test function returns `true`.\n   */\n  true: TRPCLink<TRouter> | TRPCLink<TRouter>[];\n  /**\n   * The link to execute next if the test function returns `false`.\n   */\n  false: TRPCLink<TRouter> | TRPCLink<TRouter>[];\n}"], "mappings": ";;;;;;;iBASA,SAAgB,YAIdA,IAAAA,EAG8C;IAC9C,uQAAO,aAAA,EAAW,CAAC,aAAa;QAC9B,SAAS,QAAQ,QAAQ,CAAA,EAAG,KAAK,KAAK,EAAA,EAAI;YACxC,MAAM,OAAO,KAAK,KAAA,CAAM,MAAA;YACxB,IAAA,CAAK,KACH,CAAA,MAAM,IAAI,MACR;YAGJ,MAAM,eAAe,KAAK;gBACxB;gBACA,MAAK,MAAA,EAAQ;oBACX,MAAM,eAAe,QAAQ,QAAQ,GAAG,OAAO;oBAE/C,OAAO;gBACR;YACF,EAAC;YACF,OAAO;QACR;QAED,MAAM,OAAO,SAAS;QACtB,OAAO,KAAK,SAAA,CAAU,SAAS;IAChC,EAAC;AACH;;;AClCD,SAAS,QAAeC,KAAAA,EAAwB;IAC9C,OAAO,MAAM,OAAA,CAAQ,MAAM,GAAG,QAAQ;QAAC,KAAM;KAAA;AAC9C;AACD,SAAgB,UAAiDC,IAAAA,EAU3C;IACpB,OAAO,CAAC,YAAY;QAClB,MAAM,MAAM,QAAQ,KAAK,IAAA,CAAK,CAAC,GAAA,CAAI,CAAC,OAAS,KAAK,QAAQ,CAAC;QAC3D,MAAM,KAAK,QAAQ,KAAK,KAAA,CAAM,CAAC,GAAA,CAAI,CAAC,OAAS,KAAK,QAAQ,CAAC;QAC3D,OAAO,CAAC,UAAU;YAChB,QAAO,4QAAA,EAAW,CAAC,aAAa;gBAC9B,MAAM,QAAQ,KAAK,SAAA,CAAU,MAAM,EAAA,CAAG,GAAG,MAAM;gBAC/C,OAAO,YAAY;oBAAE,IAAI,MAAM,EAAA;oBAAI;gBAAO,EAAC,CAAC,SAAA,CAAU,SAAS;YAChE,EAAC;QACH;IACF;AACF", "ignoreList": [0, 1], "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "file": "TRPCClientError-CjKyS10w.mjs", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/TRPCClientError.ts"], "sourcesContent": ["import type {\n  inferClientTypes,\n  InferrableClientTypes,\n  Maybe,\n  TRPCErrorResponse,\n} from '@trpc/server/unstable-core-do-not-import';\nimport {\n  isObject,\n  type DefaultErrorShape,\n} from '@trpc/server/unstable-core-do-not-import';\n\ntype inferErrorShape<TInferrable extends InferrableClientTypes> =\n  inferClientTypes<TInferrable>['errorShape'];\nexport interface TRPCClientErrorBase<TShape extends DefaultErrorShape> {\n  readonly message: string;\n  readonly shape: Maybe<TShape>;\n  readonly data: Maybe<TShape['data']>;\n}\nexport type TRPCClientErrorLike<TInferrable extends InferrableClientTypes> =\n  TRPCClientErrorBase<inferErrorShape<TInferrable>>;\n\nexport function isTRPCClientError<TInferrable extends InferrableClientTypes>(\n  cause: unknown,\n): cause is TRPCClientError<TInferrable> {\n  return cause instanceof TRPCClientError;\n}\n\nfunction isTRPCErrorResponse(obj: unknown): obj is TRPCErrorResponse<any> {\n  return (\n    isObject(obj) &&\n    isObject(obj['error']) &&\n    typeof obj['error']['code'] === 'number' &&\n    typeof obj['error']['message'] === 'string'\n  );\n}\n\nfunction getMessageFromUnknownError(err: unknown, fallback: string): string {\n  if (typeof err === 'string') {\n    return err;\n  }\n  if (isObject(err) && typeof err['message'] === 'string') {\n    return err['message'];\n  }\n  return fallback;\n}\n\nexport class TRPCClientError<TRouterOrProcedure extends InferrableClientTypes>\n  extends Error\n  implements TRPCClientErrorBase<inferErrorShape<TRouterOrProcedure>>\n{\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore override doesn't work in all environments due to \"This member cannot have an 'override' modifier because it is not declared in the base class 'Error'\"\n  public override readonly cause;\n  public readonly shape: Maybe<inferErrorShape<TRouterOrProcedure>>;\n  public readonly data: Maybe<inferErrorShape<TRouterOrProcedure>['data']>;\n\n  /**\n   * Additional meta data about the error\n   * In the case of HTTP-errors, we'll have `response` and potentially `responseJSON` here\n   */\n  public meta;\n\n  constructor(\n    message: string,\n    opts?: {\n      result?: Maybe<TRPCErrorResponse<inferErrorShape<TRouterOrProcedure>>>;\n      cause?: Error;\n      meta?: Record<string, unknown>;\n    },\n  ) {\n    const cause = opts?.cause;\n\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore https://github.com/tc39/proposal-error-cause\n    super(message, { cause });\n\n    this.meta = opts?.meta;\n\n    this.cause = cause;\n    this.shape = opts?.result?.error;\n    this.data = opts?.result?.error.data;\n    this.name = 'TRPCClientError';\n\n    Object.setPrototypeOf(this, TRPCClientError.prototype);\n  }\n\n  public static from<TRouterOrProcedure extends InferrableClientTypes>(\n    _cause: Error | TRPCErrorResponse<any> | object,\n    opts: { meta?: Record<string, unknown> } = {},\n  ): TRPCClientError<TRouterOrProcedure> {\n    const cause = _cause as unknown;\n\n    if (isTRPCClientError(cause)) {\n      if (opts.meta) {\n        // Decorate with meta error data\n        cause.meta = {\n          ...cause.meta,\n          ...opts.meta,\n        };\n      }\n      return cause;\n    }\n    if (isTRPCErrorResponse(cause)) {\n      return new TRPCClientError(cause.error.message, {\n        ...opts,\n        result: cause,\n      });\n    }\n    return new TRPCClientError(\n      getMessageFromUnknownError(cause, 'Unknown error'),\n      {\n        ...opts,\n        cause: cause as any,\n      },\n    );\n  }\n}\n"], "names": ["cause: unknown", "obj: unknown", "err: unknown", "fallback: string", "message: string", "opts?: {\n      result?: Maybe<TRPCErrorResponse<inferErrorShape<TRouterOrProcedure>>>;\n      cause?: Error;\n      meta?: Record<string, unknown>;\n    }", "_cause: Error | TRPCErrorResponse<any> | object", "opts: { meta?: Record<string, unknown> }"], "mappings": ";;;;;;;;;;;AAqBA,SAAgB,kBACdA,KAAAA,EACuC;IACvC,OAAO,iBAAiB;AACzB;AAED,SAAS,oBAAoBC,GAAAA,EAA6C;IACxE,kQACE,WAAA,EAAS,IAAI,QACb,kQAAA,EAAS,GAAA,CAAI,QAAA,CAAS,IAAA,OACf,GAAA,CAAI,QAAA,CAAS,OAAA,KAAY,YAAA,OACzB,GAAA,CAAI,QAAA,CAAS,UAAA,KAAe;AAEtC;AAED,SAAS,2BAA2BC,GAAAA,EAAcC,QAAAA,EAA0B;IAC1E,IAAA,OAAW,QAAQ,SACjB,CAAA,OAAO;IAET,+PAAI,WAAA,EAAS,IAAI,IAAA,OAAW,GAAA,CAAI,UAAA,KAAe,SAC7C,CAAA,OAAO,GAAA,CAAI,UAAA;IAEb,OAAO;AACR;AAED,IAAa,kBAAb,MAAa,wBACH,MAEV;IAaE,YACEC,OAAAA,EACAC,IAAAA,CAKA;;QACA,MAAM,QAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAQ,KAAM,KAAA;QAIpB,KAAA,CAAM,SAAS;YAAE;QAAO,EAAC;2CA2C1B,IAAA,EAjEwB,SAAA,KAAA;2CAiEvB,IAAA,EAhEc,SAAA,KAAA;2CAgEb,IAAA,EA/Da,QAAA,KAAA;2CA+DZ,IAAA,EAzDG,QAAA,KAAA;QAgBL,IAAA,CAAK,IAAA,GAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAO,KAAM,IAAA;QAElB,IAAA,CAAK,KAAA,GAAQ;QACb,IAAA,CAAK,KAAA,GAAA,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,eAAQ,KAAM,MAAA,MAAA,QAAA,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAQ,KAAA;QAC3B,IAAA,CAAK,IAAA,GAAA,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,gBAAO,KAAM,MAAA,MAAA,QAAA,kBAAA,KAAA,IAAA,KAAA,IAAA,cAAQ,KAAA,CAAM,IAAA;QAChC,IAAA,CAAK,IAAA,GAAO;QAEZ,OAAO,cAAA,CAAe,IAAA,EAAM,gBAAgB,SAAA,CAAU;IACvD;IAED,OAAc,KACZC,MAAAA,EACAC,OAA2C,CAAE,CAAA,EACR;QACrC,MAAM,QAAQ;QAEd,IAAI,kBAAkB,MAAM,EAAE;YAC5B,IAAI,KAAK,IAAA,CAEP,CAAA,MAAM,IAAA,GAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACD,MAAM,IAAA,GACN,KAAK,IAAA;YAGZ,OAAO;QACR;QACD,IAAI,oBAAoB,MAAM,CAC5B,CAAA,OAAO,IAAI,gBAAgB,MAAM,KAAA,CAAM,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAClC,OAAA,CAAA,GAAA;YACH,QAAQ;QAAA;QAGZ,OAAO,IAAI,gBACT,2BAA2B,OAAO,gBAAgB,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAE7C,OAAA,CAAA,GAAA;YACI;QAAA;IAGZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "file": "unstable-internals-Bg7n9BBj.mjs", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/internals/transformer.ts"], "sourcesContent": ["import type {\n  AnyClientTypes,\n  CombinedDataTransformer,\n  DataTransformerOptions,\n  TypeError,\n} from '@trpc/server/unstable-core-do-not-import';\n\n/**\n * @internal\n */\nexport type CoercedTransformerParameters = {\n  transformer?: DataTransformerOptions;\n};\n\ntype TransformerOptionYes = {\n  /**\n   * Data transformer\n   *\n   * You must use the same transformer on the backend and frontend\n   * @see https://trpc.io/docs/v11/data-transformers\n   **/\n  transformer: DataTransformerOptions;\n};\ntype TransformerOptionNo = {\n  /**\n   * Data transformer\n   *\n   * You must use the same transformer on the backend and frontend\n   * @see https://trpc.io/docs/v11/data-transformers\n   **/\n  transformer?: TypeError<'You must define a transformer on your your `initTRPC`-object first'>;\n};\n\n/**\n * @internal\n */\nexport type TransformerOptions<\n  TRoot extends Pick<AnyClientTypes, 'transformer'>,\n> = TRoot['transformer'] extends true\n  ? TransformerOptionYes\n  : TransformerOptionNo;\n/**\n * @internal\n */\n\n/**\n * @internal\n */\nexport function getTransformer(\n  transformer:\n    | TransformerOptions<{ transformer: false }>['transformer']\n    | TransformerOptions<{ transformer: true }>['transformer']\n    | undefined,\n): CombinedDataTransformer {\n  const _transformer =\n    transformer as CoercedTransformerParameters['transformer'];\n  if (!_transformer) {\n    return {\n      input: {\n        serialize: (data) => data,\n        deserialize: (data) => data,\n      },\n      output: {\n        serialize: (data) => data,\n        deserialize: (data) => data,\n      },\n    };\n  }\n  if ('input' in _transformer) {\n    return _transformer;\n  }\n  return {\n    input: _transformer,\n    output: _transformer,\n  };\n}\n"], "names": ["transformer:\n    | TransformerOptions<{ transformer: false }>['transformer']\n    | TransformerOptions<{ transformer: true }>['transformer']\n    | undefined"], "mappings": ";;;;;;;;AAgDA,SAAgB,eACdA,WAAAA,EAIyB;IACzB,MAAM,eACJ;IACF,IAAA,CAAK,aACH,CAAA,OAAO;QACL,OAAO;YACL,WAAW,CAAC,OAAS;YACrB,aAAa,CAAC,OAAS;QACxB;QACD,QAAQ;YACN,WAAW,CAAC,OAAS;YACrB,aAAa,CAAC,OAAS;QACxB;IACF;IAEH,IAAI,WAAW,aACb,CAAA,OAAO;IAET,OAAO;QACL,OAAO;QACP,QAAQ;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "file": "httpUtils-Bkv1johT.mjs", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/getFetch.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/links/internals/httpUtils.ts"], "sourcesContent": ["import type { FetchEsque, NativeFetchEsque } from './internals/types';\n\ntype AnyFn = (...args: any[]) => unknown;\n\nconst isFunction = (fn: unknown): fn is AnyFn => typeof fn === 'function';\n\nexport function getFetch(\n  customFetchImpl?: FetchEsque | NativeFetchEsque,\n): FetchEsque {\n  if (customFetchImpl) {\n    return customFetchImpl as FetchEsque;\n  }\n\n  if (typeof window !== 'undefined' && isFunction(window.fetch)) {\n    return window.fetch as FetchEsque;\n  }\n\n  if (typeof globalThis !== 'undefined' && isFunction(globalThis.fetch)) {\n    return globalThis.fetch as FetchEsque;\n  }\n\n  throw new Error('No fetch implementation found');\n}\n", "import type {\n  AnyClientTypes,\n  CombinedDataTransformer,\n  Maybe,\n  ProcedureType,\n  TRPCAcceptHeader,\n  TRPCResponse,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { getFetch } from '../../getFetch';\nimport type {\n  FetchEsque,\n  RequestInitEsque,\n  ResponseEsque,\n} from '../../internals/types';\nimport type { TransformerOptions } from '../../unstable-internals';\nimport { getTransformer } from '../../unstable-internals';\nimport type { HTTPHeaders } from '../types';\n\n/**\n * @internal\n */\nexport type HTTPLinkBaseOptions<\n  TRoot extends Pick<AnyClientTypes, 'transformer'>,\n> = {\n  url: string | URL;\n  /**\n   * Add ponyfill for fetch\n   */\n  fetch?: FetchEsque;\n  /**\n   * Send all requests `as POST`s requests regardless of the procedure type\n   * The HTTP handler must separately allow overriding the method. See:\n   * @see https://trpc.io/docs/rpc\n   */\n  methodOverride?: 'POST';\n} & TransformerOptions<TRoot>;\n\nexport interface ResolvedHTTPLinkOptions {\n  url: string;\n  fetch?: FetchEsque;\n  transformer: CombinedDataTransformer;\n  methodOverride?: 'POST';\n}\n\nexport function resolveHTTPLinkOptions(\n  opts: HTTPLinkBaseOptions<AnyClientTypes>,\n): ResolvedHTTPLinkOptions {\n  return {\n    url: opts.url.toString(),\n    fetch: opts.fetch,\n    transformer: getTransformer(opts.transformer),\n    methodOverride: opts.methodOverride,\n  };\n}\n\n// https://github.com/trpc/trpc/pull/669\nfunction arrayToDict(array: unknown[]) {\n  const dict: Record<number, unknown> = {};\n  for (let index = 0; index < array.length; index++) {\n    const element = array[index];\n    dict[index] = element;\n  }\n  return dict;\n}\n\nconst METHOD = {\n  query: 'GET',\n  mutation: 'POST',\n  subscription: 'PATCH',\n} as const;\n\nexport interface HTTPResult {\n  json: TRPCResponse;\n  meta: {\n    response: ResponseEsque;\n    responseJSON?: unknown;\n  };\n}\n\ntype GetInputOptions = {\n  transformer: CombinedDataTransformer;\n} & ({ input: unknown } | { inputs: unknown[] });\n\nexport function getInput(opts: GetInputOptions) {\n  return 'input' in opts\n    ? opts.transformer.input.serialize(opts.input)\n    : arrayToDict(\n        opts.inputs.map((_input) => opts.transformer.input.serialize(_input)),\n      );\n}\n\nexport type HTTPBaseRequestOptions = GetInputOptions &\n  ResolvedHTTPLinkOptions & {\n    type: ProcedureType;\n    path: string;\n    signal: Maybe<AbortSignal>;\n  };\n\ntype GetUrl = (opts: HTTPBaseRequestOptions) => string;\ntype GetBody = (opts: HTTPBaseRequestOptions) => RequestInitEsque['body'];\n\nexport type ContentOptions = {\n  trpcAcceptHeader?: TRPCAcceptHeader;\n  contentTypeHeader?: string;\n  getUrl: GetUrl;\n  getBody: GetBody;\n};\n\nexport const getUrl: GetUrl = (opts) => {\n  const parts = opts.url.split('?') as [string, string?];\n  const base = parts[0].replace(/\\/$/, ''); // Remove any trailing slashes\n\n  let url = base + '/' + opts.path;\n  const queryParts: string[] = [];\n\n  if (parts[1]) {\n    queryParts.push(parts[1]);\n  }\n  if ('inputs' in opts) {\n    queryParts.push('batch=1');\n  }\n  if (opts.type === 'query' || opts.type === 'subscription') {\n    const input = getInput(opts);\n    if (input !== undefined && opts.methodOverride !== 'POST') {\n      queryParts.push(`input=${encodeURIComponent(JSON.stringify(input))}`);\n    }\n  }\n  if (queryParts.length) {\n    url += '?' + queryParts.join('&');\n  }\n  return url;\n};\n\nexport const getBody: GetBody = (opts) => {\n  if (opts.type === 'query' && opts.methodOverride !== 'POST') {\n    return undefined;\n  }\n  const input = getInput(opts);\n  return input !== undefined ? JSON.stringify(input) : undefined;\n};\n\nexport type Requester = (\n  opts: HTTPBaseRequestOptions & {\n    headers: () => HTTPHeaders | Promise<HTTPHeaders>;\n  },\n) => Promise<HTTPResult>;\n\nexport const jsonHttpRequester: Requester = (opts) => {\n  return httpRequest({\n    ...opts,\n    contentTypeHeader: 'application/json',\n    getUrl,\n    getBody,\n  });\n};\n\n/**\n * Polyfill for DOMException with AbortError name\n */\nclass AbortError extends Error {\n  constructor() {\n    const name = 'AbortError';\n    super(name);\n    this.name = name;\n    this.message = name;\n  }\n}\n\nexport type HTTPRequestOptions = ContentOptions &\n  HTTPBaseRequestOptions & {\n    headers: () => HTTPHeaders | Promise<HTTPHeaders>;\n  };\n\n/**\n * Polyfill for `signal.throwIfAborted()`\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/throwIfAborted\n */\nconst throwIfAborted = (signal: Maybe<AbortSignal>) => {\n  if (!signal?.aborted) {\n    return;\n  }\n  // If available, use the native implementation\n  signal.throwIfAborted?.();\n\n  // If we have `DOMException`, use it\n  if (typeof DOMException !== 'undefined') {\n    throw new DOMException('AbortError', 'AbortError');\n  }\n\n  // Otherwise, use our own implementation\n  throw new AbortError();\n};\n\nexport async function fetchHTTPResponse(opts: HTTPRequestOptions) {\n  throwIfAborted(opts.signal);\n\n  const url = opts.getUrl(opts);\n  const body = opts.getBody(opts);\n  const { type } = opts;\n  const resolvedHeaders = await (async () => {\n    const heads = await opts.headers();\n    if (Symbol.iterator in heads) {\n      return Object.fromEntries(heads);\n    }\n    return heads;\n  })();\n  const headers = {\n    ...(opts.contentTypeHeader\n      ? { 'content-type': opts.contentTypeHeader }\n      : {}),\n    ...(opts.trpcAcceptHeader\n      ? { 'trpc-accept': opts.trpcAcceptHeader }\n      : undefined),\n    ...resolvedHeaders,\n  };\n\n  return getFetch(opts.fetch)(url, {\n    method: opts.methodOverride ?? METHOD[type],\n    signal: opts.signal,\n    body,\n    headers,\n  });\n}\n\nexport async function httpRequest(\n  opts: HTTPRequestOptions,\n): Promise<HTTPResult> {\n  const meta = {} as HTTPResult['meta'];\n\n  const res = await fetchHTTPResponse(opts);\n  meta.response = res;\n\n  const json = await res.json();\n\n  meta.responseJSON = json;\n\n  return {\n    json: json as TRPCResponse,\n    meta,\n  };\n}\n"], "names": ["fn: unknown", "customFetchImpl?: FetchEsque | NativeFetchEsque", "opts: HTTPLinkBaseOptions<AnyClientTypes>", "array: unknown[]", "dict: Record<number, unknown>", "opts: GetInputOptions", "getUrl: GetUrl", "queryParts: string[]", "getBody: GetBody", "jsonHttpRequester: Requester", "signal: Maybe<AbortSignal>", "opts: HTTPRequestOptions"], "mappings": ";;;;;;;;;;;;;;AAIA,MAAM,aAAa,CAACA,KAAAA,OAAoC,OAAO;AAE/D,SAAgB,SACdC,eAAAA,EACY;IACZ,IAAI,gBACF,CAAA,OAAO;IAGT,IAAA,OAAW,WAAW,eAAe,WAAW,OAAO,KAAA,CAAM,CAC3D,CAAA,OAAO,OAAO,KAAA;IAGhB,IAAA,OAAW,eAAe,eAAe,WAAW,WAAW,KAAA,CAAM,CACnE,CAAA,OAAO,WAAW,KAAA;IAGpB,MAAM,IAAI,MAAM;AACjB;;;;ACsBD,SAAgB,uBACdC,IAAAA,EACyB;IACzB,OAAO;QACL,KAAK,KAAK,GAAA,CAAI,QAAA,EAAU;QACxB,OAAO,KAAK,KAAA;QACZ,yVAAa,iBAAA,EAAe,KAAK,WAAA,CAAY;QAC7C,gBAAgB,KAAK,cAAA;IACtB;AACF;AAGD,SAAS,YAAYC,KAAAA,EAAkB;IACrC,MAAMC,OAAgC,CAAE;IACxC,IAAK,IAAI,QAAQ,GAAG,QAAQ,MAAM,MAAA,EAAQ,QAAS;QACjD,MAAM,UAAU,KAAA,CAAM,MAAA;QACtB,IAAA,CAAK,MAAA,GAAS;IACf;IACD,OAAO;AACR;AAED,MAAM,SAAS;IACb,OAAO;IACP,UAAU;IACV,cAAc;AACf;AAcD,SAAgB,SAASC,IAAAA,EAAuB;IAC9C,OAAO,WAAW,OACd,KAAK,WAAA,CAAY,KAAA,CAAM,SAAA,CAAU,KAAK,KAAA,CAAM,GAC5C,YACE,KAAK,MAAA,CAAO,GAAA,CAAI,CAAC,SAAW,KAAK,WAAA,CAAY,KAAA,CAAM,SAAA,CAAU,OAAO,CAAC,CACtE;AACN;AAmBD,MAAaC,SAAiB,CAAC,SAAS;IACtC,MAAM,QAAQ,KAAK,GAAA,CAAI,KAAA,CAAM,IAAI;IACjC,MAAM,OAAO,KAAA,CAAM,EAAA,CAAG,OAAA,CAAQ,OAAO,GAAG;IAExC,IAAI,MAAM,OAAO,MAAM,KAAK,IAAA;IAC5B,MAAMC,aAAuB,CAAE,CAAA;IAE/B,IAAI,KAAA,CAAM,EAAA,CACR,CAAA,WAAW,IAAA,CAAK,KAAA,CAAM,EAAA,CAAG;IAE3B,IAAI,YAAY,KACd,CAAA,WAAW,IAAA,CAAK,UAAU;IAE5B,IAAI,KAAK,IAAA,KAAS,WAAW,KAAK,IAAA,KAAS,gBAAgB;QACzD,MAAM,QAAQ,SAAS,KAAK;QAC5B,IAAI,UAAA,KAAA,KAAuB,KAAK,cAAA,KAAmB,OACjD,CAAA,WAAW,IAAA,CAAA,CAAM,MAAA,EAAQ,mBAAmB,KAAK,SAAA,CAAU,MAAM,CAAC,CAAC,CAAA,CAAE;IAExE;IACD,IAAI,WAAW,MAAA,CACb,CAAA,OAAO,MAAM,WAAW,IAAA,CAAK,IAAI;IAEnC,OAAO;AACR;AAED,MAAaC,UAAmB,CAAC,SAAS;IACxC,IAAI,KAAK,IAAA,KAAS,WAAW,KAAK,cAAA,KAAmB,OACnD,CAAA,OAAA,KAAA;IAEF,MAAM,QAAQ,SAAS,KAAK;IAC5B,OAAO,UAAA,KAAA,IAAsB,KAAK,SAAA,CAAU,MAAM,GAAA,KAAA;AACnD;AAQD,MAAaC,oBAA+B,CAAC,SAAS;IACpD,OAAO,YAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACF,OAAA,CAAA,GAAA;QACH,mBAAmB;QACnB;QACA;OACA;AACH;;;GAKD,IAAM,aAAN,cAAyB,MAAM;IAC7B,aAAc;QACZ,MAAM,OAAO;QACb,KAAA,CAAM,KAAK;QACX,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,OAAA,GAAU;IAChB;AACF;;;;;GAYD,MAAM,iBAAiB,CAACC,WAA+B;;IACrD,IAAA,CAAA,CAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAK,OAAQ,OAAA,EACX,CAAA;IAGF,CAAA,wBAAA,OAAO,cAAA,MAAA,QAAA,0BAAA,KAAA,KAAP,sBAAA,IAAA,CAAA,OAAyB;IAGzB,IAAA,OAAW,iBAAiB,YAC1B,CAAA,MAAM,IAAI,aAAa,cAAc;IAIvC,MAAM,IAAI;AACX;AAED,eAAsB,kBAAkBC,IAAAA,EAA0B;;IAChE,eAAe,KAAK,MAAA,CAAO;IAE3B,MAAM,MAAM,KAAK,MAAA,CAAO,KAAK;IAC7B,MAAM,OAAO,KAAK,OAAA,CAAQ,KAAK;IAC/B,MAAM,EAAE,IAAA,EAAM,GAAG;IACjB,MAAM,kBAAkB,MAAM,CAAC,YAAY;QACzC,MAAM,QAAQ,MAAM,KAAK,OAAA,EAAS;QAClC,IAAI,OAAO,QAAA,IAAY,MACrB,CAAA,OAAO,OAAO,WAAA,CAAY,MAAM;QAElC,OAAO;IACR,CAAA,GAAG;IACJ,MAAM,UAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACA,KAAK,iBAAA,GACL;QAAE,gBAAgB,KAAK,iBAAA;IAAmB,IAC1C,CAAE,IACF,KAAK,gBAAA,GACL;QAAE,eAAe,KAAK,gBAAA;IAAkB,IAAA,KAAA,IAEzC;IAGL,OAAO,SAAS,KAAK,KAAA,CAAM,CAAC,KAAK;QAC/B,QAAA,CAAA,uBAAQ,KAAK,cAAA,MAAA,QAAA,yBAAA,KAAA,IAAA,uBAAkB,MAAA,CAAO,KAAA;QACtC,QAAQ,KAAK,MAAA;QACb;QACA;IACD,EAAC;AACH;AAED,eAAsB,YACpBA,IAAAA,EACqB;IACrB,MAAM,OAAO,CAAE;IAEf,MAAM,MAAM,MAAM,kBAAkB,KAAK;IACzC,KAAK,QAAA,GAAW;IAEhB,MAAM,OAAO,MAAM,IAAI,IAAA,EAAM;IAE7B,KAAK,YAAA,GAAe;IAEpB,OAAO;QACC;QACN;IACD;AACF", "ignoreList": [0, 1], "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "file": "httpLink-CYOcG9kQ.mjs", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/links/internals/contentTypes.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/links/httpLink.ts"], "sourcesContent": ["export function isOctetType(input: unknown) {\n  return (\n    input instanceof Uint8Array ||\n    // File extends from Blob but is only available in nodejs from v20\n    input instanceof Blob\n  );\n}\n\nexport function isFormData(input: unknown) {\n  return input instanceof FormData;\n}\n\nexport function isNonJsonSerializable(input: unknown) {\n  return isOctetType(input) || isFormData(input);\n}\n", "import { observable } from '@trpc/server/observable';\nimport type {\n  AnyClientTypes,\n  AnyRouter,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { transformResult } from '@trpc/server/unstable-core-do-not-import';\nimport { TRPCClientError } from '../TRPCClientError';\nimport type {\n  HTTPLinkBaseOptions,\n  HTTPResult,\n  Requester,\n} from './internals/httpUtils';\nimport {\n  getUrl,\n  httpRequest,\n  jsonHttpRequester,\n  resolveHTTPLinkOptions,\n} from './internals/httpUtils';\nimport {\n  isFormData,\n  isOctetType,\n  type HTTPHeaders,\n  type Operation,\n  type TRPCLink,\n} from './types';\n\nexport type HTTPLinkOptions<TRoot extends AnyClientTypes> =\n  HTTPLinkBaseOptions<TRoot> & {\n    /**\n     * Headers to be set on outgoing requests or a callback that of said headers\n     * @see http://trpc.io/docs/client/headers\n     */\n    headers?:\n      | HTTPHeaders\n      | ((opts: { op: Operation }) => HTTPHeaders | Promise<HTTPHeaders>);\n  };\n\nconst universalRequester: Requester = (opts) => {\n  if ('input' in opts) {\n    const { input } = opts;\n    if (isFormData(input)) {\n      if (opts.type !== 'mutation' && opts.methodOverride !== 'POST') {\n        throw new Error('FormData is only supported for mutations');\n      }\n\n      return httpRequest({\n        ...opts,\n        // The browser will set this automatically and include the boundary= in it\n        contentTypeHeader: undefined,\n        getUrl,\n        getBody: () => input,\n      });\n    }\n\n    if (isOctetType(input)) {\n      if (opts.type !== 'mutation' && opts.methodOverride !== 'POST') {\n        throw new Error('Octet type input is only supported for mutations');\n      }\n\n      return httpRequest({\n        ...opts,\n        contentTypeHeader: 'application/octet-stream',\n        getUrl,\n        getBody: () => input,\n      });\n    }\n  }\n\n  return jsonHttpRequester(opts);\n};\n\n/**\n * @see https://trpc.io/docs/client/links/httpLink\n */\nexport function httpLink<TRouter extends AnyRouter = AnyRouter>(\n  opts: HTTPLinkOptions<TRouter['_def']['_config']['$types']>,\n): TRPCLink<TRouter> {\n  const resolvedOpts = resolveHTTPLinkOptions(opts);\n  return () => {\n    return ({ op }) => {\n      return observable((observer) => {\n        const { path, input, type } = op;\n        /* istanbul ignore if -- @preserve */\n        if (type === 'subscription') {\n          throw new Error(\n            'Subscriptions are unsupported by `httpLink` - use `httpSubscriptionLink` or `wsLink`',\n          );\n        }\n\n        const request = universalRequester({\n          ...resolvedOpts,\n          type,\n          path,\n          input,\n          signal: op.signal,\n          headers() {\n            if (!opts.headers) {\n              return {};\n            }\n            if (typeof opts.headers === 'function') {\n              return opts.headers({\n                op,\n              });\n            }\n            return opts.headers;\n          },\n        });\n        let meta: HTTPResult['meta'] | undefined = undefined;\n        request\n          .then((res) => {\n            meta = res.meta;\n            const transformed = transformResult(\n              res.json,\n              resolvedOpts.transformer.output,\n            );\n\n            if (!transformed.ok) {\n              observer.error(\n                TRPCClientError.from(transformed.error, {\n                  meta,\n                }),\n              );\n              return;\n            }\n            observer.next({\n              context: res.meta,\n              result: transformed.result,\n            });\n            observer.complete();\n          })\n          .catch((cause) => {\n            observer.error(TRPCClientError.from(cause, { meta }));\n          });\n\n        return () => {\n          // noop\n        };\n      });\n    };\n  };\n}\n"], "names": ["input: unknown", "universalRequester: Requester", "opts: HTTPLinkOptions<TRouter['_def']['_config']['$types']>", "meta: HTTPResult['meta'] | undefined"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,SAAgB,YAAYA,KAAAA,EAAgB;IAC1C,OACE,iBAAiB,cAEjB,iBAAiB;AAEpB;AAED,SAAgB,WAAWA,KAAAA,EAAgB;IACzC,OAAO,iBAAiB;AACzB;AAED,SAAgB,sBAAsBA,KAAAA,EAAgB;IACpD,OAAO,YAAY,MAAM,IAAI,WAAW,MAAM;AAC/C;;;;ACuBD,MAAMC,qBAAgC,CAAC,SAAS;IAC9C,IAAI,WAAW,MAAM;QACnB,MAAM,EAAE,KAAA,EAAO,GAAG;QAClB,IAAI,WAAW,MAAM,EAAE;YACrB,IAAI,KAAK,IAAA,KAAS,cAAc,KAAK,cAAA,KAAmB,OACtD,CAAA,MAAM,IAAI,MAAM;YAGlB,uUAAO,cAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACF,OAAA,CAAA,GAAA;gBAEH,mBAAA,KAAA;oVACA,SAAA;gBACA,SAAS,IAAM;eACf;QACH;QAED,IAAI,YAAY,MAAM,EAAE;YACtB,IAAI,KAAK,IAAA,KAAS,cAAc,KAAK,cAAA,KAAmB,OACtD,CAAA,MAAM,IAAI,MAAM;YAGlB,uUAAO,cAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACF,OAAA,CAAA,GAAA;gBACH,mBAAmB;oVACnB,SAAA;gBACA,SAAS,IAAM;eACf;QACH;IACF;IAED,uUAAO,oBAAA,EAAkB,KAAK;AAC/B;;;GAKD,SAAgB,SACdC,IAAAA,EACmB;IACnB,MAAM,eAAe,yVAAA,EAAuB,KAAK;IACjD,OAAO,MAAM;QACX,OAAO,CAAC,EAAE,EAAA,EAAI,KAAK;YACjB,uQAAO,aAAA,EAAW,CAAC,aAAa;gBAC9B,MAAM,EAAE,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,GAAG;sDAE9B,IAAI,SAAS,eACX,CAAA,MAAM,IAAI,MACR;gBAIJ,MAAM,UAAU,mBAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACX,eAAA,CAAA,GAAA;oBACH;oBACA;oBACA;oBACA,QAAQ,GAAG,MAAA;oBACX,UAAU;wBACR,IAAA,CAAK,KAAK,OAAA,CACR,CAAA,OAAO,CAAE;wBAEX,IAAA,OAAW,KAAK,OAAA,KAAY,WAC1B,CAAA,OAAO,KAAK,OAAA,CAAQ;4BAClB;wBACD,EAAC;wBAEJ,OAAO,KAAK,OAAA;oBACb;mBACD;gBACF,IAAIC,OAAAA,KAAAA;gBACJ,QACG,IAAA,CAAK,CAAC,QAAQ;oBACb,OAAO,IAAI,IAAA;oBACX,MAAM,eAAc,8QAAA,EAClB,IAAI,IAAA,EACJ,aAAa,WAAA,CAAY,MAAA,CAC1B;oBAED,IAAA,CAAK,YAAY,EAAA,EAAI;wBACnB,SAAS,KAAA,mUACP,kBAAA,CAAgB,IAAA,CAAK,YAAY,KAAA,EAAO;4BACtC;wBACD,EAAC,CACH;wBACD;oBACD;oBACD,SAAS,IAAA,CAAK;wBACZ,SAAS,IAAI,IAAA;wBACb,QAAQ,YAAY,MAAA;oBACrB,EAAC;oBACF,SAAS,QAAA,EAAU;gBACpB,EAAC,CACD,KAAA,CAAM,CAAC,UAAU;oBAChB,SAAS,KAAA,mUAAM,kBAAA,CAAgB,IAAA,CAAK,OAAO;wBAAE;oBAAM,EAAC,CAAC;gBACtD,EAAC;gBAEJ,OAAO,KAEN,CAFY;YAGd,EAAC;QACH;IACF;AACF", "ignoreList": [0, 1], "debugId": null}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "file": "httpBatchLink-CA96-gnJ.mjs", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/internals/dataLoader.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/internals/signals.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/links/httpBatchLink.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-non-null-assertion */\n\ntype BatchItem<TKey, TValue> = {\n  aborted: boolean;\n  key: TKey;\n  resolve: ((value: TValue) => void) | null;\n  reject: ((error: Error) => void) | null;\n  batch: Batch<TKey, TValue> | null;\n};\ntype Batch<TKey, TValue> = {\n  items: BatchItem<TKey, TValue>[];\n};\nexport type Batch<PERSON>oader<TKey, TValue> = {\n  validate: (keys: TKey[]) => boolean;\n  fetch: (keys: TKey[]) => Promise<TValue[] | Promise<TValue>[]>;\n};\n\n/**\n * A function that should never be called unless we messed something up.\n */\nconst throwFatalError = () => {\n  throw new Error(\n    'Something went wrong. Please submit an issue at https://github.com/trpc/trpc/issues/new',\n  );\n};\n\n/**\n * Dataloader that's very inspired by https://github.com/graphql/dataloader\n * Less configuration, no caching, and allows you to cancel requests\n * When cancelling a single fetch the whole batch will be cancelled only when _all_ items are cancelled\n */\nexport function dataLoader<TKey, TValue>(\n  batchLoader: BatchLoader<TKey, TValue>,\n) {\n  let pendingItems: BatchItem<TKey, TValue>[] | null = null;\n  let dispatchTimer: ReturnType<typeof setTimeout> | null = null;\n\n  const destroyTimerAndPendingItems = () => {\n    clearTimeout(dispatchTimer as any);\n    dispatchTimer = null;\n    pendingItems = null;\n  };\n\n  /**\n   * Iterate through the items and split them into groups based on the `batchLoader`'s validate function\n   */\n  function groupItems(items: BatchItem<TKey, TValue>[]) {\n    const groupedItems: BatchItem<TKey, TValue>[][] = [[]];\n    let index = 0;\n    while (true) {\n      const item = items[index];\n      if (!item) {\n        // we're done\n        break;\n      }\n      const lastGroup = groupedItems[groupedItems.length - 1]!;\n\n      if (item.aborted) {\n        // Item was aborted before it was dispatched\n        item.reject?.(new Error('Aborted'));\n        index++;\n        continue;\n      }\n\n      const isValid = batchLoader.validate(\n        lastGroup.concat(item).map((it) => it.key),\n      );\n\n      if (isValid) {\n        lastGroup.push(item);\n        index++;\n        continue;\n      }\n\n      if (lastGroup.length === 0) {\n        item.reject?.(new Error('Input is too big for a single dispatch'));\n        index++;\n        continue;\n      }\n      // Create new group, next iteration will try to add the item to that\n      groupedItems.push([]);\n    }\n    return groupedItems;\n  }\n\n  function dispatch() {\n    const groupedItems = groupItems(pendingItems!);\n    destroyTimerAndPendingItems();\n\n    // Create batches for each group of items\n    for (const items of groupedItems) {\n      if (!items.length) {\n        continue;\n      }\n      const batch: Batch<TKey, TValue> = {\n        items,\n      };\n      for (const item of items) {\n        item.batch = batch;\n      }\n      const promise = batchLoader.fetch(batch.items.map((_item) => _item.key));\n\n      promise\n        .then(async (result) => {\n          await Promise.all(\n            result.map(async (valueOrPromise, index) => {\n              const item = batch.items[index]!;\n              try {\n                const value = await Promise.resolve(valueOrPromise);\n\n                item.resolve?.(value);\n              } catch (cause) {\n                item.reject?.(cause as Error);\n              }\n\n              item.batch = null;\n              item.reject = null;\n              item.resolve = null;\n            }),\n          );\n\n          for (const item of batch.items) {\n            item.reject?.(new Error('Missing result'));\n            item.batch = null;\n          }\n        })\n        .catch((cause) => {\n          for (const item of batch.items) {\n            item.reject?.(cause);\n            item.batch = null;\n          }\n        });\n    }\n  }\n  function load(key: TKey): Promise<TValue> {\n    const item: BatchItem<TKey, TValue> = {\n      aborted: false,\n      key,\n      batch: null,\n      resolve: throwFatalError,\n      reject: throwFatalError,\n    };\n\n    const promise = new Promise<TValue>((resolve, reject) => {\n      item.reject = reject;\n      item.resolve = resolve;\n\n      pendingItems ??= [];\n      pendingItems.push(item);\n    });\n\n    dispatchTimer ??= setTimeout(dispatch);\n\n    return promise;\n  }\n\n  return {\n    load,\n  };\n}\n", "import type { Maybe } from '@trpc/server/unstable-core-do-not-import';\n\n/**\n * Like `Promise.all()` but for abort signals\n * - When all signals have been aborted, the merged signal will be aborted\n * - If one signal is `null`, no signal will be aborted\n */\nexport function allAbortSignals(...signals: Maybe<AbortSignal>[]): AbortSignal {\n  const ac = new AbortController();\n\n  const count = signals.length;\n\n  let abortedCount = 0;\n\n  const onAbort = () => {\n    if (++abortedCount === count) {\n      ac.abort();\n    }\n  };\n\n  for (const signal of signals) {\n    if (signal?.aborted) {\n      onAbort();\n    } else {\n      signal?.addEventListener('abort', onAbort, {\n        once: true,\n      });\n    }\n  }\n\n  return ac.signal;\n}\n\n/**\n * Like `Promise.race` but for abort signals\n *\n * Basically, a ponyfill for\n * [`AbortSignal.any`](https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/any_static).\n */\nexport function raceAbortSignals(\n  ...signals: Maybe<AbortSignal>[]\n): AbortSignal {\n  const ac = new AbortController();\n\n  for (const signal of signals) {\n    if (signal?.aborted) {\n      ac.abort();\n    } else {\n      signal?.addEventListener('abort', () => ac.abort(), { once: true });\n    }\n  }\n\n  return ac.signal;\n}\n\nexport function abortSignalToPromise(signal: AbortSignal): Promise<never> {\n  return new Promise((_, reject) => {\n    if (signal.aborted) {\n      reject(signal.reason);\n      return;\n    }\n    signal.addEventListener(\n      'abort',\n      () => {\n        reject(signal.reason);\n      },\n      { once: true },\n    );\n  });\n}\n", "import type { AnyRouter, ProcedureType } from '@trpc/server';\nimport { observable } from '@trpc/server/observable';\nimport { transformResult } from '@trpc/server/unstable-core-do-not-import';\nimport type { BatchLoader } from '../internals/dataLoader';\nimport { dataLoader } from '../internals/dataLoader';\nimport { allAbortSignals } from '../internals/signals';\nimport type { NonEmptyArray } from '../internals/types';\nimport { TRPCClientError } from '../TRPCClientError';\nimport type { HTTPBatchLinkOptions } from './HTTPBatchLinkOptions';\nimport type { HTTPResult } from './internals/httpUtils';\nimport {\n  getUrl,\n  jsonHttpRequester,\n  resolveHTTPLinkOptions,\n} from './internals/httpUtils';\nimport type { Operation, TRPCLink } from './types';\n\n/**\n * @see https://trpc.io/docs/client/links/httpBatchLink\n */\nexport function httpBatchLink<TRouter extends AnyRouter>(\n  opts: HTTPBatchLinkOptions<TRouter['_def']['_config']['$types']>,\n): TRPCLink<TRouter> {\n  const resolvedOpts = resolveHTTPLinkOptions(opts);\n  const maxURLLength = opts.maxURLLength ?? Infinity;\n  const maxItems = opts.maxItems ?? Infinity;\n\n  return () => {\n    const batchLoader = (\n      type: ProcedureType,\n    ): BatchLoader<Operation, HTTPResult> => {\n      return {\n        validate(batchOps) {\n          if (maxURLLength === Infinity && maxItems === Infinity) {\n            // escape hatch for quick calcs\n            return true;\n          }\n          if (batchOps.length > maxItems) {\n            return false;\n          }\n          const path = batchOps.map((op) => op.path).join(',');\n          const inputs = batchOps.map((op) => op.input);\n\n          const url = getUrl({\n            ...resolvedOpts,\n            type,\n            path,\n            inputs,\n            signal: null,\n          });\n\n          return url.length <= maxURLLength;\n        },\n        async fetch(batchOps) {\n          const path = batchOps.map((op) => op.path).join(',');\n          const inputs = batchOps.map((op) => op.input);\n          const signal = allAbortSignals(...batchOps.map((op) => op.signal));\n\n          const res = await jsonHttpRequester({\n            ...resolvedOpts,\n            path,\n            inputs,\n            type,\n            headers() {\n              if (!opts.headers) {\n                return {};\n              }\n              if (typeof opts.headers === 'function') {\n                return opts.headers({\n                  opList: batchOps as NonEmptyArray<Operation>,\n                });\n              }\n              return opts.headers;\n            },\n            signal,\n          });\n          const resJSON = Array.isArray(res.json)\n            ? res.json\n            : batchOps.map(() => res.json);\n          const result = resJSON.map((item) => ({\n            meta: res.meta,\n            json: item,\n          }));\n          return result;\n        },\n      };\n    };\n\n    const query = dataLoader(batchLoader('query'));\n    const mutation = dataLoader(batchLoader('mutation'));\n\n    const loaders = { query, mutation };\n    return ({ op }) => {\n      return observable((observer) => {\n        /* istanbul ignore if -- @preserve */\n        if (op.type === 'subscription') {\n          throw new Error(\n            'Subscriptions are unsupported by `httpLink` - use `httpSubscriptionLink` or `wsLink`',\n          );\n        }\n        const loader = loaders[op.type];\n        const promise = loader.load(op);\n\n        let _res = undefined as HTTPResult | undefined;\n        promise\n          .then((res) => {\n            _res = res;\n            const transformed = transformResult(\n              res.json,\n              resolvedOpts.transformer.output,\n            );\n\n            if (!transformed.ok) {\n              observer.error(\n                TRPCClientError.from(transformed.error, {\n                  meta: res.meta,\n                }),\n              );\n              return;\n            }\n            observer.next({\n              context: res.meta,\n              result: transformed.result,\n            });\n            observer.complete();\n          })\n          .catch((err) => {\n            observer.error(\n              TRPCClientError.from(err, {\n                meta: _res?.meta,\n              }),\n            );\n          });\n\n        return () => {\n          // noop\n        };\n      });\n    };\n  };\n}\n"], "names": ["batchLoader: <PERSON><PERSON><PERSON><PERSON><PERSON><TKey, TValue>", "pendingItems: BatchItem<T<PERSON>ey, TValue>[] | null", "dispatchTimer: ReturnType<typeof setTimeout> | null", "items: BatchItem<TKey, TValue>[]", "groupedItems: BatchItem<TKey, TValue>[][]", "batch: <PERSON><PERSON><T<PERSON><PERSON>, TValue>", "key: <PERSON><PERSON><PERSON>", "item: BatchItem<TKey, TValue>", "signal: AbortSignal", "opts: HTTPBatchLinkOptions<TRouter['_def']['_config']['$types']>", "type: ProcedureType"], "mappings": ";;;;;;;;;;;;;;;;;;;;GAoBA,MAAM,kBAAkB,MAAM;IAC5B,MAAM,IAAI,MACR;AAEH;;;;;GAOD,SAAgB,WACdA,WAAAA,EACA;IACA,IAAIC,eAAiD;IACrD,IAAIC,gBAAsD;IAE1D,MAAM,8BAA8B,MAAM;QACxC,aAAa,cAAqB;QAClC,gBAAgB;QAChB,eAAe;IAChB;;;IAKD,SAAS,WAAWC,KAAAA,EAAkC;QACpD,MAAMC,eAA4C;YAAC,CAAE,CAAC;SAAA;QACtD,IAAI,QAAQ;QACZ,MAAO,KAAM;YACX,MAAM,OAAO,KAAA,CAAM,MAAA;YACnB,IAAA,CAAK,KAEH,CAAA;YAEF,MAAM,YAAY,YAAA,CAAa,aAAa,MAAA,GAAS,EAAA;YAErD,IAAI,KAAK,OAAA,EAAS;;gBAEhB,CAAA,eAAA,KAAK,MAAA,MAAA,QAAA,iBAAA,KAAA,KAAL,aAAA,IAAA,CAAA,MAAc,IAAI,MAAM,WAAW;gBACnC;gBACA;YACD;YAED,MAAM,UAAU,YAAY,QAAA,CAC1B,UAAU,MAAA,CAAO,KAAK,CAAC,GAAA,CAAI,CAAC,KAAO,GAAG,GAAA,CAAI,CAC3C;YAED,IAAI,SAAS;gBACX,UAAU,IAAA,CAAK,KAAK;gBACpB;gBACA;YACD;YAED,IAAI,UAAU,MAAA,KAAW,GAAG;;gBAC1B,CAAA,gBAAA,KAAK,MAAA,MAAA,QAAA,kBAAA,KAAA,KAAL,cAAA,IAAA,CAAA,MAAc,IAAI,MAAM,0CAA0C;gBAClE;gBACA;YACD;YAED,aAAa,IAAA,CAAK,CAAE,CAAA,CAAC;QACtB;QACD,OAAO;IACR;IAED,SAAS,WAAW;QAClB,MAAM,eAAe,WAAW,aAAc;QAC9C,6BAA6B;QAG7B,KAAK,MAAM,SAAS,aAAc;YAChC,IAAA,CAAK,MAAM,MAAA,CACT,CAAA;YAEF,MAAMC,QAA6B;gBACjC;YACD;YACD,KAAK,MAAM,QAAQ,MACjB,KAAK,KAAA,GAAQ;YAEf,MAAM,UAAU,YAAY,KAAA,CAAM,MAAM,KAAA,CAAM,GAAA,CAAI,CAAC,QAAU,MAAM,GAAA,CAAI,CAAC;YAExE,QACG,IAAA,CAAK,OAAO,WAAW;gBACtB,MAAM,QAAQ,GAAA,CACZ,OAAO,GAAA,CAAI,OAAO,gBAAgB,UAAU;oBAC1C,MAAM,OAAO,MAAM,KAAA,CAAM,MAAA;oBACzB,IAAI;;wBACF,MAAM,QAAQ,MAAM,QAAQ,OAAA,CAAQ,eAAe;wBAEnD,CAAA,gBAAA,KAAK,OAAA,MAAA,QAAA,kBAAA,KAAA,KAAL,cAAA,IAAA,CAAA,MAAe,MAAM;oBACtB,EAAA,OAAQ,OAAO;;wBACd,CAAA,gBAAA,KAAK,MAAA,MAAA,QAAA,kBAAA,KAAA,KAAL,cAAA,IAAA,CAAA,MAAc,MAAe;oBAC9B;oBAED,KAAK,KAAA,GAAQ;oBACb,KAAK,MAAA,GAAS;oBACd,KAAK,OAAA,GAAU;gBAChB,EAAC,CACH;gBAED,KAAK,MAAM,QAAQ,MAAM,KAAA,CAAO;;oBAC9B,CAAA,gBAAA,KAAK,MAAA,MAAA,QAAA,kBAAA,KAAA,KAAL,cAAA,IAAA,CAAA,MAAc,IAAI,MAAM,kBAAkB;oBAC1C,KAAK,KAAA,GAAQ;gBACd;YACF,EAAC,CACD,KAAA,CAAM,CAAC,UAAU;gBAChB,KAAK,MAAM,QAAQ,MAAM,KAAA,CAAO;;oBAC9B,CAAA,gBAAA,KAAK,MAAA,MAAA,QAAA,kBAAA,KAAA,KAAL,cAAA,IAAA,CAAA,MAAc,MAAM;oBACpB,KAAK,KAAA,GAAQ;gBACd;YACF,EAAC;QACL;IACF;IACD,SAAS,KAAKC,GAAAA,EAA4B;;QACxC,MAAMC,OAAgC;YACpC,SAAS;YACT;YACA,OAAO;YACP,SAAS;YACT,QAAQ;QACT;QAED,MAAM,UAAU,IAAI,QAAgB,CAAC,SAAS,WAAW;;YACvD,KAAK,MAAA,GAAS;YACd,KAAK,OAAA,GAAU;YAEf,CAAA,gBAAA,YAAA,MAAA,QAAA,kBAAA,KAAA,KAAA,CAAA,eAAiB,CAAE,CAAA;YACnB,aAAa,IAAA,CAAK,KAAK;QACxB;QAED,CAAA,iBAAA,aAAA,MAAA,QAAA,mBAAA,KAAA,KAAA,CAAA,gBAAkB,WAAW,SAAS;QAEtC,OAAO;IACR;IAED,OAAO;QACL;IACD;AACF;;;;;;;GCxJD,SAAgB,gBAAgB,GAAG,OAAA,EAA4C;IAC7E,MAAM,KAAK,IAAI;IAEf,MAAM,QAAQ,QAAQ,MAAA;IAEtB,IAAI,eAAe;IAEnB,MAAM,UAAU,MAAM;QACpB,IAAI,EAAE,iBAAiB,MACrB,CAAA,GAAG,KAAA,EAAO;IAEb;IAED,KAAK,MAAM,UAAU,QACnB,IAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAI,OAAQ,OAAA,CACV,CAAA,SAAS;SAET,WAAA,QAAA,WAAA,KAAA,KAAA,OAAQ,gBAAA,CAAiB,SAAS,SAAS;QACzC,MAAM;IACP,EAAC;IAIN,OAAO,GAAG,MAAA;AACX;;;;;;GAQD,SAAgB,iBACd,GAAG,OAAA,EACU;IACb,MAAM,KAAK,IAAI;IAEf,KAAK,MAAM,UAAU,QACnB,IAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAI,OAAQ,OAAA,CACV,CAAA,GAAG,KAAA,EAAO;SAEV,WAAA,QAAA,WAAA,KAAA,KAAA,OAAQ,gBAAA,CAAiB,SAAS,IAAM,GAAG,KAAA,EAAO,EAAE;QAAE,MAAM;IAAM,EAAC;IAIvE,OAAO,GAAG,MAAA;AACX;AAED,SAAgB,qBAAqBC,MAAAA,EAAqC;IACxE,OAAO,IAAI,QAAQ,CAAC,GAAG,WAAW;QAChC,IAAI,OAAO,OAAA,EAAS;YAClB,OAAO,OAAO,MAAA,CAAO;YACrB;QACD;QACD,OAAO,gBAAA,CACL,SACA,MAAM;YACJ,OAAO,OAAO,MAAA,CAAO;QACtB,GACD;YAAE,MAAM;QAAM,EACf;IACF;AACF;;;;;;GCjDD,SAAgB,cACdC,IAAAA,EACmB;;IACnB,MAAM,gBAAe,wVAAA,EAAuB,KAAK;IACjD,MAAM,eAAA,CAAA,qBAAe,KAAK,YAAA,MAAA,QAAA,uBAAA,KAAA,IAAA,qBAAgB;IAC1C,MAAM,WAAA,CAAA,iBAAW,KAAK,QAAA,MAAA,QAAA,mBAAA,KAAA,IAAA,iBAAY;IAElC,OAAO,MAAM;QACX,MAAM,cAAc,CAClBC,SACuC;YACvC,OAAO;gBACL,UAAS,QAAA,EAAU;oBACjB,IAAI,iBAAiB,YAAY,aAAa,SAE5C,CAAA,OAAO;oBAET,IAAI,SAAS,MAAA,GAAS,SACpB,CAAA,OAAO;oBAET,MAAM,OAAO,SAAS,GAAA,CAAI,CAAC,KAAO,GAAG,IAAA,CAAK,CAAC,IAAA,CAAK,IAAI;oBACpD,MAAM,SAAS,SAAS,GAAA,CAAI,CAAC,KAAO,GAAG,KAAA,CAAM;oBAE7C,MAAM,MAAM,yUAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACP,eAAA,CAAA,GAAA;wBACH;wBACA;wBACA;wBACA,QAAQ;uBACR;oBAEF,OAAO,IAAI,MAAA,IAAU;gBACtB;gBACD,MAAM,OAAM,QAAA,EAAU;oBACpB,MAAM,OAAO,SAAS,GAAA,CAAI,CAAC,KAAO,GAAG,IAAA,CAAK,CAAC,IAAA,CAAK,IAAI;oBACpD,MAAM,SAAS,SAAS,GAAA,CAAI,CAAC,KAAO,GAAG,KAAA,CAAM;oBAC7C,MAAM,SAAS,gBAAgB,GAAG,SAAS,GAAA,CAAI,CAAC,KAAO,GAAG,MAAA,CAAO,CAAC;oBAElE,MAAM,MAAM,sUAAM,oBAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACb,eAAA,CAAA,GAAA;wBACH;wBACA;wBACA;wBACA,UAAU;4BACR,IAAA,CAAK,KAAK,OAAA,CACR,CAAA,OAAO,CAAE;4BAEX,IAAA,OAAW,KAAK,OAAA,KAAY,WAC1B,CAAA,OAAO,KAAK,OAAA,CAAQ;gCAClB,QAAQ;4BACT,EAAC;4BAEJ,OAAO,KAAK,OAAA;wBACb;wBACD;uBACA;oBACF,MAAM,UAAU,MAAM,OAAA,CAAQ,IAAI,IAAA,CAAK,GACnC,IAAI,IAAA,GACJ,SAAS,GAAA,CAAI,IAAM,IAAI,IAAA,CAAK;oBAChC,MAAM,SAAS,QAAQ,GAAA,CAAI,CAAC,OAAA,CAAU;4BACpC,MAAM,IAAI,IAAA;4BACV,MAAM;wBACP,CAAA,EAAE;oBACH,OAAO;gBACR;YACF;QACF;QAED,MAAM,QAAQ,WAAW,YAAY,QAAQ,CAAC;QAC9C,MAAM,WAAW,WAAW,YAAY,WAAW,CAAC;QAEpD,MAAM,UAAU;YAAE;YAAO;QAAU;QACnC,OAAO,CAAC,EAAE,EAAA,EAAI,KAAK;YACjB,QAAO,4QAAA,EAAW,CAAC,aAAa;sDAE9B,IAAI,GAAG,IAAA,KAAS,eACd,CAAA,MAAM,IAAI,MACR;gBAGJ,MAAM,SAAS,OAAA,CAAQ,GAAG,IAAA,CAAA;gBAC1B,MAAM,UAAU,OAAO,IAAA,CAAK,GAAG;gBAE/B,IAAI,OAAA,KAAA;gBACJ,QACG,IAAA,CAAK,CAAC,QAAQ;oBACb,OAAO;oBACP,MAAM,2QAAc,kBAAA,EAClB,IAAI,IAAA,EACJ,aAAa,WAAA,CAAY,MAAA,CAC1B;oBAED,IAAA,CAAK,YAAY,EAAA,EAAI;wBACnB,SAAS,KAAA,mUACP,kBAAA,CAAgB,IAAA,CAAK,YAAY,KAAA,EAAO;4BACtC,MAAM,IAAI,IAAA;wBACX,EAAC,CACH;wBACD;oBACD;oBACD,SAAS,IAAA,CAAK;wBACZ,SAAS,IAAI,IAAA;wBACb,QAAQ,YAAY,MAAA;oBACrB,EAAC;oBACF,SAAS,QAAA,EAAU;gBACpB,EAAC,CACD,KAAA,CAAM,CAAC,QAAQ;oBACd,SAAS,KAAA,mUACP,kBAAA,CAAgB,IAAA,CAAK,KAAK;wBACxB,MAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAM,KAAM,IAAA;oBACb,EAAC,CACH;gBACF,EAAC;gBAEJ,OAAO,KAEN,CAFY;YAGd,EAAC;QACH;IACF;AACF", "ignoreList": [0, 1, 2], "debugId": null}}, {"offset": {"line": 803, "column": 0}, "map": {"version": 3, "file": "loggerLink-ineCN1PO.mjs", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/links/loggerLink.ts"], "sourcesContent": ["/// <reference lib=\"dom.iterable\" />\n\n// `dom.iterable` types are explicitly required for extracting `FormData` values,\n// as all implementations of `Symbol.iterable` are separated from the main `dom` types.\n// Using triple-slash directive makes sure that it will be available,\n// even if end-user `tsconfig.json` omits it in the `lib` array.\n\nimport { observable, tap } from '@trpc/server/observable';\nimport type {\n  AnyRouter,\n  InferrableClientTypes,\n} from '@trpc/server/unstable-core-do-not-import';\nimport type { TRPCClientError } from '../TRPCClientError';\nimport type { Operation, OperationResultEnvelope, TRPCLink } from './types';\n\ntype ConsoleEsque = {\n  log: (...args: any[]) => void;\n  error: (...args: any[]) => void;\n};\n\ntype EnableFnOptions<TRouter extends InferrableClientTypes> =\n  | {\n      direction: 'down';\n      result:\n        | OperationResultEnvelope<unknown, TRPCClientError<TRouter>>\n        | TRPCClientError<TRouter>;\n    }\n  | (Operation & {\n      direction: 'up';\n    });\ntype EnabledFn<TRouter extends AnyRouter> = (\n  opts: EnableFnOptions<TRouter>,\n) => boolean;\n\ntype LoggerLinkFnOptions<TRouter extends AnyRouter> = Operation &\n  (\n    | {\n        /**\n         * Request result\n         */\n        direction: 'down';\n        result:\n          | OperationResultEnvelope<unknown, TRPCClientError<TRouter>>\n          | TRPCClientError<TRouter>;\n        elapsedMs: number;\n      }\n    | {\n        /**\n         * Request was just initialized\n         */\n        direction: 'up';\n      }\n  );\n\ntype LoggerLinkFn<TRouter extends AnyRouter> = (\n  opts: LoggerLinkFnOptions<TRouter>,\n) => void;\n\ntype ColorMode = 'ansi' | 'css' | 'none';\n\nexport interface LoggerLinkOptions<TRouter extends AnyRouter> {\n  logger?: LoggerLinkFn<TRouter>;\n  enabled?: EnabledFn<TRouter>;\n  /**\n   * Used in the built-in defaultLogger\n   */\n  console?: ConsoleEsque;\n  /**\n   * Color mode\n   * @default typeof window === 'undefined' ? 'ansi' : 'css'\n   */\n  colorMode?: ColorMode;\n\n  /**\n   * Include context in the log - defaults to false unless `colorMode` is 'css'\n   */\n  withContext?: boolean;\n}\n\nfunction isFormData(value: unknown): value is FormData {\n  if (typeof FormData === 'undefined') {\n    // FormData is not supported\n    return false;\n  }\n  return value instanceof FormData;\n}\n\nconst palettes = {\n  css: {\n    query: ['72e3ff', '3fb0d8'],\n    mutation: ['c5a3fc', '904dfc'],\n    subscription: ['ff49e1', 'd83fbe'],\n  },\n  ansi: {\n    regular: {\n      // Cyan background, black and white text respectively\n      query: ['\\x1b[30;46m', '\\x1b[97;46m'],\n      // Magenta background, black and white text respectively\n      mutation: ['\\x1b[30;45m', '\\x1b[97;45m'],\n      // Green background, black and white text respectively\n      subscription: ['\\x1b[30;42m', '\\x1b[97;42m'],\n    },\n    bold: {\n      query: ['\\x1b[1;30;46m', '\\x1b[1;97;46m'],\n      mutation: ['\\x1b[1;30;45m', '\\x1b[1;97;45m'],\n      subscription: ['\\x1b[1;30;42m', '\\x1b[1;97;42m'],\n    },\n  },\n} as const;\n\nfunction constructPartsAndArgs(\n  opts: LoggerLinkFnOptions<any> & {\n    colorMode: ColorMode;\n    withContext?: boolean;\n  },\n) {\n  const { direction, type, withContext, path, id, input } = opts;\n\n  const parts: string[] = [];\n  const args: any[] = [];\n\n  if (opts.colorMode === 'none') {\n    parts.push(direction === 'up' ? '>>' : '<<', type, `#${id}`, path);\n  } else if (opts.colorMode === 'ansi') {\n    const [lightRegular, darkRegular] = palettes.ansi.regular[type];\n    const [lightBold, darkBold] = palettes.ansi.bold[type];\n    const reset = '\\x1b[0m';\n\n    parts.push(\n      direction === 'up' ? lightRegular : darkRegular,\n      direction === 'up' ? '>>' : '<<',\n      type,\n      direction === 'up' ? lightBold : darkBold,\n      `#${id}`,\n      path,\n      reset,\n    );\n  } else {\n    // css color mode\n    const [light, dark] = palettes.css[type];\n    const css = `\n    background-color: #${direction === 'up' ? light : dark};\n    color: ${direction === 'up' ? 'black' : 'white'};\n    padding: 2px;\n  `;\n\n    parts.push(\n      '%c',\n      direction === 'up' ? '>>' : '<<',\n      type,\n      `#${id}`,\n      `%c${path}%c`,\n      '%O',\n    );\n    args.push(\n      css,\n      `${css}; font-weight: bold;`,\n      `${css}; font-weight: normal;`,\n    );\n  }\n\n  if (direction === 'up') {\n    args.push(withContext ? { input, context: opts.context } : { input });\n  } else {\n    args.push({\n      input,\n      result: opts.result,\n      elapsedMs: opts.elapsedMs,\n      ...(withContext && { context: opts.context }),\n    });\n  }\n\n  return { parts, args };\n}\n\n// maybe this should be moved to it's own package\nconst defaultLogger =\n  <TRouter extends AnyRouter>({\n    c = console,\n    colorMode = 'css',\n    withContext,\n  }: {\n    c?: ConsoleEsque;\n    colorMode?: ColorMode;\n    withContext?: boolean;\n  }): LoggerLinkFn<TRouter> =>\n  (props) => {\n    const rawInput = props.input;\n    const input = isFormData(rawInput)\n      ? Object.fromEntries(rawInput)\n      : rawInput;\n\n    const { parts, args } = constructPartsAndArgs({\n      ...props,\n      colorMode,\n      input,\n      withContext,\n    });\n\n    const fn: 'error' | 'log' =\n      props.direction === 'down' &&\n      props.result &&\n      (props.result instanceof Error ||\n        ('error' in props.result.result && props.result.result.error))\n        ? 'error'\n        : 'log';\n\n    c[fn].apply(null, [parts.join(' ')].concat(args));\n  };\n\n/**\n * @see https://trpc.io/docs/v11/client/links/loggerLink\n */\nexport function loggerLink<TRouter extends AnyRouter = AnyRouter>(\n  opts: LoggerLinkOptions<TRouter> = {},\n): TRPCLink<TRouter> {\n  const { enabled = () => true } = opts;\n\n  const colorMode =\n    opts.colorMode ?? (typeof window === 'undefined' ? 'ansi' : 'css');\n  const withContext = opts.withContext ?? colorMode === 'css';\n  const {\n    logger = defaultLogger({ c: opts.console, colorMode, withContext }),\n  } = opts;\n\n  return () => {\n    return ({ op, next }) => {\n      return observable((observer) => {\n        // ->\n        if (enabled({ ...op, direction: 'up' })) {\n          logger({\n            ...op,\n            direction: 'up',\n          });\n        }\n        const requestStartTime = Date.now();\n        function logResult(\n          result:\n            | OperationResultEnvelope<unknown, TRPCClientError<TRouter>>\n            | TRPCClientError<TRouter>,\n        ) {\n          const elapsedMs = Date.now() - requestStartTime;\n\n          if (enabled({ ...op, direction: 'down', result })) {\n            logger({\n              ...op,\n              direction: 'down',\n              elapsedMs,\n              result,\n            });\n          }\n        }\n        return next(op)\n          .pipe(\n            tap({\n              next(result) {\n                logResult(result);\n              },\n              error(result) {\n                logResult(result);\n              },\n            }),\n          )\n          .subscribe(observer);\n      });\n    };\n  };\n}\n"], "names": ["value: unknown", "opts: LoggerLinkFnOptions<any> & {\n    colorMode: ColorMode;\n    withContext?: boolean;\n  }", "parts: string[]", "args: any[]", "fn: 'error' | 'log'", "opts: LoggerLinkOptions<TRouter>", "result:\n            | OperationResultEnvelope<unknown, TRPCClientError<TRouter>>\n            | TRPCClientError<TRouter>"], "mappings": ";;;;;;;;;;AA+EA,SAAS,WAAWA,KAAAA,EAAmC;IACrD,IAAA,OAAW,aAAa,YAEtB,CAAA,OAAO;IAET,OAAO,iBAAiB;AACzB;AAED,MAAM,WAAW;IACf,KAAK;QACH,OAAO;YAAC;YAAU,QAAS;SAAA;QAC3B,UAAU;YAAC;YAAU,QAAS;SAAA;QAC9B,cAAc;YAAC;YAAU,QAAS;SAAA;IACnC;IACD,MAAM;QACJ,SAAS;YAEP,OAAO;gBAAC;gBAAe,aAAc;aAAA;YAErC,UAAU;gBAAC;gBAAe,aAAc;aAAA;YAExC,cAAc;gBAAC;gBAAe,aAAc;aAAA;QAC7C;QACD,MAAM;YACJ,OAAO;gBAAC;gBAAiB,eAAgB;aAAA;YACzC,UAAU;gBAAC;gBAAiB,eAAgB;aAAA;YAC5C,cAAc;gBAAC;gBAAiB,eAAgB;aAAA;QACjD;IACF;AACF;AAED,SAAS,sBACPC,IAAAA,EAIA;IACA,MAAM,EAAE,SAAA,EAAW,IAAA,EAAM,WAAA,EAAa,IAAA,EAAM,EAAA,EAAI,KAAA,EAAO,GAAG;IAE1D,MAAMC,QAAkB,CAAE,CAAA;IAC1B,MAAMC,OAAc,CAAE,CAAA;IAEtB,IAAI,KAAK,SAAA,KAAc,OACrB,CAAA,MAAM,IAAA,CAAK,cAAc,OAAO,OAAO,MAAM,MAAA,CAAO,CAAA,EAAG,GAAG,CAAA,EAAG,KAAK;aACzD,KAAK,SAAA,KAAc,QAAQ;QACpC,MAAM,CAAC,cAAc,YAAY,GAAG,SAAS,IAAA,CAAK,OAAA,CAAQ,KAAA;QAC1D,MAAM,CAAC,WAAW,SAAS,GAAG,SAAS,IAAA,CAAK,IAAA,CAAK,KAAA;QACjD,MAAM,QAAQ;QAEd,MAAM,IAAA,CACJ,cAAc,OAAO,eAAe,aACpC,cAAc,OAAO,OAAO,MAC5B,MACA,cAAc,OAAO,YAAY,UAAA,CAChC,CAAA,EAAG,GAAG,CAAA,EACP,MACA,MACD;IACF,OAAM;QAEL,MAAM,CAAC,OAAO,KAAK,GAAG,SAAS,GAAA,CAAI,KAAA;QACnC,MAAM,MAAA,CAAO;yBACQ,cAAc,OAAO,QAAQ,KAAK;aAC9C,cAAc,OAAO,UAAU,QAAQ;;;QAIhD,MAAM,IAAA,CACJ,MACA,cAAc,OAAO,OAAO,MAC5B,MAAA,CACC,CAAA,EAAG,GAAG,CAAA,EAAA,CACN,EAAA,EAAI,KAAK,EAAA,CAAA,EACV,KACD;QACD,KAAK,IAAA,CACH,KAAA,CACC,EAAE,IAAI,oBAAA,CAAA,EAAA,CACN,EAAE,IAAI,sBAAA,CAAA,CACR;IACF;IAED,IAAI,cAAc,KAChB,CAAA,KAAK,IAAA,CAAK,cAAc;QAAE;QAAO,SAAS,KAAK,OAAA;IAAS,IAAG;QAAE;IAAO,EAAC;SAErE,KAAK,IAAA,CAAA,CAAA,GAAA,qBAAA,OAAA,EAAA;QACH;QACA,QAAQ,KAAK,MAAA;QACb,WAAW,KAAK,SAAA;OACZ,eAAe;QAAE,SAAS,KAAK,OAAA;IAAS,GAC5C;IAGJ,OAAO;QAAE;QAAO;IAAM;AACvB;AAGD,MAAM,gBACJ,CAA4B,EAC1B,IAAI,OAAA,EACJ,YAAY,KAAA,EACZ,WAAA,EAKD,GACD,CAAC,UAAU;QACT,MAAM,WAAW,MAAM,KAAA;QACvB,MAAM,QAAQ,WAAW,SAAS,GAC9B,OAAO,WAAA,CAAY,SAAS,GAC5B;QAEJ,MAAM,EAAE,KAAA,EAAO,IAAA,EAAM,GAAG,sBAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACnB,QAAA,CAAA,GAAA;YACH;YACA;YACA;WACA;QAEF,MAAMC,KACJ,MAAM,SAAA,KAAc,UACpB,MAAM,MAAA,IAAA,CACL,MAAM,MAAA,YAAkB,SACtB,WAAW,MAAM,MAAA,CAAO,MAAA,IAAU,MAAM,MAAA,CAAO,MAAA,CAAO,KAAA,IACrD,UACA;QAEN,CAAA,CAAE,GAAA,CAAI,KAAA,CAAM,MAAM;YAAC,MAAM,IAAA,CAAK,IAAI,AAAC;SAAA,CAAC,MAAA,CAAO,KAAK,CAAC;IAClD;;;GAKH,SAAgB,WACdC,OAAmC,CAAE,CAAA,EAClB;;IACnB,MAAM,EAAE,UAAU,IAAM,IAAA,EAAM,GAAG;IAEjC,MAAM,YAAA,CAAA,kBACJ,KAAK,SAAA,MAAA,QAAA,oBAAA,KAAA,IAAA,kBAAA,OAAqB,WAAW,cAAc,SAAS;IAC9D,MAAM,cAAA,CAAA,oBAAc,KAAK,WAAA,MAAA,QAAA,sBAAA,KAAA,IAAA,oBAAe,cAAc;IACtD,MAAM,EACJ,SAAS,cAAc;QAAE,GAAG,KAAK,OAAA;QAAS;QAAW;IAAa,EAAC,EACpE,GAAG;IAEJ,OAAO,MAAM;QACX,OAAO,CAAC,EAAE,EAAA,EAAI,IAAA,EAAM,KAAK;YACvB,uQAAO,aAAA,EAAW,CAAC,aAAa;gBAE9B,IAAI,QAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAa,KAAA,CAAA,GAAA;oBAAI,WAAW;gBAAA,GAAO,CACrC,CAAA,OAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACK,KAAA,CAAA,GAAA;oBACH,WAAW;gBAAA,GACX;gBAEJ,MAAM,mBAAmB,KAAK,GAAA,EAAK;gBACnC,SAAS,UACPC,MAAAA,EAGA;oBACA,MAAM,YAAY,KAAK,GAAA,EAAK,GAAG;oBAE/B,IAAI,QAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAa,KAAA,CAAA,GAAA;wBAAI,WAAW;wBAAQ;uBAAS,CAC/C,CAAA,OAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACK,KAAA,CAAA,GAAA;wBACH,WAAW;wBACX;wBACA;uBACA;gBAEL;gBACD,OAAO,KAAK,GAAG,CACZ,IAAA,kQACC,MAAA,EAAI;oBACF,MAAK,MAAA,EAAQ;wBACX,UAAU,OAAO;oBAClB;oBACD,OAAM,MAAA,EAAQ;wBACZ,UAAU,OAAO;oBAClB;gBACF,EAAC,CACH,CACA,SAAA,CAAU,SAAS;YACvB,EAAC;QACH;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 966, "column": 0}, "map": {"version": 3, "file": "wsLink-H5IjZfJW.mjs", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/links/wsLink/wsClient/options.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/links/internals/urlWithConnectionParams.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/links/wsLink/wsClient/utils.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/links/wsLink/wsClient/requestManager.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/links/wsLink/wsClient/wsConnection.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/links/wsLink/wsClient/wsClient.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/links/wsLink/createWsClient.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/links/wsLink/wsLink.ts"], "sourcesContent": ["import type { UrlOptionsWithConnectionParams } from '../../internals/urlWithConnectionParams';\n\nexport interface WebSocketClientOptions extends UrlOptionsWithConnectionParams {\n  /**\n   * Ponyfill which WebSocket implementation to use\n   */\n  WebSocket?: typeof WebSocket;\n  /**\n   * The number of milliseconds before a reconnect is attempted.\n   * @default {@link exponentialBackoff}\n   */\n  retryDelayMs?: (attemptIndex: number) => number;\n  /**\n   * Triggered when a WebSocket connection is established\n   */\n  onOpen?: () => void;\n  /**\n   * Triggered when a WebSocket connection encounters an error\n   */\n  onError?: (evt?: Event) => void;\n  /**\n   * Triggered when a WebSocket connection is closed\n   */\n  onClose?: (cause?: { code?: number }) => void;\n  /**\n   * Lazy mode will close the WebSocket automatically after a period of inactivity (no messages sent or received and no pending requests)\n   */\n  lazy?: {\n    /**\n     * Enable lazy mode\n     * @default false\n     */\n    enabled: boolean;\n    /**\n     * Close the WebSocket after this many milliseconds\n     * @default 0\n     */\n    closeMs: number;\n  };\n  /**\n   * Send ping messages to the server and kill the connection if no pong message is returned\n   */\n  keepAlive?: {\n    /**\n     * @default false\n     */\n    enabled: boolean;\n    /**\n     * Send a ping message every this many milliseconds\n     * @default 5_000\n     */\n    intervalMs?: number;\n    /**\n     * Close the WebSocket after this many milliseconds if the server does not respond\n     * @default 1_000\n     */\n    pongTimeoutMs?: number;\n  };\n}\n\n/**\n * Default options for lazy WebSocket connections.\n * Determines whether the connection should be established lazily and defines the delay before closure.\n */\nexport type LazyOptions = Required<NonNullable<WebSocketClientOptions['lazy']>>;\nexport const lazyDefaults: LazyOptions = {\n  enabled: false,\n  closeMs: 0,\n};\n\n/**\n * Default options for the WebSocket keep-alive mechanism.\n * Configures whether keep-alive is enabled and specifies the timeout and interval for ping-pong messages.\n */\nexport type KeepAliveOptions = Required<\n  NonNullable<WebSocketClientOptions['keepAlive']>\n>;\nexport const keepAliveDefaults: KeepAliveOptions = {\n  enabled: false,\n  pongTimeoutMs: 1_000,\n  intervalMs: 5_000,\n};\n\n/**\n * Calculates a delay for exponential backoff based on the retry attempt index.\n * The delay starts at 0 for the first attempt and doubles for each subsequent attempt,\n * capped at 30 seconds.\n */\nexport const exponentialBackoff = (attemptIndex: number) => {\n  return attemptIndex === 0 ? 0 : Math.min(1000 * 2 ** attemptIndex, 30000);\n};\n", "import { type TRPCRequestInfo } from '@trpc/server/http';\n\n/**\n * Get the result of a value or function that returns a value\n * It also optionally accepts typesafe arguments for the function\n */\nexport const resultOf = <T, TArgs extends any[]>(\n  value: T | ((...args: TArgs) => T),\n  ...args: TArgs\n): T => {\n  return typeof value === 'function'\n    ? (value as (...args: TArgs) => T)(...args)\n    : value;\n};\n\n/**\n * A value that can be wrapped in callback\n */\nexport type CallbackOrValue<T> = T | (() => T | Promise<T>);\n\nexport interface UrlOptionsWithConnectionParams {\n  /**\n   * The URL to connect to (can be a function that returns a URL)\n   */\n  url: CallbackOrValue<string>;\n\n  /**\n   * Connection params that are available in `createContext()`\n   * - For `wsLink`/`wsClient`, these are sent as the first message\n   * - For `httpSubscriptionLink`, these are serialized as part of the URL under the `connectionParams` query\n   */\n  connectionParams?: CallbackOrValue<TRPCRequestInfo['connectionParams']>;\n}\n", "import type {\n  TRPCConnectionParamsMessage,\n  TRPCRequestInfo,\n} from '@trpc/server/unstable-core-do-not-import';\nimport type {\n  CallbackOrValue,\n  UrlOptionsWithConnectionParams,\n} from '../../internals/urlWithConnectionParams';\nimport { resultOf } from '../../internals/urlWithConnectionParams';\n\nexport class TRPCWebSocketClosedError extends Error {\n  constructor(opts: { message: string; cause?: unknown }) {\n    super(opts.message, {\n      cause: opts.cause,\n    });\n    this.name = 'TRPCWebSocketClosedError';\n    Object.setPrototypeOf(this, TRPCWebSocketClosedError.prototype);\n  }\n}\n\n/**\n * Utility class for managing a timeout that can be started, stopped, and reset.\n * Useful for scenarios where the timeout duration is reset dynamically based on events.\n */\nexport class ResettableTimeout {\n  private timeout: ReturnType<typeof setTimeout> | undefined;\n\n  constructor(\n    private readonly onTimeout: () => void,\n    private readonly timeoutMs: number,\n  ) {}\n\n  /**\n   * Resets the current timeout, restarting it with the same duration.\n   * Does nothing if no timeout is active.\n   */\n  public reset() {\n    if (!this.timeout) return;\n\n    clearTimeout(this.timeout);\n    this.timeout = setTimeout(this.onTimeout, this.timeoutMs);\n  }\n\n  public start() {\n    clearTimeout(this.timeout);\n    this.timeout = setTimeout(this.onTimeout, this.timeoutMs);\n  }\n\n  public stop() {\n    clearTimeout(this.timeout);\n    this.timeout = undefined;\n  }\n}\n\n// Ponyfill for Promise.withResolvers https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/withResolvers\nexport function withResolvers<T>() {\n  let resolve: (value: T | PromiseLike<T>) => void;\n  let reject: (reason?: any) => void;\n  const promise = new Promise<T>((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n\n  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n  return { promise, resolve: resolve!, reject: reject! };\n}\n\n/**\n * Resolves a WebSocket URL and optionally appends connection parameters.\n *\n * If connectionParams are provided, appends 'connectionParams=1' query parameter.\n */\nexport async function prepareUrl(urlOptions: UrlOptionsWithConnectionParams) {\n  const url = await resultOf(urlOptions.url);\n\n  if (!urlOptions.connectionParams) return url;\n\n  // append `?connectionParams=1` when connection params are used\n  const prefix = url.includes('?') ? '&' : '?';\n  const connectionParams = `${prefix}connectionParams=1`;\n\n  return url + connectionParams;\n}\n\nexport async function buildConnectionMessage(\n  connectionParams: CallbackOrValue<TRPCRequestInfo['connectionParams']>,\n) {\n  const message: TRPCConnectionParamsMessage = {\n    method: 'connectionParams',\n    data: await resultOf(connectionParams),\n  };\n\n  return JSON.stringify(message);\n}\n", "import type { AnyTRPCRouter, inferRouterError } from '@trpc/server';\nimport type { Observer } from '@trpc/server/observable';\nimport type {\n  TRPCClientOutgoingMessage,\n  TRPCResponseMessage,\n} from '@trpc/server/unstable-core-do-not-import';\nimport type { TRPCClientError } from '../../../TRPCClientError';\nimport { withResolvers } from './utils';\n\nexport type TCallbacks = Observer<\n  TRPCResponseMessage<unknown, inferRouterError<AnyTRPCRouter>>,\n  TRPCClientError<AnyTRPCRouter>\n>;\n\ntype MessageId = string;\ntype MessageIdLike = string | number | null;\n\n/**\n * Represents a WebSocket request managed by the RequestManager.\n * Combines the network message, a utility promise (`end`) that mirrors the lifecycle\n * handled by `callbacks`, and a set of state monitoring callbacks.\n */\ninterface Request {\n  message: TRPCClientOutgoingMessage;\n  end: Promise<void>;\n  callbacks: TCallbacks;\n}\n\n/**\n * Manages WebSocket requests, tracking their lifecycle and providing utility methods\n * for handling outgoing and pending requests.\n *\n * - **Outgoing requests**: Requests that are queued and waiting to be sent.\n * - **Pending requests**: Requests that have been sent and are in flight awaiting a response.\n *   For subscriptions, multiple responses may be received until the subscription is closed.\n */\nexport class RequestManager {\n  /**\n   * Stores requests that are outgoing, meaning they are registered but not yet sent over the WebSocket.\n   */\n  private outgoingRequests = new Array<Request & { id: MessageId }>();\n\n  /**\n   * Stores requests that are pending (in flight), meaning they have been sent over the WebSocket\n   * and are awaiting responses. For subscriptions, this includes requests\n   * that may receive multiple responses.\n   */\n  private pendingRequests: Record<MessageId, Request> = {};\n\n  /**\n   * Registers a new request by adding it to the outgoing queue and setting up\n   * callbacks for lifecycle events such as completion or error.\n   *\n   * @param message - The outgoing message to be sent.\n   * @param callbacks - Callback functions to observe the request's state.\n   * @returns A cleanup function to manually remove the request.\n   */\n  public register(message: TRPCClientOutgoingMessage, callbacks: TCallbacks) {\n    const { promise: end, resolve } = withResolvers<void>();\n\n    this.outgoingRequests.push({\n      id: String(message.id),\n      message,\n      end,\n      callbacks: {\n        next: callbacks.next,\n        complete: () => {\n          callbacks.complete();\n          resolve();\n        },\n        error: (e) => {\n          callbacks.error(e);\n          resolve();\n        },\n      },\n    });\n\n    return () => {\n      this.delete(message.id);\n      callbacks.complete();\n      resolve();\n    };\n  }\n\n  /**\n   * Deletes a request from both the outgoing and pending collections, if it exists.\n   */\n  public delete(messageId: MessageIdLike) {\n    if (messageId === null) return;\n\n    this.outgoingRequests = this.outgoingRequests.filter(\n      ({ id }) => id !== String(messageId),\n    );\n    delete this.pendingRequests[String(messageId)];\n  }\n\n  /**\n   * Moves all outgoing requests to the pending state and clears the outgoing queue.\n   *\n   * The caller is expected to handle the actual sending of the requests\n   * (e.g., sending them over the network) after this method is called.\n   *\n   * @returns The list of requests that were transitioned to the pending state.\n   */\n  public flush() {\n    const requests = this.outgoingRequests;\n    this.outgoingRequests = [];\n\n    for (const request of requests) {\n      this.pendingRequests[request.id] = request;\n    }\n    return requests;\n  }\n\n  /**\n   * Retrieves all currently pending requests, which are in flight awaiting responses\n   * or handling ongoing subscriptions.\n   */\n  public getPendingRequests() {\n    return Object.values(this.pendingRequests);\n  }\n\n  /**\n   * Retrieves a specific pending request by its message ID.\n   */\n  public getPendingRequest(messageId: MessageIdLike) {\n    if (messageId === null) return null;\n\n    return this.pendingRequests[String(messageId)];\n  }\n\n  /**\n   * Retrieves all outgoing requests, which are waiting to be sent.\n   */\n  public getOutgoingRequests() {\n    return this.outgoingRequests;\n  }\n\n  /**\n   * Retrieves all requests, both outgoing and pending, with their respective states.\n   *\n   * @returns An array of all requests with their state (\"outgoing\" or \"pending\").\n   */\n  public getRequests() {\n    return [\n      ...this.getOutgoingRequests().map((request) => ({\n        state: 'outgoing' as const,\n        message: request.message,\n        end: request.end,\n        callbacks: request.callbacks,\n      })),\n      ...this.getPendingRequests().map((request) => ({\n        state: 'pending' as const,\n        message: request.message,\n        end: request.end,\n        callbacks: request.callbacks,\n      })),\n    ];\n  }\n\n  /**\n   * Checks if there are any pending requests, including ongoing subscriptions.\n   */\n  public hasPendingRequests() {\n    return this.getPendingRequests().length > 0;\n  }\n\n  /**\n   * Checks if there are any pending subscriptions\n   */\n  public hasPendingSubscriptions() {\n    return this.getPendingRequests().some(\n      (request) => request.message.method === 'subscription',\n    );\n  }\n\n  /**\n   * Checks if there are any outgoing requests waiting to be sent.\n   */\n  public hasOutgoingRequests() {\n    return this.outgoingRequests.length > 0;\n  }\n}\n", "import { behaviorSubject } from '@trpc/server/observable';\nimport type { UrlOptionsWithConnectionParams } from '../../internals/urlWithConnectionParams';\nimport { buildConnectionMessage, prepareUrl, withResolvers } from './utils';\n\n/**\n * Opens a WebSocket connection asynchronously and returns a promise\n * that resolves when the connection is successfully established.\n * The promise rejects if an error occurs during the connection attempt.\n */\nfunction asyncWsOpen(ws: WebSocket) {\n  const { promise, resolve, reject } = withResolvers<void>();\n\n  ws.addEventListener('open', () => {\n    ws.removeEventListener('error', reject);\n    resolve();\n  });\n  ws.addEventListener('error', reject);\n\n  return promise;\n}\n\ninterface PingPongOptions {\n  /**\n   * The interval (in milliseconds) between \"PING\" messages.\n   */\n  intervalMs: number;\n\n  /**\n   * The timeout (in milliseconds) to wait for a \"PONG\" response before closing the connection.\n   */\n  pongTimeoutMs: number;\n}\n\n/**\n * Sets up a periodic ping-pong mechanism to keep the WebSocket connection alive.\n *\n * - Sends \"PING\" messages at regular intervals defined by `intervalMs`.\n * - If a \"PONG\" response is not received within the `pongTimeoutMs`, the WebSocket is closed.\n * - The ping timer resets upon receiving any message to maintain activity.\n * - Automatically starts the ping process when the WebSocket connection is opened.\n * - Cleans up timers when the WebSocket is closed.\n *\n * @param ws - The WebSocket instance to manage.\n * @param options - Configuration options for ping-pong intervals and timeouts.\n */\nfunction setupPingInterval(\n  ws: WebSocket,\n  { intervalMs, pongTimeoutMs }: PingPongOptions,\n) {\n  let pingTimeout: ReturnType<typeof setTimeout> | undefined;\n  let pongTimeout: ReturnType<typeof setTimeout> | undefined;\n\n  function start() {\n    pingTimeout = setTimeout(() => {\n      ws.send('PING');\n      pongTimeout = setTimeout(() => {\n        ws.close();\n      }, pongTimeoutMs);\n    }, intervalMs);\n  }\n\n  function reset() {\n    clearTimeout(pingTimeout);\n    start();\n  }\n\n  function pong() {\n    clearTimeout(pongTimeout);\n    reset();\n  }\n\n  ws.addEventListener('open', start);\n  ws.addEventListener('message', ({ data }) => {\n    clearTimeout(pingTimeout);\n    start();\n\n    if (data === 'PONG') {\n      pong();\n    }\n  });\n  ws.addEventListener('close', () => {\n    clearTimeout(pingTimeout);\n    clearTimeout(pongTimeout);\n  });\n}\n\nexport interface WebSocketConnectionOptions {\n  WebSocketPonyfill?: typeof WebSocket;\n  urlOptions: UrlOptionsWithConnectionParams;\n  keepAlive: PingPongOptions & {\n    enabled: boolean;\n  };\n}\n\n/**\n * Manages a WebSocket connection with support for reconnection, keep-alive mechanisms,\n * and observable state tracking.\n */\nexport class WsConnection {\n  static connectCount = 0;\n  public id = ++WsConnection.connectCount;\n\n  private readonly WebSocketPonyfill: typeof WebSocket;\n  private readonly urlOptions: UrlOptionsWithConnectionParams;\n  private readonly keepAliveOpts: WebSocketConnectionOptions['keepAlive'];\n  public readonly wsObservable = behaviorSubject<WebSocket | null>(null);\n\n  constructor(opts: WebSocketConnectionOptions) {\n    this.WebSocketPonyfill = opts.WebSocketPonyfill ?? WebSocket;\n    if (!this.WebSocketPonyfill) {\n      throw new Error(\n        \"No WebSocket implementation found - you probably don't want to use this on the server, but if you do you need to pass a `WebSocket`-ponyfill\",\n      );\n    }\n\n    this.urlOptions = opts.urlOptions;\n    this.keepAliveOpts = opts.keepAlive;\n  }\n\n  public get ws() {\n    return this.wsObservable.get();\n  }\n\n  private set ws(ws) {\n    this.wsObservable.next(ws);\n  }\n\n  /**\n   * Checks if the WebSocket connection is open and ready to communicate.\n   */\n  public isOpen(): this is { ws: WebSocket } {\n    return (\n      !!this.ws &&\n      this.ws.readyState === this.WebSocketPonyfill.OPEN &&\n      !this.openPromise\n    );\n  }\n\n  /**\n   * Checks if the WebSocket connection is closed or in the process of closing.\n   */\n  public isClosed(): this is { ws: WebSocket } {\n    return (\n      !!this.ws &&\n      (this.ws.readyState === this.WebSocketPonyfill.CLOSING ||\n        this.ws.readyState === this.WebSocketPonyfill.CLOSED)\n    );\n  }\n\n  /**\n   * Manages the WebSocket opening process, ensuring that only one open operation\n   * occurs at a time. Tracks the ongoing operation with `openPromise` to avoid\n   * redundant calls and ensure proper synchronization.\n   *\n   * Sets up the keep-alive mechanism and necessary event listeners for the connection.\n   *\n   * @returns A promise that resolves once the WebSocket connection is successfully opened.\n   */\n  private openPromise: Promise<void> | null = null;\n  public async open() {\n    if (this.openPromise) return this.openPromise;\n\n    this.id = ++WsConnection.connectCount;\n    const wsPromise = prepareUrl(this.urlOptions).then(\n      (url) => new this.WebSocketPonyfill(url),\n    );\n    this.openPromise = wsPromise.then(async (ws) => {\n      this.ws = ws;\n\n      // Setup ping listener\n      ws.addEventListener('message', function ({ data }) {\n        if (data === 'PING') {\n          this.send('PONG');\n        }\n      });\n\n      if (this.keepAliveOpts.enabled) {\n        setupPingInterval(ws, this.keepAliveOpts);\n      }\n\n      ws.addEventListener('close', () => {\n        if (this.ws === ws) {\n          this.ws = null;\n        }\n      });\n\n      await asyncWsOpen(ws);\n\n      if (this.urlOptions.connectionParams) {\n        ws.send(await buildConnectionMessage(this.urlOptions.connectionParams));\n      }\n    });\n\n    try {\n      await this.openPromise;\n    } finally {\n      this.openPromise = null;\n    }\n  }\n\n  /**\n   * Closes the WebSocket connection gracefully.\n   * Waits for any ongoing open operation to complete before closing.\n   */\n  public async close() {\n    try {\n      await this.openPromise;\n    } finally {\n      this.ws?.close();\n    }\n  }\n}\n\n/**\n * Provides a backward-compatible representation of the connection state.\n */\nexport function backwardCompatibility(connection: WsConnection) {\n  if (connection.isOpen()) {\n    return {\n      id: connection.id,\n      state: 'open',\n      ws: connection.ws,\n    } as const;\n  }\n\n  if (connection.isClosed()) {\n    return {\n      id: connection.id,\n      state: 'closed',\n      ws: connection.ws,\n    } as const;\n  }\n\n  if (!connection.ws) {\n    return null;\n  }\n\n  return {\n    id: connection.id,\n    state: 'connecting',\n    ws: connection.ws,\n  } as const;\n}\n", "import type { AnyTR<PERSON>Router } from '@trpc/server';\nimport type { BehaviorSubject } from '@trpc/server/observable';\nimport { behaviorSubject, observable } from '@trpc/server/observable';\nimport type {\n  CombinedDataTransformer,\n  TRPCClientIncomingMessage,\n  TRPCClientIncomingRequest,\n  TRPCClientOutgoingMessage,\n  TRPCResponseMessage,\n} from '@trpc/server/unstable-core-do-not-import';\nimport {\n  run,\n  sleep,\n  transformResult,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { TRPCClientError } from '../../../TRPCClientError';\nimport type { TRPCConnectionState } from '../../internals/subscriptions';\nimport type { Operation, OperationResultEnvelope } from '../../types';\nimport type { WebSocketClientOptions } from './options';\nimport { exponentialBackoff, keepAliveDefaults, lazyDefaults } from './options';\nimport type { TCallbacks } from './requestManager';\nimport { RequestManager } from './requestManager';\nimport { ResettableTimeout, TRPCWebSocketClosedError } from './utils';\nimport { backwardCompatibility, WsConnection } from './wsConnection';\n\n/**\n * A WebSocket client for managing TRPC operations, supporting lazy initialization,\n * reconnection, keep-alive, and request management.\n */\nexport class WsClient {\n  /**\n   * Observable tracking the current connection state, including errors.\n   */\n  public readonly connectionState: BehaviorSubject<\n    TRPCConnectionState<TRPCClientError<AnyTRPCRouter>>\n  >;\n\n  private allowReconnect = false;\n  private requestManager = new RequestManager();\n  private readonly activeConnection: WsConnection;\n  private readonly reconnectRetryDelay: (attemptIndex: number) => number;\n  private inactivityTimeout: ResettableTimeout;\n  private readonly callbacks: Pick<\n    WebSocketClientOptions,\n    'onOpen' | 'onClose' | 'onError'\n  >;\n  private readonly lazyMode: boolean;\n\n  constructor(opts: WebSocketClientOptions) {\n    // Initialize callbacks, connection parameters, and options.\n    this.callbacks = {\n      onOpen: opts.onOpen,\n      onClose: opts.onClose,\n      onError: opts.onError,\n    };\n\n    const lazyOptions = {\n      ...lazyDefaults,\n      ...opts.lazy,\n    };\n\n    // Set up inactivity timeout for lazy connections.\n    this.inactivityTimeout = new ResettableTimeout(() => {\n      if (\n        this.requestManager.hasOutgoingRequests() ||\n        this.requestManager.hasPendingRequests()\n      ) {\n        this.inactivityTimeout.reset();\n        return;\n      }\n\n      this.close().catch(() => null);\n    }, lazyOptions.closeMs);\n\n    // Initialize the WebSocket connection.\n    this.activeConnection = new WsConnection({\n      WebSocketPonyfill: opts.WebSocket,\n      urlOptions: opts,\n      keepAlive: {\n        ...keepAliveDefaults,\n        ...opts.keepAlive,\n      },\n    });\n    this.activeConnection.wsObservable.subscribe({\n      next: (ws) => {\n        if (!ws) return;\n        this.setupWebSocketListeners(ws);\n      },\n    });\n    this.reconnectRetryDelay = opts.retryDelayMs ?? exponentialBackoff;\n\n    this.lazyMode = lazyOptions.enabled;\n\n    this.connectionState = behaviorSubject<\n      TRPCConnectionState<TRPCClientError<AnyTRPCRouter>>\n    >({\n      type: 'state',\n      state: lazyOptions.enabled ? 'idle' : 'connecting',\n      error: null,\n    });\n\n    // Automatically open the connection if lazy mode is disabled.\n    if (!this.lazyMode) {\n      this.open().catch(() => null);\n    }\n  }\n\n  /**\n   * Opens the WebSocket connection. Handles reconnection attempts and updates\n   * the connection state accordingly.\n   */\n  private async open() {\n    this.allowReconnect = true;\n    if (this.connectionState.get().state !== 'connecting') {\n      this.connectionState.next({\n        type: 'state',\n        state: 'connecting',\n        error: null,\n      });\n    }\n\n    try {\n      await this.activeConnection.open();\n    } catch (error) {\n      this.reconnect(\n        new TRPCWebSocketClosedError({\n          message: 'Initialization error',\n          cause: error,\n        }),\n      );\n      return this.reconnecting;\n    }\n  }\n\n  /**\n   * Closes the WebSocket connection and stops managing requests.\n   * Ensures all outgoing and pending requests are properly finalized.\n   */\n  public async close() {\n    this.allowReconnect = false;\n    this.inactivityTimeout.stop();\n\n    const requestsToAwait: Promise<void>[] = [];\n    for (const request of this.requestManager.getRequests()) {\n      if (request.message.method === 'subscription') {\n        request.callbacks.complete();\n      } else if (request.state === 'outgoing') {\n        request.callbacks.error(\n          TRPCClientError.from(\n            new TRPCWebSocketClosedError({\n              message: 'Closed before connection was established',\n            }),\n          ),\n        );\n      } else {\n        requestsToAwait.push(request.end);\n      }\n    }\n\n    await Promise.all(requestsToAwait).catch(() => null);\n    await this.activeConnection.close().catch(() => null);\n\n    this.connectionState.next({\n      type: 'state',\n      state: 'idle',\n      error: null,\n    });\n  }\n\n  /**\n   * Method to request the server.\n   * Handles data transformation, batching of requests, and subscription lifecycle.\n   *\n   * @param op - The operation details including id, type, path, input and signal\n   * @param transformer - Data transformer for serializing requests and deserializing responses\n   * @param lastEventId - Optional ID of the last received event for subscriptions\n   *\n   * @returns An observable that emits operation results and handles cleanup\n   */\n  public request({\n    op: { id, type, path, input, signal },\n    transformer,\n    lastEventId,\n  }: {\n    op: Pick<Operation, 'id' | 'type' | 'path' | 'input' | 'signal'>;\n    transformer: CombinedDataTransformer;\n    lastEventId?: string;\n  }) {\n    return observable<\n      OperationResultEnvelope<unknown, TRPCClientError<AnyTRPCRouter>>,\n      TRPCClientError<AnyTRPCRouter>\n    >((observer) => {\n      const abort = this.batchSend(\n        {\n          id,\n          method: type,\n          params: {\n            input: transformer.input.serialize(input),\n            path,\n            lastEventId,\n          },\n        },\n        {\n          ...observer,\n          next(event) {\n            const transformed = transformResult(event, transformer.output);\n\n            if (!transformed.ok) {\n              observer.error(TRPCClientError.from(transformed.error));\n              return;\n            }\n\n            observer.next({\n              result: transformed.result,\n            });\n          },\n        },\n      );\n\n      return () => {\n        abort();\n\n        if (type === 'subscription' && this.activeConnection.isOpen()) {\n          this.send({\n            id,\n            method: 'subscription.stop',\n          });\n        }\n\n        signal?.removeEventListener('abort', abort);\n      };\n    });\n  }\n\n  public get connection() {\n    return backwardCompatibility(this.activeConnection);\n  }\n\n  /**\n   * Manages the reconnection process for the WebSocket using retry logic.\n   * Ensures that only one reconnection attempt is active at a time by tracking the current\n   * reconnection state in the `reconnecting` promise.\n   */\n  private reconnecting: Promise<void> | null = null;\n  private reconnect(closedError: TRPCWebSocketClosedError) {\n    this.connectionState.next({\n      type: 'state',\n      state: 'connecting',\n      error: TRPCClientError.from(closedError),\n    });\n    if (this.reconnecting) return;\n\n    const tryReconnect = async (attemptIndex: number) => {\n      try {\n        await sleep(this.reconnectRetryDelay(attemptIndex));\n        if (this.allowReconnect) {\n          await this.activeConnection.close();\n          await this.activeConnection.open();\n\n          if (this.requestManager.hasPendingRequests()) {\n            this.send(\n              this.requestManager\n                .getPendingRequests()\n                .map(({ message }) => message),\n            );\n          }\n        }\n        this.reconnecting = null;\n      } catch {\n        await tryReconnect(attemptIndex + 1);\n      }\n    };\n\n    this.reconnecting = tryReconnect(0);\n  }\n\n  private setupWebSocketListeners(ws: WebSocket) {\n    const handleCloseOrError = (cause: unknown) => {\n      const reqs = this.requestManager.getPendingRequests();\n      for (const { message, callbacks } of reqs) {\n        if (message.method === 'subscription') continue;\n\n        callbacks.error(\n          TRPCClientError.from(\n            cause ??\n              new TRPCWebSocketClosedError({\n                message: 'WebSocket closed',\n                cause,\n              }),\n          ),\n        );\n        this.requestManager.delete(message.id);\n      }\n    };\n\n    ws.addEventListener('open', () => {\n      run(async () => {\n        if (this.lazyMode) {\n          this.inactivityTimeout.start();\n        }\n\n        this.callbacks.onOpen?.();\n\n        this.connectionState.next({\n          type: 'state',\n          state: 'pending',\n          error: null,\n        });\n      }).catch((error) => {\n        ws.close(3000);\n        handleCloseOrError(error);\n      });\n    });\n\n    ws.addEventListener('message', ({ data }) => {\n      this.inactivityTimeout.reset();\n\n      if (typeof data !== 'string' || ['PING', 'PONG'].includes(data)) return;\n\n      const incomingMessage = JSON.parse(data) as TRPCClientIncomingMessage;\n      if ('method' in incomingMessage) {\n        this.handleIncomingRequest(incomingMessage);\n        return;\n      }\n\n      this.handleResponseMessage(incomingMessage);\n    });\n\n    ws.addEventListener('close', (event) => {\n      handleCloseOrError(event);\n      this.callbacks.onClose?.(event);\n\n      if (!this.lazyMode || this.requestManager.hasPendingSubscriptions()) {\n        this.reconnect(\n          new TRPCWebSocketClosedError({\n            message: 'WebSocket closed',\n            cause: event,\n          }),\n        );\n      }\n    });\n\n    ws.addEventListener('error', (event) => {\n      handleCloseOrError(event);\n      this.callbacks.onError?.(event);\n\n      this.reconnect(\n        new TRPCWebSocketClosedError({\n          message: 'WebSocket closed',\n          cause: event,\n        }),\n      );\n    });\n  }\n\n  private handleResponseMessage(message: TRPCResponseMessage) {\n    const request = this.requestManager.getPendingRequest(message.id);\n    if (!request) return;\n\n    request.callbacks.next(message);\n\n    let completed = true;\n    if ('result' in message && request.message.method === 'subscription') {\n      if (message.result.type === 'data') {\n        request.message.params.lastEventId = message.result.id;\n      }\n\n      if (message.result.type !== 'stopped') {\n        completed = false;\n      }\n    }\n\n    if (completed) {\n      request.callbacks.complete();\n      this.requestManager.delete(message.id);\n    }\n  }\n\n  private handleIncomingRequest(message: TRPCClientIncomingRequest) {\n    if (message.method === 'reconnect') {\n      this.reconnect(\n        new TRPCWebSocketClosedError({\n          message: 'Server requested reconnect',\n        }),\n      );\n    }\n  }\n\n  /**\n   * Sends a message or batch of messages directly to the server.\n   */\n  private send(\n    messageOrMessages: TRPCClientOutgoingMessage | TRPCClientOutgoingMessage[],\n  ) {\n    if (!this.activeConnection.isOpen()) {\n      throw new Error('Active connection is not open');\n    }\n\n    const messages =\n      messageOrMessages instanceof Array\n        ? messageOrMessages\n        : [messageOrMessages];\n    this.activeConnection.ws.send(\n      JSON.stringify(messages.length === 1 ? messages[0] : messages),\n    );\n  }\n\n  /**\n   * Groups requests for batch sending.\n   *\n   * @returns A function to abort the batched request.\n   */\n  private batchSend(message: TRPCClientOutgoingMessage, callbacks: TCallbacks) {\n    this.inactivityTimeout.reset();\n\n    run(async () => {\n      if (!this.activeConnection.isOpen()) {\n        await this.open();\n      }\n      await sleep(0);\n\n      if (!this.requestManager.hasOutgoingRequests()) return;\n\n      this.send(this.requestManager.flush().map(({ message }) => message));\n    }).catch((err) => {\n      this.requestManager.delete(message.id);\n      callbacks.error(TRPCClientError.from(err));\n    });\n\n    return this.requestManager.register(message, callbacks);\n  }\n}\n", "import type { WebSocketClientOptions } from './wsClient/options';\nimport { WsClient } from './wsClient/wsClient';\n\nexport function createWSClient(opts: WebSocketClientOptions) {\n  return new WsClient(opts);\n}\n\nexport type TRPCWebSocketClient = ReturnType<typeof createWSClient>;\n\nexport { WebSocketClientOptions };\n", "import { observable } from '@trpc/server/observable';\nimport type {\n  AnyRouter,\n  inferClientTypes,\n} from '@trpc/server/unstable-core-do-not-import';\nimport type { TransformerOptions } from '../../unstable-internals';\nimport { getTransformer } from '../../unstable-internals';\nimport type { TRPCLink } from '../types';\nimport type {\n  TRPCWebSocketClient,\n  WebSocketClientOptions,\n} from './createWsClient';\nimport { createWSClient } from './createWsClient';\n\nexport type WebSocketLinkOptions<TRouter extends AnyRouter> = {\n  client: TRPCWebSocketClient;\n} & TransformerOptions<inferClientTypes<TRouter>>;\n\nexport function wsLink<TRouter extends AnyRouter>(\n  opts: WebSocketLinkOptions<TRouter>,\n): TRPCLink<TRouter> {\n  const { client } = opts;\n  const transformer = getTransformer(opts.transformer);\n  return () => {\n    return ({ op }) => {\n      return observable((observer) => {\n        const connStateSubscription =\n          op.type === 'subscription'\n            ? client.connectionState.subscribe({\n                next(result) {\n                  observer.next({\n                    result,\n                    context: op.context,\n                  });\n                },\n              })\n            : null;\n\n        const requestSubscription = client\n          .request({\n            op,\n            transformer,\n          })\n          .subscribe(observer);\n\n        return () => {\n          requestSubscription.unsubscribe();\n          connStateSubscription?.unsubscribe();\n        };\n      });\n    };\n  };\n}\n\nexport { TRPCWebSocketClient, WebSocketClientOptions, createWSClient };\n"], "names": ["lazyDefaults: LazyOptions", "keepAliveDefaults: KeepAliveOptions", "attemptIndex: number", "value: T | ((...args: TArgs) => T)", "opts: { message: string; cause?: unknown }", "onTimeout: () => void", "timeoutMs: number", "resolve: (value: T | PromiseLike<T>) => void", "reject: (reason?: any) => void", "urlOptions: UrlOptionsWithConnectionParams", "connectionParams: CallbackOrValue<TRPCRequestInfo['connectionParams']>", "message: TRPCConnectionParamsMessage", "message: TRPCClientOutgoingMessage", "callbacks: T<PERSON>allbacks", "messageId: MessageIdLike", "ws: WebSocket", "pingTimeout: ReturnType<typeof setTimeout> | undefined", "pongTimeout: ReturnType<typeof setTimeout> | undefined", "opts: WebSocketConnectionOptions", "this", "connection: WsConnection", "opts: WebSocketClientOptions", "this", "requestsToAwait: Promise<void>[]", "closedError: TRPCWebSocketClosedError", "attemptIndex: number", "ws: WebSocket", "cause: unknown", "message: TRPCResponseMessage", "message: TRPCClientIncomingRequest", "messageOrMessages: TRPCClientOutgoingMessage | TRPCClientOutgoingMessage[]", "message: TRPCClientOutgoingMessage", "callbacks: T<PERSON>allbacks", "message", "opts: WebSocketClientOptions", "opts: WebSocketLinkOptions<TRouter>"], "mappings": ";;;;;;;;;;;;;;;;;;AAiEA,MAAaA,eAA4B;IACvC,SAAS;IACT,SAAS;AACV;AASD,MAAaC,oBAAsC;IACjD,SAAS;IACT,eAAe;IACf,YAAY;AACb;;;;;GAOD,MAAa,qBAAqB,CAACC,iBAAyB;IAC1D,OAAO,iBAAiB,IAAI,IAAI,KAAK,GAAA,CAAI,MAAO,KAAK,cAAc,IAAM;AAC1E;;;;;;GCpFD,MAAa,WAAW,CACtBC,OACA,GAAG,SACG;IACN,OAAA,OAAc,UAAU,aACnB,MAAgC,GAAG,KAAK,GACzC;AACL;;;;ACHD,IAAa,2BAAb,MAAa,iCAAiC,MAAM;IAClD,YAAYC,IAAAA,CAA4C;QACtD,KAAA,CAAM,KAAK,OAAA,EAAS;YAClB,OAAO,KAAK,KAAA;QACb,EAAC;QACF,IAAA,CAAK,IAAA,GAAO;QACZ,OAAO,cAAA,CAAe,IAAA,EAAM,yBAAyB,SAAA,CAAU;IAChE;AACF;;;;GAMD,IAAa,oBAAb,MAA+B;IAG7B,YACmBC,SAAAA,EACAC,SAAAA,CACjB;QAFiB,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;6CAiEnB,IAAA,EArEQ,WAAA,KAAA;IAKJ;;;;IAMG,QAAQ;QACb,IAAA,CAAK,IAAA,CAAK,OAAA,CAAS,CAAA;QAEnB,aAAa,IAAA,CAAK,OAAA,CAAQ;QAC1B,IAAA,CAAK,OAAA,GAAU,WAAW,IAAA,CAAK,SAAA,EAAW,IAAA,CAAK,SAAA,CAAU;IAC1D;IAEM,QAAQ;QACb,aAAa,IAAA,CAAK,OAAA,CAAQ;QAC1B,IAAA,CAAK,OAAA,GAAU,WAAW,IAAA,CAAK,SAAA,EAAW,IAAA,CAAK,SAAA,CAAU;IAC1D;IAEM,OAAO;QACZ,aAAa,IAAA,CAAK,OAAA,CAAQ;QAC1B,IAAA,CAAK,OAAA,GAAA,KAAA;IACN;AACF;AAGD,SAAgB,gBAAmB;IACjC,IAAIC;IACJ,IAAIC;IACJ,MAAM,UAAU,IAAI,QAAW,CAAC,KAAK,QAAQ;QAC3C,UAAU;QACV,SAAS;IACV;IAGD,OAAO;QAAE;QAAkB;QAAkB;IAAS;AACvD;;;;;GAOD,eAAsB,WAAWC,UAAAA,EAA4C;IAC3E,MAAM,MAAM,MAAM,SAAS,WAAW,GAAA,CAAI;IAE1C,IAAA,CAAK,WAAW,gBAAA,CAAkB,CAAA,OAAO;IAGzC,MAAM,SAAS,IAAI,QAAA,CAAS,IAAI,GAAG,MAAM;IACzC,MAAM,mBAAA,CAAoB,EAAE,OAAO,kBAAA,CAAA;IAEnC,OAAO,MAAM;AACd;AAED,eAAsB,uBACpBC,gBAAAA,EACA;IACA,MAAMC,UAAuC;QAC3C,QAAQ;QACR,MAAM,MAAM,SAAS,iBAAiB;IACvC;IAED,OAAO,KAAK,SAAA,CAAU,QAAQ;AAC/B;;;;;;;;;;;GCzDD,IAAa,iBAAb,MAA4B;;6CAmJ1B,IAAA,EA/IQ,oBAAmB,IAAI;6CA+I9B,IAAA,EAxIO,mBAA8C,CAAE;;;;;;;;;IAUjD,SAASC,OAAAA,EAAoCC,SAAAA,EAAuB;QACzE,MAAM,EAAE,SAAS,GAAA,EAAK,OAAA,EAAS,GAAG,eAAqB;QAEvD,IAAA,CAAK,gBAAA,CAAiB,IAAA,CAAK;YACzB,IAAI,OAAO,QAAQ,EAAA,CAAG;YACtB;YACA;YACA,WAAW;gBACT,MAAM,UAAU,IAAA;gBAChB,UAAU,MAAM;oBACd,UAAU,QAAA,EAAU;oBACpB,SAAS;gBACV;gBACD,OAAO,CAAC,MAAM;oBACZ,UAAU,KAAA,CAAM,EAAE;oBAClB,SAAS;gBACV;YACF;QACF,EAAC;QAEF,OAAO,MAAM;YACX,IAAA,CAAK,MAAA,CAAO,QAAQ,EAAA,CAAG;YACvB,UAAU,QAAA,EAAU;YACpB,SAAS;QACV;IACF;;;IAKM,OAAOC,SAAAA,EAA0B;QACtC,IAAI,cAAc,KAAM,CAAA;QAExB,IAAA,CAAK,gBAAA,GAAmB,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAC5C,CAAC,EAAE,EAAA,EAAI,GAAK,OAAO,OAAO,UAAU,CACrC;QACD,OAAO,IAAA,CAAK,eAAA,CAAgB,OAAO,UAAU,CAAA;IAC9C;;;;;;;;IAUM,QAAQ;QACb,MAAM,WAAW,IAAA,CAAK,gBAAA;QACtB,IAAA,CAAK,gBAAA,GAAmB,CAAE,CAAA;QAE1B,KAAK,MAAM,WAAW,SACpB,IAAA,CAAK,eAAA,CAAgB,QAAQ,EAAA,CAAA,GAAM;QAErC,OAAO;IACR;;;;IAMM,qBAAqB;QAC1B,OAAO,OAAO,MAAA,CAAO,IAAA,CAAK,eAAA,CAAgB;IAC3C;;;IAKM,kBAAkBA,SAAAA,EAA0B;QACjD,IAAI,cAAc,KAAM,CAAA,OAAO;QAE/B,OAAO,IAAA,CAAK,eAAA,CAAgB,OAAO,UAAU,CAAA;IAC9C;;;IAKM,sBAAsB;QAC3B,OAAO,IAAA,CAAK,gBAAA;IACb;;;;;IAOM,cAAc;QACnB,OAAO,CACL;eAAG,IAAA,CAAK,mBAAA,EAAqB,CAAC,GAAA,CAAI,CAAC,UAAA,CAAa;oBAC9C,OAAO;oBACP,SAAS,QAAQ,OAAA;oBACjB,KAAK,QAAQ,GAAA;oBACb,WAAW,QAAQ,SAAA;gBACpB,CAAA,EAAE,EACH;eAAG,IAAA,CAAK,kBAAA,EAAoB,CAAC,GAAA,CAAI,CAAC,UAAA,CAAa;oBAC7C,OAAO;oBACP,SAAS,QAAQ,OAAA;oBACjB,KAAK,QAAQ,GAAA;oBACb,WAAW,QAAQ,SAAA;gBACpB,CAAA,EAAE,AACJ;SAAA;IACF;;;IAKM,qBAAqB;QAC1B,OAAO,IAAA,CAAK,kBAAA,EAAoB,CAAC,MAAA,GAAS;IAC3C;;;IAKM,0BAA0B;QAC/B,OAAO,IAAA,CAAK,kBAAA,EAAoB,CAAC,IAAA,CAC/B,CAAC,UAAY,QAAQ,OAAA,CAAQ,MAAA,KAAW,eACzC;IACF;;;IAKM,sBAAsB;QAC3B,OAAO,IAAA,CAAK,gBAAA,CAAiB,MAAA,GAAS;IACvC;AACF;;;;;;;;GC7KD,SAAS,YAAYC,EAAAA,EAAe;IAClC,MAAM,EAAE,OAAA,EAAS,OAAA,EAAS,MAAA,EAAQ,GAAG,eAAqB;IAE1D,GAAG,gBAAA,CAAiB,QAAQ,MAAM;QAChC,GAAG,mBAAA,CAAoB,SAAS,OAAO;QACvC,SAAS;IACV,EAAC;IACF,GAAG,gBAAA,CAAiB,SAAS,OAAO;IAEpC,OAAO;AACR;;;;;;;;;;;;GA0BD,SAAS,kBACPA,EAAAA,EACA,EAAE,UAAA,EAAY,aAAA,EAAgC,EAC9C;IACA,IAAIC;IACJ,IAAIC;IAEJ,SAAS,QAAQ;QACf,cAAc,WAAW,MAAM;YAC7B,GAAG,IAAA,CAAK,OAAO;YACf,cAAc,WAAW,MAAM;gBAC7B,GAAG,KAAA,EAAO;YACX,GAAE,cAAc;QAClB,GAAE,WAAW;IACf;IAED,SAAS,QAAQ;QACf,aAAa,YAAY;QACzB,OAAO;IACR;IAED,SAAS,OAAO;QACd,aAAa,YAAY;QACzB,OAAO;IACR;IAED,GAAG,gBAAA,CAAiB,QAAQ,MAAM;IAClC,GAAG,gBAAA,CAAiB,WAAW,CAAC,EAAE,IAAA,EAAM,KAAK;QAC3C,aAAa,YAAY;QACzB,OAAO;QAEP,IAAI,SAAS,OACX,CAAA,MAAM;IAET,EAAC;IACF,GAAG,gBAAA,CAAiB,SAAS,MAAM;QACjC,aAAa,YAAY;QACzB,aAAa,YAAY;IAC1B,EAAC;AACH;;;;GAcD,IAAa,eAAb,MAAa,aAAa;IASxB,YAAYC,IAAAA,CAAkC;;6CAwI9C,IAAA,EA/IO,MAAK,EAAE,aAAa,YAAA;6CA+I1B,IAAA,EA7IgB,qBAAA,KAAA;6CA6If,IAAA,EA5Ie,cAAA,KAAA;6CA4Id,IAAA,EA3Ic,iBAAA,KAAA;6CA2Ib,IAAA,EA1IY,iRAAe,kBAAA,EAAkC,KAAK;6CA0IjE,IAAA,EArFG,eAAoC;QAlD1C,IAAA,CAAK,iBAAA,GAAA,CAAA,wBAAoB,KAAK,iBAAA,MAAA,QAAA,0BAAA,KAAA,IAAA,wBAAqB;QACnD,IAAA,CAAK,IAAA,CAAK,iBAAA,CACR,CAAA,MAAM,IAAI,MACR;QAIJ,IAAA,CAAK,UAAA,GAAa,KAAK,UAAA;QACvB,IAAA,CAAK,aAAA,GAAgB,KAAK,SAAA;IAC3B;IAED,IAAW,KAAK;QACd,OAAO,IAAA,CAAK,YAAA,CAAa,GAAA,EAAK;IAC/B;IAED,IAAY,GAAG,EAAA,EAAI;QACjB,IAAA,CAAK,YAAA,CAAa,IAAA,CAAK,GAAG;IAC3B;;;IAKM,SAAoC;QACzC,OAAA,CAAA,CACI,IAAA,CAAK,EAAA,IACP,IAAA,CAAK,EAAA,CAAG,UAAA,KAAe,IAAA,CAAK,iBAAA,CAAkB,IAAA,IAAA,CAC7C,IAAA,CAAK,WAAA;IAET;;;IAKM,WAAsC;QAC3C,OAAA,CAAA,CACI,IAAA,CAAK,EAAA,IAAA,CACN,IAAA,CAAK,EAAA,CAAG,UAAA,KAAe,IAAA,CAAK,iBAAA,CAAkB,OAAA,IAC7C,IAAA,CAAK,EAAA,CAAG,UAAA,KAAe,IAAA,CAAK,iBAAA,CAAkB,MAAA;IAEnD;IAYD,MAAa,OAAO;oBAoFd,IAAA;QAnFJ,IAAIC,MAAK,WAAA,CAAa,CAAA,OAAOA,MAAK,WAAA;QAElC,MAAK,EAAA,GAAK,EAAE,aAAa,YAAA;QACzB,MAAM,YAAY,WAAWA,MAAK,UAAA,CAAW,CAAC,IAAA,CAC5C,CAAC,MAAQ,IAAIA,MAAK,iBAAA,CAAkB,KACrC;QACD,MAAK,WAAA,GAAc,UAAU,IAAA,CAAK,OAAO,OAAO;YAC9C,MAAK,EAAA,GAAK;YAGV,GAAG,gBAAA,CAAiB,WAAW,SAAU,EAAE,IAAA,EAAM,EAAE;gBACjD,IAAI,SAAS,OACX,CAAA,IAAA,CAAK,IAAA,CAAK,OAAO;YAEpB,EAAC;YAEF,IAAIA,MAAK,aAAA,CAAc,OAAA,CACrB,CAAA,kBAAkB,IAAIA,MAAK,aAAA,CAAc;YAG3C,GAAG,gBAAA,CAAiB,SAAS,MAAM;gBACjC,IAAIA,MAAK,EAAA,KAAO,GACd,CAAA,MAAK,EAAA,GAAK;YAEb,EAAC;YAEF,MAAM,YAAY,GAAG;YAErB,IAAIA,MAAK,UAAA,CAAW,gBAAA,CAClB,CAAA,GAAG,IAAA,CAAK,MAAM,uBAAuBA,MAAK,UAAA,CAAW,gBAAA,CAAiB,CAAC;QAE1E,EAAC;QAEF,IAAI;YACF,MAAMA,MAAK,WAAA;QACZ,SAAS;YACR,MAAK,WAAA,GAAc;QACpB;IACF;;;;IAMD,MAAa,QAAQ;qBAuCd,IAAA;QAtCL,IAAI;YACF,MAAMA,OAAK,WAAA;QACZ,SAAS;;YACR,CAAA,WAAA,OAAK,EAAA,MAAA,QAAA,aAAA,KAAA,KAAL,SAAS,KAAA,EAAO;QACjB;IACF;AACF;mDAhHQ,gBAAe;;;GAqHxB,SAAgB,sBAAsBC,UAAAA,EAA0B;IAC9D,IAAI,WAAW,MAAA,EAAQ,CACrB,CAAA,OAAO;QACL,IAAI,WAAW,EAAA;QACf,OAAO;QACP,IAAI,WAAW,EAAA;IAChB;IAGH,IAAI,WAAW,QAAA,EAAU,CACvB,CAAA,OAAO;QACL,IAAI,WAAW,EAAA;QACf,OAAO;QACP,IAAI,WAAW,EAAA;IAChB;IAGH,IAAA,CAAK,WAAW,EAAA,CACd,CAAA,OAAO;IAGT,OAAO;QACL,IAAI,WAAW,EAAA;QACf,OAAO;QACP,IAAI,WAAW,EAAA;IAChB;AACF;;;;;;;;GCrND,IAAa,WAAb,MAAsB;IAmBpB,YAAYC,IAAAA,CAA8B;;2CAgYzC,IAAA,EA/Ye,mBAAA,KAAA;2CA+Yd,IAAA,EA3YM,kBAAiB;2CA2YtB,IAAA,EA1YK,kBAAiB,IAAI;2CA0YzB,IAAA,EAzYa,oBAAA,KAAA;2CAyYZ,IAAA,EAxYY,uBAAA,KAAA;2CAwYX,IAAA,EAvYE,qBAAA,KAAA;2CAuYD,IAAA,EAtYU,aAAA,KAAA;2CAsYT,IAAA,EAlYS,YAAA,KAAA;2CAkYR,IAAA,EA7LD,gBAAqC;QAjM3C,IAAA,CAAK,SAAA,GAAY;YACf,QAAQ,KAAK,MAAA;YACb,SAAS,KAAK,OAAA;YACd,SAAS,KAAK,OAAA;QACf;QAED,MAAM,cAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACD,eACA,KAAK,IAAA;QAIV,IAAA,CAAK,iBAAA,GAAoB,IAAI,kBAAkB,MAAM;YACnD,IACE,IAAA,CAAK,cAAA,CAAe,mBAAA,EAAqB,IACzC,IAAA,CAAK,cAAA,CAAe,kBAAA,EAAoB,EACxC;gBACA,IAAA,CAAK,iBAAA,CAAkB,KAAA,EAAO;gBAC9B;YACD;YAED,IAAA,CAAK,KAAA,EAAO,CAAC,KAAA,CAAM,IAAM,KAAK;QAC/B,GAAE,YAAY,OAAA;QAGf,IAAA,CAAK,gBAAA,GAAmB,IAAI,aAAa;YACvC,mBAAmB,KAAK,SAAA;YACxB,YAAY;YACZ,WAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACK,oBACA,KAAK,SAAA;QAEX;QACD,IAAA,CAAK,gBAAA,CAAiB,YAAA,CAAa,SAAA,CAAU;YAC3C,MAAM,CAAC,OAAO;gBACZ,IAAA,CAAK,GAAI,CAAA;gBACT,IAAA,CAAK,uBAAA,CAAwB,GAAG;YACjC;QACF,EAAC;QACF,IAAA,CAAK,mBAAA,GAAA,CAAA,qBAAsB,KAAK,YAAA,MAAA,QAAA,uBAAA,KAAA,IAAA,qBAAgB;QAEhD,IAAA,CAAK,QAAA,GAAW,YAAY,OAAA;QAE5B,IAAA,CAAK,eAAA,GAAkB,mRAAA,EAErB;YACA,MAAM;YACN,OAAO,YAAY,OAAA,GAAU,SAAS;YACtC,OAAO;QACR,EAAC;QAGF,IAAA,CAAK,IAAA,CAAK,QAAA,CACR,CAAA,IAAA,CAAK,IAAA,EAAM,CAAC,KAAA,CAAM,IAAM,KAAK;IAEhC;;;;IAMD,MAAc,OAAO;oBAiUX,IAAA;QAhUR,MAAK,cAAA,GAAiB;QACtB,IAAI,MAAK,eAAA,CAAgB,GAAA,EAAK,CAAC,KAAA,KAAU,aACvC,CAAA,MAAK,eAAA,CAAgB,IAAA,CAAK;YACxB,MAAM;YACN,OAAO;YACP,OAAO;QACR,EAAC;QAGJ,IAAI;YACF,MAAM,MAAK,gBAAA,CAAiB,IAAA,EAAM;QACnC,EAAA,OAAQ,OAAO;YACd,MAAK,SAAA,CACH,IAAI,yBAAyB;gBAC3B,SAAS;gBACT,OAAO;YACR,GACF;YACD,OAAOC,MAAK,YAAA;QACb;IACF;;;;IAMD,MAAa,QAAQ;qBAsSV,IAAA;QArST,OAAK,cAAA,GAAiB;QACtB,OAAK,iBAAA,CAAkB,IAAA,EAAM;QAE7B,MAAMC,kBAAmC,CAAE,CAAA;QAC3C,KAAK,MAAM,WAAW,OAAK,cAAA,CAAe,WAAA,EAAa,CACrD,IAAI,QAAQ,OAAA,CAAQ,MAAA,KAAW,eAC7B,CAAA,QAAQ,SAAA,CAAU,QAAA,EAAU;iBACnB,QAAQ,KAAA,KAAU,WAC3B,CAAA,QAAQ,SAAA,CAAU,KAAA,mUAChB,kBAAA,CAAgB,IAAA,CACd,IAAI,yBAAyB;YAC3B,SAAS;QACV,GACF,CACF;aAED,gBAAgB,IAAA,CAAK,QAAQ,GAAA,CAAI;QAIrC,MAAM,QAAQ,GAAA,CAAI,gBAAgB,CAAC,KAAA,CAAM,IAAM,KAAK;QACpD,MAAM,OAAK,gBAAA,CAAiB,KAAA,EAAO,CAAC,KAAA,CAAM,IAAM,KAAK;QAErD,OAAK,eAAA,CAAgB,IAAA,CAAK;YACxB,MAAM;YACN,OAAO;YACP,OAAO;QACR,EAAC;IACH;;;;;;;;;;IAYM,QAAQ,EACb,IAAI,EAAE,EAAA,EAAI,IAAA,EAAM,IAAA,EAAM,KAAA,EAAO,MAAA,EAAQ,EACrC,WAAA,EACA,WAAA,EAKD,EAAE;QACD,uQAAO,aAAA,EAGL,CAAC,aAAa;YACd,MAAM,QAAQ,IAAA,CAAK,SAAA,CACjB;gBACE;gBACA,QAAQ;gBACR,QAAQ;oBACN,OAAO,YAAY,KAAA,CAAM,SAAA,CAAU,MAAM;oBACzC;oBACA;gBACD;YACF,GAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAEI,WAAA,CAAA,GAAA;gBACH,MAAK,KAAA,EAAO;oBACV,MAAM,2QAAc,kBAAA,EAAgB,OAAO,YAAY,MAAA,CAAO;oBAE9D,IAAA,CAAK,YAAY,EAAA,EAAI;wBACnB,SAAS,KAAA,mUAAM,kBAAA,CAAgB,IAAA,CAAK,YAAY,KAAA,CAAM,CAAC;wBACvD;oBACD;oBAED,SAAS,IAAA,CAAK;wBACZ,QAAQ,YAAY,MAAA;oBACrB,EAAC;gBACH;YAAA,GAEJ;YAED,OAAO,MAAM;gBACX,OAAO;gBAEP,IAAI,SAAS,kBAAkB,IAAA,CAAK,gBAAA,CAAiB,MAAA,EAAQ,CAC3D,CAAA,IAAA,CAAK,IAAA,CAAK;oBACR;oBACA,QAAQ;gBACT,EAAC;gBAGJ,WAAA,QAAA,WAAA,KAAA,KAAA,OAAQ,mBAAA,CAAoB,SAAS,MAAM;YAC5C;QACF,EAAC;IACH;IAED,IAAW,aAAa;QACtB,OAAO,sBAAsB,IAAA,CAAK,gBAAA,CAAiB;IACpD;IAQO,UAAUC,WAAAA,EAAuC;qBA4L7C,IAAA;QA3LV,IAAA,CAAK,eAAA,CAAgB,IAAA,CAAK;YACxB,MAAM;YACN,OAAO;YACP,yUAAO,kBAAA,CAAgB,IAAA,CAAK,YAAY;QACzC,EAAC;QACF,IAAI,IAAA,CAAK,YAAA,CAAc,CAAA;QAEvB,MAAM,eAAe,OAAOC,iBAAyB;YACnD,IAAI;gBACF,iQAAM,QAAA,EAAM,OAAK,mBAAA,CAAoB,aAAa,CAAC;gBACnD,IAAIH,OAAK,cAAA,EAAgB;oBACvB,MAAM,OAAK,gBAAA,CAAiB,KAAA,EAAO;oBACnC,MAAM,OAAK,gBAAA,CAAiB,IAAA,EAAM;oBAElC,IAAI,OAAK,cAAA,CAAe,kBAAA,EAAoB,CAC1C,CAAA,OAAK,IAAA,CACH,OAAK,cAAA,CACF,kBAAA,EAAoB,CACpB,GAAA,CAAI,CAAC,EAAE,OAAA,EAAS,GAAK,QAAQ,CACjC;gBAEJ;gBACD,OAAK,YAAA,GAAe;YACrB,EAAA,OAAA,SAAO;gBACN,MAAM,aAAa,eAAe,EAAE;YACrC;QACF;QAED,IAAA,CAAK,YAAA,GAAe,aAAa,EAAE;IACpC;IAEO,wBAAwBI,EAAAA,EAAe;qBA4JlC,IAAA;QA3JX,MAAM,qBAAqB,CAACC,UAAmB;YAC7C,MAAM,OAAO,IAAA,CAAK,cAAA,CAAe,kBAAA,EAAoB;YACrD,KAAK,MAAM,EAAE,OAAA,EAAS,SAAA,EAAW,IAAI,KAAM;gBACzC,IAAI,QAAQ,MAAA,KAAW,eAAgB,CAAA;gBAEvC,UAAU,KAAA,kUACR,mBAAA,CAAgB,IAAA,CACd,UAAA,QAAA,UAAA,KAAA,IAAA,QACE,IAAI,yBAAyB;oBAC3B,SAAS;oBACT;gBACD,GACJ,CACF;gBACD,IAAA,CAAK,cAAA,CAAe,MAAA,CAAO,QAAQ,EAAA,CAAG;YACvC;QACF;QAED,GAAG,gBAAA,CAAiB,QAAQ,MAAM;YAChC,CAAA,GAAA,sPAAA,CAAA,MAAA,EAAI,YAAY;;gBACd,IAAIL,OAAK,QAAA,CACP,CAAA,OAAK,iBAAA,CAAkB,KAAA,EAAO;gBAGhC,CAAA,wBAAA,CAAA,kBAAA,OAAK,SAAA,EAAU,MAAA,MAAA,QAAA,0BAAA,KAAA,KAAf,sBAAA,IAAA,CAAA,gBAAyB;gBAEzB,OAAK,eAAA,CAAgB,IAAA,CAAK;oBACxB,MAAM;oBACN,OAAO;oBACP,OAAO;gBACR,EAAC;YACH,EAAC,CAAC,KAAA,CAAM,CAAC,UAAU;gBAClB,GAAG,KAAA,CAAM,IAAK;gBACd,mBAAmB,MAAM;YAC1B,EAAC;QACH,EAAC;QAEF,GAAG,gBAAA,CAAiB,WAAW,CAAC,EAAE,IAAA,EAAM,KAAK;YAC3C,IAAA,CAAK,iBAAA,CAAkB,KAAA,EAAO;YAE9B,IAAA,OAAW,SAAS,YAAY;gBAAC;gBAAQ,MAAO;aAAA,CAAC,QAAA,CAAS,KAAK,CAAE,CAAA;YAEjE,MAAM,kBAAkB,KAAK,KAAA,CAAM,KAAK;YACxC,IAAI,YAAY,iBAAiB;gBAC/B,IAAA,CAAK,qBAAA,CAAsB,gBAAgB;gBAC3C;YACD;YAED,IAAA,CAAK,qBAAA,CAAsB,gBAAgB;QAC5C,EAAC;QAEF,GAAG,gBAAA,CAAiB,SAAS,CAAC,UAAU;;YACtC,mBAAmB,MAAM;YACzB,CAAA,wBAAA,CAAA,mBAAA,IAAA,CAAK,SAAA,EAAU,OAAA,MAAA,QAAA,0BAAA,KAAA,KAAf,sBAAA,IAAA,CAAA,kBAAyB,MAAM;YAE/B,IAAA,CAAK,IAAA,CAAK,QAAA,IAAY,IAAA,CAAK,cAAA,CAAe,uBAAA,EAAyB,CACjE,CAAA,IAAA,CAAK,SAAA,CACH,IAAI,yBAAyB;gBAC3B,SAAS;gBACT,OAAO;YACR,GACF;QAEJ,EAAC;QAEF,GAAG,gBAAA,CAAiB,SAAS,CAAC,UAAU;;YACtC,mBAAmB,MAAM;YACzB,CAAA,wBAAA,CAAA,mBAAA,IAAA,CAAK,SAAA,EAAU,OAAA,MAAA,QAAA,0BAAA,KAAA,KAAf,sBAAA,IAAA,CAAA,kBAAyB,MAAM;YAE/B,IAAA,CAAK,SAAA,CACH,IAAI,yBAAyB;gBAC3B,SAAS;gBACT,OAAO;YACR,GACF;QACF,EAAC;IACH;IAEO,sBAAsBM,OAAAA,EAA8B;QAC1D,MAAM,UAAU,IAAA,CAAK,cAAA,CAAe,iBAAA,CAAkB,QAAQ,EAAA,CAAG;QACjE,IAAA,CAAK,QAAS,CAAA;QAEd,QAAQ,SAAA,CAAU,IAAA,CAAK,QAAQ;QAE/B,IAAI,YAAY;QAChB,IAAI,YAAY,WAAW,QAAQ,OAAA,CAAQ,MAAA,KAAW,gBAAgB;YACpE,IAAI,QAAQ,MAAA,CAAO,IAAA,KAAS,OAC1B,CAAA,QAAQ,OAAA,CAAQ,MAAA,CAAO,WAAA,GAAc,QAAQ,MAAA,CAAO,EAAA;YAGtD,IAAI,QAAQ,MAAA,CAAO,IAAA,KAAS,UAC1B,CAAA,YAAY;QAEf;QAED,IAAI,WAAW;YACb,QAAQ,SAAA,CAAU,QAAA,EAAU;YAC5B,IAAA,CAAK,cAAA,CAAe,MAAA,CAAO,QAAQ,EAAA,CAAG;QACvC;IACF;IAEO,sBAAsBC,OAAAA,EAAoC;QAChE,IAAI,QAAQ,MAAA,KAAW,YACrB,CAAA,IAAA,CAAK,SAAA,CACH,IAAI,yBAAyB;YAC3B,SAAS;QACV,GACF;IAEJ;;;IAKO,KACNC,iBAAAA,EACA;QACA,IAAA,CAAK,IAAA,CAAK,gBAAA,CAAiB,MAAA,EAAQ,CACjC,CAAA,MAAM,IAAI,MAAM;QAGlB,MAAM,WACJ,6BAA6B,QACzB,oBACA;YAAC,iBAAkB;SAAA;QACzB,IAAA,CAAK,gBAAA,CAAiB,EAAA,CAAG,IAAA,CACvB,KAAK,SAAA,CAAU,SAAS,MAAA,KAAW,IAAI,QAAA,CAAS,EAAA,GAAK,SAAS,CAC/D;IACF;;;;;IAOO,UAAUC,OAAAA,EAAoCC,SAAAA,EAAuB;qBAoB/D,IAAA;QAnBZ,IAAA,CAAK,iBAAA,CAAkB,KAAA,EAAO;QAE9B,CAAA,GAAA,sPAAA,CAAA,MAAA,EAAI,YAAY;YACd,IAAA,CAAK,OAAK,gBAAA,CAAiB,MAAA,EAAQ,CACjC,CAAA,MAAM,OAAK,IAAA,EAAM;YAEnB,UAAM,+PAAA,EAAM,EAAE;YAEd,IAAA,CAAK,OAAK,cAAA,CAAe,mBAAA,EAAqB,CAAE,CAAA;YAEhD,OAAK,IAAA,CAAK,OAAK,cAAA,CAAe,KAAA,EAAO,CAAC,GAAA,CAAI,CAAC,EAAE,SAAA,SAAA,EAAS,GAAKC,UAAQ,CAAC;QACrE,EAAC,CAAC,KAAA,CAAM,CAAC,QAAQ;YAChB,IAAA,CAAK,cAAA,CAAe,MAAA,CAAO,QAAQ,EAAA,CAAG;YACtC,UAAU,KAAA,mUAAM,kBAAA,CAAgB,IAAA,CAAK,IAAI,CAAC;QAC3C,EAAC;QAEF,OAAO,IAAA,CAAK,cAAA,CAAe,QAAA,CAAS,SAAS,UAAU;IACxD;AACF;;;AC5aD,SAAgB,eAAeC,IAAAA,EAA8B;IAC3D,OAAO,IAAI,SAAS;AACrB;;;ACaD,SAAgB,OACdC,IAAAA,EACmB;IACnB,MAAM,EAAE,MAAA,EAAQ,GAAG;IACnB,MAAM,0VAAc,iBAAA,EAAe,KAAK,WAAA,CAAY;IACpD,OAAO,MAAM;QACX,OAAO,CAAC,EAAE,EAAA,EAAI,KAAK;YACjB,uQAAO,aAAA,EAAW,CAAC,aAAa;gBAC9B,MAAM,wBACJ,GAAG,IAAA,KAAS,iBACR,OAAO,eAAA,CAAgB,SAAA,CAAU;oBAC/B,MAAK,MAAA,EAAQ;wBACX,SAAS,IAAA,CAAK;4BACZ;4BACA,SAAS,GAAG,OAAA;wBACb,EAAC;oBACH;gBACF,EAAC,GACF;gBAEN,MAAM,sBAAsB,OACzB,OAAA,CAAQ;oBACP;oBACA;gBACD,EAAC,CACD,SAAA,CAAU,SAAS;gBAEtB,OAAO,MAAM;oBACX,oBAAoB,WAAA,EAAa;oBACjC,0BAAA,QAAA,0BAAA,KAAA,KAAA,sBAAuB,WAAA,EAAa;gBACrC;YACF,EAAC;QACH;IACF;AACF", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7], "debugId": null}}, {"offset": {"line": 1652, "column": 0}, "map": {"version": 3, "file": "index.mjs", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/internals/TRPCUntypedClient.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/createTRPCUntypedClient.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/createTRPCClient.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/links/httpBatchStreamLink.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/internals/inputWithTrackedEventId.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/asyncIterator.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/links/httpSubscriptionLink.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/links/retryLink.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/usingCtx.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/OverloadYield.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/awaitAsyncGenerator.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/wrapAsyncGenerator.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bclient%4011.4.2_%40trpc%2Bserver%4011.4.2_typescript%405.8.3__typescript%405.8.3/node_modules/%40trpc/client/src/links/localLink.ts"], "sourcesContent": ["import type {\n  inferObservableValue,\n  Unsubscribable,\n} from '@trpc/server/observable';\nimport { observableToPromise, share } from '@trpc/server/observable';\nimport type {\n  AnyRouter,\n  inferAsyncIterableYield,\n  InferrableClientTypes,\n  Maybe,\n  TypeError,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { createChain } from '../links/internals/createChain';\nimport type { TRPCConnectionState } from '../links/internals/subscriptions';\nimport type {\n  OperationContext,\n  OperationLink,\n  TRPCClientRuntime,\n  TRPCLink,\n} from '../links/types';\nimport { TRPCClientError } from '../TRPCClientError';\n\ntype TRPCType = 'mutation' | 'query' | 'subscription';\nexport interface TRPCRequestOptions {\n  /**\n   * Pass additional context to links\n   */\n  context?: OperationContext;\n  signal?: AbortSignal;\n}\n\nexport interface TRPCSubscriptionObserver<TValue, TError> {\n  onStarted: (opts: { context: OperationContext | undefined }) => void;\n  onData: (value: inferAsyncIterableYield<TValue>) => void;\n  onError: (err: TError) => void;\n  onStopped: () => void;\n  onComplete: () => void;\n  onConnectionStateChange: (state: TRPCConnectionState<TError>) => void;\n}\n\n/** @internal */\nexport type CreateTRPCClientOptions<TRouter extends InferrableClientTypes> = {\n  links: TRPCLink<TRouter>[];\n  transformer?: TypeError<'The transformer property has moved to httpLink/httpBatchLink/wsLink'>;\n};\n\nexport class TRPCUntypedClient<TInferrable extends InferrableClientTypes> {\n  private readonly links: OperationLink<TInferrable>[];\n  public readonly runtime: TRPCClientRuntime;\n  private requestId: number;\n\n  constructor(opts: CreateTRPCClientOptions<TInferrable>) {\n    this.requestId = 0;\n\n    this.runtime = {};\n\n    // Initialize the links\n    this.links = opts.links.map((link) => link(this.runtime));\n  }\n\n  private $request<TInput = unknown, TOutput = unknown>(opts: {\n    type: TRPCType;\n    input: TInput;\n    path: string;\n    context?: OperationContext;\n    signal: Maybe<AbortSignal>;\n  }) {\n    const chain$ = createChain<AnyRouter, TInput, TOutput>({\n      links: this.links as OperationLink<any, any, any>[],\n      op: {\n        ...opts,\n        context: opts.context ?? {},\n        id: ++this.requestId,\n      },\n    });\n    return chain$.pipe(share());\n  }\n\n  private async requestAsPromise<TInput = unknown, TOutput = unknown>(opts: {\n    type: TRPCType;\n    input: TInput;\n    path: string;\n    context?: OperationContext;\n    signal: Maybe<AbortSignal>;\n  }): Promise<TOutput> {\n    try {\n      const req$ = this.$request<TInput, TOutput>(opts);\n      type TValue = inferObservableValue<typeof req$>;\n\n      const envelope = await observableToPromise<TValue>(req$);\n      const data = (envelope.result as any).data;\n      return data;\n    } catch (err) {\n      throw TRPCClientError.from(err as Error);\n    }\n  }\n  public query(path: string, input?: unknown, opts?: TRPCRequestOptions) {\n    return this.requestAsPromise<unknown, unknown>({\n      type: 'query',\n      path,\n      input,\n      context: opts?.context,\n      signal: opts?.signal,\n    });\n  }\n  public mutation(path: string, input?: unknown, opts?: TRPCRequestOptions) {\n    return this.requestAsPromise<unknown, unknown>({\n      type: 'mutation',\n      path,\n      input,\n      context: opts?.context,\n      signal: opts?.signal,\n    });\n  }\n  public subscription(\n    path: string,\n    input: unknown,\n    opts: Partial<\n      TRPCSubscriptionObserver<unknown, TRPCClientError<AnyRouter>>\n    > &\n      TRPCRequestOptions,\n  ): Unsubscribable {\n    const observable$ = this.$request({\n      type: 'subscription',\n      path,\n      input,\n      context: opts.context,\n      signal: opts.signal,\n    });\n    return observable$.subscribe({\n      next(envelope) {\n        switch (envelope.result.type) {\n          case 'state': {\n            opts.onConnectionStateChange?.(envelope.result);\n            break;\n          }\n          case 'started': {\n            opts.onStarted?.({\n              context: envelope.context,\n            });\n            break;\n          }\n          case 'stopped': {\n            opts.onStopped?.();\n            break;\n          }\n          case 'data':\n          case undefined: {\n            opts.onData?.(envelope.result.data);\n            break;\n          }\n        }\n      },\n      error(err) {\n        opts.onError?.(err);\n      },\n      complete() {\n        opts.onComplete?.();\n      },\n    });\n  }\n}\n", "import type { AnyRouter } from '@trpc/server/unstable-core-do-not-import';\nimport type { CreateTRPCClientOptions } from './internals/TRPCUntypedClient';\nimport { TRPCUntypedClient } from './internals/TRPCUntypedClient';\n\nexport function createTRPCUntypedClient<TRouter extends AnyRouter>(\n  opts: CreateTRPCClientOptions<TRouter>,\n): TRPCUntypedClient<TRouter> {\n  return new TRPCUntypedClient(opts);\n}\n\nexport type {\n  CreateTRPCClientOptions,\n  TRPCRequestOptions,\n} from './internals/TRPCUntypedClient';\nexport { TRPCUntypedClient } from './internals/TRPCUntypedClient';\n", "/* eslint-disable @typescript-eslint/no-non-null-assertion */\nimport type { Unsubscribable } from '@trpc/server/observable';\nimport type {\n  AnyProcedure,\n  AnyRouter,\n  inferClientTypes,\n  inferProcedureInput,\n  InferrableClientTypes,\n  inferTransformedProcedureOutput,\n  ProcedureType,\n  RouterRecord,\n} from '@trpc/server/unstable-core-do-not-import';\nimport {\n  createFlatProxy,\n  createRecursiveProxy,\n} from '@trpc/server/unstable-core-do-not-import';\nimport type { CreateTRPCClientOptions } from './createTRPCUntypedClient';\nimport type { TRPCSubscriptionObserver } from './internals/TRPCUntypedClient';\nimport { TRPCUntypedClient } from './internals/TRPCUntypedClient';\nimport type { TRPCProcedureOptions } from './internals/types';\nimport type { TRPCClientError } from './TRPCClientError';\n\n/**\n * @public\n * @deprecated use {@link TRPCClient} instead, will be removed in v12\n **/\nexport type inferRouterClient<TRouter extends AnyRouter> = TRPCClient<TRouter>;\n\n/**\n * @public\n * @deprecated use {@link TRPCClient} instead, will be removed in v12\n **/\nexport type CreateTRPCClient<TRouter extends AnyRouter> = TRPCClient<TRouter>;\n\nconst untypedClientSymbol = Symbol.for('trpc_untypedClient');\n\n/**\n * @public\n **/\nexport type TRPCClient<TRouter extends AnyRouter> = DecoratedProcedureRecord<\n  {\n    transformer: TRouter['_def']['_config']['$types']['transformer'];\n    errorShape: TRouter['_def']['_config']['$types']['errorShape'];\n  },\n  TRouter['_def']['record']\n> & {\n  [untypedClientSymbol]: TRPCUntypedClient<TRouter>;\n};\n\ntype ResolverDef = {\n  input: any;\n  output: any;\n  transformer: boolean;\n  errorShape: any;\n};\n\ntype coerceAsyncGeneratorToIterable<T> =\n  T extends AsyncGenerator<infer $T, infer $Return, infer $Next>\n    ? AsyncIterable<$T, $Return, $Next>\n    : T;\n\n/** @internal */\nexport type Resolver<TDef extends ResolverDef> = (\n  input: TDef['input'],\n  opts?: TRPCProcedureOptions,\n) => Promise<coerceAsyncGeneratorToIterable<TDef['output']>>;\n\ntype SubscriptionResolver<TDef extends ResolverDef> = (\n  input: TDef['input'],\n  opts: Partial<\n    TRPCSubscriptionObserver<TDef['output'], TRPCClientError<TDef>>\n  > &\n    TRPCProcedureOptions,\n) => Unsubscribable;\n\ntype DecorateProcedure<\n  TType extends ProcedureType,\n  TDef extends ResolverDef,\n> = TType extends 'query'\n  ? {\n      query: Resolver<TDef>;\n    }\n  : TType extends 'mutation'\n    ? {\n        mutate: Resolver<TDef>;\n      }\n    : TType extends 'subscription'\n      ? {\n          subscribe: SubscriptionResolver<TDef>;\n        }\n      : never;\n\n/**\n * @internal\n */\ntype DecoratedProcedureRecord<\n  TRoot extends InferrableClientTypes,\n  TRecord extends RouterRecord,\n> = {\n  [TKey in keyof TRecord]: TRecord[TKey] extends infer $Value\n    ? $Value extends AnyProcedure\n      ? DecorateProcedure<\n          $Value['_def']['type'],\n          {\n            input: inferProcedureInput<$Value>;\n            output: inferTransformedProcedureOutput<\n              inferClientTypes<TRoot>,\n              $Value\n            >;\n            errorShape: inferClientTypes<TRoot>['errorShape'];\n            transformer: inferClientTypes<TRoot>['transformer'];\n          }\n        >\n      : $Value extends RouterRecord\n        ? DecoratedProcedureRecord<TRoot, $Value>\n        : never\n    : never;\n};\n\nconst clientCallTypeMap: Record<\n  keyof DecorateProcedure<any, any>,\n  ProcedureType\n> = {\n  query: 'query',\n  mutate: 'mutation',\n  subscribe: 'subscription',\n};\n\n/** @internal */\nexport const clientCallTypeToProcedureType = (\n  clientCallType: string,\n): ProcedureType => {\n  return clientCallTypeMap[clientCallType as keyof typeof clientCallTypeMap];\n};\n\n/**\n * @internal\n */\nexport function createTRPCClientProxy<TRouter extends AnyRouter>(\n  client: TRPCUntypedClient<TRouter>,\n): TRPCClient<TRouter> {\n  const proxy = createRecursiveProxy<TRPCClient<TRouter>>(({ path, args }) => {\n    const pathCopy = [...path];\n    const procedureType = clientCallTypeToProcedureType(pathCopy.pop()!);\n\n    const fullPath = pathCopy.join('.');\n\n    return (client[procedureType] as any)(fullPath, ...(args as any));\n  });\n  return createFlatProxy<TRPCClient<TRouter>>((key) => {\n    if (key === untypedClientSymbol) {\n      return client;\n    }\n    return proxy[key];\n  });\n}\n\nexport function createTRPCClient<TRouter extends AnyRouter>(\n  opts: CreateTRPCClientOptions<TRouter>,\n): TRPCClient<TRouter> {\n  const client = new TRPCUntypedClient(opts);\n  const proxy = createTRPCClientProxy<TRouter>(client);\n  return proxy;\n}\n\n/**\n * Get an untyped client from a proxy client\n * @internal\n */\nexport function getUntypedClient<TRouter extends AnyRouter>(\n  client: TRPCClient<TRouter>,\n): TRPCUntypedClient<TRouter> {\n  return client[untypedClientSymbol];\n}\n", "import type { AnyRouter, ProcedureType } from '@trpc/server';\nimport { observable } from '@trpc/server/observable';\nimport type { TRPCErrorShape, TRPCResponse } from '@trpc/server/rpc';\nimport { jsonlStreamConsumer } from '@trpc/server/unstable-core-do-not-import';\nimport type { BatchLoader } from '../internals/dataLoader';\nimport { dataLoader } from '../internals/dataLoader';\nimport { allAbortSignals, raceAbortSignals } from '../internals/signals';\nimport type { NonEmptyArray } from '../internals/types';\nimport { TRPCClientError } from '../TRPCClientError';\nimport type { HTTPBatchLinkOptions } from './HTTPBatchLinkOptions';\nimport type { HTTPResult } from './internals/httpUtils';\nimport {\n  fetchHTTPResponse,\n  getBody,\n  getUrl,\n  resolveHTTPLinkOptions,\n} from './internals/httpUtils';\nimport type { Operation, TRPCLink } from './types';\n\n/**\n * @see https://trpc.io/docs/client/links/httpBatchStreamLink\n */\nexport function httpBatchStreamLink<TRouter extends AnyRouter>(\n  opts: HTTPBatchLinkOptions<TRouter['_def']['_config']['$types']>,\n): TRPCLink<TRouter> {\n  const resolvedOpts = resolveHTTPLinkOptions(opts);\n  const maxURLLength = opts.maxURLLength ?? Infinity;\n  const maxItems = opts.maxItems ?? Infinity;\n\n  return () => {\n    const batchLoader = (\n      type: ProcedureType,\n    ): BatchLoader<Operation, HTTPResult> => {\n      return {\n        validate(batchOps) {\n          if (maxURLLength === Infinity && maxItems === Infinity) {\n            // escape hatch for quick calcs\n            return true;\n          }\n          if (batchOps.length > maxItems) {\n            return false;\n          }\n          const path = batchOps.map((op) => op.path).join(',');\n          const inputs = batchOps.map((op) => op.input);\n\n          const url = getUrl({\n            ...resolvedOpts,\n            type,\n            path,\n            inputs,\n            signal: null,\n          });\n\n          return url.length <= maxURLLength;\n        },\n        async fetch(batchOps) {\n          const path = batchOps.map((op) => op.path).join(',');\n          const inputs = batchOps.map((op) => op.input);\n\n          const batchSignals = allAbortSignals(\n            ...batchOps.map((op) => op.signal),\n          );\n          const abortController = new AbortController();\n\n          const responsePromise = fetchHTTPResponse({\n            ...resolvedOpts,\n            signal: raceAbortSignals(batchSignals, abortController.signal),\n            type,\n            contentTypeHeader: 'application/json',\n            trpcAcceptHeader: 'application/jsonl',\n            getUrl,\n            getBody,\n            inputs,\n            path,\n            headers() {\n              if (!opts.headers) {\n                return {};\n              }\n              if (typeof opts.headers === 'function') {\n                return opts.headers({\n                  opList: batchOps as NonEmptyArray<Operation>,\n                });\n              }\n              return opts.headers;\n            },\n          });\n\n          const res = await responsePromise;\n          const [head] = await jsonlStreamConsumer<\n            Record<string, Promise<any>>\n          >({\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            from: res.body!,\n            deserialize: resolvedOpts.transformer.output.deserialize,\n            // onError: console.error,\n            formatError(opts) {\n              const error = opts.error as TRPCErrorShape;\n              return TRPCClientError.from({\n                error,\n              });\n            },\n            abortController,\n          });\n          const promises = Object.keys(batchOps).map(\n            async (key): Promise<HTTPResult> => {\n              let json: TRPCResponse = await Promise.resolve(head[key]);\n\n              if ('result' in json) {\n                /**\n                 * Not very pretty, but we need to unwrap nested data as promises\n                 * Our stream producer will only resolve top-level async values or async values that are directly nested in another async value\n                 */\n                const result = await Promise.resolve(json.result);\n                json = {\n                  result: {\n                    data: await Promise.resolve(result.data),\n                  },\n                };\n              }\n\n              return {\n                json,\n                meta: {\n                  response: res,\n                },\n              };\n            },\n          );\n          return promises;\n        },\n      };\n    };\n\n    const query = dataLoader(batchLoader('query'));\n    const mutation = dataLoader(batchLoader('mutation'));\n\n    const loaders = { query, mutation };\n    return ({ op }) => {\n      return observable((observer) => {\n        /* istanbul ignore if -- @preserve */\n        if (op.type === 'subscription') {\n          throw new Error(\n            'Subscriptions are unsupported by `httpBatchStreamLink` - use `httpSubscriptionLink` or `wsLink`',\n          );\n        }\n        const loader = loaders[op.type];\n        const promise = loader.load(op);\n\n        let _res = undefined as HTTPResult | undefined;\n        promise\n          .then((res) => {\n            _res = res;\n            if ('error' in res.json) {\n              observer.error(\n                TRPCClientError.from(res.json, {\n                  meta: res.meta,\n                }),\n              );\n              return;\n            } else if ('result' in res.json) {\n              observer.next({\n                context: res.meta,\n                result: res.json.result,\n              });\n              observer.complete();\n              return;\n            }\n\n            observer.complete();\n          })\n          .catch((err) => {\n            observer.error(\n              TRPCClientError.from(err, {\n                meta: _res?.meta,\n              }),\n            );\n          });\n\n        return () => {\n          // noop\n        };\n      });\n    };\n  };\n}\n\n/**\n * @deprecated use {@link httpBatchStreamLink} instead\n */\nexport const unstable_httpBatchStreamLink = httpBatchStreamLink;\n", "export function inputWithTrackedEventId(\n  input: unknown,\n  lastEventId: string | undefined,\n) {\n  if (!lastEventId) {\n    return input;\n  }\n  if (input != null && typeof input !== 'object') {\n    return input;\n  }\n  return {\n    ...(input ?? {}),\n    lastEventId,\n  };\n}\n", "function _asyncIterator(r) {\n  var n,\n    t,\n    o,\n    e = 2;\n  for (\"undefined\" != typeof Symbol && (t = Symbol.asyncIterator, o = Symbol.iterator); e--;) {\n    if (t && null != (n = r[t])) return n.call(r);\n    if (o && null != (n = r[o])) return new AsyncFromSyncIterator(n.call(r));\n    t = \"@@asyncIterator\", o = \"@@iterator\";\n  }\n  throw new TypeError(\"Object is not async iterable\");\n}\nfunction AsyncFromSyncIterator(r) {\n  function AsyncFromSyncIteratorContinuation(r) {\n    if (Object(r) !== r) return Promise.reject(new TypeError(r + \" is not an object.\"));\n    var n = r.done;\n    return Promise.resolve(r.value).then(function (r) {\n      return {\n        value: r,\n        done: n\n      };\n    });\n  }\n  return AsyncFromSyncIterator = function AsyncFromSyncIterator(r) {\n    this.s = r, this.n = r.next;\n  }, AsyncFromSyncIterator.prototype = {\n    s: null,\n    n: null,\n    next: function next() {\n      return AsyncFromSyncIteratorContinuation(this.n.apply(this.s, arguments));\n    },\n    \"return\": function _return(r) {\n      var n = this.s[\"return\"];\n      return void 0 === n ? Promise.resolve({\n        value: r,\n        done: !0\n      }) : AsyncFromSyncIteratorContinuation(n.apply(this.s, arguments));\n    },\n    \"throw\": function _throw(r) {\n      var n = this.s[\"return\"];\n      return void 0 === n ? Promise.reject(r) : AsyncFromSyncIteratorContinuation(n.apply(this.s, arguments));\n    }\n  }, new AsyncFromSyncIterator(r);\n}\nmodule.exports = _asyncIterator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import { behaviorSubject, observable } from '@trpc/server/observable';\nimport type { TRPCErrorShape, TRPCResult } from '@trpc/server/rpc';\nimport type {\n  AnyClientTypes,\n  EventSourceLike,\n  inferClientTypes,\n  InferrableClientTypes,\n} from '@trpc/server/unstable-core-do-not-import';\nimport {\n  retryableRpcCodes,\n  run,\n  sseStreamConsumer,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { inputWithTrackedEventId } from '../internals/inputWithTrackedEventId';\nimport { raceAbortSignals } from '../internals/signals';\nimport { TRPCClientError } from '../TRPCClientError';\nimport type { TRPCConnectionState } from '../unstable-internals';\nimport { getTransformer, type TransformerOptions } from '../unstable-internals';\nimport { getUrl } from './internals/httpUtils';\nimport {\n  resultOf,\n  type UrlOptionsWithConnectionParams,\n} from './internals/urlWithConnectionParams';\nimport type { Operation, TRPCLink } from './types';\n\nasync function urlWithConnectionParams(\n  opts: UrlOptionsWithConnectionParams,\n): Promise<string> {\n  let url = await resultOf(opts.url);\n  if (opts.connectionParams) {\n    const params = await resultOf(opts.connectionParams);\n\n    const prefix = url.includes('?') ? '&' : '?';\n    url +=\n      prefix + 'connectionParams=' + encodeURIComponent(JSON.stringify(params));\n  }\n\n  return url;\n}\n\ntype HTTPSubscriptionLinkOptions<\n  TRoot extends AnyClientTypes,\n  TEventSource extends EventSourceLike.AnyConstructor = typeof EventSource,\n> = {\n  /**\n   * EventSource ponyfill\n   */\n  EventSource?: TEventSource;\n  /**\n   * EventSource options or a callback that returns them\n   */\n  eventSourceOptions?:\n    | EventSourceLike.InitDictOf<TEventSource>\n    | ((opts: {\n        op: Operation;\n      }) =>\n        | EventSourceLike.InitDictOf<TEventSource>\n        | Promise<EventSourceLike.InitDictOf<TEventSource>>);\n} & TransformerOptions<TRoot> &\n  UrlOptionsWithConnectionParams;\n\n/**\n * @see https://trpc.io/docs/client/links/httpSubscriptionLink\n */\nexport function httpSubscriptionLink<\n  TInferrable extends InferrableClientTypes,\n  TEventSource extends EventSourceLike.AnyConstructor,\n>(\n  opts: HTTPSubscriptionLinkOptions<\n    inferClientTypes<TInferrable>,\n    TEventSource\n  >,\n): TRPCLink<TInferrable> {\n  const transformer = getTransformer(opts.transformer);\n\n  return () => {\n    return ({ op }) => {\n      return observable((observer) => {\n        const { type, path, input } = op;\n\n        /* istanbul ignore if -- @preserve */\n        if (type !== 'subscription') {\n          throw new Error('httpSubscriptionLink only supports subscriptions');\n        }\n\n        let lastEventId: string | undefined = undefined;\n        const ac = new AbortController();\n        const signal = raceAbortSignals(op.signal, ac.signal);\n        const eventSourceStream = sseStreamConsumer<{\n          EventSource: TEventSource;\n          data: Partial<{\n            id?: string;\n            data: unknown;\n          }>;\n          error: TRPCErrorShape;\n        }>({\n          url: async () =>\n            getUrl({\n              transformer,\n              url: await urlWithConnectionParams(opts),\n              input: inputWithTrackedEventId(input, lastEventId),\n              path,\n              type,\n              signal: null,\n            }),\n          init: () => resultOf(opts.eventSourceOptions, { op }),\n          signal,\n          deserialize: transformer.output.deserialize,\n          EventSource:\n            opts.EventSource ??\n            (globalThis.EventSource as never as TEventSource),\n        });\n\n        const connectionState = behaviorSubject<\n          TRPCConnectionState<TRPCClientError<any>>\n        >({\n          type: 'state',\n          state: 'connecting',\n          error: null,\n        });\n\n        const connectionSub = connectionState.subscribe({\n          next(state) {\n            observer.next({\n              result: state,\n            });\n          },\n        });\n        run(async () => {\n          for await (const chunk of eventSourceStream) {\n            switch (chunk.type) {\n              case 'ping':\n                // do nothing\n                break;\n              case 'data':\n                const chunkData = chunk.data;\n\n                let result: TRPCResult<unknown>;\n                if (chunkData.id) {\n                  // if the `tracked()`-helper is used, we always have an `id` field\n                  lastEventId = chunkData.id;\n                  result = {\n                    id: chunkData.id,\n                    data: chunkData,\n                  };\n                } else {\n                  result = {\n                    data: chunkData.data,\n                  };\n                }\n\n                observer.next({\n                  result,\n                  context: {\n                    eventSource: chunk.eventSource,\n                  },\n                });\n                break;\n              case 'connected': {\n                observer.next({\n                  result: {\n                    type: 'started',\n                  },\n                  context: {\n                    eventSource: chunk.eventSource,\n                  },\n                });\n                connectionState.next({\n                  type: 'state',\n                  state: 'pending',\n                  error: null,\n                });\n                break;\n              }\n              case 'serialized-error': {\n                const error = TRPCClientError.from({ error: chunk.error });\n\n                if (retryableRpcCodes.includes(chunk.error.code)) {\n                  //\n                  connectionState.next({\n                    type: 'state',\n                    state: 'connecting',\n                    error,\n                  });\n                  break;\n                }\n                //\n                // non-retryable error, cancel the subscription\n                throw error;\n              }\n              case 'connecting': {\n                const lastState = connectionState.get();\n\n                const error = chunk.event && TRPCClientError.from(chunk.event);\n                if (!error && lastState.state === 'connecting') {\n                  break;\n                }\n\n                connectionState.next({\n                  type: 'state',\n                  state: 'connecting',\n                  error,\n                });\n                break;\n              }\n              case 'timeout': {\n                connectionState.next({\n                  type: 'state',\n                  state: 'connecting',\n                  error: new TRPCClientError(\n                    `Timeout of ${chunk.ms}ms reached while waiting for a response`,\n                  ),\n                });\n              }\n            }\n          }\n          observer.next({\n            result: {\n              type: 'stopped',\n            },\n          });\n          connectionState.next({\n            type: 'state',\n            state: 'idle',\n            error: null,\n          });\n          observer.complete();\n        }).catch((error) => {\n          observer.error(TRPCClientError.from(error));\n        });\n\n        return () => {\n          observer.complete();\n          ac.abort();\n          connectionSub.unsubscribe();\n        };\n      });\n    };\n  };\n}\n\n/**\n * @deprecated use {@link httpSubscriptionLink} instead\n */\nexport const unstable_httpSubscriptionLink = httpSubscriptionLink;\n", "/* istanbul ignore file -- @preserve */\n// We're not actually exporting this link\nimport type { Unsubscribable } from '@trpc/server/observable';\nimport { observable } from '@trpc/server/observable';\nimport type { InferrableClientTypes } from '@trpc/server/unstable-core-do-not-import';\nimport { inputWithTrackedEventId } from '../internals/inputWithTrackedEventId';\nimport type { TRPCClientError } from '../TRPCClientError';\nimport type { Operation, TRPCLink } from './types';\n\ninterface RetryLinkOptions<TInferrable extends InferrableClientTypes> {\n  /**\n   * The retry function\n   */\n  retry: (opts: RetryFnOptions<TInferrable>) => boolean;\n  /**\n   * The delay between retries in ms (defaults to 0)\n   */\n  retryDelayMs?: (attempt: number) => number;\n}\n\ninterface RetryFnOptions<TInferrable extends InferrableClientTypes> {\n  /**\n   * The operation that failed\n   */\n  op: Operation;\n  /**\n   * The error that occurred\n   */\n  error: TRPCClientError<TInferrable>;\n  /**\n   * The number of attempts that have been made (including the first call)\n   */\n  attempts: number;\n}\n\n/**\n * @see https://trpc.io/docs/v11/client/links/retryLink\n */\nexport function retryLink<TInferrable extends InferrableClientTypes>(\n  opts: RetryLinkOptions<TInferrable>,\n): TRPCLink<TInferrable> {\n  // initialized config\n  return () => {\n    // initialized in app\n    return (callOpts) => {\n      // initialized for request\n      return observable((observer) => {\n        let next$: Unsubscribable;\n        let callNextTimeout: ReturnType<typeof setTimeout> | undefined =\n          undefined;\n\n        let lastEventId: string | undefined = undefined;\n\n        attempt(1);\n\n        function opWithLastEventId() {\n          const op = callOpts.op;\n          if (!lastEventId) {\n            return op;\n          }\n\n          return {\n            ...op,\n            input: inputWithTrackedEventId(op.input, lastEventId),\n          };\n        }\n\n        function attempt(attempts: number) {\n          const op = opWithLastEventId();\n\n          next$ = callOpts.next(op).subscribe({\n            error(error) {\n              const shouldRetry = opts.retry({\n                op,\n                attempts,\n                error,\n              });\n              if (!shouldRetry) {\n                observer.error(error);\n                return;\n              }\n              const delayMs = opts.retryDelayMs?.(attempts) ?? 0;\n\n              if (delayMs <= 0) {\n                attempt(attempts + 1);\n                return;\n              }\n              callNextTimeout = setTimeout(\n                () => attempt(attempts + 1),\n                delayMs,\n              );\n            },\n            next(envelope) {\n              //\n              if (\n                (!envelope.result.type || envelope.result.type === 'data') &&\n                envelope.result.id\n              ) {\n                //\n                lastEventId = envelope.result.id;\n              }\n\n              observer.next(envelope);\n            },\n            complete() {\n              observer.complete();\n            },\n          });\n        }\n        return () => {\n          next$.unsubscribe();\n          clearTimeout(callNextTimeout);\n        };\n      });\n    };\n  };\n}\n", "function _usingCtx() {\n  var r = \"function\" == typeof SuppressedError ? SuppressedError : function (r, e) {\n      var n = Error();\n      return n.name = \"SuppressedError\", n.error = r, n.suppressed = e, n;\n    },\n    e = {},\n    n = [];\n  function using(r, e) {\n    if (null != e) {\n      if (Object(e) !== e) throw new TypeError(\"using declarations can only be used with objects, functions, null, or undefined.\");\n      if (r) var o = e[Symbol.asyncDispose || Symbol[\"for\"](\"Symbol.asyncDispose\")];\n      if (void 0 === o && (o = e[Symbol.dispose || Symbol[\"for\"](\"Symbol.dispose\")], r)) var t = o;\n      if (\"function\" != typeof o) throw new TypeError(\"Object is not disposable.\");\n      t && (o = function o() {\n        try {\n          t.call(e);\n        } catch (r) {\n          return Promise.reject(r);\n        }\n      }), n.push({\n        v: e,\n        d: o,\n        a: r\n      });\n    } else r && n.push({\n      d: e,\n      a: r\n    });\n    return e;\n  }\n  return {\n    e: e,\n    u: using.bind(null, !1),\n    a: using.bind(null, !0),\n    d: function d() {\n      var o,\n        t = this.e,\n        s = 0;\n      function next() {\n        for (; o = n.pop();) try {\n          if (!o.a && 1 === s) return s = 0, n.push(o), Promise.resolve().then(next);\n          if (o.d) {\n            var r = o.d.call(o.v);\n            if (o.a) return s |= 2, Promise.resolve(r).then(next, err);\n          } else s |= 1;\n        } catch (r) {\n          return err(r);\n        }\n        if (1 === s) return t !== e ? Promise.reject(t) : Promise.resolve();\n        if (t !== e) throw t;\n      }\n      function err(n) {\n        return t = t !== e ? new r(n, t) : n, next();\n      }\n      return next();\n    }\n  };\n}\nmodule.exports = _usingCtx, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _OverloadYield(e, d) {\n  this.v = e, this.k = d;\n}\nmodule.exports = _OverloadYield, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var OverloadYield = require(\"./OverloadYield.js\");\nfunction _awaitAsyncGenerator(e) {\n  return new OverloadYield(e, 0);\n}\nmodule.exports = _awaitAsyncGenerator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var OverloadYield = require(\"./OverloadYield.js\");\nfunction _wrapAsyncGenerator(e) {\n  return function () {\n    return new AsyncGenerator(e.apply(this, arguments));\n  };\n}\nfunction AsyncGenerator(e) {\n  var r, t;\n  function resume(r, t) {\n    try {\n      var n = e[r](t),\n        o = n.value,\n        u = o instanceof OverloadYield;\n      Promise.resolve(u ? o.v : o).then(function (t) {\n        if (u) {\n          var i = \"return\" === r ? \"return\" : \"next\";\n          if (!o.k || t.done) return resume(i, t);\n          t = e[i](t).value;\n        }\n        settle(n.done ? \"return\" : \"normal\", t);\n      }, function (e) {\n        resume(\"throw\", e);\n      });\n    } catch (e) {\n      settle(\"throw\", e);\n    }\n  }\n  function settle(e, n) {\n    switch (e) {\n      case \"return\":\n        r.resolve({\n          value: n,\n          done: !0\n        });\n        break;\n      case \"throw\":\n        r.reject(n);\n        break;\n      default:\n        r.resolve({\n          value: n,\n          done: !1\n        });\n    }\n    (r = r.next) ? resume(r.key, r.arg) : t = null;\n  }\n  this._invoke = function (e, n) {\n    return new Promise(function (o, u) {\n      var i = {\n        key: e,\n        arg: n,\n        resolve: o,\n        reject: u,\n        next: null\n      };\n      t ? t = t.next = i : (r = t = i, resume(e, n));\n    });\n  }, \"function\" != typeof e[\"return\"] && (this[\"return\"] = void 0);\n}\nAsyncGenerator.prototype[\"function\" == typeof Symbol && Symbol.asyncIterator || \"@@asyncIterator\"] = function () {\n  return this;\n}, AsyncGenerator.prototype.next = function (e) {\n  return this._invoke(\"next\", e);\n}, AsyncGenerator.prototype[\"throw\"] = function (e) {\n  return this._invoke(\"throw\", e);\n}, AsyncGenerator.prototype[\"return\"] = function (e) {\n  return this._invoke(\"return\", e);\n};\nmodule.exports = _wrapAsyncGenerator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import {\n  getTRPCErrorFromUnknown,\n  getTRPCErrorShape,\n  isTrackedEnvelope,\n} from '@trpc/server';\nimport { behaviorSubject, observable } from '@trpc/server/observable';\nimport { TRPC_ERROR_CODES_BY_KEY, type TRPCResult } from '@trpc/server/rpc';\nimport {\n  callProcedure,\n  isAbortError,\n  isAsyncIterable,\n  iteratorResource,\n  makeResource,\n  retryableRpcCodes,\n  run,\n  type AnyRouter,\n  type ErrorHandlerOptions,\n  type inferClientTypes,\n  type inferRouterContext,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { inputWithTrackedEventId } from '../internals/inputWithTrackedEventId';\nimport { abortSignalToPromise, raceAbortSignals } from '../internals/signals';\nimport { getTransformer } from '../internals/transformer';\nimport type { TransformerOptions } from '../internals/transformer';\nimport { isTR<PERSON><PERSON><PERSON>Error, TRPCClientError } from '../TRPCClientError';\nimport type { TRPCConnectionState } from './internals/subscriptions';\nimport type { TRPCLink } from './types';\n\nexport type LocalLinkOptions<TRouter extends AnyRouter> = {\n  router: TRouter;\n  createContext: () => Promise<inferRouterContext<TRouter>>;\n  onError?: (opts: ErrorHandlerOptions<inferRouterContext<TRouter>>) => void;\n} & TransformerOptions<inferClientTypes<TRouter>>;\n\n/**\n * localLink is a terminating link that allows you to make tRPC procedure calls directly in your application without going through HTTP.\n *\n * @see https://trpc.io/docs/links/localLink\n */\nexport function unstable_localLink<TRouter extends AnyRouter>(\n  opts: LocalLinkOptions<TRouter>,\n): TRPCLink<TRouter> {\n  const transformer = getTransformer(opts.transformer);\n\n  const transformChunk = (chunk: unknown) => {\n    if (opts.transformer) {\n      // assume transformer will do the right thing\n      return chunk;\n    }\n    // Special case for undefined, because `JSON.stringify(undefined)` throws\n    if (chunk === undefined) {\n      return chunk;\n    }\n    const serialized = JSON.stringify(transformer.input.serialize(chunk));\n    const deserialized = JSON.parse(transformer.output.deserialize(serialized));\n    return deserialized;\n  };\n\n  return () =>\n    ({ op }) =>\n      observable((observer) => {\n        let ctx: inferRouterContext<TRouter> | undefined = undefined;\n        const ac = new AbortController();\n\n        const signal = raceAbortSignals(op.signal, ac.signal);\n        const signalPromise = abortSignalToPromise(signal);\n\n        signalPromise.catch(() => {\n          // prevent unhandled rejection\n        });\n\n        let input = op.input;\n        async function runProcedure(newInput: unknown): Promise<unknown> {\n          input = newInput;\n\n          ctx = await opts.createContext();\n\n          return callProcedure({\n            router: opts.router,\n            path: op.path,\n            getRawInput: async () => newInput,\n            ctx,\n            type: op.type,\n            signal,\n          });\n        }\n\n        function onErrorCallback(cause: unknown) {\n          if (isAbortError(cause)) {\n            return;\n          }\n          opts.onError?.({\n            error: getTRPCErrorFromUnknown(cause),\n            type: op.type,\n            path: op.path,\n            input,\n            ctx,\n          });\n        }\n\n        function coerceToTRPCClientError(cause: unknown) {\n          if (isTRPCClientError<TRouter>(cause)) {\n            return cause;\n          }\n          const error = getTRPCErrorFromUnknown(cause);\n\n          const shape = getTRPCErrorShape({\n            config: opts.router._def._config,\n            ctx,\n            error,\n            input,\n            path: op.path,\n            type: op.type,\n          });\n          return TRPCClientError.from({\n            error: transformChunk(shape),\n          });\n        }\n\n        run(async () => {\n          switch (op.type) {\n            case 'query':\n            case 'mutation': {\n              const result = await runProcedure(op.input);\n              if (!isAsyncIterable(result)) {\n                observer.next({\n                  result: { data: transformChunk(result) },\n                });\n                observer.complete();\n                break;\n              }\n\n              observer.next({\n                result: {\n                  data: (async function* () {\n                    await using iterator = iteratorResource(result);\n                    using _finally = makeResource({}, () => {\n                      observer.complete();\n                    });\n                    try {\n                      while (true) {\n                        const res = await Promise.race([\n                          iterator.next(),\n                          signalPromise,\n                        ]);\n                        if (res.done) {\n                          return transformChunk(res.value);\n                        }\n                        yield transformChunk(res.value);\n                      }\n                    } catch (cause) {\n                      onErrorCallback(cause);\n                      throw coerceToTRPCClientError(cause);\n                    }\n                  })(),\n                },\n              });\n              break;\n            }\n            case 'subscription': {\n              const connectionState = behaviorSubject<\n                TRPCConnectionState<TRPCClientError<any>>\n              >({\n                type: 'state',\n                state: 'connecting',\n                error: null,\n              });\n\n              const connectionSub = connectionState.subscribe({\n                next(state) {\n                  observer.next({\n                    result: state,\n                  });\n                },\n              });\n              let lastEventId: string | undefined = undefined;\n\n              using _finally = makeResource({}, async () => {\n                observer.complete();\n\n                connectionState.next({\n                  type: 'state',\n                  state: 'idle',\n                  error: null,\n                });\n                connectionSub.unsubscribe();\n              });\n              while (true) {\n                const result = await runProcedure(\n                  inputWithTrackedEventId(op.input, lastEventId),\n                );\n                if (!isAsyncIterable(result)) {\n                  throw new Error('Expected an async iterable');\n                }\n                await using iterator = iteratorResource(result);\n\n                observer.next({\n                  result: {\n                    type: 'started',\n                  },\n                });\n                connectionState.next({\n                  type: 'state',\n                  state: 'pending',\n                  error: null,\n                });\n\n                // Use a while loop to handle errors and reconnects\n                while (true) {\n                  let res;\n                  try {\n                    res = await Promise.race([iterator.next(), signalPromise]);\n                  } catch (cause) {\n                    if (isAbortError(cause)) {\n                      return;\n                    }\n                    const error = getTRPCErrorFromUnknown(cause);\n\n                    if (\n                      !retryableRpcCodes.includes(\n                        TRPC_ERROR_CODES_BY_KEY[error.code],\n                      )\n                    ) {\n                      throw coerceToTRPCClientError(error);\n                    }\n\n                    onErrorCallback(error);\n                    connectionState.next({\n                      type: 'state',\n                      state: 'connecting',\n                      error: coerceToTRPCClientError(error),\n                    });\n\n                    break;\n                  }\n\n                  if (res.done) {\n                    return;\n                  }\n                  let chunk: TRPCResult<unknown>;\n                  if (isTrackedEnvelope(res.value)) {\n                    lastEventId = res.value[0];\n\n                    chunk = {\n                      id: res.value[0],\n                      data: {\n                        id: res.value[0],\n                        data: res.value[1],\n                      },\n                    };\n                  } else {\n                    chunk = {\n                      data: res.value,\n                    };\n                  }\n\n                  observer.next({\n                    result: {\n                      ...chunk,\n                      data: transformChunk(chunk.data),\n                    },\n                  });\n                }\n              }\n              break;\n            }\n          }\n        }).catch((cause) => {\n          onErrorCallback(cause);\n          observer.error(coerceToTRPCClientError(cause));\n        });\n\n        return () => {\n          ac.abort();\n        };\n      });\n}\n/**\n * @deprecated Renamed to `unstable_localLink`. This alias will be removed in a future major release.\n */\nexport const experimental_localLink: typeof unstable_localLink =\n  unstable_localLink;\n"], "names": ["opts: CreateTRPCClientOptions<TInferrable>", "opts: {\n    type: TRPCType;\n    input: TInput;\n    path: string;\n    context?: OperationContext;\n    signal: Maybe<AbortSignal>;\n  }", "path: string", "input?: unknown", "opts?: TRPCRequestOptions", "input: unknown", "opts: Partial<\n      TRPCSubscriptionObserver<unknown, TRPCClientError<AnyRouter>>\n    > &\n      TRPCRequestOptions", "opts: CreateTRPCClientOptions<TRouter>", "clientCallTypeMap: Record<\n  keyof DecorateProcedure<any, any>,\n  ProcedureType\n>", "clientCallType: string", "client: TRPCUntypedClient<TRouter>", "opts: CreateTRPCClientOptions<TRouter>", "client: TRPCClient<TRouter>", "opts: HTTPBatchLinkOptions<TRouter['_def']['_config']['$types']>", "type: ProcedureType", "opts", "json: TRPCResponse", "input: unknown", "lastEventId: string | undefined", "_asyncIterator", "r", "AsyncFromSyncIterator", "opts: UrlOptionsWithConnectionParams", "opts: HTTPSubscriptionLinkOptions<\n    inferClientTypes<TInferrable>,\n    TEventSource\n  >", "lastEventId: string | undefined", "result: TRPCResult<unknown>", "opts: RetryLinkOptions<TInferrable>", "next$: Unsubscribable", "callNextTimeout: ReturnType<typeof setTimeout> | undefined", "lastEventId: string | undefined", "attempts: number", "r", "e", "n", "o", "OverloadYield", "_awaitAsyncGenerator", "_wrapAsyncGenerator", "r", "t", "e", "opts: LocalLinkOptions<TRouter>", "chunk: unknown", "ctx: inferRouterContext<TRouter> | undefined", "newInput: unknown", "cause: unknown", "lastEventId: string | undefined", "chunk: TRPCResult<unknown>", "experimental_localLink: typeof unstable_localLink"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA,IAAa,oBAAb,MAA0E;IAKxE,YAAYA,IAAAA,CAA4C;2CA+GvD,IAAA,EAnHgB,SAAA,KAAA;2CAmHf,IAAA,EAlHc,WAAA,KAAA;2CAkHb,IAAA,EAjHK,aAAA,KAAA;QAGN,IAAA,CAAK,SAAA,GAAY;QAEjB,IAAA,CAAK,OAAA,GAAU,CAAE;QAGjB,IAAA,CAAK,KAAA,GAAQ,KAAK,KAAA,CAAM,GAAA,CAAI,CAAC,OAAS,KAAK,IAAA,CAAK,OAAA,CAAQ,CAAC;IAC1D;IAEO,SAA8CC,IAAAA,EAMnD;;QACD,MAAM,yUAAS,cAAA,EAAwC;YACrD,OAAO,IAAA,CAAK,KAAA;YACZ,IAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACK,OAAA,CAAA,GAAA;gBACH,SAAA,CAAA,gBAAS,KAAK,OAAA,MAAA,QAAA,kBAAA,KAAA,IAAA,gBAAW,CAAE;gBAC3B,IAAI,EAAE,IAAA,CAAK,SAAA;;QAEd,EAAC;QACF,OAAO,OAAO,IAAA,kQAAK,QAAA,EAAO,CAAC;IAC5B;IAED,MAAc,iBAAsDA,IAAAA,EAM/C;oBA8EjB,IAAA;QA7EF,IAAI;YACF,MAAM,OAAO,MAAK,QAAA,CAA0B,KAAK;YAGjD,MAAM,WAAW,sQAAM,sBAAA,EAA4B,KAAK;YACxD,MAAM,OAAQ,SAAS,MAAA,CAAe,IAAA;YACtC,OAAO;QACR,EAAA,OAAQ,KAAK;YACZ,wUAAM,kBAAA,CAAgB,IAAA,CAAK,IAAa;QACzC;IACF;IACM,MAAMC,IAAAA,EAAcC,KAAAA,EAAiBC,IAAAA,EAA2B;QACrE,OAAO,IAAA,CAAK,gBAAA,CAAmC;YAC7C,MAAM;YACN;YACA;YACA,SAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAS,KAAM,OAAA;YACf,QAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAQ,KAAM,MAAA;QACf,EAAC;IACH;IACM,SAASF,IAAAA,EAAcC,KAAAA,EAAiBC,IAAAA,EAA2B;QACxE,OAAO,IAAA,CAAK,gBAAA,CAAmC;YAC7C,MAAM;YACN;YACA;YACA,SAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAS,KAAM,OAAA;YACf,QAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAQ,KAAM,MAAA;QACf,EAAC;IACH;IACM,aACLF,IAAAA,EACAG,KAAAA,EACAC,IAAAA,EAIgB;QAChB,MAAM,cAAc,IAAA,CAAK,QAAA,CAAS;YAChC,MAAM;YACN;YACA;YACA,SAAS,KAAK,OAAA;YACd,QAAQ,KAAK,MAAA;QACd,EAAC;QACF,OAAO,YAAY,SAAA,CAAU;YAC3B,MAAK,QAAA,EAAU;gBACb,OAAQ,SAAS,MAAA,CAAO,IAAA,EAAxB;oBACE,KAAK;wBAAS;;4BACZ,CAAA,wBAAA,KAAK,uBAAA,MAAA,QAAA,0BAAA,KAAA,KAAL,sBAAA,IAAA,CAAA,MAA+B,SAAS,MAAA,CAAO;4BAC/C;wBACD;oBACD,KAAK;wBAAW;;4BACd,CAAA,kBAAA,KAAK,SAAA,MAAA,QAAA,oBAAA,KAAA,KAAL,gBAAA,IAAA,CAAA,MAAiB;gCACf,SAAS,SAAS,OAAA;4BACnB,EAAC;4BACF;wBACD;oBACD,KAAK;wBAAW;;4BACd,CAAA,kBAAA,KAAK,SAAA,MAAA,QAAA,oBAAA,KAAA,KAAL,gBAAA,IAAA,CAAA,KAAkB;4BAClB;wBACD;oBACD,KAAK;oBACL,KAAA,KAAA;wBAAgB;;4BACd,CAAA,eAAA,KAAK,MAAA,MAAA,QAAA,iBAAA,KAAA,KAAL,aAAA,IAAA,CAAA,MAAc,SAAS,MAAA,CAAO,IAAA,CAAK;4BACnC;wBACD;gBACF;YACF;YACD,OAAM,GAAA,EAAK;;gBACT,CAAA,gBAAA,KAAK,OAAA,MAAA,QAAA,kBAAA,KAAA,KAAL,cAAA,IAAA,CAAA,MAAe,IAAI;YACpB;YACD,WAAW;;gBACT,CAAA,mBAAA,KAAK,UAAA,MAAA,QAAA,qBAAA,KAAA,KAAL,iBAAA,IAAA,CAAA,KAAmB;YACpB;QACF,EAAC;IACH;AACF;;;AC7JD,SAAgB,wBACdC,IAAAA,EAC4B;IAC5B,OAAO,IAAI,kBAAkB;AAC9B;;;AC0BD,MAAM,sBAAsB,OAAO,GAAA,CAAI,qBAAqB;AAqF5D,MAAMC,oBAGF;IACF,OAAO;IACP,QAAQ;IACR,WAAW;AACZ;iBAGD,MAAa,gCAAgC,CAC3CC,mBACkB;IAClB,OAAO,iBAAA,CAAkB,eAAA;AAC1B;;;GAKD,SAAgB,sBACdC,MAAAA,EACqB;IACrB,MAAM,2QAAQ,uBAAA,EAA0C,CAAC,EAAE,IAAA,EAAM,IAAA,EAAM,KAAK;QAC1E,MAAM,WAAW,CAAC;eAAG,IAAK;SAAA;QAC1B,MAAM,gBAAgB,8BAA8B,SAAS,GAAA,EAAK,CAAE;QAEpE,MAAM,WAAW,SAAS,IAAA,CAAK,IAAI;QAEnC,OAAQ,MAAA,CAAO,cAAA,CAAuB,UAAU,GAAI,KAAa;IAClE,EAAC;IACF,QAAO,oRAAA,EAAqC,CAAC,QAAQ;QACnD,IAAI,QAAQ,oBACV,CAAA,OAAO;QAET,OAAO,KAAA,CAAM,IAAA;IACd,EAAC;AACH;AAED,SAAgB,iBACdC,IAAAA,EACqB;IACrB,MAAM,SAAS,IAAI,kBAAkB;IACrC,MAAM,QAAQ,sBAA+B,OAAO;IACpD,OAAO;AACR;;;;GAMD,SAAgB,iBACdC,MAAAA,EAC4B;IAC5B,OAAO,MAAA,CAAO,oBAAA;AACf;;;;;;GCvJD,SAAgB,oBACdC,IAAAA,EACmB;;IACnB,MAAM,eAAe,yVAAA,EAAuB,KAAK;IACjD,MAAM,eAAA,CAAA,qBAAe,KAAK,YAAA,MAAA,QAAA,uBAAA,KAAA,IAAA,qBAAgB;IAC1C,MAAM,WAAA,CAAA,iBAAW,KAAK,QAAA,MAAA,QAAA,mBAAA,KAAA,IAAA,iBAAY;IAElC,OAAO,MAAM;QACX,MAAM,cAAc,CAClBC,SACuC;YACvC,OAAO;gBACL,UAAS,QAAA,EAAU;oBACjB,IAAI,iBAAiB,YAAY,aAAa,SAE5C,CAAA,OAAO;oBAET,IAAI,SAAS,MAAA,GAAS,SACpB,CAAA,OAAO;oBAET,MAAM,OAAO,SAAS,GAAA,CAAI,CAAC,KAAO,GAAG,IAAA,CAAK,CAAC,IAAA,CAAK,IAAI;oBACpD,MAAM,SAAS,SAAS,GAAA,CAAI,CAAC,KAAO,GAAG,KAAA,CAAM;oBAE7C,MAAM,sUAAM,SAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACP,eAAA,CAAA,GAAA;wBACH;wBACA;wBACA;wBACA,QAAQ;uBACR;oBAEF,OAAO,IAAI,MAAA,IAAU;gBACtB;gBACD,MAAM,OAAM,QAAA,EAAU;oBACpB,MAAM,OAAO,SAAS,GAAA,CAAI,CAAC,KAAO,GAAG,IAAA,CAAK,CAAC,IAAA,CAAK,IAAI;oBACpD,MAAM,SAAS,SAAS,GAAA,CAAI,CAAC,KAAO,GAAG,KAAA,CAAM;oBAE7C,MAAM,sVAAe,kBAAA,CACnB,IAAG,SAAS,GAAA,CAAI,CAAC,KAAO,GAAG,MAAA,CAAO,CACnC;oBACD,MAAM,kBAAkB,IAAI;oBAE5B,MAAM,kVAAkB,oBAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACnB,eAAA,CAAA,GAAA;wBACH,+UAAQ,mBAAA,EAAiB,cAAc,gBAAgB,MAAA,CAAO;wBAC9D;wBACA,mBAAmB;wBACnB,kBAAkB;4VAClB,SAAA;6VACA,UAAA;wBACA;wBACA;wBACA,UAAU;4BACR,IAAA,CAAK,KAAK,OAAA,CACR,CAAA,OAAO,CAAE;4BAEX,IAAA,OAAW,KAAK,OAAA,KAAY,WAC1B,CAAA,OAAO,KAAK,OAAA,CAAQ;gCAClB,QAAQ;4BACT,EAAC;4BAEJ,OAAO,KAAK,OAAA;wBACb;uBACD;oBAEF,MAAM,MAAM,MAAM;oBAClB,MAAM,CAAC,KAAK,GAAG,2QAAM,sBAAA,EAEnB;wBAEA,MAAM,IAAI,IAAA;wBACV,aAAa,aAAa,WAAA,CAAY,MAAA,CAAO,WAAA;wBAE7C,aAAYC,MAAAA,EAAM;4BAChB,MAAM,QAAQA,OAAK,KAAA;4BACnB,yUAAO,kBAAA,CAAgB,IAAA,CAAK;gCAC1B;4BACD,EAAC;wBACH;wBACD;oBACD,EAAC;oBACF,MAAM,WAAW,OAAO,IAAA,CAAK,SAAS,CAAC,GAAA,CACrC,OAAO,QAA6B;wBAClC,IAAIC,OAAqB,MAAM,QAAQ,OAAA,CAAQ,IAAA,CAAK,IAAA,CAAK;wBAEzD,IAAI,YAAY,MAAM;;;;UAKpB,MAAM,SAAS,MAAM,QAAQ,OAAA,CAAQ,KAAK,MAAA,CAAO;4BACjD,OAAO;gCACL,QAAQ;oCACN,MAAM,MAAM,QAAQ,OAAA,CAAQ,OAAO,IAAA,CAAK;gCACzC;4BACF;wBACF;wBAED,OAAO;4BACL;4BACA,MAAM;gCACJ,UAAU;4BACX;wBACF;oBACF,EACF;oBACD,OAAO;gBACR;YACF;QACF;QAED,MAAM,+UAAQ,aAAA,EAAW,YAAY,QAAQ,CAAC;QAC9C,MAAM,eAAW,gVAAA,EAAW,YAAY,WAAW,CAAC;QAEpD,MAAM,UAAU;YAAE;YAAO;QAAU;QACnC,OAAO,CAAC,EAAE,EAAA,EAAI,KAAK;YACjB,OAAO,6QAAA,EAAW,CAAC,aAAa;sDAE9B,IAAI,GAAG,IAAA,KAAS,eACd,CAAA,MAAM,IAAI,MACR;gBAGJ,MAAM,SAAS,OAAA,CAAQ,GAAG,IAAA,CAAA;gBAC1B,MAAM,UAAU,OAAO,IAAA,CAAK,GAAG;gBAE/B,IAAI,OAAA,KAAA;gBACJ,QACG,IAAA,CAAK,CAAC,QAAQ;oBACb,OAAO;oBACP,IAAI,WAAW,IAAI,IAAA,EAAM;wBACvB,SAAS,KAAA,mUACP,kBAAA,CAAgB,IAAA,CAAK,IAAI,IAAA,EAAM;4BAC7B,MAAM,IAAI,IAAA;wBACX,EAAC,CACH;wBACD;oBACD,OAAA,IAAU,YAAY,IAAI,IAAA,EAAM;wBAC/B,SAAS,IAAA,CAAK;4BACZ,SAAS,IAAI,IAAA;4BACb,QAAQ,IAAI,IAAA,CAAK,MAAA;wBAClB,EAAC;wBACF,SAAS,QAAA,EAAU;wBACnB;oBACD;oBAED,SAAS,QAAA,EAAU;gBACpB,EAAC,CACD,KAAA,CAAM,CAAC,QAAQ;oBACd,SAAS,KAAA,mUACP,kBAAA,CAAgB,IAAA,CAAK,KAAK;wBACxB,MAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAM,KAAM,IAAA;oBACb,EAAC,CACH;gBACF,EAAC;gBAEJ,OAAO,KAEN,CAFY;YAGd,EAAC;QACH;IACF;AACF;;;GAKD,MAAa,+BAA+B;;;;AC7L5C,SAAgB,wBACdC,KAAAA,EACAC,WAAAA,EACA;IACA,IAAA,CAAK,YACH,CAAA,OAAO;IAET,IAAI,SAAS,QAAA,OAAe,UAAU,SACpC,CAAA,OAAO;IAET,OAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACM,UAAA,QAAA,UAAA,KAAA,IAAA,QAAS,CAAE,IAAA,CAAA,GAAA;QACf;IAAA;AAEH;;;;;QCdD,SAASC,iBAAe,CAAA,EAAG;YACzB,IAAI,GACF,GACA,GACA,IAAI;YACN,IAAK,eAAA,OAAsB,UAAA,CAAW,IAAI,OAAO,aAAA,EAAe,IAAI,OAAO,QAAA,GAAW,KAAM;gBAC1F,IAAI,KAAK,QAAA,CAAS,IAAI,CAAA,CAAE,EAAA,EAAK,CAAA,OAAO,EAAE,IAAA,CAAK,EAAE;gBAC7C,IAAI,KAAK,QAAA,CAAS,IAAI,CAAA,CAAE,EAAA,EAAK,CAAA,OAAO,IAAI,sBAAsB,EAAE,IAAA,CAAK,EAAE;gBACvE,IAAI,mBAAmB,IAAI;YAC5B;YACD,MAAM,IAAI,UAAU;QACrB;QACD,SAAS,sBAAsB,CAAA,EAAG;YAChC,SAAS,kCAAkCC,GAAAA,EAAG;gBAC5C,IAAI,OAAOA,IAAE,KAAKA,IAAG,CAAA,OAAO,QAAQ,MAAA,CAAO,IAAI,UAAUA,MAAI,sBAAsB;gBACnF,IAAI,IAAIA,IAAE,IAAA;gBACV,OAAO,QAAQ,OAAA,CAAQA,IAAE,KAAA,CAAM,CAAC,IAAA,CAAK,SAAUA,GAAAA,EAAG;oBAChD,OAAO;wBACL,OAAOA;wBACP,MAAM;oBACP;gBACF,EAAC;YACH;YACD,OAAO,wBAAwB,SAASC,wBAAsBD,GAAAA,EAAG;gBAC/D,IAAA,CAAK,CAAA,GAAIA,KAAG,IAAA,CAAK,CAAA,GAAIA,IAAE,IAAA;YACxB,GAAE,sBAAsB,SAAA,GAAY;gBACnC,GAAG;gBACH,GAAG;gBACH,MAAM,SAAS,OAAO;oBACpB,OAAO,kCAAkC,IAAA,CAAK,CAAA,CAAE,KAAA,CAAM,IAAA,CAAK,CAAA,EAAG,UAAU,CAAC;gBAC1E;gBACD,UAAU,SAAS,QAAQA,GAAAA,EAAG;oBAC5B,IAAI,IAAI,IAAA,CAAK,CAAA,CAAE,SAAA;oBACf,OAAA,KAAY,MAAM,IAAI,QAAQ,OAAA,CAAQ;wBACpC,OAAOA;wBACP,MAAA,CAAO;oBACR,EAAC,GAAG,kCAAkC,EAAE,KAAA,CAAM,IAAA,CAAK,CAAA,EAAG,UAAU,CAAC;gBACnE;gBACD,SAAS,SAAS,OAAOA,GAAAA,EAAG;oBAC1B,IAAI,IAAI,IAAA,CAAK,CAAA,CAAE,SAAA;oBACf,OAAA,KAAY,MAAM,IAAI,QAAQ,MAAA,CAAOA,IAAE,GAAG,kCAAkC,EAAE,KAAA,CAAM,IAAA,CAAK,CAAA,EAAG,UAAU,CAAC;gBACxG;YACF,GAAE,IAAI,sBAAsB;QAC9B;QACD,OAAO,OAAA,GAAUD,kBAAgB,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;ACnBtG,eAAe,wBACbG,IAAAA,EACiB;IACjB,IAAI,MAAM,mUAAM,WAAA,EAAS,KAAK,GAAA,CAAI;IAClC,IAAI,KAAK,gBAAA,EAAkB;QACzB,MAAM,SAAS,mUAAM,WAAA,EAAS,KAAK,gBAAA,CAAiB;QAEpD,MAAM,SAAS,IAAI,QAAA,CAAS,IAAI,GAAG,MAAM;QACzC,OACE,SAAS,sBAAsB,mBAAmB,KAAK,SAAA,CAAU,OAAO,CAAC;IAC5E;IAED,OAAO;AACR;;;GA0BD,SAAgB,qBAIdC,IAAAA,EAIuB;IACvB,MAAM,0VAAc,iBAAA,EAAe,KAAK,WAAA,CAAY;IAEpD,OAAO,MAAM;QACX,OAAO,CAAC,EAAE,EAAA,EAAI,KAAK;YACjB,uQAAO,aAAA,EAAW,CAAC,aAAa;;gBAC9B,MAAM,EAAE,IAAA,EAAM,IAAA,EAAM,KAAA,EAAO,GAAG;sDAG9B,IAAI,SAAS,eACX,CAAA,MAAM,IAAI,MAAM;gBAGlB,IAAIC,cAAAA,KAAAA;gBACJ,MAAM,KAAK,IAAI;gBACf,MAAM,+UAAS,oBAAA,EAAiB,GAAG,MAAA,EAAQ,GAAG,MAAA,CAAO;gBACrD,MAAM,yRAAoB,oBAAA,EAOvB;oBACD,KAAK,0UACH,SAAA,EAAO;4BACL;4BACA,KAAK,MAAM,wBAAwB,KAAK;4BACxC,OAAO,wBAAwB,OAAO,YAAY;4BAClD;4BACA;4BACA,QAAQ;wBACT,EAAC;oBACJ,MAAM,iUAAM,WAAA,EAAS,KAAK,kBAAA,EAAoB;4BAAE;wBAAI,EAAC;oBACrD;oBACA,aAAa,YAAY,MAAA,CAAO,WAAA;oBAChC,aAAA,CAAA,oBACE,KAAK,WAAA,MAAA,QAAA,sBAAA,KAAA,IAAA,oBACJ,WAAW,WAAA;gBACf,EAAC;gBAEF,MAAM,mRAAkB,kBAAA,EAEtB;oBACA,MAAM;oBACN,OAAO;oBACP,OAAO;gBACR,EAAC;gBAEF,MAAM,gBAAgB,gBAAgB,SAAA,CAAU;oBAC9C,MAAK,KAAA,EAAO;wBACV,SAAS,IAAA,CAAK;4BACZ,QAAQ;wBACT,EAAC;oBACH;gBACF,EAAC;gBACF,CAAA,GAAA,sPAAA,CAAA,MAAA,EAAI,YAAY;;;;;8EACY,oBAAA,OAAA,4BAAA,CAAA,CAAA,QAAA,MAAA,UAAA,IAAA,EAAA,EAAA,IAAA,EAAA,4BAAA,MAAA;kCAAT,QAAA,MAAA,KAAA;4BACf,OAAQ,MAAM,IAAA,EAAd;gCACE,KAAK,OAEH;oCAAA;gCACF,KAAK;oCACH,MAAM,YAAY,MAAM,IAAA;oCAExB,IAAIC;oCACJ,IAAI,UAAU,EAAA,EAAI;wCAEhB,cAAc,UAAU,EAAA;wCACxB,SAAS;4CACP,IAAI,UAAU,EAAA;4CACd,MAAM;wCACP;oCACF,MACC,CAAA,SAAS;wCACP,MAAM,UAAU,IAAA;oCACjB;oCAGH,SAAS,IAAA,CAAK;wCACZ;wCACA,SAAS;4CACP,aAAa,MAAM,WAAA;wCACpB;oCACF,EAAC;oCACF;gCACF,KAAK;oCAAa;wCAChB,SAAS,IAAA,CAAK;4CACZ,QAAQ;gDACN,MAAM;4CACP;4CACD,SAAS;gDACP,aAAa,MAAM,WAAA;4CACpB;wCACF,EAAC;wCACF,gBAAgB,IAAA,CAAK;4CACnB,MAAM;4CACN,OAAO;4CACP,OAAO;wCACR,EAAC;wCACF;oCACD;gCACD,KAAK;oCAAoB;wCACvB,MAAM,0UAAQ,kBAAA,CAAgB,IAAA,CAAK;4CAAE,OAAO,MAAM,KAAA;wCAAO,EAAC;wCAE1D,2PAAI,oBAAA,CAAkB,QAAA,CAAS,MAAM,KAAA,CAAM,IAAA,CAAK,EAAE;4CAEhD,gBAAgB,IAAA,CAAK;gDACnB,MAAM;gDACN,OAAO;gDACP;4CACD,EAAC;4CACF;wCACD;wCAGD,MAAM;oCACP;gCACD,KAAK;oCAAc;wCACjB,MAAM,YAAY,gBAAgB,GAAA,EAAK;wCAEvC,MAAM,QAAQ,MAAM,KAAA,sUAAS,kBAAA,CAAgB,IAAA,CAAK,MAAM,KAAA,CAAM;wCAC9D,IAAA,CAAK,SAAS,UAAU,KAAA,KAAU,aAChC,CAAA;wCAGF,gBAAgB,IAAA,CAAK;4CACnB,MAAM;4CACN,OAAO;4CACP;wCACD,EAAC;wCACF;oCACD;gCACD,KAAK,UACH;oCAAA,gBAAgB,IAAA,CAAK;wCACnB,MAAM;wCACN,OAAO;wCACP,OAAO,sUAAI,kBAAA,CAAA,CACR,WAAA,EAAa,MAAM,EAAA,CAAG,uCAAA,CAAA;oCAE1B,EAAC;4BAEL;;;;;;;;;;;;oBAEH,SAAS,IAAA,CAAK;wBACZ,QAAQ;4BACN,MAAM;wBACP;oBACF,EAAC;oBACF,gBAAgB,IAAA,CAAK;wBACnB,MAAM;wBACN,OAAO;wBACP,OAAO;oBACR,EAAC;oBACF,SAAS,QAAA,EAAU;gBACpB,EAAC,CAAC,KAAA,CAAM,CAAC,UAAU;oBAClB,SAAS,KAAA,mUAAM,kBAAA,CAAgB,IAAA,CAAK,MAAM,CAAC;gBAC5C,EAAC;gBAEF,OAAO,MAAM;oBACX,SAAS,QAAA,EAAU;oBACnB,GAAG,KAAA,EAAO;oBACV,cAAc,WAAA,EAAa;gBAC5B;YACF,EAAC;QACH;IACF;AACF;;;GAKD,MAAa,gCAAgC;;;;;;GC9M7C,SAAgB,UACdC,IAAAA,EACuB;IAEvB,OAAO,MAAM;QAEX,OAAO,CAAC,aAAa;YAEnB,uQAAO,aAAA,EAAW,CAAC,aAAa;gBAC9B,IAAIC;gBACJ,IAAIC,kBAAAA,KAAAA;gBAGJ,IAAIC,cAAAA,KAAAA;gBAEJ,QAAQ,EAAE;gBAEV,SAAS,oBAAoB;oBAC3B,MAAM,KAAK,SAAS,EAAA;oBACpB,IAAA,CAAK,YACH,CAAA,OAAO;oBAGT,OAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACK,KAAA,CAAA,GAAA;wBACH,OAAO,wBAAwB,GAAG,KAAA,EAAO,YAAY;oBAAA;gBAExD;gBAED,SAAS,QAAQC,QAAAA,EAAkB;oBACjC,MAAM,KAAK,mBAAmB;oBAE9B,QAAQ,SAAS,IAAA,CAAK,GAAG,CAAC,SAAA,CAAU;wBAClC,OAAM,KAAA,EAAO;;4BACX,MAAM,cAAc,KAAK,KAAA,CAAM;gCAC7B;gCACA;gCACA;4BACD,EAAC;4BACF,IAAA,CAAK,aAAa;gCAChB,SAAS,KAAA,CAAM,MAAM;gCACrB;4BACD;4BACD,MAAM,UAAA,CAAA,qBAAA,CAAA,sBAAU,KAAK,YAAA,MAAA,QAAA,wBAAA,KAAA,IAAA,KAAA,IAAL,oBAAA,IAAA,CAAA,MAAoB,SAAS,MAAA,QAAA,uBAAA,KAAA,IAAA,qBAAI;4BAEjD,IAAI,WAAW,GAAG;gCAChB,QAAQ,WAAW,EAAE;gCACrB;4BACD;4BACD,kBAAkB,WAChB,IAAM,QAAQ,WAAW,EAAE,EAC3B,QACD;wBACF;wBACD,MAAK,QAAA,EAAU;4BAEb,IAAA,CAAA,CACI,SAAS,MAAA,CAAO,IAAA,IAAQ,SAAS,MAAA,CAAO,IAAA,KAAS,MAAA,KACnD,SAAS,MAAA,CAAO,EAAA,CAGhB,CAAA,cAAc,SAAS,MAAA,CAAO,EAAA;4BAGhC,SAAS,IAAA,CAAK,SAAS;wBACxB;wBACD,WAAW;4BACT,SAAS,QAAA,EAAU;wBACpB;oBACF,EAAC;gBACH;gBACD,OAAO,MAAM;oBACX,MAAM,WAAA,EAAa;oBACnB,aAAa,gBAAgB;gBAC9B;YACF,EAAC;QACH;IACF;AACF;;;;;QCpHD,SAAS,YAAY;YACnB,IAAI,IAAI,cAAA,OAAqB,kBAAkB,kBAAkB,SAAUC,GAAAA,EAAGC,GAAAA,EAAG;gBAC7E,IAAIC,MAAI,OAAO;gBACf,OAAOA,IAAE,IAAA,GAAO,mBAAmBA,IAAE,KAAA,GAAQF,KAAGE,IAAE,UAAA,GAAaD,KAAGC;YACnE,GACD,IAAI,CAAE,GACN,IAAI,CAAE,CAAA;YACR,SAAS,MAAMF,GAAAA,EAAGC,GAAAA,EAAG;gBACnB,IAAI,QAAQA,KAAG;oBACb,IAAI,OAAOA,IAAE,KAAKA,IAAG,CAAA,MAAM,IAAI,UAAU;oBACzC,IAAID,KAAG,IAAI,IAAIC,GAAAA,CAAE,OAAO,YAAA,IAAgB,MAAA,CAAO,MAAA,CAAO,sBAAsB,CAAA;oBAC5E,IAAA,KAAS,MAAM,KAAA,CAAM,IAAIA,GAAAA,CAAE,OAAO,OAAA,IAAW,MAAA,CAAO,MAAA,CAAO,iBAAiB,CAAA,EAAGD,GAAAA,GAAI,IAAI,IAAI;oBAC3F,IAAI,cAAA,OAAqB,EAAG,CAAA,MAAM,IAAI,UAAU;oBAChD,KAAA,CAAM,IAAI,SAASG,MAAI;wBACrB,IAAI;4BACF,EAAE,IAAA,CAAKF,IAAE;wBACV,EAAA,OAAQD,KAAG;4BACV,OAAO,QAAQ,MAAA,CAAOA,IAAE;wBACzB;oBACF,CAAA,GAAG,EAAE,IAAA,CAAK;wBACT,GAAGC;wBACH,GAAG;wBACH,GAAGD;oBACJ,EAAC;gBACH,MAAM,CAAA,OAAK,EAAE,IAAA,CAAK;oBACjB,GAAGC;oBACH,GAAGD;gBACJ,EAAC;gBACF,OAAOC;YACR;YACD,OAAO;gBACF;gBACH,GAAG,MAAM,IAAA,CAAK,MAAA,CAAO,EAAE;gBACvB,GAAG,MAAM,IAAA,CAAK,MAAA,CAAO,EAAE;gBACvB,GAAG,SAAS,IAAI;oBACd,IAAI,GACF,IAAI,IAAA,CAAK,CAAA,EACT,IAAI;oBACN,SAAS,OAAO;wBACd,MAAO,IAAI,EAAE,GAAA,EAAK,EAAG,IAAI;4BACvB,IAAA,CAAK,EAAE,CAAA,IAAK,MAAM,EAAG,CAAA,OAAO,IAAI,GAAG,EAAE,IAAA,CAAK,EAAE,EAAE,QAAQ,OAAA,EAAS,CAAC,IAAA,CAAK,KAAK;4BAC1E,IAAI,EAAE,CAAA,EAAG;gCACP,IAAID,MAAI,EAAE,CAAA,CAAE,IAAA,CAAK,EAAE,CAAA,CAAE;gCACrB,IAAI,EAAE,CAAA,CAAG,CAAA,OAAO,KAAK,GAAG,QAAQ,OAAA,CAAQA,IAAE,CAAC,IAAA,CAAK,MAAM,IAAI;4BAC3D,MAAM,CAAA,KAAK;wBACb,EAAA,OAAQA,KAAG;4BACV,OAAO,IAAIA,IAAE;wBACd;wBACD,IAAI,MAAM,EAAG,CAAA,OAAO,MAAM,IAAI,QAAQ,MAAA,CAAO,EAAE,GAAG,QAAQ,OAAA,EAAS;wBACnE,IAAI,MAAM,EAAG,CAAA,MAAM;oBACpB;oBACD,SAAS,IAAIE,GAAAA,EAAG;wBACd,OAAO,IAAI,MAAM,IAAI,IAAI,EAAEA,KAAG,KAAKA,KAAG,MAAM;oBAC7C;oBACD,OAAO,MAAM;gBACd;YACF;QACF;QACD,OAAO,OAAA,GAAU,WAAW,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QC1DjG,SAAS,eAAe,CAAA,EAAG,CAAA,EAAG;YAC5B,IAAA,CAAK,CAAA,GAAI,GAAG,IAAA,CAAK,CAAA,GAAI;QACtB;QACD,OAAO,OAAA,GAAU,gBAAgB,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCHtG,IAAIE,kBAAAA;QACJ,SAASC,uBAAqB,CAAA,EAAG;YAC/B,OAAO,IAAID,gBAAc,GAAG;QAC7B;QACD,OAAO,OAAA,GAAUC,wBAAsB,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCJ5G,IAAI,gBAAA;QACJ,SAASC,sBAAoB,CAAA,EAAG;YAC9B,OAAO,WAAY;gBACjB,OAAO,IAAI,eAAe,EAAE,KAAA,CAAM,IAAA,EAAM,UAAU;YACnD;QACF;QACD,SAAS,eAAe,CAAA,EAAG;YACzB,IAAI,GAAG;YACP,SAAS,OAAOC,GAAAA,EAAGC,GAAAA,EAAG;gBACpB,IAAI;oBACF,IAAI,IAAI,CAAA,CAAED,IAAAA,CAAGC,IAAE,EACb,IAAI,EAAE,KAAA,EACN,IAAI,aAAa;oBACnB,QAAQ,OAAA,CAAQ,IAAI,EAAE,CAAA,GAAI,EAAE,CAAC,IAAA,CAAK,SAAUA,GAAAA,EAAG;wBAC7C,IAAI,GAAG;4BACL,IAAI,IAAI,aAAaD,MAAI,WAAW;4BACpC,IAAA,CAAK,EAAE,CAAA,IAAKC,IAAE,IAAA,CAAM,CAAA,OAAO,OAAO,GAAGA,IAAE;4BACvC,MAAI,CAAA,CAAE,EAAA,CAAGA,IAAE,CAAC,KAAA;wBACb;wBACD,OAAO,EAAE,IAAA,GAAO,WAAW,UAAUA,IAAE;oBACxC,GAAE,SAAUC,GAAAA,EAAG;wBACd,OAAO,SAASA,IAAE;oBACnB,EAAC;gBACH,EAAA,OAAQA,KAAG;oBACV,OAAO,SAASA,IAAE;gBACnB;YACF;YACD,SAAS,OAAOA,GAAAA,EAAG,CAAA,EAAG;gBACpB,OAAQA,KAAR;oBACE,KAAK;wBACH,EAAE,OAAA,CAAQ;4BACR,OAAO;4BACP,MAAA,CAAO;wBACR,EAAC;wBACF;oBACF,KAAK;wBACH,EAAE,MAAA,CAAO,EAAE;wBACX;oBACF,QACE;wBAAA,EAAE,OAAA,CAAQ;4BACR,OAAO;4BACP,MAAA,CAAO;wBACR,EAAC;gBACL;gBACD,CAAC,IAAI,EAAE,IAAA,IAAQ,OAAO,EAAE,GAAA,EAAK,EAAE,GAAA,CAAI,GAAG,IAAI;YAC3C;YACD,IAAA,CAAK,OAAA,GAAU,SAAUA,GAAAA,EAAG,CAAA,EAAG;gBAC7B,OAAO,IAAI,QAAQ,SAAU,CAAA,EAAG,CAAA,EAAG;oBACjC,IAAI,IAAI;wBACN,KAAKA;wBACL,KAAK;wBACL,SAAS;wBACT,QAAQ;wBACR,MAAM;oBACP;oBACD,IAAI,IAAI,EAAE,IAAA,GAAO,IAAA,CAAK,IAAI,IAAI,GAAG,OAAOA,KAAG,EAAE;gBAC9C;YACF,GAAE,cAAA,OAAqB,CAAA,CAAE,SAAA,IAAA,CAAc,IAAA,CAAK,SAAA,GAAA,KAAiB,CAAA;QAC/D;QACD,eAAe,SAAA,CAAU,cAAA,OAAqB,UAAU,OAAO,aAAA,IAAiB,kBAAA,GAAqB,WAAY;YAC/G,OAAO,IAAA;QACR,GAAE,eAAe,SAAA,CAAU,IAAA,GAAO,SAAU,CAAA,EAAG;YAC9C,OAAO,IAAA,CAAK,OAAA,CAAQ,QAAQ,EAAE;QAC/B,GAAE,eAAe,SAAA,CAAU,QAAA,GAAW,SAAU,CAAA,EAAG;YAClD,OAAO,IAAA,CAAK,OAAA,CAAQ,SAAS,EAAE;QAChC,GAAE,eAAe,SAAA,CAAU,SAAA,GAAY,SAAU,CAAA,EAAG;YACnD,OAAO,IAAA,CAAK,OAAA,CAAQ,UAAU,EAAE;QACjC;QACD,OAAO,OAAA,GAAUH,uBAAqB,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;;;;;;;GC7B3G,SAAgB,mBACdI,IAAAA,EACmB;IACnB,MAAM,0VAAc,iBAAA,EAAe,KAAK,WAAA,CAAY;IAEpD,MAAM,iBAAiB,CAACC,UAAmB;QACzC,IAAI,KAAK,WAAA,CAEP,CAAA,OAAO;QAGT,IAAI,UAAA,KAAA,EACF,CAAA,OAAO;QAET,MAAM,aAAa,KAAK,SAAA,CAAU,YAAY,KAAA,CAAM,SAAA,CAAU,MAAM,CAAC;QACrE,MAAM,eAAe,KAAK,KAAA,CAAM,YAAY,MAAA,CAAO,WAAA,CAAY,WAAW,CAAC;QAC3E,OAAO;IACR;IAED,OAAO,IACL,CAAC,EAAE,EAAA,EAAI,mQACL,aAAA,EAAW,CAAC,aAAa;gBACvB,IAAIC,MAAAA,KAAAA;gBACJ,MAAM,KAAK,IAAI;gBAEf,MAAM,gVAAS,mBAAA,EAAiB,GAAG,MAAA,EAAQ,GAAG,MAAA,CAAO;gBACrD,MAAM,uVAAgB,uBAAA,EAAqB,OAAO;gBAElD,cAAc,KAAA,CAAM,KAEnB,CAFyB,CAExB;gBAEF,IAAI,QAAQ,GAAG,KAAA;gBACf,eAAe,aAAaC,QAAAA,EAAqC;oBAC/D,QAAQ;oBAER,MAAM,MAAM,KAAK,aAAA,EAAe;oBAEhC,oQAAO,gBAAA,EAAc;wBACnB,QAAQ,KAAK,MAAA;wBACb,MAAM,GAAG,IAAA;wBACT,aAAa,UAAY;wBACzB;wBACA,MAAM,GAAG,IAAA;wBACT;oBACD,EAAC;gBACH;gBAED,SAAS,gBAAgBC,KAAAA,EAAgB;;oBACvC,yQAAI,eAAA,EAAa,MAAM,CACrB,CAAA;oBAEF,CAAA,gBAAA,KAAK,OAAA,MAAA,QAAA,kBAAA,KAAA,KAAL,cAAA,IAAA,CAAA,MAAe;wBACb,oQAAO,0BAAA,EAAwB,MAAM;wBACrC,MAAM,GAAG,IAAA;wBACT,MAAM,GAAG,IAAA;wBACT;wBACA;oBACD,EAAC;gBACH;gBAED,SAAS,wBAAwBA,KAAAA,EAAgB;oBAC/C,0UAAI,oBAAA,EAA2B,MAAM,CACnC,CAAA,OAAO;oBAET,MAAM,qQAAQ,0BAAA,EAAwB,MAAM;oBAE5C,MAAM,SAAQ,4UAAA,EAAkB;wBAC9B,QAAQ,KAAK,MAAA,CAAO,IAAA,CAAK,OAAA;wBACzB;wBACA;wBACA;wBACA,MAAM,GAAG,IAAA;wBACT,MAAM,GAAG,IAAA;oBACV,EAAC;oBACF,yUAAO,kBAAA,CAAgB,IAAA,CAAK;wBAC1B,OAAO,eAAe,MAAM;oBAC7B,EAAC;gBACH;gBAED,CAAA,GAAA,sPAAA,CAAA,MAAA,EAAI,YAAY;oBACd,OAAQ,GAAG,IAAA,EAAX;wBACE,KAAK;wBACL,KAAK;4BAAY;gCACf,MAAM,SAAS,MAAM,aAAa,GAAG,KAAA,CAAM;gCAC3C,IAAA,4PAAK,kBAAA,EAAgB,OAAO,EAAE;oCAC5B,SAAS,IAAA,CAAK;wCACZ,QAAQ;4CAAE,MAAM,eAAe,OAAO;wCAAE;oCACzC,EAAC;oCACF,SAAS,QAAA,EAAU;oCACnB;gCACD;gCAED,SAAS,IAAA,CAAK;oCACZ,QAAQ;wCACN,MAAM,CAAA,GAAA,0BAAA,OAAA,EAAA,aAAoB;;;gDACxB,MAAY,WAAA,YAAA,CAAA,sQAAW,mBAAA,EAAiB,OAAO;gDAC/C,MAAM,WAAA,YAAA,CAAA,sQAAW,eAAA,EAAa,CAAE,GAAE,MAAM;oDACtC,SAAS,QAAA,EAAU;gDACpB,EAAC;gDACF,IAAI;oDACF,MAAO,KAAM;wDACX,MAAM,MAAA,MAAA,CAAA,GAAA,2BAAA,OAAA,EAAY,QAAQ,IAAA,CAAK;4DAC7B,SAAS,IAAA,EAAM;4DACf,aACD;yDAAA,CAAC;wDACF,IAAI,IAAI,IAAA,CACN,CAAA,OAAO,eAAe,IAAI,KAAA,CAAM;wDAElC,MAAM,eAAe,IAAI,KAAA,CAAM;oDAChC;gDACF,EAAA,OAAQ,OAAO;oDACd,gBAAgB,MAAM;oDACtB,MAAM,wBAAwB,MAAM;gDACrC;;;;;;wCACF,IAAG;oCACL;gCACF,EAAC;gCACF;4BACD;wBACD,KAAK;4BAAA,IAAA;;gCACH,MAAM,mRAAkB,kBAAA,EAEtB;oCACA,MAAM;oCACN,OAAO;oCACP,OAAO;gCACR,EAAC;gCAEF,MAAM,gBAAgB,gBAAgB,SAAA,CAAU;oCAC9C,MAAK,KAAA,EAAO;wCACV,SAAS,IAAA,CAAK;4CACZ,QAAQ;wCACT,EAAC;oCACH;gCACF,EAAC;gCACF,IAAIC,cAAAA,KAAAA;gCAEJ,MAAM,WAAA,WAAA,CAAA,sQAAW,eAAA,EAAa,CAAE,GAAE,YAAY;oCAC5C,SAAS,QAAA,EAAU;oCAEnB,gBAAgB,IAAA,CAAK;wCACnB,MAAM;wCACN,OAAO;wCACP,OAAO;oCACR,EAAC;oCACF,cAAc,WAAA,EAAa;gCAC5B,EAAC;gCACF,MAAO,KAAA,IAAA;;oCACL,MAAM,SAAS,MAAM,aACnB,wBAAwB,GAAG,KAAA,EAAO,YAAY,CAC/C;oCACD,IAAA,4PAAK,kBAAA,EAAgB,OAAO,CAC1B,CAAA,MAAM,IAAI,MAAM;oCAElB,MAAY,WAAA,WAAA,CAAA,CAAW,wRAAA,EAAiB,OAAO;oCAE/C,SAAS,IAAA,CAAK;wCACZ,QAAQ;4CACN,MAAM;wCACP;oCACF,EAAC;oCACF,gBAAgB,IAAA,CAAK;wCACnB,MAAM;wCACN,OAAO;wCACP,OAAO;oCACR,EAAC;oCAGF,MAAO,KAAM;wCACX,IAAI;wCACJ,IAAI;4CACF,MAAM,MAAM,QAAQ,IAAA,CAAK;gDAAC,SAAS,IAAA,EAAM;gDAAE,aAAc;6CAAA,CAAC;wCAC3D,EAAA,OAAQ,OAAO;4CACd,yQAAI,eAAA,EAAa,MAAM,CACrB,CAAA;4CAEF,MAAM,qQAAQ,0BAAA,EAAwB,MAAM;4CAE5C,IAAA,wPACG,oBAAA,CAAkB,QAAA,wPACjB,0BAAA,CAAwB,MAAM,IAAA,CAAA,CAC/B,CAED,CAAA,MAAM,wBAAwB,MAAM;4CAGtC,gBAAgB,MAAM;4CACtB,gBAAgB,IAAA,CAAK;gDACnB,MAAM;gDACN,OAAO;gDACP,OAAO,wBAAwB,MAAM;4CACtC,EAAC;4CAEF;wCACD;wCAED,IAAI,IAAI,IAAA,CACN,CAAA;wCAEF,IAAIC;wCACJ,iQAAI,oBAAA,EAAkB,IAAI,KAAA,CAAM,EAAE;4CAChC,cAAc,IAAI,KAAA,CAAM,EAAA;4CAExB,QAAQ;gDACN,IAAI,IAAI,KAAA,CAAM,EAAA;gDACd,MAAM;oDACJ,IAAI,IAAI,KAAA,CAAM,EAAA;oDACd,MAAM,IAAI,KAAA,CAAM,EAAA;gDACjB;4CACF;wCACF,MACC,CAAA,QAAQ;4CACN,MAAM,IAAI,KAAA;wCACX;wCAGH,SAAS,IAAA,CAAK;4CACZ,QAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACK,QAAA,CAAA,GAAA;gDACH,MAAM,eAAe,MAAM,IAAA,CAAK;4CAAA;wCAEnC,EAAC;oCACH;;;;;;gCAEH;;;;;;oBAEH;gBACF,EAAC,CAAC,KAAA,CAAM,CAAC,UAAU;oBAClB,gBAAgB,MAAM;oBACtB,SAAS,KAAA,CAAM,wBAAwB,MAAM,CAAC;gBAC/C,EAAC;gBAEF,OAAO,MAAM;oBACX,GAAG,KAAA,EAAO;gBACX;YACF,EAAC;AACP;;;GAID,MAAaC,yBACX", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], "debugId": null}}]}