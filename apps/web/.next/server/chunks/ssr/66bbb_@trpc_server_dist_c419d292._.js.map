{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "observable-UMO3vUa_.mjs", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/observable/observable.ts"], "sourcesContent": ["import type { Result } from '../unstable-core-do-not-import';\nimport type {\n  Observable,\n  Observer,\n  OperatorFunction,\n  TeardownLogic,\n  UnaryFunction,\n  Unsubscribable,\n} from './types';\n\n/** @public */\nexport type inferObservableValue<TObservable> =\n  TObservable extends Observable<infer TValue, unknown> ? TValue : never;\n\n/** @public */\nexport function isObservable(x: unknown): x is Observable<unknown, unknown> {\n  return typeof x === 'object' && x !== null && 'subscribe' in x;\n}\n\n/** @public */\nexport function observable<TValue, TError = unknown>(\n  subscribe: (observer: Observer<TValue, TError>) => TeardownLogic,\n): Observable<TValue, TError> {\n  const self: Observable<TValue, TError> = {\n    subscribe(observer) {\n      let teardownRef: TeardownLogic | null = null;\n      let isDone = false;\n      let unsubscribed = false;\n      let teardownImmediately = false;\n      function unsubscribe() {\n        if (teardownRef === null) {\n          teardownImmediately = true;\n          return;\n        }\n        if (unsubscribed) {\n          return;\n        }\n        unsubscribed = true;\n\n        if (typeof teardownRef === 'function') {\n          teardownRef();\n        } else if (teardownRef) {\n          teardownRef.unsubscribe();\n        }\n      }\n      teardownRef = subscribe({\n        next(value) {\n          if (isDone) {\n            return;\n          }\n          observer.next?.(value);\n        },\n        error(err) {\n          if (isDone) {\n            return;\n          }\n          isDone = true;\n          observer.error?.(err);\n          unsubscribe();\n        },\n        complete() {\n          if (isDone) {\n            return;\n          }\n          isDone = true;\n          observer.complete?.();\n          unsubscribe();\n        },\n      });\n      if (teardownImmediately) {\n        unsubscribe();\n      }\n      return {\n        unsubscribe,\n      };\n    },\n    pipe(\n      ...operations: OperatorFunction<any, any, any, any>[]\n    ): Observable<any, any> {\n      return operations.reduce(pipeReducer, self);\n    },\n  };\n  return self;\n}\n\nfunction pipeReducer(prev: any, fn: UnaryFunction<any, any>) {\n  return fn(prev);\n}\n\n/** @internal */\nexport function observableToPromise<TValue>(\n  observable: Observable<TValue, unknown>,\n) {\n  const ac = new AbortController();\n  const promise = new Promise<TValue>((resolve, reject) => {\n    let isDone = false;\n    function onDone() {\n      if (isDone) {\n        return;\n      }\n      isDone = true;\n      obs$.unsubscribe();\n    }\n    ac.signal.addEventListener('abort', () => {\n      reject(ac.signal.reason);\n    });\n    const obs$ = observable.subscribe({\n      next(data) {\n        isDone = true;\n        resolve(data);\n        onDone();\n      },\n      error(data) {\n        reject(data);\n      },\n      complete() {\n        ac.abort();\n        onDone();\n      },\n    });\n  });\n  return promise;\n}\n\n/**\n * @internal\n */\nfunction observableToReadableStream<TValue>(\n  observable: Observable<TValue, unknown>,\n  signal: AbortSignal,\n): ReadableStream<Result<TValue>> {\n  let unsub: Unsubscribable | null = null;\n\n  const onAbort = () => {\n    unsub?.unsubscribe();\n    unsub = null;\n    signal.removeEventListener('abort', onAbort);\n  };\n\n  return new ReadableStream<Result<TValue>>({\n    start(controller) {\n      unsub = observable.subscribe({\n        next(data) {\n          controller.enqueue({ ok: true, value: data });\n        },\n        error(error) {\n          controller.enqueue({ ok: false, error });\n          controller.close();\n        },\n        complete() {\n          controller.close();\n        },\n      });\n\n      if (signal.aborted) {\n        onAbort();\n      } else {\n        signal.addEventListener('abort', onAbort, { once: true });\n      }\n    },\n    cancel() {\n      onAbort();\n    },\n  });\n}\n\n/** @internal */\nexport function observableToAsyncIterable<TValue>(\n  observable: Observable<TValue, unknown>,\n  signal: AbortSignal,\n): AsyncIterable<TValue> {\n  const stream = observableToReadableStream(observable, signal);\n\n  const reader = stream.getReader();\n  const iterator: AsyncIterator<TValue> = {\n    async next() {\n      const value = await reader.read();\n      if (value.done) {\n        return {\n          value: undefined,\n          done: true,\n        };\n      }\n      const { value: result } = value;\n      if (!result.ok) {\n        throw result.error;\n      }\n      return {\n        value: result.value,\n        done: false,\n      };\n    },\n    async return() {\n      await reader.cancel();\n      return {\n        value: undefined,\n        done: true,\n      };\n    },\n  };\n  return {\n    [Symbol.asyncIterator]() {\n      return iterator;\n    },\n  };\n}\n"], "names": ["x: unknown", "subscribe: (observer: Observer<TValue, TError>) => TeardownLogic", "self: Observable<TV<PERSON><PERSON>, TError>", "teardownRef: TeardownLogic | null", "prev: any", "fn: UnaryFunction<any, any>", "observable: Observable<TValue, unknown>", "signal: AbortSignal", "unsub: Unsubscribable | null", "observable", "iterator: AsyncIterator<TValue>"], "mappings": ";;;;;;;AAeA,SAAgB,aAAaA,CAAAA,EAA+C;IAC1E,OAAA,OAAc,MAAM,YAAY,MAAM,QAAQ,eAAe;AAC9D;eAGD,SAAgB,WACdC,SAAAA,EAC4B;IAC5B,MAAMC,OAAmC;QACvC,WAAU,QAAA,EAAU;YAClB,IAAIC,cAAoC;YACxC,IAAI,SAAS;YACb,IAAI,eAAe;YACnB,IAAI,sBAAsB;YAC1B,SAAS,cAAc;gBACrB,IAAI,gBAAgB,MAAM;oBACxB,sBAAsB;oBACtB;gBACD;gBACD,IAAI,aACF,CAAA;gBAEF,eAAe;gBAEf,IAAA,OAAW,gBAAgB,WACzB,CAAA,aAAa;yBACJ,YACT,CAAA,YAAY,WAAA,EAAa;YAE5B;YACD,cAAc,UAAU;gBACtB,MAAK,KAAA,EAAO;;oBACV,IAAI,OACF,CAAA;oBAEF,CAAA,iBAAA,SAAS,IAAA,MAAA,QAAA,mBAAA,KAAA,KAAT,eAAA,IAAA,CAAA,UAAgB,MAAM;gBACvB;gBACD,OAAM,GAAA,EAAK;;oBACT,IAAI,OACF,CAAA;oBAEF,SAAS;oBACT,CAAA,kBAAA,SAAS,KAAA,MAAA,QAAA,oBAAA,KAAA,KAAT,gBAAA,IAAA,CAAA,UAAiB,IAAI;oBACrB,aAAa;gBACd;gBACD,WAAW;;oBACT,IAAI,OACF,CAAA;oBAEF,SAAS;oBACT,CAAA,qBAAA,SAAS,QAAA,MAAA,QAAA,uBAAA,KAAA,KAAT,mBAAA,IAAA,CAAA,SAAqB;oBACrB,aAAa;gBACd;YACF,EAAC;YACF,IAAI,oBACF,CAAA,aAAa;YAEf,OAAO;gBACL;YACD;QACF;QACD,MACE,GAAG,UAAA,EACmB;YACtB,OAAO,WAAW,MAAA,CAAO,aAAa,KAAK;QAC5C;IACF;IACD,OAAO;AACR;AAED,SAAS,YAAYC,IAAAA,EAAWC,EAAAA,EAA6B;IAC3D,OAAO,GAAG,KAAK;AAChB;iBAGD,SAAgB,oBACdC,YAAAA,EACA;IACA,MAAM,KAAK,IAAI;IACf,MAAM,UAAU,IAAI,QAAgB,CAAC,SAAS,WAAW;QACvD,IAAI,SAAS;QACb,SAAS,SAAS;YAChB,IAAI,OACF,CAAA;YAEF,SAAS;YACT,KAAK,WAAA,EAAa;QACnB;QACD,GAAG,MAAA,CAAO,gBAAA,CAAiB,SAAS,MAAM;YACxC,OAAO,GAAG,MAAA,CAAO,MAAA,CAAO;QACzB,EAAC;QACF,MAAM,OAAO,aAAW,SAAA,CAAU;YAChC,MAAK,IAAA,EAAM;gBACT,SAAS;gBACT,QAAQ,KAAK;gBACb,QAAQ;YACT;YACD,OAAM,IAAA,EAAM;gBACV,OAAO,KAAK;YACb;YACD,WAAW;gBACT,GAAG,KAAA,EAAO;gBACV,QAAQ;YACT;QACF,EAAC;IACH;IACD,OAAO;AACR;;;GAKD,SAAS,2BACPA,YAAAA,EACAC,MAAAA,EACgC;IAChC,IAAIC,QAA+B;IAEnC,MAAM,UAAU,MAAM;QACpB,UAAA,QAAA,UAAA,KAAA,KAAA,MAAO,WAAA,EAAa;QACpB,QAAQ;QACR,OAAO,mBAAA,CAAoB,SAAS,QAAQ;IAC7C;IAED,OAAO,IAAI,eAA+B;QACxC,OAAM,UAAA,EAAY;YAChB,QAAQ,aAAW,SAAA,CAAU;gBAC3B,MAAK,IAAA,EAAM;oBACT,WAAW,OAAA,CAAQ;wBAAE,IAAI;wBAAM,OAAO;oBAAM,EAAC;gBAC9C;gBACD,OAAM,KAAA,EAAO;oBACX,WAAW,OAAA,CAAQ;wBAAE,IAAI;wBAAO;oBAAO,EAAC;oBACxC,WAAW,KAAA,EAAO;gBACnB;gBACD,WAAW;oBACT,WAAW,KAAA,EAAO;gBACnB;YACF,EAAC;YAEF,IAAI,OAAO,OAAA,CACT,CAAA,SAAS;iBAET,OAAO,gBAAA,CAAiB,SAAS,SAAS;gBAAE,MAAM;YAAM,EAAC;QAE5D;QACD,SAAS;YACP,SAAS;QACV;IACF;AACF;iBAGD,SAAgB,0BACdF,YAAAA,EACAC,MAAAA,EACuB;IACvB,MAAM,SAAS,2BAA2BE,cAAY,OAAO;IAE7D,MAAM,SAAS,OAAO,SAAA,EAAW;IACjC,MAAMC,WAAkC;QACtC,MAAM,OAAO;YACX,MAAM,QAAQ,MAAM,OAAO,IAAA,EAAM;YACjC,IAAI,MAAM,IAAA,CACR,CAAA,OAAO;gBACL,OAAA,KAAA;gBACA,MAAM;YACP;YAEH,MAAM,EAAE,OAAO,MAAA,EAAQ,GAAG;YAC1B,IAAA,CAAK,OAAO,EAAA,CACV,CAAA,MAAM,OAAO,KAAA;YAEf,OAAO;gBACL,OAAO,OAAO,KAAA;gBACd,MAAM;YACP;QACF;QACD,MAAM,SAAS;YACb,MAAM,OAAO,MAAA,EAAQ;YACrB,OAAO;gBACL,OAAA,KAAA;gBACA,MAAM;YACP;QACF;IACF;IACD,OAAO;QACL,CAAC,OAAO,aAAA,CAAA,GAAiB;YACvB,OAAO;QACR;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "file": "utils-DdbbrDku.mjs", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/rpc/codes.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/utils.ts"], "sourcesContent": ["import type { InvertKeyValue, ValueOf } from '../types';\n\n// reference: https://www.jsonrpc.org/specification\n\n/**\n * JSON-RPC 2.0 Error codes\n *\n * `-32000` to `-32099` are reserved for implementation-defined server-errors.\n * For tRPC we're copying the last digits of HTTP 4XX errors.\n */\nexport const TRPC_ERROR_CODES_BY_KEY = {\n  /**\n   * Invalid JSON was received by the server.\n   * An error occurred on the server while parsing the JSON text.\n   */\n  PARSE_ERROR: -32700,\n  /**\n   * The JSON sent is not a valid Request object.\n   */\n  BAD_REQUEST: -32600, // 400\n\n  // Internal JSON-RPC error\n  INTERNAL_SERVER_ERROR: -32603, // 500\n  NOT_IMPLEMENTED: -32603, // 501\n  BAD_GATEWAY: -32603, // 502\n  SERVICE_UNAVAILABLE: -32603, // 503\n  GATEWAY_TIMEOUT: -32603, // 504\n\n  // Implementation specific errors\n  UNAUTHORIZED: -32001, // 401\n  PAYMENT_REQUIRED: -32002, // 402\n  FORBIDDEN: -32003, // 403\n  NOT_FOUND: -32004, // 404\n  METHOD_NOT_SUPPORTED: -32005, // 405\n  TIMEOUT: -32008, // 408\n  CONFLICT: -32009, // 409\n  PRECONDITION_FAILED: -32012, // 412\n  PAYLOAD_TOO_LARGE: -32013, // 413\n  UNSUPPORTED_MEDIA_TYPE: -32015, // 415\n  UNPROCESSABLE_CONTENT: -32022, // 422\n  TOO_MANY_REQUESTS: -32029, // 429\n  CLIENT_CLOSED_REQUEST: -32099, // 499\n} as const;\n\n// pure\nexport const TRPC_ERROR_CODES_BY_NUMBER: InvertKeyValue<\n  typeof TRPC_ERROR_CODES_BY_KEY\n> = {\n  [-32700]: 'PARSE_ERROR',\n  [-32600]: 'BAD_REQUEST',\n  [-32603]: 'INTERNAL_SERVER_ERROR',\n  [-32001]: 'UNAUTHORIZED',\n  [-32002]: 'PAYMENT_REQUIRED',\n  [-32003]: 'FORBIDDEN',\n  [-32004]: 'NOT_FOUND',\n  [-32005]: 'METHOD_NOT_SUPPORTED',\n  [-32008]: 'TIMEOUT',\n  [-32009]: 'CONFLICT',\n  [-32012]: 'PRECONDITION_FAILED',\n  [-32013]: 'PAYLOAD_TOO_LARGE',\n  [-32015]: 'UNSUPPORTED_MEDIA_TYPE',\n  [-32022]: 'UNPROCESSABLE_CONTENT',\n  [-32029]: 'TOO_MANY_REQUESTS',\n  [-32099]: 'CLIENT_CLOSED_REQUEST',\n};\n\nexport type TRPC_ERROR_CODE_NUMBER = ValueOf<typeof TRPC_ERROR_CODES_BY_KEY>;\nexport type TRPC_ERROR_CODE_KEY = keyof typeof TRPC_ERROR_CODES_BY_KEY;\n\n/**\n * tRPC error codes that are considered retryable\n * With out of the box SSE, the client will reconnect when these errors are encountered\n */\nexport const retryableRpcCodes: TRPC_ERROR_CODE_NUMBER[] = [\n  TRPC_ERROR_CODES_BY_KEY.BAD_GATEWAY,\n  TRPC_ERROR_CODES_BY_KEY.SERVICE_UNAVAILABLE,\n  TRPC_ERROR_CODES_BY_KEY.GATEWAY_TIMEOUT,\n  TRPC_ERROR_CODES_BY_KEY.INTERNAL_SERVER_ERROR,\n];\n", "/** @internal */\nexport type UnsetMarker = 'unsetMarker' & {\n  __brand: 'unsetMarker';\n};\n\n/**\n * Ensures there are no duplicate keys when building a procedure.\n * @internal\n */\nexport function mergeWithoutOverrides<TType extends Record<string, unknown>>(\n  obj1: TType,\n  ...objs: Partial<TType>[]\n): TType {\n  const newObj: TType = Object.assign(Object.create(null), obj1);\n\n  for (const overrides of objs) {\n    for (const key in overrides) {\n      if (key in newObj && newObj[key] !== overrides[key]) {\n        throw new Error(`Duplicate key ${key}`);\n      }\n      newObj[key as keyof TType] = overrides[key] as TType[keyof TType];\n    }\n  }\n  return newObj;\n}\n\n/**\n * Check that value is object\n * @internal\n */\nexport function isObject(value: unknown): value is Record<string, unknown> {\n  return !!value && !Array.isArray(value) && typeof value === 'object';\n}\n\ntype AnyFn = ((...args: any[]) => unknown) & Record<keyof any, unknown>;\nexport function isFunction(fn: unknown): fn is AnyFn {\n  return typeof fn === 'function';\n}\n\n/**\n * Create an object without inheriting anything from `Object.prototype`\n * @internal\n */\nexport function omitPrototype<TObj extends Record<string, unknown>>(\n  obj: TObj,\n): TObj {\n  return Object.assign(Object.create(null), obj);\n}\n\nconst asyncIteratorsSupported =\n  typeof Symbol === 'function' && !!Symbol.asyncIterator;\n\nexport function isAsyncIterable<TValue>(\n  value: unknown,\n): value is AsyncIterable<TValue> {\n  return (\n    asyncIteratorsSupported && isObject(value) && Symbol.asyncIterator in value\n  );\n}\n\n/**\n * Run an IIFE\n */\nexport const run = <TValue>(fn: () => TValue): TValue => fn();\n\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nexport function noop(): void {}\n\nexport function identity<T>(it: T): T {\n  return it;\n}\n\n/**\n * Generic runtime assertion function. Throws, if the condition is not `true`.\n *\n * Can be used as a slightly less dangerous variant of type assertions. Code\n * mistakes would be revealed at runtime then (hopefully during testing).\n */\nexport function assert(\n  condition: boolean,\n  msg = 'no additional info',\n): asserts condition {\n  if (!condition) {\n    throw new Error(`AssertionError: ${msg}`);\n  }\n}\n\nexport function sleep(ms = 0): Promise<void> {\n  return new Promise<void>((res) => setTimeout(res, ms));\n}\n\n/**\n * Ponyfill for\n * [`AbortSignal.any`](https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/any_static).\n */\nexport function abortSignalsAnyPonyfill(signals: AbortSignal[]): AbortSignal {\n  if (typeof AbortSignal.any === 'function') {\n    return AbortSignal.any(signals);\n  }\n\n  const ac = new AbortController();\n\n  for (const signal of signals) {\n    if (signal.aborted) {\n      trigger();\n      break;\n    }\n    signal.addEventListener('abort', trigger, { once: true });\n  }\n\n  return ac.signal;\n\n  function trigger() {\n    ac.abort();\n    for (const signal of signals) {\n      signal.removeEventListener('abort', trigger);\n    }\n  }\n}\n"], "names": ["TRPC_ERROR_CODES_BY_NUMBER: InvertKeyValue<\n  typeof TRPC_ERROR_CODES_BY_KEY\n>", "retryableRpcCodes: TRPC_ERROR_CODE_NUMBER[]", "obj1: TType", "newObj: TType", "value: unknown", "fn: unknown", "obj: TObj", "fn: () => TValue", "it: T", "condition: boolean", "signals: AbortSignal[]"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAUA,MAAa,0BAA0B;IAKrC,aAAa,CAAA;IAIb,aAAa,CAAA;IAGb,uBAAuB,CAAA;IACvB,iBAAiB,CAAA;IACjB,aAAa,CAAA;IACb,qBAAqB,CAAA;IACrB,iBAAiB,CAAA;IAGjB,cAAc,CAAA;IACd,kBAAkB,CAAA;IAClB,WAAW,CAAA;IACX,WAAW,CAAA;IACX,sBAAsB,CAAA;IACtB,SAAS,CAAA;IACT,UAAU,CAAA;IACV,qBAAqB,CAAA;IACrB,mBAAmB,CAAA;IACnB,wBAAwB,CAAA;IACxB,uBAAuB,CAAA;IACvB,mBAAmB,CAAA;IACnB,uBAAuB,CAAA;AACxB;AAGD,MAAaA,6BAET;KACD,CAAA,MAAA,EAAS;KAC<PERSON>,CAAA,MAAA,EAAS;KACT,CAAA,MAAA,EAAS;KACT,CAAA,MAAA,EAAS;KACT,CAAA,MAAA,EAAS;KACT,CAAA,MAAA,EAAS;KACT,CAAA,MAAA,EAAS;KACT,CAAA,MAAA,EAAS;KACT,CAAA,MAAA,EAAS;KACT,CAAA,MAAA,EAAS;KACT,CAAA,MAAA,EAAS;KACT,CAAA,MAAA,EAAS;KACT,CAAA,MAAA,EAAS;KACT,CAAA,MAAA,EAAS;KACT,CAAA,MAAA,EAAS;KACT,CAAA,MAAA,EAAS;AACX;;;;GASD,MAAaC,oBAA8C;IACzD,wBAAwB,WAAA;IACxB,wBAAwB,mBAAA;IACxB,wBAAwB,eAAA;IACxB,wBAAwB,qBAAA;CACzB;;;;;;GCrED,SAAgB,sBACdC,IAAAA,EACA,GAAG,IAAA,EACI;IACP,MAAMC,SAAgB,OAAO,MAAA,CAAO,OAAO,MAAA,CAAO,KAAK,EAAE,KAAK;IAE9D,KAAK,MAAM,aAAa,KACtB,IAAK,MAAM,OAAO,UAAW;QAC3B,IAAI,OAAO,UAAU,MAAA,CAAO,IAAA,KAAS,SAAA,CAAU,IAAA,CAC7C,CAAA,MAAM,IAAI,MAAA,CAAO,cAAA,EAAgB,IAAI,CAAA;QAEvC,MAAA,CAAO,IAAA,GAAsB,SAAA,CAAU,IAAA;IACxC;IAEH,OAAO;AACR;;;;GAMD,SAAgB,SAASC,KAAAA,EAAkD;IACzE,OAAA,CAAA,CAAS,SAAA,CAAU,MAAM,OAAA,CAAQ,MAAM,IAAA,OAAW,UAAU;AAC7D;AAGD,SAAgB,WAAWC,EAAAA,EAA0B;IACnD,OAAA,OAAc,OAAO;AACtB;;;;GAMD,SAAgB,cACdC,GAAAA,EACM;IACN,OAAO,OAAO,MAAA,CAAO,OAAO,MAAA,CAAO,KAAK,EAAE,IAAI;AAC/C;AAED,MAAM,0BAAA,OACG,WAAW,cAAA,CAAA,CAAgB,OAAO,aAAA;AAE3C,SAAgB,gBACdF,KAAAA,EACgC;IAChC,OACE,2BAA2B,SAAS,MAAM,IAAI,OAAO,aAAA,IAAiB;AAEzE;;;GAKD,MAAa,MAAM,CAASG,KAA6B,IAAI;AAG7D,SAAgB,OAAa,CAAE;AAE/B,SAAgB,SAAYC,EAAAA,EAAU;IACpC,OAAO;AACR;;;;;;GAQD,SAAgB,OACdC,SAAAA,EACA,MAAM,oBAAA,EACa;IACnB,IAAA,CAAK,UACH,CAAA,MAAM,IAAI,MAAA,CAAO,gBAAA,EAAkB,IAAI,CAAA;AAE1C;AAED,SAAgB,MAAM,KAAK,CAAA,EAAkB;IAC3C,OAAO,IAAI,QAAc,CAAC,MAAQ,WAAW,KAAK,GAAG;AACtD;;;;GAMD,SAAgB,wBAAwBC,OAAAA,EAAqC;IAC3E,IAAA,OAAW,YAAY,GAAA,KAAQ,WAC7B,CAAA,OAAO,YAAY,GAAA,CAAI,QAAQ;IAGjC,MAAM,KAAK,IAAI;IAEf,KAAK,MAAM,UAAU,QAAS;QAC5B,IAAI,OAAO,OAAA,EAAS;YAClB,SAAS;YACT;QACD;QACD,OAAO,gBAAA,CAAiB,SAAS,SAAS;YAAE,MAAM;QAAM,EAAC;IAC1D;IAED,OAAO,GAAG,MAAA;;IAEV,SAAS,UAAU;QACjB,GAAG,KAAA,EAAO;QACV,KAAK,MAAM,UAAU,QACnB,OAAO,mBAAA,CAAoB,SAAS,QAAQ;IAE/C;AACF", "ignoreList": [0, 1], "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "file": "getErrorShape-Uhlrl4Bk.mjs", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/createProxy.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/http/getHTTPStatusCode.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/typeof.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/toPrimitive.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/toPropertyKey.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/defineProperty.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/objectSpread2.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/error/getErrorShape.ts"], "sourcesContent": ["interface ProxyCallbackOptions {\n  path: readonly string[];\n  args: readonly unknown[];\n}\ntype ProxyCallback = (opts: ProxyCallbackOptions) => unknown;\n\nconst noop = () => {\n  // noop\n};\n\nconst freezeIfAvailable = (obj: object) => {\n  if (Object.freeze) {\n    Object.freeze(obj);\n  }\n};\n\nfunction createInnerProxy(\n  callback: ProxyCallback,\n  path: readonly string[],\n  memo: Record<string, unknown>,\n) {\n  const cacheKey = path.join('.');\n\n  memo[cacheKey] ??= new Proxy(noop, {\n    get(_obj, key) {\n      if (typeof key !== 'string' || key === 'then') {\n        // special case for if the proxy is accidentally treated\n        // like a PromiseLike (like in `Promise.resolve(proxy)`)\n        return undefined;\n      }\n      return createInnerProxy(callback, [...path, key], memo);\n    },\n    apply(_1, _2, args) {\n      const lastOfPath = path[path.length - 1];\n\n      let opts = { args, path };\n      // special handling for e.g. `trpc.hello.call(this, 'there')` and `trpc.hello.apply(this, ['there'])\n      if (lastOfPath === 'call') {\n        opts = {\n          args: args.length >= 2 ? [args[1]] : [],\n          path: path.slice(0, -1),\n        };\n      } else if (lastOfPath === 'apply') {\n        opts = {\n          args: args.length >= 2 ? args[1] : [],\n          path: path.slice(0, -1),\n        };\n      }\n      freezeIfAvailable(opts.args);\n      freezeIfAvailable(opts.path);\n      return callback(opts);\n    },\n  });\n\n  return memo[cacheKey];\n}\n\n/**\n * Creates a proxy that calls the callback with the path and arguments\n *\n * @internal\n */\nexport const createRecursiveProxy = <TFaux = unknown>(\n  callback: ProxyCallback,\n): TFaux => createInnerProxy(callback, [], Object.create(null)) as TFaux;\n\n/**\n * Used in place of `new Proxy` where each handler will map 1 level deep to another value.\n *\n * @internal\n */\nexport const createFlatProxy = <TFaux>(\n  callback: (path: keyof TFaux) => any,\n): TFaux => {\n  return new Proxy(noop, {\n    get(_obj, name) {\n      if (name === 'then') {\n        // special case for if the proxy is accidentally treated\n        // like a PromiseLike (like in `Promise.resolve(proxy)`)\n        return undefined;\n      }\n      return callback(name as any);\n    },\n  }) as TFaux;\n};\n", "import type { TRPCError } from '../error/TRPCError';\nimport type { TRPC_ERROR_CODES_BY_KEY, TRPCResponse } from '../rpc';\nimport { TRPC_ERROR_CODES_BY_NUMBER } from '../rpc';\nimport type { InvertKeyValue, ValueOf } from '../types';\nimport { isObject } from '../utils';\n\nexport const JSONRPC2_TO_HTTP_CODE: Record<\n  keyof typeof TRPC_ERROR_CODES_BY_KEY,\n  number\n> = {\n  PARSE_ERROR: 400,\n  BAD_REQUEST: 400,\n  UNAUTHORIZED: 401,\n  PAYMENT_REQUIRED: 402,\n  FORBIDDEN: 403,\n  NOT_FOUND: 404,\n  METHOD_NOT_SUPPORTED: 405,\n  TIMEOUT: 408,\n  CONFLICT: 409,\n  PRECONDITION_FAILED: 412,\n  PAYLOAD_TOO_LARGE: 413,\n  UNSUPPORTED_MEDIA_TYPE: 415,\n  UNPROCESSABLE_CONTENT: 422,\n  TOO_MANY_REQUESTS: 429,\n  CLIENT_CLOSED_REQUEST: 499,\n  INTERNAL_SERVER_ERROR: 500,\n  NOT_IMPLEMENTED: 501,\n  BAD_GATEWAY: 502,\n  SERVICE_UNAVAILABLE: 503,\n  GATEWAY_TIMEOUT: 504,\n};\n\nexport const HTTP_CODE_TO_JSONRPC2: InvertKeyValue<\n  typeof JSONRPC2_TO_HTTP_CODE\n> = {\n  400: 'BAD_REQUEST',\n  401: 'UNAUTHORIZED',\n  402: 'PAYMENT_REQUIRED',\n  403: 'FORBIDDEN',\n  404: 'NOT_FOUND',\n  405: 'METHOD_NOT_SUPPORTED',\n  408: 'TIMEOUT',\n  409: 'CONFLICT',\n  412: 'PRECONDITION_FAILED',\n  413: 'PAYLOAD_TOO_LARGE',\n  415: 'UNSUPPORTED_MEDIA_TYPE',\n  422: 'UNPROCESSABLE_CONTENT',\n  429: 'TOO_MANY_REQUESTS',\n  499: 'CLIENT_CLOSED_REQUEST',\n  500: 'INTERNAL_SERVER_ERROR',\n  501: 'NOT_IMPLEMENTED',\n  502: 'BAD_GATEWAY',\n  503: 'SERVICE_UNAVAILABLE',\n  504: 'GATEWAY_TIMEOUT',\n} as const;\n\nexport function getStatusCodeFromKey(\n  code: keyof typeof TRPC_ERROR_CODES_BY_KEY,\n) {\n  return JSONRPC2_TO_HTTP_CODE[code] ?? 500;\n}\n\nexport function getStatusKeyFromCode(\n  code: keyof typeof HTTP_CODE_TO_JSONRPC2,\n): ValueOf<typeof HTTP_CODE_TO_JSONRPC2> {\n  return HTTP_CODE_TO_JSONRPC2[code] ?? 'INTERNAL_SERVER_ERROR';\n}\n\nexport function getHTTPStatusCode(json: TRPCResponse | TRPCResponse[]) {\n  const arr = Array.isArray(json) ? json : [json];\n  const httpStatuses = new Set<number>(\n    arr.map((res) => {\n      if ('error' in res && isObject(res.error.data)) {\n        if (typeof res.error.data?.['httpStatus'] === 'number') {\n          return res.error.data['httpStatus'];\n        }\n        const code = TRPC_ERROR_CODES_BY_NUMBER[res.error.code];\n        return getStatusCodeFromKey(code);\n      }\n      return 200;\n    }),\n  );\n\n  if (httpStatuses.size !== 1) {\n    return 207;\n  }\n\n  const httpStatus = httpStatuses.values().next().value;\n\n  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n  return httpStatus!;\n}\n\nexport function getHTTPStatusCodeFromError(error: TRPCError) {\n  return getStatusCodeFromKey(error.code);\n}\n", "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _typeof(o);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nmodule.exports = toPrimitive, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nvar toPrimitive = require(\"./toPrimitive.js\");\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nmodule.exports = toPropertyKey, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var defineProperty = require(\"./defineProperty.js\");\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nmodule.exports = _objectSpread2, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import { getHTTPStatusCodeFromError } from '../http/getHTTPStatusCode';\nimport type { ProcedureType } from '../procedure';\nimport type { AnyRootTypes, RootConfig } from '../rootConfig';\nimport { TRPC_ERROR_CODES_BY_KEY } from '../rpc';\nimport type { DefaultErrorShape } from './formatter';\nimport type { TRPCError } from './TRPCError';\n\n/**\n * @internal\n */\nexport function getErrorShape<TRoot extends AnyRootTypes>(opts: {\n  config: RootConfig<TRoot>;\n  error: TRPCError;\n  type: ProcedureType | 'unknown';\n  path: string | undefined;\n  input: unknown;\n  ctx: TRoot['ctx'] | undefined;\n}): TRoot['errorShape'] {\n  const { path, error, config } = opts;\n  const { code } = opts.error;\n  const shape: DefaultErrorShape = {\n    message: error.message,\n    code: TRPC_ERROR_CODES_BY_KEY[code],\n    data: {\n      code,\n      httpStatus: getHTTPStatusCodeFromError(error),\n    },\n  };\n  if (config.isDev && typeof opts.error.stack === 'string') {\n    shape.data.stack = opts.error.stack;\n  }\n  if (typeof path === 'string') {\n    shape.data.path = path;\n  }\n  return config.errorFormatter({ ...opts, shape });\n}\n"], "names": ["obj: object", "callback: ProxyC<PERSON>back", "path: readonly string[]", "memo: Record<string, unknown>", "callback: (path: keyof TFaux) => any", "JSONRPC2_TO_HTTP_CODE: Record<\n  keyof typeof TRPC_ERROR_CODES_BY_KEY,\n  number\n>", "HTTP_CODE_TO_JSONRPC2: InvertKeyValue<\n  typeof JSONRPC2_TO_HTTP_CODE\n>", "code: keyof typeof TRPC_ERROR_CODES_BY_KEY", "code: keyof typeof HTTP_CODE_TO_JSONRPC2", "json: TRPCResponse | TRPCResponse[]", "error: TRPCError", "_typeof", "o", "_typeof", "toPrimitive", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "r", "opts: {\n  config: RootConfig<TRoot>;\n  error: TRPCError;\n  type: ProcedureType | 'unknown';\n  path: string | undefined;\n  input: unknown;\n  ctx: TRoot['ctx'] | undefined;\n}", "shape: DefaultErrorShape"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,MAAM,OAAO,KAEZ,CAFkB;AAInB,MAAM,oBAAoB,CAACA,QAAgB;IACzC,IAAI,OAAO,MAAA,CACT,CAAA,OAAO,MAAA,CAAO,IAAI;AAErB;AAED,SAAS,iBACPC,QAAAA,EACAC,IAAAA,EACAC,IAAAA,EACA;;IACA,MAAM,WAAW,KAAK,IAAA,CAAK,IAAI;IAE/B,CAAA,iBAAA,IAAA,CAAK,SAAA,MAAA,QAAA,mBAAA,KAAA,KAAA,CAAL,IAAA,CAAK,SAAA,GAAc,IAAI,MAAM,MAAM;QACjC,KAAI,IAAA,EAAM,GAAA,EAAK;YACb,IAAA,OAAW,QAAQ,YAAY,QAAQ,OAGrC,CAAA,OAAA,KAAA;YAEF,OAAO,iBAAiB,UAAU,CAAC;mBAAG;gBAAM,GAAI;aAAA,EAAE,KAAK;QACxD;QACD,OAAM,EAAA,EAAI,EAAA,EAAI,IAAA,EAAM;YAClB,MAAM,aAAa,IAAA,CAAK,KAAK,MAAA,GAAS,EAAA;YAEtC,IAAI,OAAO;gBAAE;gBAAM;YAAM;YAEzB,IAAI,eAAe,OACjB,CAAA,OAAO;gBACL,MAAM,KAAK,MAAA,IAAU,IAAI;oBAAC,IAAA,CAAK,EAAG;iBAAA,GAAG,CAAE,CAAA;gBACvC,MAAM,KAAK,KAAA,CAAM,GAAG,CAAA,EAAG;YACxB;qBACQ,eAAe,QACxB,CAAA,OAAO;gBACL,MAAM,KAAK,MAAA,IAAU,IAAI,IAAA,CAAK,EAAA,GAAK,CAAE,CAAA;gBACrC,MAAM,KAAK,KAAA,CAAM,GAAG,CAAA,EAAG;YACxB;YAEH,kBAAkB,KAAK,IAAA,CAAK;YAC5B,kBAAkB,KAAK,IAAA,CAAK;YAC5B,OAAO,SAAS,KAAK;QACtB;IACF,EAAA;IAED,OAAO,IAAA,CAAK,SAAA;AACb;;;;;GAOD,MAAa,uBAAuB,CAClCF,WACU,iBAAiB,UAAU,CAAE,CAAA,EAAE,OAAO,MAAA,CAAO,KAAK,CAAC;;;;;GAO/D,MAAa,kBAAkB,CAC7BG,aACU;IACV,OAAO,IAAI,MAAM,MAAM;QACrB,KAAI,IAAA,EAAM,IAAA,EAAM;YACd,IAAI,SAAS,OAGX,CAAA,OAAA,KAAA;YAEF,OAAO,SAAS,KAAY;QAC7B;IACF;AACF;;;AC9ED,MAAaC,wBAGT;IACF,aAAa;IACb,aAAa;IACb,cAAc;IACd,kBAAkB;IAClB,WAAW;IACX,WAAW;IACX,sBAAsB;IACtB,SAAS;IACT,UAAU;IACV,qBAAqB;IACrB,mBAAmB;IACnB,wBAAwB;IACxB,uBAAuB;IACvB,mBAAmB;IACnB,uBAAuB;IACvB,uBAAuB;IACvB,iBAAiB;IACjB,aAAa;IACb,qBAAqB;IACrB,iBAAiB;AAClB;AAED,MAAaC,wBAET;IACF,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACN;AAED,SAAgB,qBACdC,IAAAA,EACA;;IACA,OAAA,CAAA,wBAAO,qBAAA,CAAsB,KAAA,MAAA,QAAA,0BAAA,KAAA,IAAA,wBAAS;AACvC;AAED,SAAgB,qBACdC,IAAAA,EACuC;;IACvC,OAAA,CAAA,wBAAO,qBAAA,CAAsB,KAAA,MAAA,QAAA,0BAAA,KAAA,IAAA,wBAAS;AACvC;AAED,SAAgB,kBAAkBC,IAAAA,EAAqC;IACrE,MAAM,MAAM,MAAM,OAAA,CAAQ,KAAK,GAAG,OAAO;QAAC,IAAK;KAAA;IAC/C,MAAM,eAAe,IAAI,IACvB,IAAI,GAAA,CAAI,CAAC,QAAQ;QACf,IAAI,WAAW,kQAAO,WAAA,EAAS,IAAI,KAAA,CAAM,IAAA,CAAK,EAAE;;YAC9C,IAAA,OAAA,CAAA,CAAA,kBAAW,IAAI,KAAA,CAAM,IAAA,MAAA,QAAA,oBAAA,KAAA,IAAA,KAAA,IAAA,eAAA,CAAO,aAAA,MAAkB,SAC5C,CAAA,OAAO,IAAI,KAAA,CAAM,IAAA,CAAK,aAAA;YAExB,MAAM,8PAAO,6BAAA,CAA2B,IAAI,KAAA,CAAM,IAAA,CAAA;YAClD,OAAO,qBAAqB,KAAK;QAClC;QACD,OAAO;IACR,EAAC;IAGJ,IAAI,aAAa,IAAA,KAAS,EACxB,CAAA,OAAO;IAGT,MAAM,aAAa,aAAa,MAAA,EAAQ,CAAC,IAAA,EAAM,CAAC,KAAA;IAGhD,OAAO;AACR;AAED,SAAgB,2BAA2BC,KAAAA,EAAkB;IAC3D,OAAO,qBAAqB,MAAM,IAAA,CAAK;AACxC;;;;;QC/FD,SAASC,UAAQ,CAAA,EAAG;YAClB;YAEA,OAAO,OAAO,OAAA,GAAUA,YAAU,cAAA,OAAqB,UAAU,YAAA,OAAmB,OAAO,QAAA,GAAW,SAAUC,GAAAA,EAAG;gBACjH,OAAA,OAAcA;YACf,IAAG,SAAUA,GAAAA,EAAG;gBACf,OAAOA,OAAK,cAAA,OAAqB,UAAUA,IAAE,WAAA,KAAgB,UAAUA,QAAM,OAAO,SAAA,GAAY,WAAA,OAAkBA;YACnH,GAAE,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA,EAAS,UAAQ,EAAE;QAC5F;QACD,OAAO,OAAA,GAAUD,WAAS,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCT/F,IAAIE,YAAAA,gBAAAA,CAAiC,UAAA;QACrC,SAASC,cAAY,CAAA,EAAG,CAAA,EAAG;YACzB,IAAI,YAAY,UAAQ,EAAE,IAAA,CAAK,EAAG,CAAA,OAAO;YACzC,IAAI,IAAI,CAAA,CAAE,OAAO,WAAA,CAAA;YACjB,IAAA,KAAS,MAAM,GAAG;gBAChB,IAAI,IAAI,EAAE,IAAA,CAAK,GAAG,KAAK,UAAU;gBACjC,IAAI,YAAY,UAAQ,EAAE,CAAE,CAAA,OAAO;gBACnC,MAAM,IAAI,UAAU;YACrB;YACD,OAAO,CAAC,aAAa,IAAI,SAAS,MAAA,EAAQ,EAAE;QAC7C;QACD,OAAO,OAAA,GAAUA,eAAa,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCXnG,IAAI,UAAA,gBAAA,CAAiC,UAAA;QACrC,IAAI,cAAA;QACJ,SAASC,gBAAc,CAAA,EAAG;YACxB,IAAI,IAAI,YAAY,GAAG,SAAS;YAChC,OAAO,YAAY,QAAQ,EAAE,GAAG,IAAI,IAAI;QACzC;QACD,OAAO,OAAA,GAAUA,iBAAe,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCNrG,IAAI,gBAAA;QACJ,SAAS,gBAAgB,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;YAChC,OAAA,CAAQ,IAAI,cAAc,EAAE,KAAK,IAAI,OAAO,cAAA,CAAe,GAAG,GAAG;gBAC/D,OAAO;gBACP,YAAA,CAAa;gBACb,cAAA,CAAe;gBACf,UAAA,CAAW;YACZ,EAAC,GAAG,CAAA,CAAE,EAAA,GAAK,GAAG;QAChB;QACD,OAAO,OAAA,GAAU,iBAAiB,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCTvG,IAAI,iBAAA;QACJ,SAAS,QAAQ,CAAA,EAAG,CAAA,EAAG;YACrB,IAAI,IAAI,OAAO,IAAA,CAAK,EAAE;YACtB,IAAI,OAAO,qBAAA,EAAuB;gBAChC,IAAI,IAAI,OAAO,qBAAA,CAAsB,EAAE;gBACvC,KAAA,CAAM,IAAI,EAAE,MAAA,CAAO,SAAUC,GAAAA,EAAG;oBAC9B,OAAO,OAAO,wBAAA,CAAyB,GAAGA,IAAE,CAAC,UAAA;gBAC9C,EAAC,GAAG,EAAE,IAAA,CAAK,KAAA,CAAM,GAAG,EAAE;YACxB;YACD,OAAO;QACR;QACD,SAAS,eAAe,CAAA,EAAG;YACzB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAA,EAAQ,IAAK;gBACzC,IAAI,IAAI,QAAQ,SAAA,CAAU,EAAA,GAAK,SAAA,CAAU,EAAA,GAAK,CAAE;gBAChD,IAAI,IAAI,QAAQ,OAAO,EAAE,EAAA,CAAG,EAAE,CAAC,OAAA,CAAQ,SAAUA,GAAAA,EAAG;oBAClD,eAAe,GAAGA,KAAG,CAAA,CAAEA,IAAAA,CAAG;gBAC3B,EAAC,GAAG,OAAO,yBAAA,GAA4B,OAAO,gBAAA,CAAiB,GAAG,OAAO,yBAAA,CAA0B,EAAE,CAAC,GAAG,QAAQ,OAAO,EAAE,CAAC,CAAC,OAAA,CAAQ,SAAUA,GAAAA,EAAG;oBAChJ,OAAO,cAAA,CAAe,GAAGA,KAAG,OAAO,wBAAA,CAAyB,GAAGA,IAAE,CAAC;gBACnE,EAAC;YACH;YACD,OAAO;QACR;QACD,OAAO,OAAA,GAAU,gBAAgB,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;;GCZtG,SAAgB,cAA0CC,IAAAA,EAOlC;IACtB,MAAM,EAAE,IAAA,EAAM,KAAA,EAAO,MAAA,EAAQ,GAAG;IAChC,MAAM,EAAE,IAAA,EAAM,GAAG,KAAK,KAAA;IACtB,MAAMC,QAA2B;QAC/B,SAAS,MAAM,OAAA;QACf,6PAAM,0BAAA,CAAwB,KAAA;QAC9B,MAAM;YACJ;YACA,YAAY,2BAA2B,MAAM;QAC9C;IACF;IACD,IAAI,OAAO,KAAA,IAAA,OAAgB,KAAK,KAAA,CAAM,KAAA,KAAU,SAC9C,CAAA,MAAM,IAAA,CAAK,KAAA,GAAQ,KAAK,KAAA,CAAM,KAAA;IAEhC,IAAA,OAAW,SAAS,SAClB,CAAA,MAAM,IAAA,CAAK,IAAA,GAAO;IAEpB,OAAO,OAAO,cAAA,CAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAoB,OAAA,CAAA,GAAA;QAAM;IAAA,GAAQ;AACjD", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7], "debugId": null}}, {"offset": {"line": 621, "column": 0}, "map": {"version": 3, "file": "tracked-gU3ttYjg.mjs", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/error/formatter.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/error/TRPCError.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/transformer.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/router.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/stream/tracked.ts"], "sourcesContent": ["import type { ProcedureType } from '../procedure';\nimport type {\n  TRPC_ERROR_CODE_KEY,\n  TRPC_ERROR_CODE_NUMBER,\n  TRPCErrorShape,\n} from '../rpc';\nimport type { TRPCError } from './TRPCError';\n\n/**\n * @internal\n */\nexport type ErrorFormatter<TContext, T<PERSON><PERSON><PERSON> extends TRPCErrorShape> = (opts: {\n  error: TRPCError;\n  type: ProcedureType | 'unknown';\n  path: string | undefined;\n  input: unknown;\n  ctx: TContext | undefined;\n  shape: DefaultErrorShape;\n}) => TShape;\n\n/**\n * @internal\n */\nexport type DefaultErrorData = {\n  code: TRPC_ERROR_CODE_KEY;\n  httpStatus: number;\n  /**\n   * Path to the procedure that threw the error\n   */\n  path?: string;\n  /**\n   * Stack trace of the error (only in development)\n   */\n  stack?: string;\n};\n\n/**\n * @internal\n */\nexport interface DefaultErrorShape extends TRPCErrorShape<DefaultErrorData> {\n  message: string;\n  code: TRPC_ERROR_CODE_NUMBER;\n}\n\nexport const defaultFormatter: ErrorFormatter<any, any> = ({ shape }) => {\n  return shape;\n};\n", "import type { TRPC_ERROR_CODE_KEY } from '../rpc/codes';\nimport { isObject } from '../utils';\n\nclass UnknownCauseError extends Error {\n  [key: string]: unknown;\n}\nexport function getCauseFromUnknown(cause: unknown): Error | undefined {\n  if (cause instanceof Error) {\n    return cause;\n  }\n\n  const type = typeof cause;\n  if (type === 'undefined' || type === 'function' || cause === null) {\n    return undefined;\n  }\n\n  // Primitive types just get wrapped in an error\n  if (type !== 'object') {\n    // eslint-disable-next-line @typescript-eslint/no-base-to-string\n    return new Error(String(cause));\n  }\n\n  // If it's an object, we'll create a synthetic error\n  if (isObject(cause)) {\n    return Object.assign(new UnknownCauseError(), cause);\n  }\n\n  return undefined;\n}\n\nexport function getTRPCErrorFromUnknown(cause: unknown): TRPCError {\n  if (cause instanceof TRPCError) {\n    return cause;\n  }\n  if (cause instanceof Error && cause.name === 'TRPCError') {\n    // https://github.com/trpc/trpc/pull/4848\n    return cause as TRPCError;\n  }\n\n  const trpcError = new TRPCError({\n    code: 'INTERNAL_SERVER_ERROR',\n    cause,\n  });\n\n  // Inherit stack from error\n  if (cause instanceof Error && cause.stack) {\n    trpcError.stack = cause.stack;\n  }\n\n  return trpcError;\n}\n\nexport class TRPCError extends Error {\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore override doesn't work in all environments due to \"This member cannot have an 'override' modifier because it is not declared in the base class 'Error'\"\n  public override readonly cause?: Error;\n  public readonly code;\n\n  constructor(opts: {\n    message?: string;\n    code: TRPC_ERROR_CODE_KEY;\n    cause?: unknown;\n  }) {\n    const cause = getCauseFromUnknown(opts.cause);\n    const message = opts.message ?? cause?.message ?? opts.code;\n\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore https://github.com/tc39/proposal-error-cause\n    super(message, { cause });\n\n    this.code = opts.code;\n    this.name = 'TRPCError';\n    this.cause ??= cause;\n  }\n}\n", "import type { AnyRootTypes, RootConfig } from './rootConfig';\nimport type { AnyRouter, inferRouterError } from './router';\nimport type {\n  TRPCResponse,\n  TRPCResponseMessage,\n  TRPCResultMessage,\n} from './rpc';\nimport { isObject } from './utils';\n\n/**\n * @public\n */\nexport interface DataTransformer {\n  serialize: (object: any) => any;\n  deserialize: (object: any) => any;\n}\n\ninterface InputDataTransformer extends DataTransformer {\n  /**\n   * This function runs **on the client** before sending the data to the server.\n   */\n  serialize: (object: any) => any;\n  /**\n   * This function runs **on the server** to transform the data before it is passed to the resolver\n   */\n  deserialize: (object: any) => any;\n}\n\ninterface OutputDataTransformer extends DataTransformer {\n  /**\n   * This function runs **on the server** before sending the data to the client.\n   */\n  serialize: (object: any) => any;\n  /**\n   * This function runs **only on the client** to transform the data sent from the server.\n   */\n  deserialize: (object: any) => any;\n}\n\n/**\n * @public\n */\nexport interface CombinedDataTransformer {\n  /**\n   * Specify how the data sent from the client to the server should be transformed.\n   */\n  input: InputDataTransformer;\n  /**\n   * Specify how the data sent from the server to the client should be transformed.\n   */\n  output: OutputDataTransformer;\n}\n\n/**\n * @public\n */\nexport type CombinedDataTransformerClient = {\n  input: Pick<CombinedDataTransformer['input'], 'serialize'>;\n  output: Pick<CombinedDataTransformer['output'], 'deserialize'>;\n};\n\n/**\n * @public\n */\nexport type DataTransformerOptions = CombinedDataTransformer | DataTransformer;\n\n/**\n * @internal\n */\nexport function getDataTransformer(\n  transformer: DataTransformerOptions,\n): CombinedDataTransformer {\n  if ('input' in transformer) {\n    return transformer;\n  }\n  return { input: transformer, output: transformer };\n}\n\n/**\n * @internal\n */\nexport const defaultTransformer: CombinedDataTransformer = {\n  input: { serialize: (obj) => obj, deserialize: (obj) => obj },\n  output: { serialize: (obj) => obj, deserialize: (obj) => obj },\n};\n\nfunction transformTRPCResponseItem<\n  TResponseItem extends TRPCResponse | TRPCResponseMessage,\n>(config: RootConfig<AnyRootTypes>, item: TResponseItem): TResponseItem {\n  if ('error' in item) {\n    return {\n      ...item,\n      error: config.transformer.output.serialize(item.error),\n    };\n  }\n\n  if ('data' in item.result) {\n    return {\n      ...item,\n      result: {\n        ...item.result,\n        data: config.transformer.output.serialize(item.result.data),\n      },\n    };\n  }\n\n  return item;\n}\n\n/**\n * Takes a unserialized `TRPCResponse` and serializes it with the router's transformers\n **/\nexport function transformTRPCResponse<\n  TResponse extends\n    | TRPCResponse\n    | TRPCResponse[]\n    | TRPCResponseMessage\n    | TRPCResponseMessage[],\n>(config: RootConfig<AnyRootTypes>, itemOrItems: TResponse) {\n  return Array.isArray(itemOrItems)\n    ? itemOrItems.map((item) => transformTRPCResponseItem(config, item))\n    : transformTRPCResponseItem(config, itemOrItems);\n}\n\n// FIXME:\n// - the generics here are probably unnecessary\n// - the RPC-spec could probably be simplified to combine HTTP + WS\n/** @internal */\nfunction transformResultInner<TRouter extends AnyRouter, TOutput>(\n  response:\n    | TRPCResponse<TOutput, inferRouterError<TRouter>>\n    | TRPCResponseMessage<TOutput, inferRouterError<TRouter>>,\n  transformer: DataTransformer,\n) {\n  if ('error' in response) {\n    const error = transformer.deserialize(\n      response.error,\n    ) as inferRouterError<TRouter>;\n    return {\n      ok: false,\n      error: {\n        ...response,\n        error,\n      },\n    } as const;\n  }\n\n  const result = {\n    ...response.result,\n    ...((!response.result.type || response.result.type === 'data') && {\n      type: 'data',\n      data: transformer.deserialize(response.result.data),\n    }),\n  } as TRPCResultMessage<TOutput>['result'];\n  return { ok: true, result } as const;\n}\n\nclass TransformResultError extends Error {\n  constructor() {\n    super('Unable to transform response from server');\n  }\n}\n\n/**\n * Transforms and validates that the result is a valid TRPCResponse\n * @internal\n */\nexport function transformResult<TRouter extends AnyRouter, TOutput>(\n  response:\n    | TRPCResponse<TOutput, inferRouterError<TRouter>>\n    | TRPCResponseMessage<TOutput, inferRouterError<TRouter>>,\n  transformer: DataTransformer,\n): ReturnType<typeof transformResultInner> {\n  let result: ReturnType<typeof transformResultInner>;\n  try {\n    // Use the data transformers on the JSON-response\n    result = transformResultInner(response, transformer);\n  } catch {\n    throw new TransformResultError();\n  }\n\n  // check that output of the transformers is a valid TRPCResponse\n  if (\n    !result.ok &&\n    (!isObject(result.error.error) ||\n      typeof result.error.error['code'] !== 'number')\n  ) {\n    throw new TransformResultError();\n  }\n  if (result.ok && !isObject(result.result)) {\n    throw new TransformResultError();\n  }\n  return result;\n}\n", "import type { Observable } from '../observable';\nimport { createRecursiveProxy } from './createProxy';\nimport { defaultFormatter } from './error/formatter';\nimport { getTRPCErrorFromUnknown, TRPCError } from './error/TRPCError';\nimport type {\n  AnyProcedure,\n  ErrorHandlerOptions,\n  inferProcedureInput,\n  inferProcedureOutput,\n  LegacyObservableSubscriptionProcedure,\n} from './procedure';\nimport type { ProcedureCallOptions } from './procedureBuilder';\nimport type { AnyRootTypes, RootConfig } from './rootConfig';\nimport { defaultTransformer } from './transformer';\nimport type { MaybePromise, ValueOf } from './types';\nimport {\n  isFunction,\n  isObject,\n  mergeWithoutOverrides,\n  omitPrototype,\n} from './utils';\n\nexport interface RouterRecord {\n  [key: string]: AnyProcedure | RouterRecord;\n}\n\ntype DecorateProcedure<TProcedure extends AnyProcedure> = (\n  input: inferProcedureInput<TProcedure>,\n) => Promise<\n  TProcedure['_def']['type'] extends 'subscription'\n    ? TProcedure extends LegacyObservableSubscriptionProcedure<any>\n      ? Observable<inferProcedureOutput<TProcedure>, TRPCError>\n      : inferProcedureOutput<TProcedure>\n    : inferProcedureOutput<TProcedure>\n>;\n\n/**\n * @internal\n */\nexport type DecorateRouterRecord<TRecord extends RouterRecord> = {\n  [TKey in keyof TRecord]: TRecord[TKey] extends infer $Value\n    ? $Value extends AnyProcedure\n      ? DecorateProcedure<$Value>\n      : $Value extends RouterRecord\n        ? DecorateRouterRecord<$Value>\n        : never\n    : never;\n};\n\n/**\n * @internal\n */\n\nexport type RouterCallerErrorHandler<TContext> = (\n  opts: ErrorHandlerOptions<TContext>,\n) => void;\n\n/**\n * @internal\n */\nexport type RouterCaller<\n  TRoot extends AnyRootTypes,\n  TRecord extends RouterRecord,\n> = (\n  /**\n   * @note\n   * If passing a function, we recommend it's a cached function\n   * e.g. wrapped in `React.cache` to avoid unnecessary computations\n   */\n  ctx: TRoot['ctx'] | (() => MaybePromise<TRoot['ctx']>),\n  options?: {\n    onError?: RouterCallerErrorHandler<TRoot['ctx']>;\n    signal?: AbortSignal;\n  },\n) => DecorateRouterRecord<TRecord>;\n\nconst lazySymbol = Symbol('lazy');\nexport type Lazy<TAny> = (() => Promise<TAny>) & { [lazySymbol]: true };\n\ntype LazyLoader<TAny> = {\n  load: () => Promise<void>;\n  ref: Lazy<TAny>;\n};\n\nfunction once<T>(fn: () => T): () => T {\n  const uncalled = Symbol();\n  let result: T | typeof uncalled = uncalled;\n  return (): T => {\n    if (result === uncalled) {\n      result = fn();\n    }\n    return result;\n  };\n}\n\n/**\n * Lazy load a router\n * @see https://trpc.io/docs/server/merging-routers#lazy-load\n */\nexport function lazy<TRouter extends AnyRouter>(\n  importRouter: () => Promise<\n    | TRouter\n    | {\n        [key: string]: TRouter;\n      }\n  >,\n): Lazy<NoInfer<TRouter>> {\n  async function resolve(): Promise<TRouter> {\n    const mod = await importRouter();\n\n    // if the module is a router, return it\n    if (isRouter(mod)) {\n      return mod;\n    }\n\n    const routers = Object.values(mod);\n\n    if (routers.length !== 1 || !isRouter(routers[0])) {\n      throw new Error(\n        \"Invalid router module - either define exactly 1 export or return the router directly.\\nExample: `lazy(() => import('./slow.js').then((m) => m.slowRouter))`\",\n      );\n    }\n\n    return routers[0];\n  }\n  resolve[lazySymbol] = true as const;\n\n  return resolve;\n}\n\nfunction isLazy<TAny>(input: unknown): input is Lazy<TAny> {\n  return typeof input === 'function' && lazySymbol in input;\n}\n\n/**\n * @internal\n */\nexport interface RouterDef<\n  TRoot extends AnyRootTypes,\n  TRecord extends RouterRecord,\n> {\n  _config: RootConfig<TRoot>;\n  router: true;\n  procedure?: never;\n  procedures: TRecord;\n  record: TRecord;\n  lazy: Record<string, LazyLoader<AnyRouter>>;\n}\n\nexport interface Router<\n  TRoot extends AnyRootTypes,\n  TRecord extends RouterRecord,\n> {\n  _def: RouterDef<TRoot, TRecord>;\n  /**\n   * @see https://trpc.io/docs/v11/server/server-side-calls\n   */\n  createCaller: RouterCaller<TRoot, TRecord>;\n}\n\nexport type BuiltRouter<\n  TRoot extends AnyRootTypes,\n  TRecord extends RouterRecord,\n> = Router<TRoot, TRecord> & TRecord;\n\nexport interface RouterBuilder<TRoot extends AnyRootTypes> {\n  <TIn extends CreateRouterOptions>(\n    _: TIn,\n  ): BuiltRouter<TRoot, DecorateCreateRouterOptions<TIn>>;\n}\n\nexport type AnyRouter = Router<any, any>;\n\nexport type inferRouterRootTypes<TRouter extends AnyRouter> =\n  TRouter['_def']['_config']['$types'];\n\nexport type inferRouterContext<TRouter extends AnyRouter> =\n  inferRouterRootTypes<TRouter>['ctx'];\nexport type inferRouterError<TRouter extends AnyRouter> =\n  inferRouterRootTypes<TRouter>['errorShape'];\nexport type inferRouterMeta<TRouter extends AnyRouter> =\n  inferRouterRootTypes<TRouter>['meta'];\n\nfunction isRouter(value: unknown): value is AnyRouter {\n  return (\n    isObject(value) && isObject(value['_def']) && 'router' in value['_def']\n  );\n}\n\nconst emptyRouter = {\n  _ctx: null as any,\n  _errorShape: null as any,\n  _meta: null as any,\n  queries: {},\n  mutations: {},\n  subscriptions: {},\n  errorFormatter: defaultFormatter,\n  transformer: defaultTransformer,\n};\n\n/**\n * Reserved words that can't be used as router or procedure names\n */\nconst reservedWords = [\n  /**\n   * Then is a reserved word because otherwise we can't return a promise that returns a Proxy\n   * since JS will think that `.then` is something that exists\n   */\n  'then',\n  /**\n   * `fn.call()` and `fn.apply()` are reserved words because otherwise we can't call a function using `.call` or `.apply`\n   */\n  'call',\n  'apply',\n];\n\n/** @internal */\nexport type CreateRouterOptions = {\n  [key: string]:\n    | AnyProcedure\n    | AnyRouter\n    | CreateRouterOptions\n    | Lazy<AnyRouter>;\n};\n\n/** @internal */\nexport type DecorateCreateRouterOptions<\n  TRouterOptions extends CreateRouterOptions,\n> = {\n  [K in keyof TRouterOptions]: TRouterOptions[K] extends infer $Value\n    ? $Value extends AnyProcedure\n      ? $Value\n      : $Value extends Router<any, infer TRecord>\n        ? TRecord\n        : $Value extends Lazy<Router<any, infer TRecord>>\n          ? TRecord\n          : $Value extends CreateRouterOptions\n            ? DecorateCreateRouterOptions<$Value>\n            : never\n    : never;\n};\n\n/**\n * @internal\n */\nexport function createRouterFactory<TRoot extends AnyRootTypes>(\n  config: RootConfig<TRoot>,\n) {\n  function createRouterInner<TInput extends CreateRouterOptions>(\n    input: TInput,\n  ): BuiltRouter<TRoot, DecorateCreateRouterOptions<TInput>> {\n    const reservedWordsUsed = new Set(\n      Object.keys(input).filter((v) => reservedWords.includes(v)),\n    );\n    if (reservedWordsUsed.size > 0) {\n      throw new Error(\n        'Reserved words used in `router({})` call: ' +\n          Array.from(reservedWordsUsed).join(', '),\n      );\n    }\n\n    const procedures: Record<string, AnyProcedure> = omitPrototype({});\n    const lazy: Record<string, LazyLoader<AnyRouter>> = omitPrototype({});\n\n    function createLazyLoader(opts: {\n      ref: Lazy<AnyRouter>;\n      path: readonly string[];\n      key: string;\n      aggregate: RouterRecord;\n    }): LazyLoader<AnyRouter> {\n      return {\n        ref: opts.ref,\n        load: once(async () => {\n          const router = await opts.ref();\n          const lazyPath = [...opts.path, opts.key];\n          const lazyKey = lazyPath.join('.');\n\n          opts.aggregate[opts.key] = step(router._def.record, lazyPath);\n\n          delete lazy[lazyKey];\n\n          // add lazy loaders for nested routers\n          for (const [nestedKey, nestedItem] of Object.entries(\n            router._def.lazy,\n          )) {\n            const nestedRouterKey = [...lazyPath, nestedKey].join('.');\n\n            // console.log('adding lazy', nestedRouterKey);\n            lazy[nestedRouterKey] = createLazyLoader({\n              ref: nestedItem.ref,\n              path: lazyPath,\n              key: nestedKey,\n              aggregate: opts.aggregate[opts.key] as RouterRecord,\n            });\n          }\n        }),\n      };\n    }\n\n    function step(from: CreateRouterOptions, path: readonly string[] = []) {\n      const aggregate: RouterRecord = omitPrototype({});\n      for (const [key, item] of Object.entries(from ?? {})) {\n        if (isLazy(item)) {\n          lazy[[...path, key].join('.')] = createLazyLoader({\n            path,\n            ref: item,\n            key,\n            aggregate,\n          });\n          continue;\n        }\n        if (isRouter(item)) {\n          aggregate[key] = step(item._def.record, [...path, key]);\n          continue;\n        }\n        if (!isProcedure(item)) {\n          // RouterRecord\n          aggregate[key] = step(item, [...path, key]);\n          continue;\n        }\n\n        const newPath = [...path, key].join('.');\n\n        if (procedures[newPath]) {\n          throw new Error(`Duplicate key: ${newPath}`);\n        }\n\n        procedures[newPath] = item;\n        aggregate[key] = item;\n      }\n\n      return aggregate;\n    }\n    const record = step(input);\n\n    const _def: AnyRouter['_def'] = {\n      _config: config,\n      router: true,\n      procedures,\n      lazy,\n      ...emptyRouter,\n      record,\n    };\n\n    const router: BuiltRouter<TRoot, {}> = {\n      ...(record as {}),\n      _def,\n      createCaller: createCallerFactory<TRoot>()({\n        _def,\n      }),\n    };\n    return router as BuiltRouter<TRoot, DecorateCreateRouterOptions<TInput>>;\n  }\n\n  return createRouterInner;\n}\n\nfunction isProcedure(\n  procedureOrRouter: ValueOf<CreateRouterOptions>,\n): procedureOrRouter is AnyProcedure {\n  return typeof procedureOrRouter === 'function';\n}\n\n/**\n * @internal\n */\nexport async function getProcedureAtPath(\n  router: Pick<Router<any, any>, '_def'>,\n  path: string,\n): Promise<AnyProcedure | null> {\n  const { _def } = router;\n  let procedure = _def.procedures[path];\n\n  while (!procedure) {\n    const key = Object.keys(_def.lazy).find((key) => path.startsWith(key));\n    // console.log(`found lazy: ${key ?? 'NOPE'} (fullPath: ${fullPath})`);\n\n    if (!key) {\n      return null;\n    }\n    // console.log('loading', key, '.......');\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const lazyRouter = _def.lazy[key]!;\n    await lazyRouter.load();\n\n    procedure = _def.procedures[path];\n  }\n\n  return procedure;\n}\n\n/**\n * @internal\n */\nexport async function callProcedure(\n  opts: ProcedureCallOptions<unknown> & {\n    router: AnyRouter;\n    allowMethodOverride?: boolean;\n  },\n) {\n  const { type, path } = opts;\n  const proc = await getProcedureAtPath(opts.router, path);\n  if (\n    !proc ||\n    !isProcedure(proc) ||\n    (proc._def.type !== type && !opts.allowMethodOverride)\n  ) {\n    throw new TRPCError({\n      code: 'NOT_FOUND',\n      message: `No \"${type}\"-procedure on path \"${path}\"`,\n    });\n  }\n\n  /* istanbul ignore if -- @preserve */\n  if (\n    proc._def.type !== type &&\n    opts.allowMethodOverride &&\n    proc._def.type === 'subscription'\n  ) {\n    throw new TRPCError({\n      code: 'METHOD_NOT_SUPPORTED',\n      message: `Method override is not supported for subscriptions`,\n    });\n  }\n\n  return proc(opts);\n}\n\nexport interface RouterCallerFactory<TRoot extends AnyRootTypes> {\n  <TRecord extends RouterRecord>(\n    router: Pick<Router<TRoot, TRecord>, '_def'>,\n  ): RouterCaller<TRoot, TRecord>;\n}\n\nexport function createCallerFactory<\n  TRoot extends AnyRootTypes,\n>(): RouterCallerFactory<TRoot> {\n  return function createCallerInner<TRecord extends RouterRecord>(\n    router: Pick<Router<TRoot, TRecord>, '_def'>,\n  ): RouterCaller<TRoot, TRecord> {\n    const { _def } = router;\n    type Context = TRoot['ctx'];\n\n    return function createCaller(ctxOrCallback, opts) {\n      return createRecursiveProxy<ReturnType<RouterCaller<any, any>>>(\n        async ({ path, args }) => {\n          const fullPath = path.join('.');\n\n          if (path.length === 1 && path[0] === '_def') {\n            return _def;\n          }\n\n          const procedure = await getProcedureAtPath(router, fullPath);\n\n          let ctx: Context | undefined = undefined;\n          try {\n            if (!procedure) {\n              throw new TRPCError({\n                code: 'NOT_FOUND',\n                message: `No procedure found on path \"${path}\"`,\n              });\n            }\n            ctx = isFunction(ctxOrCallback)\n              ? await Promise.resolve(ctxOrCallback())\n              : ctxOrCallback;\n\n            return await procedure({\n              path: fullPath,\n              getRawInput: async () => args[0],\n              ctx,\n              type: procedure._def.type,\n              signal: opts?.signal,\n            });\n          } catch (cause) {\n            opts?.onError?.({\n              ctx,\n              error: getTRPCErrorFromUnknown(cause),\n              input: args[0],\n              path: fullPath,\n              type: procedure?._def.type ?? 'unknown',\n            });\n            throw cause;\n          }\n        },\n      );\n    };\n  };\n}\n\n/** @internal */\nexport type MergeRouters<\n  TRouters extends AnyRouter[],\n  TRoot extends AnyRootTypes = TRouters[0]['_def']['_config']['$types'],\n  TRecord extends RouterRecord = {},\n> = TRouters extends [\n  infer Head extends AnyRouter,\n  ...infer Tail extends AnyRouter[],\n]\n  ? MergeRouters<Tail, TRoot, Head['_def']['record'] & TRecord>\n  : BuiltRouter<TRoot, TRecord>;\n\nexport function mergeRouters<TRouters extends AnyRouter[]>(\n  ...routerList: [...TRouters]\n): MergeRouters<TRouters> {\n  const record = mergeWithoutOverrides(\n    {},\n    ...routerList.map((r) => r._def.record),\n  );\n  const errorFormatter = routerList.reduce(\n    (currentErrorFormatter, nextRouter) => {\n      if (\n        nextRouter._def._config.errorFormatter &&\n        nextRouter._def._config.errorFormatter !== defaultFormatter\n      ) {\n        if (\n          currentErrorFormatter !== defaultFormatter &&\n          currentErrorFormatter !== nextRouter._def._config.errorFormatter\n        ) {\n          throw new Error('You seem to have several error formatters');\n        }\n        return nextRouter._def._config.errorFormatter;\n      }\n      return currentErrorFormatter;\n    },\n    defaultFormatter,\n  );\n\n  const transformer = routerList.reduce((prev, current) => {\n    if (\n      current._def._config.transformer &&\n      current._def._config.transformer !== defaultTransformer\n    ) {\n      if (\n        prev !== defaultTransformer &&\n        prev !== current._def._config.transformer\n      ) {\n        throw new Error('You seem to have several transformers');\n      }\n      return current._def._config.transformer;\n    }\n    return prev;\n  }, defaultTransformer);\n\n  const router = createRouterFactory({\n    errorFormatter,\n    transformer,\n    isDev: routerList.every((r) => r._def._config.isDev),\n    allowOutsideOfServer: routerList.every(\n      (r) => r._def._config.allowOutsideOfServer,\n    ),\n    isServer: routerList.every((r) => r._def._config.isServer),\n    $types: routerList[0]?._def._config.$types,\n  })(record);\n\n  return router as MergeRouters<TRouters>;\n}\n", "const trackedSymbol = Symbol();\n\ntype TrackedId = string & {\n  __brand: 'TrackedId';\n};\nexport type TrackedEnvelope<TData> = [TrackedId, TData, typeof trackedSymbol];\n\ntype TrackedData<TData> = {\n  /**\n   * The id of the message to keep track of in case the connection gets lost\n   */\n  id: string;\n  /**\n   * The data field of the message - this can be anything\n   */\n  data: TData;\n};\n/**\n * Produce a typed server-sent event message\n * @deprecated use `tracked(id, data)` instead\n */\nexport function sse<TData>(event: { id: string; data: TData }) {\n  return tracked(event.id, event.data);\n}\n\nexport function isTrackedEnvelope<TData>(\n  value: unknown,\n): value is TrackedEnvelope<TData> {\n  return Array.isArray(value) && value[2] === trackedSymbol;\n}\n\n/**\n * Automatically track an event so that it can be resumed from a given id if the connection is lost\n */\nexport function tracked<TData>(\n  id: string,\n  data: TData,\n): TrackedEnvelope<TData> {\n  if (id === '') {\n    // This limitation could be removed by using different SSE event names / channels for tracked event and non-tracked event\n    throw new Error(\n      '`id` must not be an empty string as empty string is the same as not setting the id at all',\n    );\n  }\n  return [id as TrackedId, data, trackedSymbol];\n}\n\nexport type inferTrackedOutput<TData> =\n  TData extends TrackedEnvelope<infer $Data> ? TrackedData<$Data> : TData;\n"], "names": ["defaultFormatter: ErrorFormatter<any, any>", "cause: unknown", "opts: {\n    message?: string;\n    code: TRPC_ERROR_CODE_KEY;\n    cause?: unknown;\n  }", "transformer: DataTransformerOptions", "defaultTransformer: CombinedDataTransformer", "config: RootConfig<AnyRootTypes>", "item: TResponseItem", "itemOrItems: TResponse", "response:\n    | TRPCResponse<TOutput, inferRouterError<TRouter>>\n    | TRPCResponseMessage<TOutput, inferRouterError<TRouter>>", "transformer: DataTransformer", "result: ReturnType<typeof transformResultInner>", "fn: () => T", "result: T | typeof uncalled", "importRouter: () => Promise<\n    | TRouter\n    | {\n        [key: string]: TRouter;\n      }\n  >", "input: unknown", "value: unknown", "config: RootConfig<TRoot>", "input: TInput", "procedures: Record<string, AnyProcedure>", "lazy: Record<string, <PERSON><PERSON><PERSON><PERSON>der<AnyRouter>>", "opts: {\n      ref: <PERSON><PERSON><AnyRouter>;\n      path: readonly string[];\n      key: string;\n      aggregate: RouterRecord;\n    }", "router", "lazy", "from: CreateRouterOptions", "path: readonly string[]", "aggregate: RouterRecord", "_def: <PERSON><PERSON><PERSON><PERSON>['_def']", "router: BuiltRouter<TRoot, {}>", "procedureOrRouter: ValueOf<CreateRouterOptions>", "router: Pick<Router<any, any>, '_def'>", "path: string", "key", "opts: ProcedureCallOptions<unknown> & {\n    router: AnyRouter;\n    allowMethodOverride?: boolean;\n  }", "router: Pick<Router<TRoot, TRecord>, '_def'>", "ctx: Context | undefined", "event: { id: string; data: TData }", "value: unknown", "id: string", "data: TData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AA4CA,MAAaA,mBAA6C,CAAC,EAAE,KAAA,EAAO,KAAK;IACvE,OAAO;AACR;;;;AC3CD,IAAM,oBAAN,cAAgC,MAAM;AAErC;AACD,SAAgB,oBAAoBC,KAAAA,EAAmC;IACrE,IAAI,iBAAiB,MACnB,CAAA,OAAO;IAGT,MAAM,OAAA,OAAc;IACpB,IAAI,SAAS,eAAe,SAAS,cAAc,UAAU,KAC3D,CAAA,OAAA,KAAA;IAIF,IAAI,SAAS,SAEX,CAAA,OAAO,IAAI,MAAM,OAAO,MAAM;IAIhC,+PAAI,WAAA,EAAS,MAAM,CACjB,CAAA,OAAO,OAAO,MAAA,CAAO,IAAI,qBAAqB,MAAM;IAGtD,OAAA,KAAA;AACD;AAED,SAAgB,wBAAwBA,KAAAA,EAA2B;IACjE,IAAI,iBAAiB,UACnB,CAAA,OAAO;IAET,IAAI,iBAAiB,SAAS,MAAM,IAAA,KAAS,YAE3C,CAAA,OAAO;IAGT,MAAM,YAAY,IAAI,UAAU;QAC9B,MAAM;QACN;IACD;IAGD,IAAI,iBAAiB,SAAS,MAAM,KAAA,CAClC,CAAA,UAAU,KAAA,GAAQ,MAAM,KAAA;IAG1B,OAAO;AACR;AAED,IAAa,YAAb,cAA+B,MAAM;IAMnC,YAAYC,IAAAA,CAIT;;QACD,MAAM,QAAQ,oBAAoB,KAAK,KAAA,CAAM;QAC7C,MAAM,UAAA,CAAA,OAAA,CAAA,gBAAU,KAAK,OAAA,MAAA,QAAA,kBAAA,KAAA,IAAA,gBAAA,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAW,MAAO,OAAA,MAAA,QAAA,SAAA,KAAA,IAAA,OAAW,KAAK,IAAA;QAIvD,KAAA,CAAM,SAAS;YAAE;QAAO,EAAC;2CAO3B,IAAA,EApByB,SAAA,KAAA;2CAoBxB,IAAA,EAnBe,QAAA,KAAA;QAcd,IAAA,CAAK,IAAA,GAAO,KAAK,IAAA;QACjB,IAAA,CAAK,IAAA,GAAO;QACZ,CAAA,cAAA,IAAA,CAAK,KAAA,MAAA,QAAA,gBAAA,KAAA,KAAA,CAGL,IAAA,CAHK,KAAA,GAAU,KAAA;IAChB;AACF;;;;;;GCLD,SAAgB,mBACdC,WAAAA,EACyB;IACzB,IAAI,WAAW,YACb,CAAA,OAAO;IAET,OAAO;QAAE,OAAO;QAAa,QAAQ;IAAa;AACnD;;;GAKD,MAAaC,qBAA8C;IACzD,OAAO;QAAE,WAAW,CAAC,MAAQ;QAAK,aAAa,CAAC,MAAQ;IAAK;IAC7D,QAAQ;QAAE,WAAW,CAAC,MAAQ;QAAK,aAAa,CAAC,MAAQ;IAAK;AAC/D;AAED,SAAS,0BAEPC,MAAAA,EAAkCC,IAAAA,EAAoC;IACtE,IAAI,WAAW,KACb,CAAA,OAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACK,OAAA,CAAA,GAAA;QACH,OAAO,OAAO,WAAA,CAAY,MAAA,CAAO,SAAA,CAAU,KAAK,KAAA,CAAM;IAAA;IAI1D,IAAI,UAAU,KAAK,MAAA,CACjB,CAAA,OAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACK,OAAA,CAAA,GAAA;QACH,QAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACK,KAAK,MAAA,GAAA,CAAA,GAAA;YACR,MAAM,OAAO,WAAA,CAAY,MAAA,CAAO,SAAA,CAAU,KAAK,MAAA,CAAO,IAAA,CAAK;QAAA;IAAA;IAKjE,OAAO;AACR;;;IAKD,SAAgB,sBAMdD,MAAAA,EAAkCE,WAAAA,EAAwB;IAC1D,OAAO,MAAM,OAAA,CAAQ,YAAY,GAC7B,YAAY,GAAA,CAAI,CAAC,OAAS,0BAA0B,QAAQ,KAAK,CAAC,GAClE,0BAA0B,QAAQ,YAAY;AACnD;iBAMD,SAAS,qBACPC,QAAAA,EAGAC,WAAAA,EACA;IACA,IAAI,WAAW,UAAU;QACvB,MAAM,QAAQ,YAAY,WAAA,CACxB,SAAS,KAAA,CACV;QACD,OAAO;YACL,IAAI;YACJ,OAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACK,WAAA,CAAA,GAAA;gBACH;YAAA;QAEH;IACF;IAED,MAAM,SAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACD,SAAS,MAAA,GAAA,CAAA,CACN,SAAS,MAAA,CAAO,IAAA,IAAQ,SAAS,MAAA,CAAO,IAAA,KAAS,MAAA,KAAW;QAChE,MAAM;QACN,MAAM,YAAY,WAAA,CAAY,SAAS,MAAA,CAAO,IAAA,CAAK;IACpD;IAEH,OAAO;QAAE,IAAI;QAAM;IAAQ;AAC5B;AAED,IAAM,uBAAN,cAAmC,MAAM;IACvC,aAAc;QACZ,KAAA,CAAM,2CAA2C;IAClD;AACF;;;;GAMD,SAAgB,gBACdD,QAAAA,EAGAC,WAAAA,EACyC;IACzC,IAAIC;IACJ,IAAI;QAEF,SAAS,qBAAqB,UAAU,YAAY;IACrD,EAAA,OAAA,SAAO;QACN,MAAM,IAAI;IACX;IAGD,IAAA,CACG,OAAO,EAAA,IAAA,CAAA,CACN,sQAAA,EAAS,OAAO,KAAA,CAAM,KAAA,CAAM,IAAA,OACrB,OAAO,KAAA,CAAM,KAAA,CAAM,OAAA,KAAY,QAAA,EAExC,CAAA,MAAM,IAAI;IAEZ,IAAI,OAAO,EAAA,IAAA,4PAAO,WAAA,EAAS,OAAO,MAAA,CAAO,CACvC,CAAA,MAAM,IAAI;IAEZ,OAAO;AACR;;;;ACrHD,MAAM,aAAa,OAAO,OAAO;AAQjC,SAAS,KAAQC,EAAAA,EAAsB;IACrC,MAAM,WAAW,QAAQ;IACzB,IAAIC,SAA8B;IAClC,OAAO,MAAS;QACd,IAAI,WAAW,SACb,CAAA,SAAS,IAAI;QAEf,OAAO;IACR;AACF;;;;GAMD,SAAgB,KACdC,YAAAA,EAMwB;IACxB,eAAe,UAA4B;QACzC,MAAM,MAAM,MAAM,cAAc;QAGhC,IAAI,SAAS,IAAI,CACf,CAAA,OAAO;QAGT,MAAM,UAAU,OAAO,MAAA,CAAO,IAAI;QAElC,IAAI,QAAQ,MAAA,KAAW,KAAA,CAAM,SAAS,OAAA,CAAQ,EAAA,CAAG,CAC/C,CAAA,MAAM,IAAI,MACR;QAIJ,OAAO,OAAA,CAAQ,EAAA;IAChB;IACD,OAAA,CAAQ,WAAA,GAAc;IAEtB,OAAO;AACR;AAED,SAAS,OAAaC,KAAAA,EAAqC;IACzD,OAAA,OAAc,UAAU,cAAc,cAAc;AACrD;AAmDD,SAAS,SAASC,KAAAA,EAAoC;IACpD,iQACE,YAAA,EAAS,MAAM,+PAAI,WAAA,EAAS,KAAA,CAAM,OAAA,CAAQ,IAAI,YAAY,KAAA,CAAM,OAAA;AAEnE;AAED,MAAM,cAAc;IAClB,MAAM;IACN,aAAa;IACb,OAAO;IACP,SAAS,CAAE;IACX,WAAW,CAAE;IACb,eAAe,CAAE;IACjB,gBAAgB;IAChB,aAAa;AACd;;;GAKD,MAAM,gBAAgB;IAKpB;IAIA;IACA;CACD;;;GA+BD,SAAgB,oBACdC,MAAAA,EACA;IACA,SAAS,kBACPC,KAAAA,EACyD;QACzD,MAAM,oBAAoB,IAAI,IAC5B,OAAO,IAAA,CAAK,MAAM,CAAC,MAAA,CAAO,CAAC,IAAM,cAAc,QAAA,CAAS,EAAE,CAAC;QAE7D,IAAI,kBAAkB,IAAA,GAAO,EAC3B,CAAA,MAAM,IAAI,MACR,+CACE,MAAM,IAAA,CAAK,kBAAkB,CAAC,IAAA,CAAK,KAAK;QAI9C,MAAMC,wQAA2C,gBAAA,EAAc,CAAE,EAAC;QAClE,MAAMC,aAA8C,uQAAA,EAAc,CAAE,EAAC;QAErE,SAAS,iBAAiBC,IAAAA,EAKA;YACxB,OAAO;gBACL,KAAK,KAAK,GAAA;gBACV,MAAM,KAAK,YAAY;oBACrB,MAAMC,WAAS,MAAM,KAAK,GAAA,EAAK;oBAC/B,MAAM,WAAW,CAAC;2BAAG,KAAK,IAAA;wBAAM,KAAK,GAAI;qBAAA;oBACzC,MAAM,UAAU,SAAS,IAAA,CAAK,IAAI;oBAElC,KAAK,SAAA,CAAU,KAAK,GAAA,CAAA,GAAO,KAAKA,SAAO,IAAA,CAAK,MAAA,EAAQ,SAAS;oBAE7D,OAAOC,MAAAA,CAAK,QAAA;oBAGZ,KAAK,MAAM,CAAC,WAAW,WAAW,IAAI,OAAO,OAAA,CAC3CD,SAAO,IAAA,CAAK,IAAA,CACb,CAAE;wBACD,MAAM,kBAAkB,CAAC;+BAAG;4BAAU,SAAU;yBAAA,CAAC,IAAA,CAAK,IAAI;wBAG1D,MAAA,CAAK,gBAAA,GAAmB,iBAAiB;4BACvC,KAAK,WAAW,GAAA;4BAChB,MAAM;4BACN,KAAK;4BACL,WAAW,KAAK,SAAA,CAAU,KAAK,GAAA,CAAA;wBAChC,EAAC;oBACH;gBACF,EAAC;YACH;QACF;QAED,SAAS,KAAKE,IAAAA,EAA2BC,OAA0B,CAAE,CAAA,EAAE;YACrE,MAAMC,aAA0B,0QAAA,EAAc,CAAE,EAAC;YACjD,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,OAAO,OAAA,CAAQ,SAAA,QAAA,SAAA,KAAA,IAAA,OAAQ,CAAE,EAAC,CAAE;gBACpD,IAAI,OAAO,KAAK,EAAE;oBAChB,MAAA,CAAK,CAAC;2BAAG;wBAAM,GAAI;qBAAA,CAAC,IAAA,CAAK,IAAI,CAAA,GAAI,iBAAiB;wBAChD;wBACA,KAAK;wBACL;wBACA;oBACD,EAAC;oBACF;gBACD;gBACD,IAAI,SAAS,KAAK,EAAE;oBAClB,SAAA,CAAU,IAAA,GAAO,KAAK,KAAK,IAAA,CAAK,MAAA,EAAQ,CAAC;2BAAG;wBAAM,GAAI;qBAAA,CAAC;oBACvD;gBACD;gBACD,IAAA,CAAK,YAAY,KAAK,EAAE;oBAEtB,SAAA,CAAU,IAAA,GAAO,KAAK,MAAM,CAAC;2BAAG;wBAAM,GAAI;qBAAA,CAAC;oBAC3C;gBACD;gBAED,MAAM,UAAU,CAAC;uBAAG;oBAAM,GAAI;iBAAA,CAAC,IAAA,CAAK,IAAI;gBAExC,IAAI,UAAA,CAAW,QAAA,CACb,CAAA,MAAM,IAAI,MAAA,CAAO,eAAA,EAAiB,QAAQ,CAAA;gBAG5C,UAAA,CAAW,QAAA,GAAW;gBACtB,SAAA,CAAU,IAAA,GAAO;YAClB;YAED,OAAO;QACR;QACD,MAAM,SAAS,KAAK,MAAM;QAE1B,MAAMC,OAAAA,CAAAA,GAAAA,qBAAAA,OAAAA,EAAAA,CAAAA,GAAAA,qBAAAA,OAAAA,EAAAA;YACJ,SAAS;YACT,QAAQ;YACR;YACA,MAAA;WACG,cAAA,CAAA,GAAA;YACH;QAAA;QAGF,MAAMC,SAAAA,CAAAA,GAAAA,qBAAAA,OAAAA,EAAAA,CAAAA,GAAAA,qBAAAA,OAAAA,EAAAA,CAAAA,GACA,SAAA,CAAA,GAAA;YACJ;YACA,cAAc,qBAA4B,CAAC;gBACzC;YACD,EAAC;;QAEJ,OAAO;IACR;IAED,OAAO;AACR;AAED,SAAS,YACPC,iBAAAA,EACmC;IACnC,OAAA,OAAc,sBAAsB;AACrC;;;GAKD,eAAsB,mBACpBC,MAAAA,EACAC,IAAAA,EAC8B;IAC9B,MAAM,EAAE,IAAA,EAAM,GAAG;IACjB,IAAI,YAAY,KAAK,UAAA,CAAW,KAAA;IAEhC,MAAA,CAAQ,UAAW;QACjB,MAAM,MAAM,OAAO,IAAA,CAAK,KAAK,IAAA,CAAK,CAAC,IAAA,CAAK,CAACC,QAAQ,KAAK,UAAA,CAAWA,MAAI,CAAC;QAGtE,IAAA,CAAK,IACH,CAAA,OAAO;QAIT,MAAM,aAAa,KAAK,IAAA,CAAK,IAAA;QAC7B,MAAM,WAAW,IAAA,EAAM;QAEvB,YAAY,KAAK,UAAA,CAAW,KAAA;IAC7B;IAED,OAAO;AACR;;;GAKD,eAAsB,cACpBC,IAAAA,EAIA;IACA,MAAM,EAAE,IAAA,EAAM,IAAA,EAAM,GAAG;IACvB,MAAM,OAAO,MAAM,mBAAmB,KAAK,MAAA,EAAQ,KAAK;IACxD,IAAA,CACG,QAAA,CACA,YAAY,KAAK,IACjB,KAAK,IAAA,CAAK,IAAA,KAAS,QAAA,CAAS,KAAK,mBAAA,CAElC,CAAA,MAAM,IAAI,UAAU;QAClB,MAAM;QACN,SAAA,CAAU,IAAA,EAAM,KAAK,qBAAA,EAAuB,KAAK,CAAA,CAAA;IAClD;0CAIH,IACE,KAAK,IAAA,CAAK,IAAA,KAAS,QACnB,KAAK,mBAAA,IACL,KAAK,IAAA,CAAK,IAAA,KAAS,eAEnB,CAAA,MAAM,IAAI,UAAU;QAClB,MAAM;QACN,SAAA,CAAU,kDAAA,CAAA;IACX;IAGH,OAAO,KAAK,KAAK;AAClB;AAQD,SAAgB,sBAEgB;IAC9B,OAAO,SAAS,kBACdC,MAAAA,EAC8B;QAC9B,MAAM,EAAE,IAAA,EAAM,GAAG;QAGjB,OAAO,SAAS,aAAa,aAAA,EAAe,IAAA,EAAM;YAChD,0QAAO,uBAAA,EACL,OAAO,EAAE,IAAA,EAAM,IAAA,EAAM,KAAK;gBACxB,MAAM,WAAW,KAAK,IAAA,CAAK,IAAI;gBAE/B,IAAI,KAAK,MAAA,KAAW,KAAK,IAAA,CAAK,EAAA,KAAO,OACnC,CAAA,OAAO;gBAGT,MAAM,YAAY,MAAM,mBAAmB,QAAQ,SAAS;gBAE5D,IAAIC,MAAAA,KAAAA;gBACJ,IAAI;oBACF,IAAA,CAAK,UACH,CAAA,MAAM,IAAI,UAAU;wBAClB,MAAM;wBACN,SAAA,CAAU,4BAAA,EAA8B,KAAK,CAAA,CAAA;oBAC9C;oBAEH,OAAM,uQAAA,EAAW,cAAc,GAC3B,MAAM,QAAQ,OAAA,CAAQ,eAAe,CAAC,GACtC;oBAEJ,OAAO,MAAM,UAAU;wBACrB,MAAM;wBACN,aAAa,UAAY,IAAA,CAAK,EAAA;wBAC9B;wBACA,MAAM,UAAU,IAAA,CAAK,IAAA;wBACrB,QAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAQ,KAAM,MAAA;oBACf,EAAC;gBACH,EAAA,OAAQ,OAAO;;oBACd,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,gBAAA,KAAM,OAAA,MAAA,QAAA,kBAAA,KAAA,KAAN,cAAA,IAAA,CAAA,MAAgB;wBACd;wBACA,OAAO,wBAAwB,MAAM;wBACrC,OAAO,IAAA,CAAK,EAAA;wBACZ,MAAM;wBACN,MAAA,CAAA,uBAAA,cAAA,QAAA,cAAA,KAAA,IAAA,KAAA,IAAM,UAAW,IAAA,CAAK,IAAA,MAAA,QAAA,yBAAA,KAAA,IAAA,uBAAQ;oBAC/B,EAAC;oBACF,MAAM;gBACP;YACF,EACF;QACF;IACF;AACF;AAcD,SAAgB,aACd,GAAG,UAAA,EACqB;;IACxB,MAAM,aAAS,+QAAA,EACb,CAAE,GACF,GAAG,WAAW,GAAA,CAAI,CAAC,IAAM,EAAE,IAAA,CAAK,MAAA,CAAO,CACxC;IACD,MAAM,iBAAiB,WAAW,MAAA,CAChC,CAAC,uBAAuB,eAAe;QACrC,IACE,WAAW,IAAA,CAAK,OAAA,CAAQ,cAAA,IACxB,WAAW,IAAA,CAAK,OAAA,CAAQ,cAAA,KAAmB,kBAC3C;YACA,IACE,0BAA0B,oBAC1B,0BAA0B,WAAW,IAAA,CAAK,OAAA,CAAQ,cAAA,CAElD,CAAA,MAAM,IAAI,MAAM;YAElB,OAAO,WAAW,IAAA,CAAK,OAAA,CAAQ,cAAA;QAChC;QACD,OAAO;IACR,GACD,iBACD;IAED,MAAM,cAAc,WAAW,MAAA,CAAO,CAAC,MAAM,YAAY;QACvD,IACE,QAAQ,IAAA,CAAK,OAAA,CAAQ,WAAA,IACrB,QAAQ,IAAA,CAAK,OAAA,CAAQ,WAAA,KAAgB,oBACrC;YACA,IACE,SAAS,sBACT,SAAS,QAAQ,IAAA,CAAK,OAAA,CAAQ,WAAA,CAE9B,CAAA,MAAM,IAAI,MAAM;YAElB,OAAO,QAAQ,IAAA,CAAK,OAAA,CAAQ,WAAA;QAC7B;QACD,OAAO;IACR,GAAE,mBAAmB;IAEtB,MAAM,SAAS,oBAAoB;QACjC;QACA;QACA,OAAO,WAAW,KAAA,CAAM,CAAC,IAAM,EAAE,IAAA,CAAK,OAAA,CAAQ,KAAA,CAAM;QACpD,sBAAsB,WAAW,KAAA,CAC/B,CAAC,IAAM,EAAE,IAAA,CAAK,OAAA,CAAQ,oBAAA,CACvB;QACD,UAAU,WAAW,KAAA,CAAM,CAAC,IAAM,EAAE,IAAA,CAAK,OAAA,CAAQ,QAAA,CAAS;QAC1D,QAAA,CAAA,eAAQ,UAAA,CAAW,EAAA,MAAA,QAAA,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAI,IAAA,CAAK,OAAA,CAAQ,MAAA;IACrC,EAAC,CAAC,OAAO;IAEV,OAAO;AACR;;;AC3iBD,MAAM,gBAAgB,QAAQ;;;;GAqB9B,SAAgB,IAAWC,KAAAA,EAAoC;IAC7D,OAAO,QAAQ,MAAM,EAAA,EAAI,MAAM,IAAA,CAAK;AACrC;AAED,SAAgB,kBACdC,KAAAA,EACiC;IACjC,OAAO,MAAM,OAAA,CAAQ,MAAM,IAAI,KAAA,CAAM,EAAA,KAAO;AAC7C;;;GAKD,SAAgB,QACdC,EAAAA,EACAC,IAAAA,EACwB;IACxB,IAAI,OAAO,GAET,CAAA,MAAM,IAAI,MACR;IAGJ,OAAO;QAAC;QAAiB;QAAM;KAAc;AAC9C", "ignoreList": [0, 1, 2, 3, 4], "debugId": null}}, {"offset": {"line": 1032, "column": 0}, "map": {"version": 3, "file": "observable-CUiPknO-.mjs", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/observable/operators.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/observable/behaviorSubject.ts"], "sourcesContent": ["import { observable } from './observable';\nimport type {\n  MonoTypeOperatorFunction,\n  Observer,\n  OperatorFunction,\n  Unsubscribable,\n} from './types';\n\nexport function map<TValueBefore, TError, TValueAfter>(\n  project: (value: TValueBefore, index: number) => TValueAfter,\n): OperatorFunction<TValueBefore, TError, TValueAfter, TError> {\n  return (source) => {\n    return observable((destination) => {\n      let index = 0;\n      const subscription = source.subscribe({\n        next(value) {\n          destination.next(project(value, index++));\n        },\n        error(error) {\n          destination.error(error);\n        },\n        complete() {\n          destination.complete();\n        },\n      });\n      return subscription;\n    });\n  };\n}\n\ninterface ShareConfig {}\nexport function share<TValue, TError>(\n  _opts?: ShareConfig,\n): MonoTypeOperatorFunction<TValue, TError> {\n  return (source) => {\n    let refCount = 0;\n\n    let subscription: Unsubscribable | null = null;\n    const observers: Partial<Observer<TValue, TError>>[] = [];\n\n    function startIfNeeded() {\n      if (subscription) {\n        return;\n      }\n      subscription = source.subscribe({\n        next(value) {\n          for (const observer of observers) {\n            observer.next?.(value);\n          }\n        },\n        error(error) {\n          for (const observer of observers) {\n            observer.error?.(error);\n          }\n        },\n        complete() {\n          for (const observer of observers) {\n            observer.complete?.();\n          }\n        },\n      });\n    }\n    function resetIfNeeded() {\n      // \"resetOnRefCountZero\"\n      if (refCount === 0 && subscription) {\n        const _sub = subscription;\n        subscription = null;\n        _sub.unsubscribe();\n      }\n    }\n\n    return observable((subscriber) => {\n      refCount++;\n\n      observers.push(subscriber);\n      startIfNeeded();\n      return {\n        unsubscribe() {\n          refCount--;\n          resetIfNeeded();\n\n          const index = observers.findIndex((v) => v === subscriber);\n\n          if (index > -1) {\n            observers.splice(index, 1);\n          }\n        },\n      };\n    });\n  };\n}\n\nexport function tap<TValue, TError>(\n  observer: Partial<Observer<TValue, TError>>,\n): MonoTypeOperatorFunction<TValue, TError> {\n  return (source) => {\n    return observable((destination) => {\n      return source.subscribe({\n        next(value) {\n          observer.next?.(value);\n          destination.next(value);\n        },\n        error(error) {\n          observer.error?.(error);\n          destination.error(error);\n        },\n        complete() {\n          observer.complete?.();\n          destination.complete();\n        },\n      });\n    });\n  };\n}\n\nconst distinctUnsetMarker = Symbol();\nexport function distinctUntilChanged<TValue, TError>(\n  compare: (a: TValue, b: TValue) => boolean = (a, b) => a === b,\n): MonoTypeOperatorFunction<TValue, TError> {\n  return (source) => {\n    return observable((destination) => {\n      let lastValue: TValue | typeof distinctUnsetMarker = distinctUnsetMarker;\n\n      return source.subscribe({\n        next(value) {\n          if (lastValue !== distinctUnsetMarker && compare(lastValue, value)) {\n            return;\n          }\n          lastValue = value;\n          destination.next(value);\n        },\n        error(error) {\n          destination.error(error);\n        },\n        complete() {\n          destination.complete();\n        },\n      });\n    });\n  };\n}\n\nconst isDeepEqual = <T>(a: T, b: T): boolean => {\n  if (a === b) {\n    return true;\n  }\n  const bothAreObjects =\n    a && b && typeof a === 'object' && typeof b === 'object';\n\n  return (\n    !!bothAreObjects &&\n    Object.keys(a).length === Object.keys(b).length &&\n    Object.entries(a).every(([k, v]) => isDeepEqual(v, b[k as keyof T]))\n  );\n};\nexport function distinctUntilDeepChanged<\n  TValue,\n  TError,\n>(): MonoTypeOperatorFunction<TValue, TError> {\n  return distinctUntilChanged(isDeepEqual);\n}\n", "import { observable } from './observable';\nimport type { Observable, Observer } from './types';\n\nexport interface BehaviorSubject<TValue> extends Observable<TValue, never> {\n  observable: Observable<TValue, never>;\n  next: (value: TValue) => void;\n  get: () => TValue;\n}\n\nexport interface ReadonlyBehaviorSubject<TValue>\n  extends Omit<BehaviorSubject<TValue>, 'next'> {}\n\n/**\n * @internal\n * An observable that maintains and provides a \"current value\" to subscribers\n * @see https://www.learnrxjs.io/learn-rxjs/subjects/behaviorsubject\n */\nexport function behaviorSubject<TValue>(\n  initialValue: TValue,\n): BehaviorSubject<TValue> {\n  let value: TValue = initialValue;\n\n  const observerList: Observer<TValue, never>[] = [];\n\n  const addObserver = (observer: Observer<TValue, never>) => {\n    if (value !== undefined) {\n      observer.next(value);\n    }\n    observerList.push(observer);\n  };\n  const removeObserver = (observer: Observer<TValue, never>) => {\n    observerList.splice(observerList.indexOf(observer), 1);\n  };\n\n  const obs = observable<TValue, never>((observer) => {\n    addObserver(observer);\n    return () => {\n      removeObserver(observer);\n    };\n  }) as BehaviorSubject<TValue>;\n\n  obs.next = (nextValue: TValue) => {\n    if (value === nextValue) {\n      return;\n    }\n    value = nextValue;\n    for (const observer of observerList) {\n      observer.next(nextValue);\n    }\n  };\n\n  obs.get = () => value;\n\n  return obs;\n}\n"], "names": ["project: (value: TValueBefore, index: number) => TValueAfter", "_opts?: ShareConfig", "subscription: Unsubscribable | null", "observers: <PERSON><PERSON><Observer<TV<PERSON>ue, TError>>[]", "observer: <PERSON><PERSON><Observer<TValue, TError>>", "compare: (a: TValue, b: TValue) => boolean", "lastValue: TValue | typeof distinctUnsetMarker", "a: T", "b: T", "initialValue: TValue", "value: TValue", "observerList: Observer<TValue, never>[]", "observer: Observer<TValue, never>", "nextValue: TValue"], "mappings": ";;;;;;;;;;;AAQA,SAAgB,IACdA,OAAAA,EAC6D;IAC7D,OAAO,CAAC,WAAW;QACjB,uQAAO,aAAA,EAAW,CAAC,gBAAgB;YACjC,IAAI,QAAQ;YACZ,MAAM,eAAe,OAAO,SAAA,CAAU;gBACpC,MAAK,KAAA,EAAO;oBACV,YAAY,IAAA,CAAK,QAAQ,OAAO,QAAQ,CAAC;gBAC1C;gBACD,OAAM,KAAA,EAAO;oBACX,YAAY,KAAA,CAAM,MAAM;gBACzB;gBACD,WAAW;oBACT,YAAY,QAAA,EAAU;gBACvB;YACF,EAAC;YACF,OAAO;QACR,EAAC;IACH;AACF;AAGD,SAAgB,MACdC,KAAAA,EAC0C;IAC1C,OAAO,CAAC,WAAW;QACjB,IAAI,WAAW;QAEf,IAAIC,eAAsC;QAC1C,MAAMC,YAAiD,CAAE,CAAA;QAEzD,SAAS,gBAAgB;YACvB,IAAI,aACF,CAAA;YAEF,eAAe,OAAO,SAAA,CAAU;gBAC9B,MAAK,KAAA,EAAO;oBACV,KAAK,MAAM,YAAY,UAAW;;wBAChC,CAAA,iBAAA,SAAS,IAAA,MAAA,QAAA,mBAAA,KAAA,KAAT,eAAA,IAAA,CAAA,UAAgB,MAAM;oBACvB;gBACF;gBACD,OAAM,KAAA,EAAO;oBACX,KAAK,MAAM,YAAY,UAAW;;wBAChC,CAAA,kBAAA,SAAS,KAAA,MAAA,QAAA,oBAAA,KAAA,KAAT,gBAAA,IAAA,CAAA,UAAiB,MAAM;oBACxB;gBACF;gBACD,WAAW;oBACT,KAAK,MAAM,YAAY,UAAW;;wBAChC,CAAA,qBAAA,SAAS,QAAA,MAAA,QAAA,uBAAA,KAAA,KAAT,mBAAA,IAAA,CAAA,SAAqB;oBACtB;gBACF;YACF,EAAC;QACH;QACD,SAAS,gBAAgB;YAEvB,IAAI,aAAa,KAAK,cAAc;gBAClC,MAAM,OAAO;gBACb,eAAe;gBACf,KAAK,WAAA,EAAa;YACnB;QACF;QAED,uQAAO,aAAA,EAAW,CAAC,eAAe;YAChC;YAEA,UAAU,IAAA,CAAK,WAAW;YAC1B,eAAe;YACf,OAAO;gBACL,cAAc;oBACZ;oBACA,eAAe;oBAEf,MAAM,QAAQ,UAAU,SAAA,CAAU,CAAC,IAAM,MAAM,WAAW;oBAE1D,IAAI,QAAQ,CAAA,EACV,CAAA,UAAU,MAAA,CAAO,OAAO,EAAE;gBAE7B;YACF;QACF,EAAC;IACH;AACF;AAED,SAAgB,IACdC,QAAAA,EAC0C;IAC1C,OAAO,CAAC,WAAW;QACjB,uQAAO,aAAA,EAAW,CAAC,gBAAgB;YACjC,OAAO,OAAO,SAAA,CAAU;gBACtB,MAAK,KAAA,EAAO;;oBACV,CAAA,kBAAA,SAAS,IAAA,MAAA,QAAA,oBAAA,KAAA,KAAT,gBAAA,IAAA,CAAA,UAAgB,MAAM;oBACtB,YAAY,IAAA,CAAK,MAAM;gBACxB;gBACD,OAAM,KAAA,EAAO;;oBACX,CAAA,mBAAA,SAAS,KAAA,MAAA,QAAA,qBAAA,KAAA,KAAT,iBAAA,IAAA,CAAA,UAAiB,MAAM;oBACvB,YAAY,KAAA,CAAM,MAAM;gBACzB;gBACD,WAAW;;oBACT,CAAA,sBAAA,SAAS,QAAA,MAAA,QAAA,wBAAA,KAAA,KAAT,oBAAA,IAAA,CAAA,SAAqB;oBACrB,YAAY,QAAA,EAAU;gBACvB;YACF,EAAC;QACH,EAAC;IACH;AACF;AAED,MAAM,sBAAsB,QAAQ;AACpC,SAAgB,qBACdC,UAA6C,CAAC,GAAG,IAAM,MAAM,CAAA,EACnB;IAC1C,OAAO,CAAC,WAAW;QACjB,uQAAO,aAAA,EAAW,CAAC,gBAAgB;YACjC,IAAIC,YAAiD;YAErD,OAAO,OAAO,SAAA,CAAU;gBACtB,MAAK,KAAA,EAAO;oBACV,IAAI,cAAc,uBAAuB,QAAQ,WAAW,MAAM,CAChE,CAAA;oBAEF,YAAY;oBACZ,YAAY,IAAA,CAAK,MAAM;gBACxB;gBACD,OAAM,KAAA,EAAO;oBACX,YAAY,KAAA,CAAM,MAAM;gBACzB;gBACD,WAAW;oBACT,YAAY,QAAA,EAAU;gBACvB;YACF,EAAC;QACH,EAAC;IACH;AACF;AAED,MAAM,cAAc,CAAIC,GAAMC,MAAkB;IAC9C,IAAI,MAAM,EACR,CAAA,OAAO;IAET,MAAM,iBACJ,KAAK,KAAA,OAAY,MAAM,YAAA,OAAmB,MAAM;IAElD,OAAA,CAAA,CACI,kBACF,OAAO,IAAA,CAAK,EAAE,CAAC,MAAA,KAAW,OAAO,IAAA,CAAK,EAAE,CAAC,MAAA,IACzC,OAAO,OAAA,CAAQ,EAAE,CAAC,KAAA,CAAM,CAAC,CAAC,GAAG,EAAE,GAAK,YAAY,GAAG,CAAA,CAAE,EAAA,CAAc,CAAC;AAEvE;AACD,SAAgB,2BAG8B;IAC5C,OAAO,qBAAqB,YAAY;AACzC;;;;;;;GC/ID,SAAgB,gBACdC,YAAAA,EACyB;IACzB,IAAIC,QAAgB;IAEpB,MAAMC,eAA0C,CAAE,CAAA;IAElD,MAAM,cAAc,CAACC,aAAsC;QACzD,IAAI,UAAA,KAAA,EACF,CAAA,SAAS,IAAA,CAAK,MAAM;QAEtB,aAAa,IAAA,CAAK,SAAS;IAC5B;IACD,MAAM,iBAAiB,CAACA,aAAsC;QAC5D,aAAa,MAAA,CAAO,aAAa,OAAA,CAAQ,SAAS,EAAE,EAAE;IACvD;IAED,MAAM,sQAAM,aAAA,EAA0B,CAAC,aAAa;QAClD,YAAY,SAAS;QACrB,OAAO,MAAM;YACX,eAAe,SAAS;QACzB;IACF,EAAC;IAEF,IAAI,IAAA,GAAO,CAACC,cAAsB;QAChC,IAAI,UAAU,UACZ,CAAA;QAEF,QAAQ;QACR,KAAK,MAAM,YAAY,aACrB,SAAS,IAAA,CAAK,UAAU;IAE3B;IAED,IAAI,GAAA,GAAM,IAAM;IAEhB,OAAO;AACR", "ignoreList": [0, 1], "debugId": null}}, {"offset": {"line": 1202, "column": 0}, "map": {"version": 3, "file": "resolveResponse-CzlbRpCI.mjs", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/http/parseConnectionParams.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/http/contentType.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/http/abortError.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/vendor/unpromise/unpromise.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/stream/utils/disposable.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/stream/utils/timerResource.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/usingCtx.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/OverloadYield.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/awaitAsyncGenerator.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/wrapAsyncGenerator.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/stream/utils/asyncIterable.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/stream/utils/createDeferred.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/stream/utils/mergeAsyncIterables.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/stream/utils/readableStreamFrom.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/stream/utils/withPing.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/asyncIterator.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/stream/jsonl.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/node_modules/.pnpm/%40oxc-project%2Bruntime%400.72.2/node_modules/%40oxc-project/runtime/src/helpers/asyncGeneratorDelegate.js", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/stream/sse.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40trpc%2Bserver%4011.4.2_typescript%405.8.3/node_modules/%40trpc/server/src/unstable-core-do-not-import/http/resolveResponse.ts"], "sourcesContent": ["import { TRPCError } from '../error/TRPCError';\nimport { isObject } from '../utils';\nimport type { TRPCRequestInfo } from './types';\n\nexport function parseConnectionParamsFromUnknown(\n  parsed: unknown,\n): TRPCRequestInfo['connectionParams'] {\n  try {\n    if (parsed === null) {\n      return null;\n    }\n    if (!isObject(parsed)) {\n      throw new Error('Expected object');\n    }\n    const nonStringValues = Object.entries(parsed).filter(\n      ([_key, value]) => typeof value !== 'string',\n    );\n\n    if (nonStringValues.length > 0) {\n      throw new Error(\n        `Expected connectionParams to be string values. Got ${nonStringValues\n          .map(([key, value]) => `${key}: ${typeof value}`)\n          .join(', ')}`,\n      );\n    }\n    return parsed as Record<string, string>;\n  } catch (cause) {\n    throw new TRPCError({\n      code: 'PARSE_ERROR',\n      message: 'Invalid connection params shape',\n      cause,\n    });\n  }\n}\nexport function parseConnectionParamsFromString(\n  str: string,\n): TRPCRequestInfo['connectionParams'] {\n  let parsed: unknown;\n  try {\n    parsed = JSON.parse(str);\n  } catch (cause) {\n    throw new TRPCError({\n      code: 'PARSE_ERROR',\n      message: 'Not JSON-parsable query params',\n      cause,\n    });\n  }\n  return parseConnectionParamsFromUnknown(parsed);\n}\n", "import { TRPCError } from '../error/TRPCError';\nimport type { ProcedureType } from '../procedure';\nimport { getProcedureAtPath, type AnyRouter } from '../router';\nimport { isObject } from '../utils';\nimport { parseConnectionParamsFromString } from './parseConnectionParams';\nimport type { TRPCAcceptHeader, TRPCRequestInfo } from './types';\n\ntype GetRequestInfoOptions = {\n  path: string;\n  req: Request;\n  url: URL | null;\n  searchParams: URLSearchParams;\n  headers: Headers;\n  router: AnyRouter;\n};\n\ntype ContentTypeHandler = {\n  isMatch: (opts: Request) => boolean;\n  parse: (opts: GetRequestInfoOptions) => Promise<TRPCRequestInfo>;\n};\n\n/**\n * Memoize a function that takes no arguments\n * @internal\n */\nfunction memo<TReturn>(fn: () => Promise<TReturn>) {\n  let promise: Promise<TReturn> | null = null;\n  const sym = Symbol.for('@trpc/server/http/memo');\n  let value: TReturn | typeof sym = sym;\n  return {\n    /**\n     * Lazily read the value\n     */\n    read: async (): Promise<TReturn> => {\n      if (value !== sym) {\n        return value;\n      }\n\n      // dedupes promises and catches errors\n      promise ??= fn().catch((cause) => {\n        if (cause instanceof TRPCError) {\n          throw cause;\n        }\n        throw new TRPCError({\n          code: 'BAD_REQUEST',\n          message: cause instanceof Error ? cause.message : 'Invalid input',\n          cause,\n        });\n      });\n\n      value = await promise;\n      promise = null;\n\n      return value;\n    },\n    /**\n     * Get an already stored result\n     */\n    result: (): TReturn | undefined => {\n      return value !== sym ? value : undefined;\n    },\n  };\n}\n\nconst jsonContentTypeHandler: ContentTypeHandler = {\n  isMatch(req) {\n    return !!req.headers.get('content-type')?.startsWith('application/json');\n  },\n  async parse(opts) {\n    const { req } = opts;\n    const isBatchCall = opts.searchParams.get('batch') === '1';\n    const paths = isBatchCall ? opts.path.split(',') : [opts.path];\n\n    type InputRecord = Record<number, unknown>;\n    const getInputs = memo(async (): Promise<InputRecord> => {\n      let inputs: unknown = undefined;\n      if (req.method === 'GET') {\n        const queryInput = opts.searchParams.get('input');\n        if (queryInput) {\n          inputs = JSON.parse(queryInput);\n        }\n      } else {\n        inputs = await req.json();\n      }\n      if (inputs === undefined) {\n        return {};\n      }\n\n      if (!isBatchCall) {\n        return {\n          0: opts.router._def._config.transformer.input.deserialize(inputs),\n        };\n      }\n\n      if (!isObject(inputs)) {\n        throw new TRPCError({\n          code: 'BAD_REQUEST',\n          message: '\"input\" needs to be an object when doing a batch call',\n        });\n      }\n      const acc: InputRecord = {};\n      for (const index of paths.keys()) {\n        const input = inputs[index];\n        if (input !== undefined) {\n          acc[index] =\n            opts.router._def._config.transformer.input.deserialize(input);\n        }\n      }\n\n      return acc;\n    });\n\n    const calls = await Promise.all(\n      paths.map(\n        async (path, index): Promise<TRPCRequestInfo['calls'][number]> => {\n          const procedure = await getProcedureAtPath(opts.router, path);\n          return {\n            path,\n            procedure,\n            getRawInput: async () => {\n              const inputs = await getInputs.read();\n              let input = inputs[index];\n\n              if (procedure?._def.type === 'subscription') {\n                const lastEventId =\n                  opts.headers.get('last-event-id') ??\n                  opts.searchParams.get('lastEventId') ??\n                  opts.searchParams.get('Last-Event-Id');\n\n                if (lastEventId) {\n                  if (isObject(input)) {\n                    input = {\n                      ...input,\n                      lastEventId: lastEventId,\n                    };\n                  } else {\n                    input ??= {\n                      lastEventId: lastEventId,\n                    };\n                  }\n                }\n              }\n              return input;\n            },\n            result: () => {\n              return getInputs.result()?.[index];\n            },\n          };\n        },\n      ),\n    );\n\n    const types = new Set(\n      calls.map((call) => call.procedure?._def.type).filter(Boolean),\n    );\n\n    /* istanbul ignore if -- @preserve */\n    if (types.size > 1) {\n      throw new TRPCError({\n        code: 'BAD_REQUEST',\n        message: `Cannot mix procedure types in call: ${Array.from(types).join(\n          ', ',\n        )}`,\n      });\n    }\n    const type: ProcedureType | 'unknown' =\n      types.values().next().value ?? 'unknown';\n\n    const connectionParamsStr = opts.searchParams.get('connectionParams');\n\n    const info: TRPCRequestInfo = {\n      isBatchCall,\n      accept: req.headers.get('trpc-accept') as TRPCAcceptHeader | null,\n      calls,\n      type,\n      connectionParams:\n        connectionParamsStr === null\n          ? null\n          : parseConnectionParamsFromString(connectionParamsStr),\n      signal: req.signal,\n      url: opts.url,\n    };\n    return info;\n  },\n};\n\nconst formDataContentTypeHandler: ContentTypeHandler = {\n  isMatch(req) {\n    return !!req.headers.get('content-type')?.startsWith('multipart/form-data');\n  },\n  async parse(opts) {\n    const { req } = opts;\n    if (req.method !== 'POST') {\n      throw new TRPCError({\n        code: 'METHOD_NOT_SUPPORTED',\n        message:\n          'Only POST requests are supported for multipart/form-data requests',\n      });\n    }\n    const getInputs = memo(async () => {\n      const fd = await req.formData();\n      return fd;\n    });\n    const procedure = await getProcedureAtPath(opts.router, opts.path);\n    return {\n      accept: null,\n      calls: [\n        {\n          path: opts.path,\n          getRawInput: getInputs.read,\n          result: getInputs.result,\n          procedure,\n        },\n      ],\n      isBatchCall: false,\n      type: 'mutation',\n      connectionParams: null,\n      signal: req.signal,\n      url: opts.url,\n    };\n  },\n};\n\nconst octetStreamContentTypeHandler: ContentTypeHandler = {\n  isMatch(req) {\n    return !!req.headers\n      .get('content-type')\n      ?.startsWith('application/octet-stream');\n  },\n  async parse(opts) {\n    const { req } = opts;\n    if (req.method !== 'POST') {\n      throw new TRPCError({\n        code: 'METHOD_NOT_SUPPORTED',\n        message:\n          'Only POST requests are supported for application/octet-stream requests',\n      });\n    }\n    const getInputs = memo(async () => {\n      return req.body;\n    });\n    return {\n      calls: [\n        {\n          path: opts.path,\n          getRawInput: getInputs.read,\n          result: getInputs.result,\n          procedure: await getProcedureAtPath(opts.router, opts.path),\n        },\n      ],\n      isBatchCall: false,\n      accept: null,\n      type: 'mutation',\n      connectionParams: null,\n      signal: req.signal,\n      url: opts.url,\n    };\n  },\n};\n\nconst handlers = [\n  jsonContentTypeHandler,\n  formDataContentTypeHandler,\n  octetStreamContentTypeHandler,\n];\n\nfunction getContentTypeHandler(req: Request): ContentTypeHandler {\n  const handler = handlers.find((handler) => handler.isMatch(req));\n  if (handler) {\n    return handler;\n  }\n\n  if (!handler && req.method === 'GET') {\n    // fallback to JSON for get requests so GET-requests can be opened in browser easily\n    return jsonContentTypeHandler;\n  }\n\n  throw new TRPCError({\n    code: 'UNSUPPORTED_MEDIA_TYPE',\n    message: req.headers.has('content-type')\n      ? `Unsupported content-type \"${req.headers.get('content-type')}`\n      : 'Missing content-type header',\n  });\n}\n\nexport async function getRequestInfo(\n  opts: GetRequestInfoOptions,\n): Promise<TRPCRequestInfo> {\n  const handler = getContentTypeHandler(opts.req);\n  return await handler.parse(opts);\n}\n", "import { isObject } from '../utils';\n\nexport function isAbortError(\n  error: unknown,\n): error is DOMException | Error | { name: 'AbortError' } {\n  return isObject(error) && error['name'] === 'AbortError';\n}\n\nexport function throwAbortError(message = 'AbortError'): never {\n  throw new DOMException(message, 'AbortError');\n}\n", "/* eslint-disable @typescript-eslint/unbound-method */\n \n \n\nimport type {\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  PromiseWithResolvers,\n  ProxyPromise,\n  SubscribedPromise,\n} from \"./types\";\n\n/** Memory safe (weakmapped) cache of the ProxyPromise for each Promise,\n * which is retained for the lifetime of the original Promise.\n */\nconst subscribableCache = new WeakMap<\n  PromiseLike<unknown>,\n  ProxyPromise<unknown>\n>();\n\n/** A NOOP function allowing a consistent interface for settled\n * SubscribedPromises (settled promises are not subscribed - they resolve\n * immediately). */\nconst NOOP = () => {\n  // noop\n};\n\n/**\n * Every `Promise<T>` can be shadowed by a single `ProxyPromise<T>`. It is\n * created once, cached and reused throughout the lifetime of the Promise. Get a\n * Promise's ProxyPromise using `Unpromise.proxy(promise)`.\n *\n * The `ProxyPromise<T>` attaches handlers to the original `Promise<T>`\n * `.then()` and `.catch()` just once. Promises derived from it use a\n * subscription- (and unsubscription-) based mechanism that monitors these\n * handlers.\n *\n * Every time you call `.subscribe()`, `.then()` `.catch()` or `.finally()` on a\n * `ProxyPromise<T>` it returns a `SubscribedPromise<T>` having an additional\n * `unsubscribe()` method. Calling `unsubscribe()` detaches reference chains\n * from the original, potentially long-lived Promise, eliminating memory leaks.\n *\n * This approach can eliminate the memory leaks that otherwise come about from\n * repeated `race()` or `any()` calls invoking `.then()` and `.catch()` multiple\n * times on the same long-lived native Promise (subscriptions which can never be\n * cleaned up).\n *\n * `Unpromise.race(promises)` is a reference implementation of `Promise.race`\n * avoiding memory leaks when using long-lived unsettled Promises.\n *\n * `Unpromise.any(promises)` is a reference implementation of `Promise.any`\n * avoiding memory leaks when using long-lived unsettled Promises.\n *\n * `Unpromise.resolve(promise)` returns an ephemeral `SubscribedPromise<T>` for\n * any given `Promise<T>` facilitating arbitrary async/await patterns. Behind\n * the scenes, `resolve` is implemented simply as\n * `Unpromise.proxy(promise).subscribe()`. Don't forget to call `.unsubscribe()`\n * to tidy up!\n *\n */\nexport class Unpromise<T> implements ProxyPromise<T> {\n  /** INSTANCE IMPLEMENTATION */\n\n  /** The promise shadowed by this Unpromise<T>  */\n  protected readonly promise: Promise<T> | PromiseLike<T>;\n\n  /** Promises expecting eventual settlement (unless unsubscribed first). This list is deleted\n   * after the original promise settles - no further notifications will be issued. */\n  protected subscribers: ReadonlyArray<PromiseWithResolvers<T>> | null = [];\n\n  /** The Promise's settlement (recorded when it fulfils or rejects). This is consulted when\n   * calling .subscribe() .then() .catch() .finally() to see if an immediately-resolving Promise\n   * can be returned, and therefore subscription can be bypassed. */\n  protected settlement: PromiseSettledResult<T> | null = null;\n\n  /** Constructor accepts a normal Promise executor function like `new\n   * Unpromise((resolve, reject) => {...})` or accepts a pre-existing Promise\n   * like `new Unpromise(existingPromise)`. Adds `.then()` and `.catch()`\n   * handlers to the Promise. These handlers pass fulfilment and rejection\n   * notifications to downstream subscribers and maintains records of value\n   * or error if the Promise ever settles. */\n  protected constructor(promise: Promise<T>);\n  protected constructor(promise: PromiseLike<T>);\n  protected constructor(executor: PromiseExecutor<T>);\n  protected constructor(arg: Promise<T> | PromiseLike<T> | PromiseExecutor<T>) {\n    // handle either a Promise or a Promise executor function\n    if (typeof arg === \"function\") {\n      this.promise = new Promise(arg);\n    } else {\n      this.promise = arg;\n    }\n\n    // subscribe for eventual fulfilment and rejection\n\n    // handle PromiseLike objects (that at least have .then)\n    const thenReturn = this.promise.then((value) => {\n      // atomically record fulfilment and detach subscriber list\n      const { subscribers } = this;\n      this.subscribers = null;\n      this.settlement = {\n        status: \"fulfilled\",\n        value,\n      };\n      // notify fulfilment to subscriber list\n      subscribers?.forEach(({ resolve }) => {\n        resolve(value);\n      });\n    });\n\n    // handle Promise (that also have a .catch behaviour)\n    if (\"catch\" in thenReturn) {\n      thenReturn.catch((reason) => {\n        // atomically record rejection and detach subscriber list\n        const { subscribers } = this;\n        this.subscribers = null;\n        this.settlement = {\n          status: \"rejected\",\n          reason,\n        };\n        // notify rejection to subscriber list\n        subscribers?.forEach(({ reject }) => {\n          reject(reason);\n        });\n      });\n    }\n  }\n\n  /** Create a promise that mitigates uncontrolled subscription to a long-lived\n   * Promise via .then() and .catch() - otherwise a source of memory leaks.\n   *\n   * The returned promise has an `unsubscribe()` method which can be called when\n   * the Promise is no longer being tracked by application logic, and which\n   * ensures that there is no reference chain from the original promise to the\n   * new one, and therefore no memory leak.\n   *\n   * If original promise has not yet settled, this adds a new unique promise\n   * that listens to then/catch events, along with an `unsubscribe()` method to\n   * detach it.\n   *\n   * If original promise has settled, then creates a new Promise.resolve() or\n   * Promise.reject() and provided unsubscribe is a noop.\n   *\n   * If you call `unsubscribe()` before the returned Promise has settled, it\n   * will never settle.\n   */\n  subscribe(): SubscribedPromise<T> {\n    // in all cases we will combine some promise with its unsubscribe function\n    let promise: Promise<T>;\n    let unsubscribe: () => void;\n\n    const { settlement } = this;\n    if (settlement === null) {\n      // not yet settled - subscribe new promise. Expect eventual settlement\n      if (this.subscribers === null) {\n        // invariant - it is not settled, so it must have subscribers\n        throw new Error(\"Unpromise settled but still has subscribers\");\n      }\n      const subscriber = withResolvers<T>();\n      this.subscribers = listWithMember(this.subscribers, subscriber);\n      promise = subscriber.promise;\n      unsubscribe = () => {\n        if (this.subscribers !== null) {\n          this.subscribers = listWithoutMember(this.subscribers, subscriber);\n        }\n      };\n    } else {\n      // settled - don't create subscribed promise. Just resolve or reject\n      const { status } = settlement;\n      if (status === \"fulfilled\") {\n        promise = Promise.resolve(settlement.value);\n      } else {\n        promise = Promise.reject(settlement.reason);\n      }\n      unsubscribe = NOOP;\n    }\n\n    // extend promise signature with the extra method\n    return Object.assign(promise, { unsubscribe });\n  }\n\n  /** STANDARD PROMISE METHODS (but returning a SubscribedPromise) */\n\n  then<TResult1 = T, TResult2 = never>(\n    onfulfilled?:\n      | ((value: T) => TResult1 | PromiseLike<TResult1>)\n      | null\n       ,\n    onrejected?:\n      | ((reason: any) => TResult2 | PromiseLike<TResult2>)\n      | null\n       \n  ): SubscribedPromise<TResult1 | TResult2> {\n    const subscribed = this.subscribe();\n    const { unsubscribe } = subscribed;\n    return Object.assign(subscribed.then(onfulfilled, onrejected), {\n      unsubscribe,\n    });\n  }\n\n  catch<TResult = never>(\n    onrejected?:\n      | ((reason: any) => TResult | PromiseLike<TResult>)\n      | null\n       \n  ): SubscribedPromise<T | TResult> {\n    const subscribed = this.subscribe();\n    const { unsubscribe } = subscribed;\n    return Object.assign(subscribed.catch(onrejected), {\n      unsubscribe,\n    });\n  }\n\n  finally(onfinally?: (() => void) | null  ): SubscribedPromise<T> {\n    const subscribed = this.subscribe();\n    const { unsubscribe } = subscribed;\n    return Object.assign(subscribed.finally(onfinally), {\n      unsubscribe,\n    });\n  }\n\n  /** TOSTRING SUPPORT */\n\n  readonly [Symbol.toStringTag] = \"Unpromise\";\n\n  /** Unpromise STATIC METHODS */\n\n  /** Create or Retrieve the proxy Unpromise (a re-used Unpromise for the VM lifetime\n   * of the provided Promise reference) */\n  static proxy<T>(promise: PromiseLike<T>): ProxyPromise<T> {\n    const cached = Unpromise.getSubscribablePromise(promise);\n    return typeof cached !== \"undefined\"\n      ? cached\n      : Unpromise.createSubscribablePromise(promise);\n  }\n\n  /** Create and store an Unpromise keyed by an original Promise. */\n  protected static createSubscribablePromise<T>(promise: PromiseLike<T>) {\n    const created = new Unpromise<T>(promise);\n    subscribableCache.set(promise, created as Unpromise<unknown>); // resolve promise to unpromise\n    subscribableCache.set(created, created as Unpromise<unknown>); // resolve the unpromise to itself\n    return created;\n  }\n\n  /** Retrieve a previously-created Unpromise keyed by an original Promise. */\n  protected static getSubscribablePromise<T>(promise: PromiseLike<T>) {\n    return subscribableCache.get(promise) as ProxyPromise<T> | undefined;\n  }\n\n  /** Promise STATIC METHODS */\n\n  /** Lookup the Unpromise for this promise, and derive a SubscribedPromise from\n   * it (that can be later unsubscribed to eliminate Memory leaks) */\n  static resolve<T>(value: T | PromiseLike<T>) {\n    const promise: PromiseLike<T> =\n      typeof value === \"object\" &&\n      value !== null &&\n      \"then\" in value &&\n      typeof value.then === \"function\"\n        ? value\n        : Promise.resolve(value);\n    return Unpromise.proxy(promise).subscribe() as SubscribedPromise<\n      Awaited<T>\n    >;\n  }\n\n  /** Perform Promise.any() via SubscribedPromises, then unsubscribe them.\n   * Equivalent to Promise.any but eliminates memory leaks from long-lived\n   * promises accumulating .then() and .catch() subscribers. */\n  static async any<T extends readonly unknown[] | []>(\n    values: T\n  ): Promise<Awaited<T[number]>>;\n  static async any<T>(\n    values: Iterable<T | PromiseLike<T>>\n  ): Promise<Awaited<T>> {\n    const valuesArray = Array.isArray(values) ? values : [...values];\n    const subscribedPromises = valuesArray.map(Unpromise.resolve);\n    try {\n      return await Promise.any(subscribedPromises);\n    } finally {\n      subscribedPromises.forEach(({ unsubscribe }) => {\n        unsubscribe();\n      });\n    }\n  }\n\n  /** Perform Promise.race via SubscribedPromises, then unsubscribe them.\n   * Equivalent to Promise.race but eliminates memory leaks from long-lived\n   * promises accumulating .then() and .catch() subscribers. */\n  static async race<T extends readonly unknown[] | []>(\n    values: T\n  ): Promise<Awaited<T[number]>>;\n  static async race<T>(\n    values: Iterable<T | PromiseLike<T>>\n  ): Promise<Awaited<T>> {\n    const valuesArray = Array.isArray(values) ? values : [...values];\n    const subscribedPromises = valuesArray.map(Unpromise.resolve);\n    try {\n      return await Promise.race(subscribedPromises);\n    } finally {\n      subscribedPromises.forEach(({ unsubscribe }) => {\n        unsubscribe();\n      });\n    }\n  }\n\n  /** Create a race of SubscribedPromises that will fulfil to a single winning\n   * Promise (in a 1-Tuple). Eliminates memory leaks from long-lived promises\n   * accumulating .then() and .catch() subscribers. Allows simple logic to\n   * consume the result, like...\n   * ```ts\n   * const [ winner ] = await Unpromise.race([ promiseA, promiseB ]);\n   * if(winner === promiseB){\n   *   const result = await promiseB;\n   *   // do the thing\n   * }\n   * ```\n   * */\n  static async raceReferences<TPromise extends Promise<unknown>>(\n    promises: readonly TPromise[]\n  ) {\n    // map each promise to an eventual 1-tuple containing itself\n    const selfPromises = promises.map(resolveSelfTuple);\n\n    // now race them. They will fulfil to a readonly [P] or reject.\n    try {\n      return await Promise.race(selfPromises);\n    } finally {\n      for (const promise of selfPromises) {\n        // unsubscribe proxy promises when the race is over to mitigate memory leaks\n        promise.unsubscribe();\n      }\n    }\n  }\n}\n\n/** Promises a 1-tuple containing the original promise when it resolves. Allows\n * awaiting the eventual Promise ***reference*** (easy to destructure and\n * exactly compare with ===). Avoids resolving to the Promise ***value*** (which\n * may be ambiguous and therefore hard to identify as the winner of a race).\n * You can call unsubscribe on the Promise to mitigate memory leaks.\n * */\nexport function resolveSelfTuple<TPromise extends Promise<unknown>>(\n  promise: TPromise\n): SubscribedPromise<readonly [TPromise]> {\n  return Unpromise.proxy(promise).then(() => [promise] as const);\n}\n\n/** VENDORED (Future) PROMISE UTILITIES */\n\n/** Reference implementation of https://github.com/tc39/proposal-promise-with-resolvers */\nfunction withResolvers<T>(): PromiseWithResolvers<T> {\n  let resolve!: PromiseWithResolvers<T>[\"resolve\"];\n  let reject!: PromiseWithResolvers<T>[\"reject\"];\n  const promise = new Promise<T>((_resolve, _reject) => {\n    resolve = _resolve;\n    reject = _reject;\n  });\n  return {\n    promise,\n    resolve,\n    reject,\n  };\n}\n\n/** IMMUTABLE LIST OPERATIONS */\n\nfunction listWithMember<T>(arr: readonly T[], member: T): readonly T[] {\n  return [...arr, member];\n}\n\nfunction listWithoutIndex<T>(arr: readonly T[], index: number) {\n  return [...arr.slice(0, index), ...arr.slice(index + 1)];\n}\n\nfunction listWithoutMember<T>(arr: readonly T[], member: unknown) {\n  const index = arr.indexOf(member as T);\n  if (index !== -1) {\n    return listWithoutIndex(arr, index);\n  }\n  return arr;\n}\n", "// @ts-expect-error - polyfilling symbol\n// eslint-disable-next-line no-restricted-syntax\nSymbol.dispose ??= Symbol();\n\n// @ts-expect-error - polyfilling symbol\n// eslint-disable-next-line no-restricted-syntax\nSymbol.asyncDispose ??= Symbol();\n\n/**\n * Takes a value and a dispose function and returns a new object that implements the Disposable interface.\n * The returned object is the original value augmented with a Symbol.dispose method.\n * @param thing The value to make disposable\n * @param dispose Function to call when disposing the resource\n * @returns The original value with Symbol.dispose method added\n */\nexport function makeResource<T>(thing: T, dispose: () => void): T & Disposable {\n  const it = thing as T & Partial<Disposable>;\n\n  // eslint-disable-next-line no-restricted-syntax\n  const existing = it[Symbol.dispose];\n\n  // eslint-disable-next-line no-restricted-syntax\n  it[Symbol.dispose] = () => {\n    dispose();\n    existing?.();\n  };\n\n  return it as T & Disposable;\n}\n\n/**\n * Takes a value and an async dispose function and returns a new object that implements the AsyncDisposable interface.\n * The returned object is the original value augmented with a Symbol.asyncDispose method.\n * @param thing The value to make async disposable\n * @param dispose Async function to call when disposing the resource\n * @returns The original value with Symbol.asyncDispose method added\n */\nexport function makeAsyncResource<T>(\n  thing: T,\n  dispose: () => Promise<void>,\n): T & AsyncDisposable {\n  const it = thing as T & Partial<AsyncDisposable>;\n\n  // eslint-disable-next-line no-restricted-syntax\n  const existing = it[Symbol.asyncDispose];\n\n  // eslint-disable-next-line no-restricted-syntax\n  it[Symbol.asyncDispose] = async () => {\n    await dispose();\n    await existing?.();\n  };\n\n  return it as T & AsyncDisposable;\n}\n", "import { makeResource } from './disposable';\n\nexport const disposablePromiseTimerResult = Symbol();\n\nexport function timerResource(ms: number) {\n  let timer: ReturnType<typeof setTimeout> | null = null;\n\n  return makeResource(\n    {\n      start() {\n        if (timer) {\n          throw new Error('Timer already started');\n        }\n\n        const promise = new Promise<typeof disposablePromiseTimerResult>(\n          (resolve) => {\n            timer = setTimeout(() => resolve(disposablePromiseTimerResult), ms);\n          },\n        );\n        return promise;\n      },\n    },\n    () => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n    },\n  );\n}\n", "function _usingCtx() {\n  var r = \"function\" == typeof SuppressedError ? SuppressedError : function (r, e) {\n      var n = Error();\n      return n.name = \"SuppressedError\", n.error = r, n.suppressed = e, n;\n    },\n    e = {},\n    n = [];\n  function using(r, e) {\n    if (null != e) {\n      if (Object(e) !== e) throw new TypeError(\"using declarations can only be used with objects, functions, null, or undefined.\");\n      if (r) var o = e[Symbol.asyncDispose || Symbol[\"for\"](\"Symbol.asyncDispose\")];\n      if (void 0 === o && (o = e[Symbol.dispose || Symbol[\"for\"](\"Symbol.dispose\")], r)) var t = o;\n      if (\"function\" != typeof o) throw new TypeError(\"Object is not disposable.\");\n      t && (o = function o() {\n        try {\n          t.call(e);\n        } catch (r) {\n          return Promise.reject(r);\n        }\n      }), n.push({\n        v: e,\n        d: o,\n        a: r\n      });\n    } else r && n.push({\n      d: e,\n      a: r\n    });\n    return e;\n  }\n  return {\n    e: e,\n    u: using.bind(null, !1),\n    a: using.bind(null, !0),\n    d: function d() {\n      var o,\n        t = this.e,\n        s = 0;\n      function next() {\n        for (; o = n.pop();) try {\n          if (!o.a && 1 === s) return s = 0, n.push(o), Promise.resolve().then(next);\n          if (o.d) {\n            var r = o.d.call(o.v);\n            if (o.a) return s |= 2, Promise.resolve(r).then(next, err);\n          } else s |= 1;\n        } catch (r) {\n          return err(r);\n        }\n        if (1 === s) return t !== e ? Promise.reject(t) : Promise.resolve();\n        if (t !== e) throw t;\n      }\n      function err(n) {\n        return t = t !== e ? new r(n, t) : n, next();\n      }\n      return next();\n    }\n  };\n}\nmodule.exports = _usingCtx, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _OverloadYield(e, d) {\n  this.v = e, this.k = d;\n}\nmodule.exports = _OverloadYield, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var OverloadYield = require(\"./OverloadYield.js\");\nfunction _awaitAsyncGenerator(e) {\n  return new OverloadYield(e, 0);\n}\nmodule.exports = _awaitAsyncGenerator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var OverloadYield = require(\"./OverloadYield.js\");\nfunction _wrapAsyncGenerator(e) {\n  return function () {\n    return new AsyncGenerator(e.apply(this, arguments));\n  };\n}\nfunction AsyncGenerator(e) {\n  var r, t;\n  function resume(r, t) {\n    try {\n      var n = e[r](t),\n        o = n.value,\n        u = o instanceof OverloadYield;\n      Promise.resolve(u ? o.v : o).then(function (t) {\n        if (u) {\n          var i = \"return\" === r ? \"return\" : \"next\";\n          if (!o.k || t.done) return resume(i, t);\n          t = e[i](t).value;\n        }\n        settle(n.done ? \"return\" : \"normal\", t);\n      }, function (e) {\n        resume(\"throw\", e);\n      });\n    } catch (e) {\n      settle(\"throw\", e);\n    }\n  }\n  function settle(e, n) {\n    switch (e) {\n      case \"return\":\n        r.resolve({\n          value: n,\n          done: !0\n        });\n        break;\n      case \"throw\":\n        r.reject(n);\n        break;\n      default:\n        r.resolve({\n          value: n,\n          done: !1\n        });\n    }\n    (r = r.next) ? resume(r.key, r.arg) : t = null;\n  }\n  this._invoke = function (e, n) {\n    return new Promise(function (o, u) {\n      var i = {\n        key: e,\n        arg: n,\n        resolve: o,\n        reject: u,\n        next: null\n      };\n      t ? t = t.next = i : (r = t = i, resume(e, n));\n    });\n  }, \"function\" != typeof e[\"return\"] && (this[\"return\"] = void 0);\n}\nAsyncGenerator.prototype[\"function\" == typeof Symbol && Symbol.asyncIterator || \"@@asyncIterator\"] = function () {\n  return this;\n}, AsyncGenerator.prototype.next = function (e) {\n  return this._invoke(\"next\", e);\n}, AsyncGenerator.prototype[\"throw\"] = function (e) {\n  return this._invoke(\"throw\", e);\n}, AsyncGenerator.prototype[\"return\"] = function (e) {\n  return this._invoke(\"return\", e);\n};\nmodule.exports = _wrapAsyncGenerator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import { Unpromise } from '../../../vendor/unpromise';\nimport { throwAbortError } from '../../http/abortError';\nimport { makeAsyncResource } from './disposable';\nimport { disposablePromiseTimerResult, timerResource } from './timerResource';\n\nexport function iteratorResource<TYield, TReturn, TNext>(\n  iterable: AsyncIterable<TYield, TReturn, TNext>,\n): AsyncIterator<TYield, TReturn, TNext> & AsyncDisposable {\n  const iterator = iterable[Symbol.asyncIterator]();\n\n  // @ts-expect-error - this is added in node 24 which we don't officially support yet\n  // eslint-disable-next-line no-restricted-syntax\n  if (iterator[Symbol.asyncDispose]) {\n    return iterator as AsyncIterator<TYield, TReturn, TNext> & AsyncDisposable;\n  }\n\n  return makeAsyncResource(iterator, async () => {\n    await iterator.return?.();\n  });\n}\n/**\n * Derives a new {@link AsyncGenerator} based on {@link iterable}, that automatically aborts after the specified duration.\n */\nexport async function* withMaxDuration<T>(\n  iterable: AsyncIterable<T>,\n  opts: { maxDurationMs: number },\n): AsyncGenerator<T> {\n  await using iterator = iteratorResource(iterable);\n\n  using timer = timerResource(opts.maxDurationMs);\n\n  const timerPromise = timer.start();\n\n  // declaration outside the loop for garbage collection reasons\n  let result: null | IteratorResult<T> | typeof disposablePromiseTimerResult;\n\n  while (true) {\n    result = await Unpromise.race([iterator.next(), timerPromise]);\n    if (result === disposablePromiseTimerResult) {\n      // cancelled due to timeout\n      throwAbortError();\n    }\n    if (result.done) {\n      return result;\n    }\n    yield result.value;\n    // free up reference for garbage collection\n    result = null;\n  }\n}\n\n/**\n * Derives a new {@link AsyncGenerator} based of {@link iterable}, that yields its first\n * {@link count} values. Then, a grace period of {@link gracePeriodMs} is started in which further\n * values may still come through. After this period, the generator aborts.\n */\nexport async function* takeWithGrace<T>(\n  iterable: AsyncIterable<T>,\n  opts: {\n    count: number;\n    gracePeriodMs: number;\n  },\n): AsyncGenerator<T> {\n  await using iterator = iteratorResource(iterable);\n\n  // declaration outside the loop for garbage collection reasons\n  let result: null | IteratorResult<T> | typeof disposablePromiseTimerResult;\n\n  using timer = timerResource(opts.gracePeriodMs);\n\n  let count = opts.count;\n\n  let timerPromise = new Promise<typeof disposablePromiseTimerResult>(() => {\n    // never resolves\n  });\n\n  while (true) {\n    result = await Unpromise.race([iterator.next(), timerPromise]);\n    if (result === disposablePromiseTimerResult) {\n      throwAbortError();\n    }\n    if (result.done) {\n      return result.value;\n    }\n    yield result.value;\n    if (--count === 0) {\n      timerPromise = timer.start();\n    }\n    // free up reference for garbage collection\n    result = null;\n  }\n}\n", "/* eslint-disable @typescript-eslint/no-non-null-assertion */\nexport function createDeferred<TValue = void>() {\n  let resolve: (value: TValue) => void;\n  let reject: (error: unknown) => void;\n  const promise = new Promise<TValue>((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n\n  return { promise, resolve: resolve!, reject: reject! };\n}\nexport type Deferred<TValue> = ReturnType<typeof createDeferred<TValue>>;\n", "import { createDeferred } from './createDeferred';\nimport { makeAsyncResource } from './disposable';\n\ntype ManagedIteratorResult<TYield, TReturn> =\n  | { status: 'yield'; value: TYield }\n  | { status: 'return'; value: TReturn }\n  | { status: 'error'; error: unknown };\nfunction createManagedIterator<TYield, TReturn>(\n  iterable: AsyncIterable<TYield, TReturn>,\n  onResult: (result: ManagedIteratorResult<TYield, TReturn>) => void,\n) {\n  const iterator = iterable[Symbol.asyncIterator]();\n  let state: 'idle' | 'pending' | 'done' = 'idle';\n\n  function cleanup() {\n    state = 'done';\n    onResult = () => {\n      // noop\n    };\n  }\n\n  function pull() {\n    if (state !== 'idle') {\n      return;\n    }\n    state = 'pending';\n\n    const next = iterator.next();\n    next\n      .then((result) => {\n        if (result.done) {\n          state = 'done';\n          onResult({ status: 'return', value: result.value });\n          cleanup();\n          return;\n        }\n        state = 'idle';\n        onResult({ status: 'yield', value: result.value });\n      })\n      .catch((cause) => {\n        onResult({ status: 'error', error: cause });\n        cleanup();\n      });\n  }\n\n  return {\n    pull,\n    destroy: async () => {\n      cleanup();\n      await iterator.return?.();\n    },\n  };\n}\ntype ManagedIterator<TYield, TReturn> = ReturnType<\n  typeof createManagedIterator<TYield, TReturn>\n>;\n\ninterface MergedAsyncIterables<TYield>\n  extends AsyncIterable<TYield, void, unknown> {\n  add(iterable: AsyncIterable<TYield>): void;\n}\n\n/**\n * Creates a new async iterable that merges multiple async iterables into a single stream.\n * Values from the input iterables are yielded in the order they resolve, similar to Promise.race().\n *\n * New iterables can be added dynamically using the returned {@link MergedAsyncIterables.add} method, even after iteration has started.\n *\n * If any of the input iterables throws an error, that error will be propagated through the merged stream.\n * Other iterables will not continue to be processed.\n *\n * @template TYield The type of values yielded by the input iterables\n */\nexport function mergeAsyncIterables<TYield>(): MergedAsyncIterables<TYield> {\n  let state: 'idle' | 'pending' | 'done' = 'idle';\n  let flushSignal = createDeferred();\n\n  /**\n   * used while {@link state} is `idle`\n   */\n  const iterables: AsyncIterable<TYield, void, unknown>[] = [];\n  /**\n   * used while {@link state} is `pending`\n   */\n  const iterators = new Set<ManagedIterator<TYield, void>>();\n\n  const buffer: Array<\n    [\n      iterator: ManagedIterator<TYield, void>,\n      result: Exclude<\n        ManagedIteratorResult<TYield, void>,\n        { status: 'return' }\n      >,\n    ]\n  > = [];\n\n  function initIterable(iterable: AsyncIterable<TYield, void, unknown>) {\n    if (state !== 'pending') {\n      // shouldn't happen\n      return;\n    }\n    const iterator = createManagedIterator(iterable, (result) => {\n      if (state !== 'pending') {\n        // shouldn't happen\n        return;\n      }\n      switch (result.status) {\n        case 'yield':\n          buffer.push([iterator, result]);\n          break;\n        case 'return':\n          iterators.delete(iterator);\n          break;\n        case 'error':\n          buffer.push([iterator, result]);\n          iterators.delete(iterator);\n          break;\n      }\n      flushSignal.resolve();\n    });\n    iterators.add(iterator);\n    iterator.pull();\n  }\n\n  return {\n    add(iterable: AsyncIterable<TYield, void, unknown>) {\n      switch (state) {\n        case 'idle':\n          iterables.push(iterable);\n          break;\n        case 'pending':\n          initIterable(iterable);\n          break;\n        case 'done': {\n          // shouldn't happen\n          break;\n        }\n      }\n    },\n    async *[Symbol.asyncIterator]() {\n      if (state !== 'idle') {\n        throw new Error('Cannot iterate twice');\n      }\n      state = 'pending';\n\n      await using _finally = makeAsyncResource({}, async () => {\n        state = 'done';\n\n        const errors: unknown[] = [];\n        await Promise.all(\n          Array.from(iterators.values()).map(async (it) => {\n            try {\n              await it.destroy();\n            } catch (cause) {\n              errors.push(cause);\n            }\n          }),\n        );\n        buffer.length = 0;\n        iterators.clear();\n        flushSignal.resolve();\n\n        if (errors.length > 0) {\n          throw new AggregateError(errors);\n        }\n      });\n\n      while (iterables.length > 0) {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        initIterable(iterables.shift()!);\n      }\n\n      while (iterators.size > 0) {\n        await flushSignal.promise;\n\n        while (buffer.length > 0) {\n          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n          const [iterator, result] = buffer.shift()!;\n\n          switch (result.status) {\n            case 'yield':\n              yield result.value;\n              iterator.pull();\n              break;\n            case 'error':\n              throw result.error;\n          }\n        }\n        flushSignal = createDeferred();\n      }\n    },\n  };\n}\n", "/**\n * Creates a ReadableStream from an AsyncIterable.\n *\n * @param iterable - The source AsyncIterable to stream from\n * @returns A ReadableStream that yields values from the AsyncIterable\n */\nexport function readableStreamFrom<TYield>(\n  iterable: AsyncIterable<TYield, void>,\n): ReadableStream<TYield> {\n  const iterator = iterable[Symbol.asyncIterator]();\n\n  return new ReadableStream({\n    async cancel() {\n      await iterator.return?.();\n    },\n\n    async pull(controller) {\n      const result = await iterator.next();\n\n      if (result.done) {\n        controller.close();\n        return;\n      }\n\n      controller.enqueue(result.value);\n    },\n  });\n}\n", "import { Unpromise } from '../../../vendor/unpromise';\nimport { iteratorResource } from './asyncIterable';\nimport { disposablePromiseTimerResult, timerResource } from './timerResource';\n\nexport const PING_SYM = Symbol('ping');\n\n/**\n * Derives a new {@link AsyncGenerator} based of {@link iterable}, that yields {@link PING_SYM}\n * whenever no value has been yielded for {@link pingIntervalMs}.\n */\nexport async function* withPing<TValue>(\n  iterable: AsyncIterable<TValue>,\n  pingIntervalMs: number,\n): AsyncGenerator<TValue | typeof PING_SYM> {\n  await using iterator = iteratorResource(iterable);\n\n  // declaration outside the loop for garbage collection reasons\n  let result:\n    | null\n    | IteratorResult<TValue>\n    | typeof disposablePromiseTimerResult;\n\n  let nextPromise = iterator.next();\n\n  while (true) {\n    using pingPromise = timerResource(pingIntervalMs);\n\n    result = await Unpromise.race([nextPromise, pingPromise.start()]);\n\n    if (result === disposablePromiseTimerResult) {\n      // cancelled\n\n      yield PING_SYM;\n      continue;\n    }\n\n    if (result.done) {\n      return result.value;\n    }\n\n    nextPromise = iterator.next();\n    yield result.value;\n\n    // free up reference for garbage collection\n    result = null;\n  }\n}\n", "function _asyncIterator(r) {\n  var n,\n    t,\n    o,\n    e = 2;\n  for (\"undefined\" != typeof Symbol && (t = Symbol.asyncIterator, o = Symbol.iterator); e--;) {\n    if (t && null != (n = r[t])) return n.call(r);\n    if (o && null != (n = r[o])) return new AsyncFromSyncIterator(n.call(r));\n    t = \"@@asyncIterator\", o = \"@@iterator\";\n  }\n  throw new TypeError(\"Object is not async iterable\");\n}\nfunction AsyncFromSyncIterator(r) {\n  function AsyncFromSyncIteratorContinuation(r) {\n    if (Object(r) !== r) return Promise.reject(new TypeError(r + \" is not an object.\"));\n    var n = r.done;\n    return Promise.resolve(r.value).then(function (r) {\n      return {\n        value: r,\n        done: n\n      };\n    });\n  }\n  return AsyncFromSyncIterator = function AsyncFromSyncIterator(r) {\n    this.s = r, this.n = r.next;\n  }, AsyncFromSyncIterator.prototype = {\n    s: null,\n    n: null,\n    next: function next() {\n      return AsyncFromSyncIteratorContinuation(this.n.apply(this.s, arguments));\n    },\n    \"return\": function _return(r) {\n      var n = this.s[\"return\"];\n      return void 0 === n ? Promise.resolve({\n        value: r,\n        done: !0\n      }) : AsyncFromSyncIteratorContinuation(n.apply(this.s, arguments));\n    },\n    \"throw\": function _throw(r) {\n      var n = this.s[\"return\"];\n      return void 0 === n ? Promise.reject(r) : AsyncFromSyncIteratorContinuation(n.apply(this.s, arguments));\n    }\n  }, new AsyncFromSyncIterator(r);\n}\nmodule.exports = _asyncIterator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import { isAsyncIterable, isFunction, isObject, run } from '../utils';\nimport { iteratorResource } from './utils/asyncIterable';\nimport type { Deferred } from './utils/createDeferred';\nimport { createDeferred } from './utils/createDeferred';\nimport { makeResource } from './utils/disposable';\nimport { mergeAsyncIterables } from './utils/mergeAsyncIterables';\nimport { readableStreamFrom } from './utils/readableStreamFrom';\nimport { PING_SYM, withPing } from './utils/withPing';\n\n/**\n * A subset of the standard ReadableStream properties needed by tRPC internally.\n * @see ReadableStream from lib.dom.d.ts\n */\nexport type WebReadableStreamEsque = {\n  getReader: () => ReadableStreamDefaultReader<Uint8Array>;\n};\n\nexport type NodeJSReadableStreamEsque = {\n  on(\n    eventName: string | symbol,\n    listener: (...args: any[]) => void,\n  ): NodeJSReadableStreamEsque;\n};\n\nfunction isPlainObject(value: unknown): value is Record<string, unknown> {\n  return Object.prototype.toString.call(value) === '[object Object]';\n}\n\n// ---------- types\nconst CHUNK_VALUE_TYPE_PROMISE = 0;\ntype CHUNK_VALUE_TYPE_PROMISE = typeof CHUNK_VALUE_TYPE_PROMISE;\nconst CHUNK_VALUE_TYPE_ASYNC_ITERABLE = 1;\ntype CHUNK_VALUE_TYPE_ASYNC_ITERABLE = typeof CHUNK_VALUE_TYPE_ASYNC_ITERABLE;\n\nconst PROMISE_STATUS_FULFILLED = 0;\ntype PROMISE_STATUS_FULFILLED = typeof PROMISE_STATUS_FULFILLED;\nconst PROMISE_STATUS_REJECTED = 1;\ntype PROMISE_STATUS_REJECTED = typeof PROMISE_STATUS_REJECTED;\n\nconst ASYNC_ITERABLE_STATUS_RETURN = 0;\ntype ASYNC_ITERABLE_STATUS_RETURN = typeof ASYNC_ITERABLE_STATUS_RETURN;\nconst ASYNC_ITERABLE_STATUS_YIELD = 1;\ntype ASYNC_ITERABLE_STATUS_YIELD = typeof ASYNC_ITERABLE_STATUS_YIELD;\nconst ASYNC_ITERABLE_STATUS_ERROR = 2;\ntype ASYNC_ITERABLE_STATUS_ERROR = typeof ASYNC_ITERABLE_STATUS_ERROR;\n\ntype ChunkDefinitionKey =\n  // root should be replaced\n  | null\n  // at array path\n  | number\n  // at key path\n  | string;\n\ntype ChunkIndex = number & { __chunkIndex: true };\ntype ChunkValueType =\n  | CHUNK_VALUE_TYPE_PROMISE\n  | CHUNK_VALUE_TYPE_ASYNC_ITERABLE;\ntype ChunkDefinition = [\n  key: ChunkDefinitionKey,\n  type: ChunkValueType,\n  chunkId: ChunkIndex,\n];\ntype EncodedValue = [\n  // data\n  [unknown] | [],\n  // chunk descriptions\n  ...ChunkDefinition[],\n];\n\ntype Head = Record<string, EncodedValue>;\ntype PromiseChunk =\n  | [\n      chunkIndex: ChunkIndex,\n      status: PROMISE_STATUS_FULFILLED,\n      value: EncodedValue,\n    ]\n  | [chunkIndex: ChunkIndex, status: PROMISE_STATUS_REJECTED, error: unknown];\ntype IterableChunk =\n  | [\n      chunkIndex: ChunkIndex,\n      status: ASYNC_ITERABLE_STATUS_RETURN,\n      value: EncodedValue,\n    ]\n  | [\n      chunkIndex: ChunkIndex,\n      status: ASYNC_ITERABLE_STATUS_YIELD,\n      value: EncodedValue,\n    ]\n  | [\n      chunkIndex: ChunkIndex,\n      status: ASYNC_ITERABLE_STATUS_ERROR,\n      error: unknown,\n    ];\ntype ChunkData = PromiseChunk | IterableChunk;\ntype PlaceholderValue = 0 & { __placeholder: true };\nexport function isPromise(value: unknown): value is Promise<unknown> {\n  return (\n    (isObject(value) || isFunction(value)) &&\n    typeof value?.['then'] === 'function' &&\n    typeof value?.['catch'] === 'function'\n  );\n}\n\ntype Serialize = (value: any) => any;\ntype Deserialize = (value: any) => any;\n\ntype PathArray = readonly (string | number)[];\nexport type ProducerOnError = (opts: {\n  error: unknown;\n  path: PathArray;\n}) => void;\nexport interface JSONLProducerOptions {\n  serialize?: Serialize;\n  data: Record<string, unknown> | unknown[];\n  onError?: ProducerOnError;\n  formatError?: (opts: { error: unknown; path: PathArray }) => unknown;\n  maxDepth?: number;\n  /**\n   * Interval in milliseconds to send a ping to the client to keep the connection alive\n   * This will be sent as a whitespace character\n   * @default undefined\n   */\n  pingMs?: number;\n}\n\nclass MaxDepthError extends Error {\n  constructor(public path: (string | number)[]) {\n    super('Max depth reached at path: ' + path.join('.'));\n  }\n}\n\nasync function* createBatchStreamProducer(\n  opts: JSONLProducerOptions,\n): AsyncIterable<Head | ChunkData | typeof PING_SYM, void> {\n  const { data } = opts;\n  let counter = 0 as ChunkIndex;\n  const placeholder = 0 as PlaceholderValue;\n\n  const mergedIterables = mergeAsyncIterables<ChunkData>();\n  function registerAsync(\n    callback: (idx: ChunkIndex) => AsyncIterable<ChunkData, void>,\n  ) {\n    const idx = counter++ as ChunkIndex;\n\n    const iterable = callback(idx);\n    mergedIterables.add(iterable);\n\n    return idx;\n  }\n\n  function encodePromise(promise: Promise<unknown>, path: (string | number)[]) {\n    return registerAsync(async function* (idx) {\n      const error = checkMaxDepth(path);\n      if (error) {\n        // Catch any errors from the original promise to ensure they're reported\n        promise.catch((cause) => {\n          opts.onError?.({ error: cause, path });\n        });\n        // Replace the promise with a rejected one containing the max depth error\n        promise = Promise.reject(error);\n      }\n      try {\n        const next = await promise;\n        yield [idx, PROMISE_STATUS_FULFILLED, encode(next, path)];\n      } catch (cause) {\n        opts.onError?.({ error: cause, path });\n        yield [\n          idx,\n          PROMISE_STATUS_REJECTED,\n          opts.formatError?.({ error: cause, path }),\n        ];\n      }\n    });\n  }\n  function encodeAsyncIterable(\n    iterable: AsyncIterable<unknown>,\n    path: (string | number)[],\n  ) {\n    return registerAsync(async function* (idx) {\n      const error = checkMaxDepth(path);\n      if (error) {\n        throw error;\n      }\n      await using iterator = iteratorResource(iterable);\n\n      try {\n        while (true) {\n          const next = await iterator.next();\n          if (next.done) {\n            yield [idx, ASYNC_ITERABLE_STATUS_RETURN, encode(next.value, path)];\n            break;\n          }\n          yield [idx, ASYNC_ITERABLE_STATUS_YIELD, encode(next.value, path)];\n        }\n      } catch (cause) {\n        opts.onError?.({ error: cause, path });\n\n        yield [\n          idx,\n          ASYNC_ITERABLE_STATUS_ERROR,\n          opts.formatError?.({ error: cause, path }),\n        ];\n      }\n    });\n  }\n  function checkMaxDepth(path: (string | number)[]) {\n    if (opts.maxDepth && path.length > opts.maxDepth) {\n      return new MaxDepthError(path);\n    }\n    return null;\n  }\n  function encodeAsync(\n    value: unknown,\n    path: (string | number)[],\n  ): null | [type: ChunkValueType, chunkId: ChunkIndex] {\n    if (isPromise(value)) {\n      return [CHUNK_VALUE_TYPE_PROMISE, encodePromise(value, path)];\n    }\n    if (isAsyncIterable(value)) {\n      if (opts.maxDepth && path.length >= opts.maxDepth) {\n        throw new Error('Max depth reached');\n      }\n      return [\n        CHUNK_VALUE_TYPE_ASYNC_ITERABLE,\n        encodeAsyncIterable(value, path),\n      ];\n    }\n    return null;\n  }\n  function encode(value: unknown, path: (string | number)[]): EncodedValue {\n    if (value === undefined) {\n      return [[]];\n    }\n    const reg = encodeAsync(value, path);\n    if (reg) {\n      return [[placeholder], [null, ...reg]];\n    }\n\n    if (!isPlainObject(value)) {\n      return [[value]];\n    }\n\n    const newObj: Record<string, unknown> = {};\n    const asyncValues: ChunkDefinition[] = [];\n    for (const [key, item] of Object.entries(value)) {\n      const transformed = encodeAsync(item, [...path, key]);\n      if (!transformed) {\n        newObj[key] = item;\n        continue;\n      }\n      newObj[key] = placeholder;\n      asyncValues.push([key, ...transformed]);\n    }\n    return [[newObj], ...asyncValues];\n  }\n\n  const newHead: Head = {};\n  for (const [key, item] of Object.entries(data)) {\n    newHead[key] = encode(item, [key]);\n  }\n\n  yield newHead;\n\n  let iterable: AsyncIterable<ChunkData | typeof PING_SYM, void> =\n    mergedIterables;\n  if (opts.pingMs) {\n    iterable = withPing(mergedIterables, opts.pingMs);\n  }\n\n  for await (const value of iterable) {\n    yield value;\n  }\n}\n/**\n * JSON Lines stream producer\n * @see https://jsonlines.org/\n */\nexport function jsonlStreamProducer(opts: JSONLProducerOptions) {\n  let stream = readableStreamFrom(createBatchStreamProducer(opts));\n\n  const { serialize } = opts;\n  if (serialize) {\n    stream = stream.pipeThrough(\n      new TransformStream({\n        transform(chunk, controller) {\n          if (chunk === PING_SYM) {\n            controller.enqueue(PING_SYM);\n          } else {\n            controller.enqueue(serialize(chunk));\n          }\n        },\n      }),\n    );\n  }\n\n  return stream\n    .pipeThrough(\n      new TransformStream({\n        transform(chunk, controller) {\n          if (chunk === PING_SYM) {\n            controller.enqueue(' ');\n          } else {\n            controller.enqueue(JSON.stringify(chunk) + '\\n');\n          }\n        },\n      }),\n    )\n    .pipeThrough(new TextEncoderStream());\n}\n\nclass AsyncError extends Error {\n  constructor(public readonly data: unknown) {\n    super('Received error from server');\n  }\n}\nexport type ConsumerOnError = (opts: { error: unknown }) => void;\n\nconst nodeJsStreamToReaderEsque = (source: NodeJSReadableStreamEsque) => {\n  return {\n    getReader() {\n      const stream = new ReadableStream<Uint8Array>({\n        start(controller) {\n          source.on('data', (chunk) => {\n            controller.enqueue(chunk);\n          });\n          source.on('end', () => {\n            controller.close();\n          });\n          source.on('error', (error) => {\n            controller.error(error);\n          });\n        },\n      });\n      return stream.getReader();\n    },\n  };\n};\n\nfunction createLineAccumulator(\n  from: NodeJSReadableStreamEsque | WebReadableStreamEsque,\n) {\n  const reader =\n    'getReader' in from\n      ? from.getReader()\n      : nodeJsStreamToReaderEsque(from).getReader();\n\n  let lineAggregate = '';\n\n  return new ReadableStream({\n    async pull(controller) {\n      const { done, value } = await reader.read();\n\n      if (done) {\n        controller.close();\n      } else {\n        controller.enqueue(value);\n      }\n    },\n    cancel() {\n      return reader.cancel();\n    },\n  })\n    .pipeThrough(new TextDecoderStream())\n    .pipeThrough(\n      new TransformStream<string, string>({\n        transform(chunk, controller) {\n          lineAggregate += chunk;\n          const parts = lineAggregate.split('\\n');\n          lineAggregate = parts.pop() ?? '';\n          for (const part of parts) {\n            controller.enqueue(part);\n          }\n        },\n      }),\n    );\n}\nfunction createConsumerStream<THead>(\n  from: NodeJSReadableStreamEsque | WebReadableStreamEsque,\n) {\n  const stream = createLineAccumulator(from);\n\n  let sentHead = false;\n  return stream.pipeThrough(\n    new TransformStream<string, ChunkData | THead>({\n      transform(line, controller) {\n        if (!sentHead) {\n          const head = JSON.parse(line);\n          controller.enqueue(head as THead);\n          sentHead = true;\n        } else {\n          const chunk: ChunkData = JSON.parse(line);\n          controller.enqueue(chunk);\n        }\n      },\n    }),\n  );\n}\n\n/**\n * Creates a handler for managing stream controllers and their lifecycle\n */\nfunction createStreamsManager(abortController: AbortController) {\n  const controllerMap = new Map<\n    ChunkIndex,\n    ReturnType<typeof createStreamController>\n  >();\n\n  /**\n   * Checks if there are no pending controllers or deferred promises\n   */\n  function isEmpty() {\n    return Array.from(controllerMap.values()).every((c) => c.closed);\n  }\n\n  /**\n   * Creates a stream controller\n   */\n  function createStreamController() {\n    let originalController: ReadableStreamDefaultController<ChunkData>;\n    const stream = new ReadableStream<ChunkData>({\n      start(controller) {\n        originalController = controller;\n      },\n    });\n\n    const streamController = {\n      enqueue: (v: ChunkData) => originalController.enqueue(v),\n      close: () => {\n        originalController.close();\n\n        clear();\n\n        if (isEmpty()) {\n          abortController.abort();\n        }\n      },\n      closed: false,\n      getReaderResource: () => {\n        const reader = stream.getReader();\n\n        return makeResource(reader, () => {\n          reader.releaseLock();\n          streamController.close();\n        });\n      },\n      error: (reason: unknown) => {\n        originalController.error(reason);\n        clear();\n      },\n    };\n    function clear() {\n      Object.assign(streamController, {\n        closed: true,\n        close: () => {\n          // noop\n        },\n        enqueue: () => {\n          // noop\n        },\n        getReaderResource: null,\n        error: () => {\n          // noop\n        },\n      });\n    }\n\n    return streamController;\n  }\n\n  /**\n   * Gets or creates a stream controller\n   */\n  function getOrCreate(chunkId: ChunkIndex) {\n    let c = controllerMap.get(chunkId);\n    if (!c) {\n      c = createStreamController();\n      controllerMap.set(chunkId, c);\n    }\n    return c;\n  }\n\n  /**\n   * Cancels all pending controllers and rejects deferred promises\n   */\n  function cancelAll(reason: unknown) {\n    for (const controller of controllerMap.values()) {\n      controller.error(reason);\n    }\n  }\n\n  return {\n    getOrCreate,\n    isEmpty,\n    cancelAll,\n  };\n}\n\n/**\n * JSON Lines stream consumer\n * @see https://jsonlines.org/\n */\nexport async function jsonlStreamConsumer<THead>(opts: {\n  from: NodeJSReadableStreamEsque | WebReadableStreamEsque;\n  deserialize?: Deserialize;\n  onError?: ConsumerOnError;\n  formatError?: (opts: { error: unknown }) => Error;\n  /**\n   * This `AbortController` will be triggered when there are no more listeners to the stream.\n   */\n  abortController: AbortController;\n}) {\n  const { deserialize = (v) => v } = opts;\n\n  let source = createConsumerStream<Head>(opts.from);\n  if (deserialize) {\n    source = source.pipeThrough(\n      new TransformStream({\n        transform(chunk, controller) {\n          controller.enqueue(deserialize(chunk));\n        },\n      }),\n    );\n  }\n  let headDeferred: null | Deferred<THead> = createDeferred();\n\n  const streamManager = createStreamsManager(opts.abortController);\n\n  function decodeChunkDefinition(value: ChunkDefinition) {\n    const [_path, type, chunkId] = value;\n\n    const controller = streamManager.getOrCreate(chunkId);\n\n    switch (type) {\n      case CHUNK_VALUE_TYPE_PROMISE: {\n        return run(async () => {\n          using reader = controller.getReaderResource();\n\n          const { value } = await reader.read();\n          const [_chunkId, status, data] = value as PromiseChunk;\n          switch (status) {\n            case PROMISE_STATUS_FULFILLED:\n              return decode(data);\n            case PROMISE_STATUS_REJECTED:\n              throw opts.formatError?.({ error: data }) ?? new AsyncError(data);\n          }\n        });\n      }\n      case CHUNK_VALUE_TYPE_ASYNC_ITERABLE: {\n        return run(async function* () {\n          using reader = controller.getReaderResource();\n\n          while (true) {\n            const { value } = await reader.read();\n\n            const [_chunkId, status, data] = value as IterableChunk;\n\n            switch (status) {\n              case ASYNC_ITERABLE_STATUS_YIELD:\n                yield decode(data);\n                break;\n              case ASYNC_ITERABLE_STATUS_RETURN:\n                return decode(data);\n              case ASYNC_ITERABLE_STATUS_ERROR:\n                throw (\n                  opts.formatError?.({ error: data }) ?? new AsyncError(data)\n                );\n            }\n          }\n        });\n      }\n    }\n  }\n\n  function decode(value: EncodedValue): unknown {\n    const [[data], ...asyncProps] = value;\n\n    for (const value of asyncProps) {\n      const [key] = value;\n      const decoded = decodeChunkDefinition(value);\n\n      if (key === null) {\n        return decoded;\n      }\n\n      (data as any)[key] = decoded;\n    }\n    return data;\n  }\n\n  const closeOrAbort = (reason: unknown) => {\n    headDeferred?.reject(reason);\n    streamManager.cancelAll(reason);\n  };\n  source\n    .pipeTo(\n      new WritableStream({\n        write(chunkOrHead) {\n          if (headDeferred) {\n            const head = chunkOrHead as Record<number | string, unknown>;\n\n            for (const [key, value] of Object.entries(chunkOrHead)) {\n              const parsed = decode(value as any);\n              head[key] = parsed;\n            }\n            headDeferred.resolve(head as THead);\n            headDeferred = null;\n\n            return;\n          }\n          const chunk = chunkOrHead as ChunkData;\n          const [idx] = chunk;\n\n          const controller = streamManager.getOrCreate(idx);\n          controller.enqueue(chunk);\n        },\n        close: () => closeOrAbort(new Error('Stream closed')),\n        abort: closeOrAbort,\n      }),\n      {\n        signal: opts.abortController.signal,\n      },\n    )\n    .catch((error) => {\n      opts.onError?.({ error });\n      closeOrAbort(error);\n    });\n\n  return [await headDeferred.promise, streamManager] as const;\n}\n", "var OverloadYield = require(\"./OverloadYield.js\");\nfunction _asyncGeneratorDelegate(t) {\n  var e = {},\n    n = !1;\n  function pump(e, r) {\n    return n = !0, r = new Promise(function (n) {\n      n(t[e](r));\n    }), {\n      done: !1,\n      value: new OverloadYield(r, 1)\n    };\n  }\n  return e[\"undefined\" != typeof Symbol && Symbol.iterator || \"@@iterator\"] = function () {\n    return this;\n  }, e.next = function (t) {\n    return n ? (n = !1, t) : pump(\"next\", t);\n  }, \"function\" == typeof t[\"throw\"] && (e[\"throw\"] = function (t) {\n    if (n) throw n = !1, t;\n    return pump(\"throw\", t);\n  }), \"function\" == typeof t[\"return\"] && (e[\"return\"] = function (t) {\n    return n ? (n = !1, t) : pump(\"return\", t);\n  }), e;\n}\nmodule.exports = _asyncGeneratorDelegate, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import { Unpromise } from '../../vendor/unpromise';\nimport { getTRPCErrorFromUnknown } from '../error/TRPCError';\nimport { isAbortError } from '../http/abortError';\nimport type { MaybePromise } from '../types';\nimport { identity, run } from '../utils';\nimport type { EventSourceLike } from './sse.types';\nimport type { inferTrackedOutput } from './tracked';\nimport { isTrackedEnvelope } from './tracked';\nimport { takeWithGrace, withMaxDuration } from './utils/asyncIterable';\nimport { makeAsyncResource } from './utils/disposable';\nimport { readableStreamFrom } from './utils/readableStreamFrom';\nimport {\n  disposablePromiseTimerResult,\n  timerResource,\n} from './utils/timerResource';\nimport { PING_SYM, withPing } from './utils/withPing';\n\ntype Serialize = (value: any) => any;\ntype Deserialize = (value: any) => any;\n\n/**\n * @internal\n */\nexport interface SSEPingOptions {\n  /**\n   * Enable ping comments sent from the server\n   * @default false\n   */\n  enabled: boolean;\n  /**\n   * Interval in milliseconds\n   * @default 1000\n   */\n  intervalMs?: number;\n}\n\nexport interface SSEClientOptions {\n  /**\n   * Timeout and reconnect after inactivity in milliseconds\n   * @default undefined\n   */\n  reconnectAfterInactivityMs?: number;\n}\n\nexport interface SSEStreamProducerOptions<TValue = unknown> {\n  serialize?: Serialize;\n  data: AsyncIterable<TValue>;\n\n  maxDepth?: number;\n  ping?: SSEPingOptions;\n  /**\n   * Maximum duration in milliseconds for the request before ending the stream\n   * @default undefined\n   */\n  maxDurationMs?: number;\n  /**\n   * End the request immediately after data is sent\n   * Only useful for serverless runtimes that do not support streaming responses\n   * @default false\n   */\n  emitAndEndImmediately?: boolean;\n  formatError?: (opts: { error: unknown }) => unknown;\n  /**\n   * Client-specific options - these will be sent to the client as part of the first message\n   * @default {}\n   */\n  client?: SSEClientOptions;\n}\n\nconst PING_EVENT = 'ping';\nconst SERIALIZED_ERROR_EVENT = 'serialized-error';\nconst CONNECTED_EVENT = 'connected';\nconst RETURN_EVENT = 'return';\n\ninterface SSEvent {\n  id?: string;\n  data: unknown;\n  comment?: string;\n  event?: string;\n}\n/**\n *\n * @see https://html.spec.whatwg.org/multipage/server-sent-events.html\n */\nexport function sseStreamProducer<TValue = unknown>(\n  opts: SSEStreamProducerOptions<TValue>,\n) {\n  const { serialize = identity } = opts;\n\n  const ping: Required<SSEPingOptions> = {\n    enabled: opts.ping?.enabled ?? false,\n    intervalMs: opts.ping?.intervalMs ?? 1000,\n  };\n  const client: SSEClientOptions = opts.client ?? {};\n\n  if (\n    ping.enabled &&\n    client.reconnectAfterInactivityMs &&\n    ping.intervalMs > client.reconnectAfterInactivityMs\n  ) {\n    throw new Error(\n      `Ping interval must be less than client reconnect interval to prevent unnecessary reconnection - ping.intervalMs: ${ping.intervalMs} client.reconnectAfterInactivityMs: ${client.reconnectAfterInactivityMs}`,\n    );\n  }\n\n  async function* generator(): AsyncIterable<SSEvent, void> {\n    yield {\n      event: CONNECTED_EVENT,\n      data: JSON.stringify(client),\n    };\n\n    type TIteratorValue = Awaited<TValue> | typeof PING_SYM;\n\n    let iterable: AsyncIterable<TValue | typeof PING_SYM> = opts.data;\n\n    if (opts.emitAndEndImmediately) {\n      iterable = takeWithGrace(iterable, {\n        count: 1,\n        gracePeriodMs: 1,\n      });\n    }\n\n    if (\n      opts.maxDurationMs &&\n      opts.maxDurationMs > 0 &&\n      opts.maxDurationMs !== Infinity\n    ) {\n      iterable = withMaxDuration(iterable, {\n        maxDurationMs: opts.maxDurationMs,\n      });\n    }\n\n    if (ping.enabled && ping.intervalMs !== Infinity && ping.intervalMs > 0) {\n      iterable = withPing(iterable, ping.intervalMs);\n    }\n\n    // We need those declarations outside the loop for garbage collection reasons. If they were\n    // declared inside, they would not be freed until the next value is present.\n    let value: null | TIteratorValue;\n    let chunk: null | SSEvent;\n\n    for await (value of iterable) {\n      if (value === PING_SYM) {\n        yield { event: PING_EVENT, data: '' };\n        continue;\n      }\n\n      chunk = isTrackedEnvelope(value)\n        ? { id: value[0], data: value[1] }\n        : { data: value };\n\n      chunk.data = JSON.stringify(serialize(chunk.data));\n\n      yield chunk;\n\n      // free up references for garbage collection\n      value = null;\n      chunk = null;\n    }\n  }\n\n  async function* generatorWithErrorHandling(): AsyncIterable<SSEvent, void> {\n    try {\n      yield* generator();\n\n      yield {\n        event: RETURN_EVENT,\n        data: '',\n      };\n    } catch (cause) {\n      if (isAbortError(cause)) {\n        // ignore abort errors, send any other errors\n        return;\n      }\n      // `err` must be caused by `opts.data`, `JSON.stringify` or `serialize`.\n      // So, a user error in any case.\n      const error = getTRPCErrorFromUnknown(cause);\n      const data = opts.formatError?.({ error }) ?? null;\n      yield {\n        event: SERIALIZED_ERROR_EVENT,\n        data: JSON.stringify(serialize(data)),\n      };\n    }\n  }\n\n  const stream = readableStreamFrom(generatorWithErrorHandling());\n\n  return stream\n    .pipeThrough(\n      new TransformStream({\n        transform(chunk, controller: TransformStreamDefaultController<string>) {\n          if ('event' in chunk) {\n            controller.enqueue(`event: ${chunk.event}\\n`);\n          }\n          if ('data' in chunk) {\n            controller.enqueue(`data: ${chunk.data}\\n`);\n          }\n          if ('id' in chunk) {\n            controller.enqueue(`id: ${chunk.id}\\n`);\n          }\n          if ('comment' in chunk) {\n            controller.enqueue(`: ${chunk.comment}\\n`);\n          }\n          controller.enqueue('\\n\\n');\n        },\n      }),\n    )\n    .pipeThrough(new TextEncoderStream());\n}\n\ninterface ConsumerStreamResultBase<TConfig extends ConsumerConfig> {\n  eventSource: InstanceType<TConfig['EventSource']> | null;\n}\n\ninterface ConsumerStreamResultData<TConfig extends ConsumerConfig>\n  extends ConsumerStreamResultBase<TConfig> {\n  type: 'data';\n  data: inferTrackedOutput<TConfig['data']>;\n}\n\ninterface ConsumerStreamResultError<TConfig extends ConsumerConfig>\n  extends ConsumerStreamResultBase<TConfig> {\n  type: 'serialized-error';\n  error: TConfig['error'];\n}\n\ninterface ConsumerStreamResultConnecting<TConfig extends ConsumerConfig>\n  extends ConsumerStreamResultBase<TConfig> {\n  type: 'connecting';\n  event: EventSourceLike.EventOf<TConfig['EventSource']> | null;\n}\ninterface ConsumerStreamResultTimeout<TConfig extends ConsumerConfig>\n  extends ConsumerStreamResultBase<TConfig> {\n  type: 'timeout';\n  ms: number;\n}\ninterface ConsumerStreamResultPing<TConfig extends ConsumerConfig>\n  extends ConsumerStreamResultBase<TConfig> {\n  type: 'ping';\n}\n\ninterface ConsumerStreamResultConnected<TConfig extends ConsumerConfig>\n  extends ConsumerStreamResultBase<TConfig> {\n  type: 'connected';\n  options: SSEClientOptions;\n}\n\ntype ConsumerStreamResult<TConfig extends ConsumerConfig> =\n  | ConsumerStreamResultData<TConfig>\n  | ConsumerStreamResultError<TConfig>\n  | ConsumerStreamResultConnecting<TConfig>\n  | ConsumerStreamResultTimeout<TConfig>\n  | ConsumerStreamResultPing<TConfig>\n  | ConsumerStreamResultConnected<TConfig>;\n\nexport interface SSEStreamConsumerOptions<TConfig extends ConsumerConfig> {\n  url: () => MaybePromise<string>;\n  init: () =>\n    | MaybePromise<EventSourceLike.InitDictOf<TConfig['EventSource']>>\n    | undefined;\n  signal: AbortSignal;\n  deserialize?: Deserialize;\n  EventSource: TConfig['EventSource'];\n}\n\ninterface ConsumerConfig {\n  data: unknown;\n  error: unknown;\n  EventSource: EventSourceLike.AnyConstructor;\n}\n\nasync function withTimeout<T>(opts: {\n  promise: Promise<T>;\n  timeoutMs: number;\n  onTimeout: () => Promise<NoInfer<T>>;\n}): Promise<T> {\n  using timeoutPromise = timerResource(opts.timeoutMs);\n  const res = await Unpromise.race([opts.promise, timeoutPromise.start()]);\n\n  if (res === disposablePromiseTimerResult) {\n    return await opts.onTimeout();\n  }\n  return res;\n}\n\n/**\n * @see https://html.spec.whatwg.org/multipage/server-sent-events.html\n */\nexport function sseStreamConsumer<TConfig extends ConsumerConfig>(\n  opts: SSEStreamConsumerOptions<TConfig>,\n): AsyncIterable<ConsumerStreamResult<TConfig>> {\n  const { deserialize = (v) => v } = opts;\n\n  let clientOptions: SSEClientOptions = {};\n\n  const signal = opts.signal;\n\n  let _es: InstanceType<TConfig['EventSource']> | null = null;\n\n  const createStream = () =>\n    new ReadableStream<ConsumerStreamResult<TConfig>>({\n      async start(controller) {\n        const [url, init] = await Promise.all([opts.url(), opts.init()]);\n        const eventSource = (_es = new opts.EventSource(\n          url,\n          init,\n        ) as InstanceType<TConfig['EventSource']>);\n\n        controller.enqueue({\n          type: 'connecting',\n          eventSource: _es,\n          event: null,\n        });\n\n        eventSource.addEventListener(CONNECTED_EVENT, (_msg) => {\n          const msg = _msg as EventSourceLike.MessageEvent;\n\n          const options: SSEClientOptions = JSON.parse(msg.data);\n\n          clientOptions = options;\n          controller.enqueue({\n            type: 'connected',\n            options,\n            eventSource,\n          });\n        });\n\n        eventSource.addEventListener(SERIALIZED_ERROR_EVENT, (_msg) => {\n          const msg = _msg as EventSourceLike.MessageEvent;\n\n          controller.enqueue({\n            type: 'serialized-error',\n            error: deserialize(JSON.parse(msg.data)),\n            eventSource,\n          });\n        });\n        eventSource.addEventListener(PING_EVENT, () => {\n          controller.enqueue({\n            type: 'ping',\n            eventSource,\n          });\n        });\n        eventSource.addEventListener(RETURN_EVENT, () => {\n          eventSource.close();\n          controller.close();\n          _es = null;\n        });\n        eventSource.addEventListener('error', (event) => {\n          if (eventSource.readyState === eventSource.CLOSED) {\n            controller.error(event);\n          } else {\n            controller.enqueue({\n              type: 'connecting',\n              eventSource,\n              event,\n            });\n          }\n        });\n        eventSource.addEventListener('message', (_msg) => {\n          const msg = _msg as EventSourceLike.MessageEvent;\n\n          const chunk = deserialize(JSON.parse(msg.data));\n\n          const def: SSEvent = {\n            data: chunk,\n          };\n          if (msg.lastEventId) {\n            def.id = msg.lastEventId;\n          }\n          controller.enqueue({\n            type: 'data',\n            data: def as inferTrackedOutput<TConfig['data']>,\n            eventSource,\n          });\n        });\n\n        const onAbort = () => {\n          try {\n            eventSource.close();\n            controller.close();\n          } catch {\n            // ignore errors in case the controller is already closed\n          }\n        };\n        if (signal.aborted) {\n          onAbort();\n        } else {\n          signal.addEventListener('abort', onAbort);\n        }\n      },\n      cancel() {\n        _es?.close();\n      },\n    });\n\n  const getStreamResource = () => {\n    let stream = createStream();\n    let reader = stream.getReader();\n\n    async function dispose() {\n      await reader.cancel();\n      _es = null;\n    }\n\n    return makeAsyncResource(\n      {\n        read() {\n          return reader.read();\n        },\n        async recreate() {\n          await dispose();\n\n          stream = createStream();\n          reader = stream.getReader();\n        },\n      },\n      dispose,\n    );\n  };\n\n  return run(async function* () {\n    await using stream = getStreamResource();\n\n    while (true) {\n      let promise = stream.read();\n\n      const timeoutMs = clientOptions.reconnectAfterInactivityMs;\n      if (timeoutMs) {\n        promise = withTimeout({\n          promise,\n          timeoutMs,\n          onTimeout: async () => {\n            const res: Awaited<typeof promise> = {\n              value: {\n                type: 'timeout',\n                ms: timeoutMs,\n                eventSource: _es,\n              },\n              done: false,\n            };\n            // Close and release old reader\n            await stream.recreate();\n\n            return res;\n          },\n        });\n      }\n\n      const result = await promise;\n\n      if (result.done) {\n        return result.value;\n      }\n      yield result.value;\n    }\n  });\n}\n\nexport const sseHeaders = {\n  'Content-Type': 'text/event-stream',\n  'Cache-Control': 'no-cache, no-transform',\n  'X-Accel-Buffering': 'no',\n  Connection: 'keep-alive',\n} as const;\n", "/* eslint-disable @typescript-eslint/no-non-null-assertion */\nimport {\n  isObservable,\n  observableToAsyncIterable,\n} from '../../observable/observable';\nimport { getErrorShape } from '../error/getErrorShape';\nimport { getTRPCErrorFromUnknown, TRPCError } from '../error/TRPCError';\nimport type { ProcedureType } from '../procedure';\nimport {\n  type AnyRouter,\n  type inferRouterContext,\n  type inferRouterError,\n} from '../router';\nimport type { TRPCResponse } from '../rpc';\nimport { isPromise, jsonlStreamProducer } from '../stream/jsonl';\nimport { sseHeaders, sseStreamProducer } from '../stream/sse';\nimport { transformTRPCResponse } from '../transformer';\nimport { isAsyncIterable, isObject, run } from '../utils';\nimport { getRequestInfo } from './contentType';\nimport { getHTTPStatusCode } from './getHTTPStatusCode';\nimport type {\n  HTTPBaseHandlerOptions,\n  ResolveHTTPRequestOptionsContextFn,\n  TRPCRequestInfo,\n} from './types';\n\nfunction errorToAsyncIterable(err: TRPCError): AsyncIterable<never> {\n  return run(async function* () {\n    throw err;\n  });\n}\ntype HTTPMethods =\n  | 'GET'\n  | 'POST'\n  | 'HEAD'\n  | 'OPTIONS'\n  | 'PUT'\n  | 'DELETE'\n  | 'PATCH';\n\nconst TYPE_ACCEPTED_METHOD_MAP: Record<ProcedureType, HTTPMethods[]> = {\n  mutation: ['POST'],\n  query: ['GET'],\n  subscription: ['GET'],\n};\nconst TYPE_ACCEPTED_METHOD_MAP_WITH_METHOD_OVERRIDE: Record<\n  ProcedureType,\n  HTTPMethods[]\n> = {\n  // never allow GET to do a mutation\n  mutation: ['POST'],\n  query: ['GET', 'POST'],\n  subscription: ['GET', 'POST'],\n};\n\ninterface ResolveHTTPRequestOptions<TRouter extends AnyRouter>\n  extends HTTPBaseHandlerOptions<TRouter, Request> {\n  createContext: ResolveHTTPRequestOptionsContextFn<TRouter>;\n  req: Request;\n  path: string;\n  /**\n   * If the request had an issue before reaching the handler\n   */\n  error: TRPCError | null;\n}\n\nfunction initResponse<TRouter extends AnyRouter, TRequest>(initOpts: {\n  ctx: inferRouterContext<TRouter> | undefined;\n  info: TRPCRequestInfo | undefined;\n  responseMeta?: HTTPBaseHandlerOptions<TRouter, TRequest>['responseMeta'];\n  untransformedJSON:\n    | TRPCResponse<unknown, inferRouterError<TRouter>>\n    | TRPCResponse<unknown, inferRouterError<TRouter>>[]\n    | null;\n  errors: TRPCError[];\n  headers: Headers;\n}) {\n  const {\n    ctx,\n    info,\n    responseMeta,\n    untransformedJSON,\n    errors = [],\n    headers,\n  } = initOpts;\n\n  let status = untransformedJSON ? getHTTPStatusCode(untransformedJSON) : 200;\n\n  const eagerGeneration = !untransformedJSON;\n  const data = eagerGeneration\n    ? []\n    : Array.isArray(untransformedJSON)\n      ? untransformedJSON\n      : [untransformedJSON];\n\n  const meta =\n    responseMeta?.({\n      ctx,\n      info,\n      paths: info?.calls.map((call) => call.path),\n      data,\n      errors,\n      eagerGeneration,\n      type:\n        info?.calls.find((call) => call.procedure?._def.type)?.procedure?._def\n          .type ?? 'unknown',\n    }) ?? {};\n\n  if (meta.headers) {\n    if (meta.headers instanceof Headers) {\n      for (const [key, value] of meta.headers.entries()) {\n        headers.append(key, value);\n      }\n    } else {\n      /**\n       * @deprecated, delete in v12\n       */\n      for (const [key, value] of Object.entries(meta.headers)) {\n        if (Array.isArray(value)) {\n          for (const v of value) {\n            headers.append(key, v);\n          }\n        } else if (typeof value === 'string') {\n          headers.set(key, value);\n        }\n      }\n    }\n  }\n  if (meta.status) {\n    status = meta.status;\n  }\n\n  return {\n    status,\n  };\n}\n\nfunction caughtErrorToData<TRouter extends AnyRouter>(\n  cause: unknown,\n  errorOpts: {\n    opts: Pick<\n      ResolveHTTPRequestOptions<TRouter>,\n      'onError' | 'req' | 'router'\n    >;\n    ctx: inferRouterContext<TRouter> | undefined;\n    type: ProcedureType | 'unknown';\n    path?: string;\n    input?: unknown;\n  },\n) {\n  const { router, req, onError } = errorOpts.opts;\n  const error = getTRPCErrorFromUnknown(cause);\n  onError?.({\n    error,\n    path: errorOpts.path,\n    input: errorOpts.input,\n    ctx: errorOpts.ctx,\n    type: errorOpts.type,\n    req,\n  });\n  const untransformedJSON = {\n    error: getErrorShape({\n      config: router._def._config,\n      error,\n      type: errorOpts.type,\n      path: errorOpts.path,\n      input: errorOpts.input,\n      ctx: errorOpts.ctx,\n    }),\n  };\n  const transformedJSON = transformTRPCResponse(\n    router._def._config,\n    untransformedJSON,\n  );\n  const body = JSON.stringify(transformedJSON);\n  return {\n    error,\n    untransformedJSON,\n    body,\n  };\n}\n\n/**\n * Check if a value is a stream-like object\n * - if it's an async iterable\n * - if it's an object with async iterables or promises\n */\nfunction isDataStream(v: unknown) {\n  if (!isObject(v)) {\n    return false;\n  }\n\n  if (isAsyncIterable(v)) {\n    return true;\n  }\n\n  return (\n    Object.values(v).some(isPromise) || Object.values(v).some(isAsyncIterable)\n  );\n}\n\ntype ResultTuple<T> = [undefined, T] | [TRPCError, undefined];\n\nexport async function resolveResponse<TRouter extends AnyRouter>(\n  opts: ResolveHTTPRequestOptions<TRouter>,\n): Promise<Response> {\n  const { router, req } = opts;\n  const headers = new Headers([['vary', 'trpc-accept']]);\n  const config = router._def._config;\n\n  const url = new URL(req.url);\n\n  if (req.method === 'HEAD') {\n    // can be used for lambda warmup\n    return new Response(null, {\n      status: 204,\n    });\n  }\n\n  const allowBatching = opts.allowBatching ?? opts.batching?.enabled ?? true;\n  const allowMethodOverride =\n    (opts.allowMethodOverride ?? false) && req.method === 'POST';\n\n  type $Context = inferRouterContext<TRouter>;\n\n  const infoTuple: ResultTuple<TRPCRequestInfo> = await run(async () => {\n    try {\n      return [\n        undefined,\n        await getRequestInfo({\n          req,\n          path: decodeURIComponent(opts.path),\n          router,\n          searchParams: url.searchParams,\n          headers: opts.req.headers,\n          url,\n        }),\n      ];\n    } catch (cause) {\n      return [getTRPCErrorFromUnknown(cause), undefined];\n    }\n  });\n\n  interface ContextManager {\n    valueOrUndefined: () => $Context | undefined;\n    value: () => $Context;\n    create: (info: TRPCRequestInfo) => Promise<void>;\n  }\n  const ctxManager: ContextManager = run(() => {\n    let result: ResultTuple<$Context> | undefined = undefined;\n    return {\n      valueOrUndefined: () => {\n        if (!result) {\n          return undefined;\n        }\n        return result[1];\n      },\n      value: () => {\n        const [err, ctx] = result!;\n        if (err) {\n          throw err;\n        }\n        return ctx;\n      },\n      create: async (info) => {\n        if (result) {\n          throw new Error(\n            'This should only be called once - report a bug in tRPC',\n          );\n        }\n        try {\n          const ctx = await opts.createContext({\n            info,\n          });\n          result = [undefined, ctx];\n        } catch (cause) {\n          result = [getTRPCErrorFromUnknown(cause), undefined];\n        }\n      },\n    };\n  });\n\n  const methodMapper = allowMethodOverride\n    ? TYPE_ACCEPTED_METHOD_MAP_WITH_METHOD_OVERRIDE\n    : TYPE_ACCEPTED_METHOD_MAP;\n\n  /**\n   * @deprecated\n   */\n  const isStreamCall = req.headers.get('trpc-accept') === 'application/jsonl';\n\n  const experimentalSSE = config.sse?.enabled ?? true;\n  try {\n    const [infoError, info] = infoTuple;\n    if (infoError) {\n      throw infoError;\n    }\n    if (info.isBatchCall && !allowBatching) {\n      throw new TRPCError({\n        code: 'BAD_REQUEST',\n        message: `Batching is not enabled on the server`,\n      });\n    }\n    /* istanbul ignore if -- @preserve */\n    if (isStreamCall && !info.isBatchCall) {\n      throw new TRPCError({\n        message: `Streaming requests must be batched (you can do a batch of 1)`,\n        code: 'BAD_REQUEST',\n      });\n    }\n    await ctxManager.create(info);\n\n    interface RPCResultOk {\n      data: unknown;\n    }\n    type RPCResult = ResultTuple<RPCResultOk>;\n    const rpcCalls = info.calls.map(async (call): Promise<RPCResult> => {\n      const proc = call.procedure;\n      try {\n        if (opts.error) {\n          throw opts.error;\n        }\n\n        if (!proc) {\n          throw new TRPCError({\n            code: 'NOT_FOUND',\n            message: `No procedure found on path \"${call.path}\"`,\n          });\n        }\n\n        if (!methodMapper[proc._def.type].includes(req.method as HTTPMethods)) {\n          throw new TRPCError({\n            code: 'METHOD_NOT_SUPPORTED',\n            message: `Unsupported ${req.method}-request to ${proc._def.type} procedure at path \"${call.path}\"`,\n          });\n        }\n\n        if (proc._def.type === 'subscription') {\n          /* istanbul ignore if -- @preserve */\n          if (info.isBatchCall) {\n            throw new TRPCError({\n              code: 'BAD_REQUEST',\n              message: `Cannot batch subscription calls`,\n            });\n          }\n        }\n        const data: unknown = await proc({\n          path: call.path,\n          getRawInput: call.getRawInput,\n          ctx: ctxManager.value(),\n          type: proc._def.type,\n          signal: opts.req.signal,\n        });\n        return [undefined, { data }];\n      } catch (cause) {\n        const error = getTRPCErrorFromUnknown(cause);\n        const input = call.result();\n\n        opts.onError?.({\n          error,\n          path: call.path,\n          input,\n          ctx: ctxManager.valueOrUndefined(),\n          type: call.procedure?._def.type ?? 'unknown',\n          req: opts.req,\n        });\n\n        return [error, undefined];\n      }\n    });\n\n    // ----------- response handlers -----------\n    if (!info.isBatchCall) {\n      const [call] = info.calls;\n      const [error, result] = await rpcCalls[0]!;\n\n      switch (info.type) {\n        case 'unknown':\n        case 'mutation':\n        case 'query': {\n          // httpLink\n          headers.set('content-type', 'application/json');\n\n          if (isDataStream(result?.data)) {\n            throw new TRPCError({\n              code: 'UNSUPPORTED_MEDIA_TYPE',\n              message:\n                'Cannot use stream-like response in non-streaming request - use httpBatchStreamLink',\n            });\n          }\n          const res: TRPCResponse<unknown, inferRouterError<TRouter>> = error\n            ? {\n                error: getErrorShape({\n                  config,\n                  ctx: ctxManager.valueOrUndefined(),\n                  error,\n                  input: call!.result(),\n                  path: call!.path,\n                  type: info.type,\n                }),\n              }\n            : { result: { data: result.data } };\n\n          const headResponse = initResponse({\n            ctx: ctxManager.valueOrUndefined(),\n            info,\n            responseMeta: opts.responseMeta,\n            errors: error ? [error] : [],\n            headers,\n            untransformedJSON: [res],\n          });\n          return new Response(\n            JSON.stringify(transformTRPCResponse(config, res)),\n            {\n              status: headResponse.status,\n              headers,\n            },\n          );\n        }\n        case 'subscription': {\n          // httpSubscriptionLink\n\n          const iterable: AsyncIterable<unknown> = run(() => {\n            if (error) {\n              return errorToAsyncIterable(error);\n            }\n            if (!experimentalSSE) {\n              return errorToAsyncIterable(\n                new TRPCError({\n                  code: 'METHOD_NOT_SUPPORTED',\n                  message: 'Missing experimental flag \"sseSubscriptions\"',\n                }),\n              );\n            }\n\n            if (!isObservable(result.data) && !isAsyncIterable(result.data)) {\n              return errorToAsyncIterable(\n                new TRPCError({\n                  message: `Subscription ${\n                    call!.path\n                  } did not return an observable or a AsyncGenerator`,\n                  code: 'INTERNAL_SERVER_ERROR',\n                }),\n              );\n            }\n            const dataAsIterable = isObservable(result.data)\n              ? observableToAsyncIterable(result.data, opts.req.signal)\n              : result.data;\n            return dataAsIterable;\n          });\n\n          const stream = sseStreamProducer({\n            ...config.sse,\n            data: iterable,\n            serialize: (v) => config.transformer.output.serialize(v),\n            formatError(errorOpts) {\n              const error = getTRPCErrorFromUnknown(errorOpts.error);\n              const input = call?.result();\n              const path = call?.path;\n              const type = call?.procedure?._def.type ?? 'unknown';\n\n              opts.onError?.({\n                error,\n                path,\n                input,\n                ctx: ctxManager.valueOrUndefined(),\n                req: opts.req,\n                type,\n              });\n\n              const shape = getErrorShape({\n                config,\n                ctx: ctxManager.valueOrUndefined(),\n                error,\n                input,\n                path,\n                type,\n              });\n\n              return shape;\n            },\n          });\n          for (const [key, value] of Object.entries(sseHeaders)) {\n            headers.set(key, value);\n          }\n\n          const headResponse = initResponse({\n            ctx: ctxManager.valueOrUndefined(),\n            info,\n            responseMeta: opts.responseMeta,\n            errors: [],\n            headers,\n            untransformedJSON: null,\n          });\n\n          return new Response(stream, {\n            headers,\n            status: headResponse.status,\n          });\n        }\n      }\n    }\n\n    // batch response handlers\n    if (info.accept === 'application/jsonl') {\n      // httpBatchStreamLink\n      headers.set('content-type', 'application/json');\n      headers.set('transfer-encoding', 'chunked');\n      const headResponse = initResponse({\n        ctx: ctxManager.valueOrUndefined(),\n        info,\n        responseMeta: opts.responseMeta,\n        errors: [],\n        headers,\n        untransformedJSON: null,\n      });\n      const stream = jsonlStreamProducer({\n        ...config.jsonl,\n        /**\n         * Example structure for `maxDepth: 4`:\n         * {\n         *   // 1\n         *   0: {\n         *     // 2\n         *     result: {\n         *       // 3\n         *       data: // 4\n         *     }\n         *   }\n         * }\n         */\n        maxDepth: Infinity,\n        data: rpcCalls.map(async (res) => {\n          const [error, result] = await res;\n\n          const call = info.calls[0];\n\n          if (error) {\n            return {\n              error: getErrorShape({\n                config,\n                ctx: ctxManager.valueOrUndefined(),\n                error,\n                input: call!.result(),\n                path: call!.path,\n                type: call!.procedure?._def.type ?? 'unknown',\n              }),\n            };\n          }\n\n          /**\n           * Not very pretty, but we need to wrap nested data in promises\n           * Our stream producer will only resolve top-level async values or async values that are directly nested in another async value\n           */\n          const iterable = isObservable(result.data)\n            ? observableToAsyncIterable(result.data, opts.req.signal)\n            : Promise.resolve(result.data);\n          return {\n            result: Promise.resolve({\n              data: iterable,\n            }),\n          };\n        }),\n        serialize: config.transformer.output.serialize,\n        onError: (cause) => {\n          opts.onError?.({\n            error: getTRPCErrorFromUnknown(cause),\n            path: undefined,\n            input: undefined,\n            ctx: ctxManager.valueOrUndefined(),\n            req: opts.req,\n            type: info?.type ?? 'unknown',\n          });\n        },\n\n        formatError(errorOpts) {\n          const call = info?.calls[errorOpts.path[0] as any];\n\n          const error = getTRPCErrorFromUnknown(errorOpts.error);\n          const input = call?.result();\n          const path = call?.path;\n          const type = call?.procedure?._def.type ?? 'unknown';\n\n          // no need to call `onError` here as it will be propagated through the stream itself\n\n          const shape = getErrorShape({\n            config,\n            ctx: ctxManager.valueOrUndefined(),\n            error,\n            input,\n            path,\n            type,\n          });\n\n          return shape;\n        },\n      });\n\n      return new Response(stream, {\n        headers,\n        status: headResponse.status,\n      });\n    }\n\n    // httpBatchLink\n    /**\n     * Non-streaming response:\n     * - await all responses in parallel, blocking on the slowest one\n     * - create headers with known response body\n     * - return a complete HTTPResponse\n     */\n    headers.set('content-type', 'application/json');\n    const results: RPCResult[] = (await Promise.all(rpcCalls)).map(\n      (res): RPCResult => {\n        const [error, result] = res;\n        if (error) {\n          return res;\n        }\n\n        if (isDataStream(result.data)) {\n          return [\n            new TRPCError({\n              code: 'UNSUPPORTED_MEDIA_TYPE',\n              message:\n                'Cannot use stream-like response in non-streaming request - use httpBatchStreamLink',\n            }),\n            undefined,\n          ];\n        }\n        return res;\n      },\n    );\n    const resultAsRPCResponse = results.map(\n      (\n        [error, result],\n        index,\n      ): TRPCResponse<unknown, inferRouterError<TRouter>> => {\n        const call = info.calls[index]!;\n        if (error) {\n          return {\n            error: getErrorShape({\n              config,\n              ctx: ctxManager.valueOrUndefined(),\n              error,\n              input: call.result(),\n              path: call.path,\n              type: call.procedure?._def.type ?? 'unknown',\n            }),\n          };\n        }\n        return {\n          result: { data: result.data },\n        };\n      },\n    );\n\n    const errors = results\n      .map(([error]) => error)\n      .filter(Boolean) as TRPCError[];\n\n    const headResponse = initResponse({\n      ctx: ctxManager.valueOrUndefined(),\n      info,\n      responseMeta: opts.responseMeta,\n      untransformedJSON: resultAsRPCResponse,\n      errors,\n      headers,\n    });\n\n    return new Response(\n      JSON.stringify(transformTRPCResponse(config, resultAsRPCResponse)),\n      {\n        status: headResponse.status,\n        headers,\n      },\n    );\n  } catch (cause) {\n    const [_infoError, info] = infoTuple;\n    const ctx = ctxManager.valueOrUndefined();\n    // we get here if\n    // - batching is called when it's not enabled\n    // - `createContext()` throws\n    // - `router._def._config.transformer.output.serialize()` throws\n    // - post body is too large\n    // - input deserialization fails\n    // - `errorFormatter` return value is malformed\n    const { error, untransformedJSON, body } = caughtErrorToData(cause, {\n      opts,\n      ctx: ctxManager.valueOrUndefined(),\n      type: info?.type ?? 'unknown',\n    });\n\n    const headResponse = initResponse({\n      ctx,\n      info,\n      responseMeta: opts.responseMeta,\n      untransformedJSON,\n      errors: [error],\n      headers,\n    });\n\n    return new Response(body, {\n      status: headResponse.status,\n      headers,\n    });\n  }\n}\n"], "names": ["parsed: unknown", "str: string", "fn: () => Promise<TReturn>", "promise: Promise<TReturn> | null", "value: TReturn | typeof sym", "jsonContentTypeHandler: ContentTypeHandler", "inputs: unknown", "acc: InputRecord", "type: ProcedureType | 'unknown'", "info: TRPCRequestInfo", "formDataContentTypeHandler: ContentTypeHandler", "octetStreamContentTypeHandler: ContentTypeHandler", "req: Request", "handler", "opts: GetRequestInfoOptions", "error: unknown", "arg: Promise<T> | PromiseLike<T> | PromiseExecutor<T>", "promise: Promise<T>", "unsubscribe: () => void", "onfulfilled?:\n      | ((value: T) => TResult1 | PromiseLike<TResult1>)\n      | null", "onrejected?:\n      | ((reason: any) => TResult2 | PromiseLike<TResult2>)\n      | null", "onrejected?:\n      | ((reason: any) => TResult | PromiseLike<TResult>)\n      | null", "onfinally?: (() => void) | null", "promise: Promise<PERSON>ike<T>", "value: T | PromiseLike<T>", "values: Iterable<T | PromiseLike<T>>", "promises: readonly <PERSON><PERSON><PERSON><PERSON>[]", "promise: T<PERSON><PERSON><PERSON>", "resolve!: PromiseWithResolvers<T>[\"resolve\"]", "reject!: PromiseWithResolvers<T>[\"reject\"]", "arr: readonly T[]", "member: T", "index: number", "member: unknown", "thing: T", "dispose: () => void", "dispose: () => Promise<void>", "ms: number", "timer: ReturnType<typeof setTimeout> | null", "r", "e", "n", "o", "OverloadYield", "_awaitAsyncGenerator", "OverloadYield", "_wrapAsyncGenerator", "r", "t", "e", "iterable: AsyncIterable<T<PERSON>ield, TReturn, TNext>", "iterable: AsyncIterable<T>", "opts: { maxDurationMs: number }", "result: null | IteratorResult<T> | typeof disposablePromiseTimerResult", "opts: {\n    count: number;\n    gracePeriodMs: number;\n  }", "resolve: (value: TValue) => void", "reject: (error: unknown) => void", "iterable: AsyncIterable<TY<PERSON>, TReturn>", "onResult: (result: ManagedIteratorResult<TYield, TReturn>) => void", "state: 'idle' | 'pending' | 'done'", "iterables: AsyncIterable<TYield, void, unknown>[]", "buffer: <PERSON><PERSON>y<\n    [\n      iterator: ManagedIterator<TYield, void>,\n      result: Exclude<\n        ManagedIteratorResult<TYield, void>,\n        { status: 'return' }\n      >,\n    ]\n  >", "iterable: AsyncIterable<TYield, void, unknown>", "errors: unknown[]", "iterable: AsyncIterable<TYield, void>", "iterable: AsyncIterable<TValue>", "pingIntervalMs: number", "result:\n    | null\n    | IteratorResult<TValue>\n    | typeof disposablePromiseTimerResult", "_asyncIterator", "r", "AsyncFromSyncIterator", "value: unknown", "path: (string | number)[]", "opts: JSONLProducerOptions", "callback: (idx: ChunkIndex) => AsyncIterable<ChunkData, void>", "iterable", "promise: Promise<unknown>", "iterable: AsyncIterable<unknown>", "newObj: Record<string, unknown>", "asyncValues: ChunkDefinition[]", "newHead: Head", "iterable: AsyncIterable<ChunkData | typeof PING_SYM, void>", "data: unknown", "source: NodeJSReadableStreamEsque", "from: NodeJSReadableStreamEsque | WebReadableStreamEsque", "chunk: ChunkData", "abortController: AbortController", "originalController: ReadableStreamDefaultController<ChunkData>", "v: ChunkData", "reason: unknown", "chunkId: ChunkIndex", "opts: {\n  from: NodeJSReadableStreamEsque | WebReadableStreamEsque;\n  deserialize?: Deserialize;\n  onError?: ConsumerOnError;\n  formatError?: (opts: { error: unknown }) => Error;\n  /**\n   * This `AbortController` will be triggered when there are no more listeners to the stream.\n   */\n  abortController: AbortController;\n}", "headDeferred: null | Deferred<THead>", "value: ChunkDefinition", "value", "value: EncodedValue", "_asyncGeneratorDelegate", "e", "n", "t", "opts: SSEStreamProducerOptions<TValue>", "ping: Required<SSEPingOptions>", "client: SSEClientOptions", "iterable: AsyncIterable<TValue | typeof PING_SYM>", "value: null | TIteratorValue", "chunk: null | SSEvent", "controller: TransformStreamDefaultController<string>", "opts: {\n  promise: Promise<T>;\n  timeoutMs: number;\n  onTimeout: () => Promise<NoInfer<T>>;\n}", "opts: SSEStreamConsumerOptions<TConfig>", "clientOptions: SSEClientOptions", "_es: InstanceType<TConfig['EventSource']> | null", "options: SSEClientOptions", "def: <PERSON><PERSON><PERSON>", "res: Awaited<typeof promise>", "err: TRPCError", "TYPE_ACCEPTED_METHOD_MAP: Record<ProcedureType, HTTPMethods[]>", "TYPE_ACCEPTED_METHOD_MAP_WITH_METHOD_OVERRIDE: Record<\n  ProcedureType,\n  HTTPMethods[]\n>", "initOpts: {\n  ctx: inferRouterContext<TRouter> | undefined;\n  info: TRPCRequestInfo | undefined;\n  responseMeta?: HTTPBaseHandlerOptions<TRouter, TRequest>['responseMeta'];\n  untransformedJSON:\n    | TRPCResponse<unknown, inferRouterError<TRouter>>\n    | TRPCResponse<unknown, inferRouterError<TRouter>>[]\n    | null;\n  errors: TRPCError[];\n  headers: Headers;\n}", "cause: unknown", "errorOpts: {\n    opts: Pick<\n      ResolveHTTPRequestOptions<TRouter>,\n      'onError' | 'req' | 'router'\n    >;\n    ctx: inferRouterContext<TRouter> | undefined;\n    type: ProcedureType | 'unknown';\n    path?: string;\n    input?: unknown;\n  }", "v: unknown", "opts: ResolveHTTPRequestOptions<TRouter>", "infoTuple: ResultTuple<TRPCRequestInfo>", "ctxManager: ContextManager", "result: ResultTuple<$Context> | undefined", "data: unknown", "res: TRPCResponse<unknown, inferRouterError<TRouter>>", "headResponse", "iterable: AsyncIterable<unknown>", "error", "results: RPCResult[]"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,SAAgB,iCACdA,MAAAA,EACqC;IACrC,IAAI;QACF,IAAI,WAAW,KACb,CAAA,OAAO;QAET,IAAA,KAAK,kQAAA,EAAS,OAAO,CACnB,CAAA,MAAM,IAAI,MAAM;QAElB,MAAM,kBAAkB,OAAO,OAAA,CAAQ,OAAO,CAAC,MAAA,CAC7C,CAAC,CAAC,MAAM,MAAM,GAAA,OAAY,UAAU,SACrC;QAED,IAAI,gBAAgB,MAAA,GAAS,EAC3B,CAAA,MAAM,IAAI,MAAA,CACP,mDAAA,EAAqD,gBACnD,GAAA,CAAI,CAAC,CAAC,KAAK,MAAM,GAAA,CAAM,EAAE,IAAI,EAAA,EAAA,OAAW,MAAM,CAAA,CAAE,CAChD,IAAA,CAAK,KAAK,CAAC,CAAA;QAGlB,OAAO;IACR,EAAA,OAAQ,OAAO;QACd,MAAM,6PAAI,YAAA,CAAU;YAClB,MAAM;YACN,SAAS;YACT;QACD;IACF;AACF;AACD,SAAgB,gCACdC,GAAAA,EACqC;IACrC,IAAID;IACJ,IAAI;QACF,SAAS,KAAK,KAAA,CAAM,IAAI;IACzB,EAAA,OAAQ,OAAO;QACd,MAAM,6PAAI,YAAA,CAAU;YAClB,MAAM;YACN,SAAS;YACT;QACD;IACF;IACD,OAAO,iCAAiC,OAAO;AAChD;;;;;;;GCvBD,SAAS,KAAcE,EAAAA,EAA4B;IACjD,IAAIC,UAAmC;IACvC,MAAM,MAAM,OAAO,GAAA,CAAI,yBAAyB;IAChD,IAAIC,QAA8B;IAClC,OAAO;QAIL,MAAM,YAA8B;;YAClC,IAAI,UAAU,IACZ,CAAA,OAAO;YAIT,CAAA,WAAA,OAAA,MAAA,QAAA,aAAA,KAAA,KAAA,CAAA,UAAY,IAAI,CAAC,KAAA,CAAM,CAAC,UAAU;gBAChC,IAAI,0QAAiB,YAAA,CACnB,CAAA,MAAM;gBAER,MAAM,6PAAI,YAAA,CAAU;oBAClB,MAAM;oBACN,SAAS,iBAAiB,QAAQ,MAAM,OAAA,GAAU;oBAClD;gBACD;YACF,EAAC;YAEF,QAAQ,MAAM;YACd,UAAU;YAEV,OAAO;QACR;QAID,QAAQ,MAA2B;YACjC,OAAO,UAAU,MAAM,QAAA,KAAA;QACxB;IACF;AACF;AAED,MAAMC,yBAA6C;IACjD,SAAQ,GAAA,EAAK;;QACX,OAAA,CAAA,CAAA,CAAA,CAAA,mBAAS,IAAI,OAAA,CAAQ,GAAA,CAAI,eAAe,MAAA,QAAA,qBAAA,KAAA,IAAA,KAAA,IAA/B,iBAAiC,UAAA,CAAW,mBAAmB;IACzE;IACD,MAAM,OAAM,IAAA,EAAM;;QAChB,MAAM,EAAE,GAAA,EAAK,GAAG;QAChB,MAAM,cAAc,KAAK,YAAA,CAAa,GAAA,CAAI,QAAQ,KAAK;QACvD,MAAM,QAAQ,cAAc,KAAK,IAAA,CAAK,KAAA,CAAM,IAAI,GAAG;YAAC,KAAK,IAAK;SAAA;QAG9D,MAAM,YAAY,KAAK,YAAkC;YACvD,IAAIC,SAAAA,KAAAA;YACJ,IAAI,IAAI,MAAA,KAAW,OAAO;gBACxB,MAAM,aAAa,KAAK,YAAA,CAAa,GAAA,CAAI,QAAQ;gBACjD,IAAI,WACF,CAAA,SAAS,KAAK,KAAA,CAAM,WAAW;YAElC,MACC,CAAA,SAAS,MAAM,IAAI,IAAA,EAAM;YAE3B,IAAI,WAAA,KAAA,EACF,CAAA,OAAO,CAAE;YAGX,IAAA,CAAK,YACH,CAAA,OAAO;gBACL,GAAG,KAAK,MAAA,CAAO,IAAA,CAAK,OAAA,CAAQ,WAAA,CAAY,KAAA,CAAM,WAAA,CAAY,OAAO;YAClE;YAGH,IAAA,4PAAK,WAAA,EAAS,OAAO,CACnB,CAAA,MAAM,6PAAI,YAAA,CAAU;gBAClB,MAAM;gBACN,SAAS;YACV;YAEH,MAAMC,MAAmB,CAAE;YAC3B,KAAK,MAAM,SAAS,MAAM,IAAA,EAAM,CAAE;gBAChC,MAAM,QAAQ,MAAA,CAAO,MAAA;gBACrB,IAAI,UAAA,KAAA,EACF,CAAA,GAAA,CAAI,MAAA,GACF,KAAK,MAAA,CAAO,IAAA,CAAK,OAAA,CAAQ,WAAA,CAAY,KAAA,CAAM,WAAA,CAAY,MAAM;YAElE;YAED,OAAO;QACR,EAAC;QAEF,MAAM,QAAQ,MAAM,QAAQ,GAAA,CAC1B,MAAM,GAAA,CACJ,OAAO,MAAM,UAAqD;YAChE,MAAM,YAAY,mQAAM,qBAAA,EAAmB,KAAK,MAAA,EAAQ,KAAK;YAC7D,OAAO;gBACL;gBACA;gBACA,aAAa,YAAY;oBACvB,MAAM,SAAS,MAAM,UAAU,IAAA,EAAM;oBACrC,IAAI,QAAQ,MAAA,CAAO,MAAA;oBAEnB,IAAA,CAAA,cAAA,QAAA,cAAA,KAAA,IAAA,KAAA,IAAI,UAAW,IAAA,CAAK,IAAA,MAAS,gBAAgB;;wBAC3C,MAAM,cAAA,CAAA,OAAA,CAAA,oBACJ,KAAK,OAAA,CAAQ,GAAA,CAAI,gBAAgB,MAAA,QAAA,sBAAA,KAAA,IAAA,oBACjC,KAAK,YAAA,CAAa,GAAA,CAAI,cAAc,MAAA,QAAA,SAAA,KAAA,IAAA,OACpC,KAAK,YAAA,CAAa,GAAA,CAAI,gBAAgB;wBAExC,IAAI,YACF,CAAA,+PAAI,WAAA,EAAS,MAAM,CACjB,CAAA,QAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GAAA,uBAAA,OAAA,EAAA,CAAA,GACK,QAAA,CAAA,GAAA;4BACU;wBAAA;6BAEV;;4BACL,CAAA,SAAA,KAAA,MAAA,QAAA,WAAA,KAAA,KAAA,CAAA,QAAU;gCACK;4BACd,CAAA;wBACF;oBAEJ;oBACD,OAAO;gBACR;gBACD,QAAQ,MAAM;;oBACZ,OAAA,CAAA,oBAAO,UAAU,MAAA,EAAQ,MAAA,QAAA,sBAAA,KAAA,IAAA,KAAA,IAAA,iBAAA,CAAG,MAAA;gBAC7B;YACF;QACF,EACF,CACF;QAED,MAAM,QAAQ,IAAI,IAChB,MAAM,GAAA,CAAI,CAAC,SAAS;;2CAAK,SAAA,MAAA,QAAA,oBAAA,KAAA,IAAA,KAAA,IAAA,gBAAW,IAAA,CAAK,IAAA;QAAI,EAAC,CAAC,MAAA,CAAO,QAAQ;8CAIhE,IAAI,MAAM,IAAA,GAAO,EACf,CAAA,MAAM,6PAAI,YAAA,CAAU;YAClB,MAAM;YACN,SAAA,CAAU,oCAAA,EAAsC,MAAM,IAAA,CAAK,MAAM,CAAC,IAAA,CAChE,KACD,CAAC,CAAA;QACH;QAEH,MAAMC,OAAAA,CAAAA,wBACJ,MAAM,MAAA,EAAQ,CAAC,IAAA,EAAM,CAAC,KAAA,MAAA,QAAA,0BAAA,KAAA,IAAA,wBAAS;QAEjC,MAAM,sBAAsB,KAAK,YAAA,CAAa,GAAA,CAAI,mBAAmB;QAErE,MAAMC,OAAwB;YAC5B;YACA,QAAQ,IAAI,OAAA,CAAQ,GAAA,CAAI,cAAc;YACtC;YACA;YACA,kBACE,wBAAwB,OACpB,OACA,gCAAgC,oBAAoB;YAC1D,QAAQ,IAAI,MAAA;YACZ,KAAK,KAAK,GAAA;QACX;QACD,OAAO;IACR;AACF;AAED,MAAMC,6BAAiD;IACrD,SAAQ,GAAA,EAAK;;QACX,OAAA,CAAA,CAAA,CAAA,CAAA,oBAAS,IAAI,OAAA,CAAQ,GAAA,CAAI,eAAe,MAAA,QAAA,sBAAA,KAAA,IAAA,KAAA,IAA/B,kBAAiC,UAAA,CAAW,sBAAsB;IAC5E;IACD,MAAM,OAAM,IAAA,EAAM;QAChB,MAAM,EAAE,GAAA,EAAK,GAAG;QAChB,IAAI,IAAI,MAAA,KAAW,OACjB,CAAA,MAAM,6PAAI,YAAA,CAAU;YAClB,MAAM;YACN,SACE;QACH;QAEH,MAAM,YAAY,KAAK,YAAY;YACjC,MAAM,KAAK,MAAM,IAAI,QAAA,EAAU;YAC/B,OAAO;QACR,EAAC;QACF,MAAM,YAAY,UAAM,8QAAA,EAAmB,KAAK,MAAA,EAAQ,KAAK,IAAA,CAAK;QAClE,OAAO;YACL,QAAQ;YACR,OAAO;gBACL;oBACE,MAAM,KAAK,IAAA;oBACX,aAAa,UAAU,IAAA;oBACvB,QAAQ,UAAU,MAAA;oBAClB;gBACD,CACF;aAAA;YACD,aAAa;YACb,MAAM;YACN,kBAAkB;YAClB,QAAQ,IAAI,MAAA;YACZ,KAAK,KAAK,GAAA;QACX;IACF;AACF;AAED,MAAMC,gCAAoD;IACxD,SAAQ,GAAA,EAAK;;QACX,OAAA,CAAA,CAAA,CAAA,CAAA,oBAAS,IAAI,OAAA,CACV,GAAA,CAAI,eAAe,MAAA,QAAA,sBAAA,KAAA,IAAA,KAAA,IADb,kBAEL,UAAA,CAAW,2BAA2B;IAC3C;IACD,MAAM,OAAM,IAAA,EAAM;QAChB,MAAM,EAAE,GAAA,EAAK,GAAG;QAChB,IAAI,IAAI,MAAA,KAAW,OACjB,CAAA,MAAM,IAAI,qQAAA,CAAU;YAClB,MAAM;YACN,SACE;QACH;QAEH,MAAM,YAAY,KAAK,YAAY;YACjC,OAAO,IAAI,IAAA;QACZ,EAAC;QACF,OAAO;YACL,OAAO;gBACL;oBACE,MAAM,KAAK,IAAA;oBACX,aAAa,UAAU,IAAA;oBACvB,QAAQ,UAAU,MAAA;oBAClB,WAAW,mQAAM,qBAAA,EAAmB,KAAK,MAAA,EAAQ,KAAK,IAAA,CAAK;gBAC5D,CACF;aAAA;YACD,aAAa;YACb,QAAQ;YACR,MAAM;YACN,kBAAkB;YAClB,QAAQ,IAAI,MAAA;YACZ,KAAK,KAAK,GAAA;QACX;IACF;AACF;AAED,MAAM,WAAW;IACf;IACA;IACA;CACD;AAED,SAAS,sBAAsBC,GAAAA,EAAkC;IAC/D,MAAM,UAAU,SAAS,IAAA,CAAK,CAACC,YAAY,UAAQ,OAAA,CAAQ,IAAI,CAAC;IAChE,IAAI,QACF,CAAA,OAAO;IAGT,IAAA,CAAK,WAAW,IAAI,MAAA,KAAW,MAE7B,CAAA,OAAO;IAGT,MAAM,6PAAI,YAAA,CAAU;QAClB,MAAM;QACN,SAAS,IAAI,OAAA,CAAQ,GAAA,CAAI,eAAe,GAAA,CACnC,0BAAA,EAA4B,IAAI,OAAA,CAAQ,GAAA,CAAI,eAAe,CAAC,CAAA,GAC7D;IACL;AACF;AAED,eAAsB,eACpBC,IAAAA,EAC0B;IAC1B,MAAM,UAAU,sBAAsB,KAAK,GAAA,CAAI;IAC/C,OAAO,MAAM,QAAQ,KAAA,CAAM,KAAK;AACjC;;;AChSD,SAAgB,aACdC,KAAAA,EACwD;IACxD,kQAAO,WAAA,EAAS,MAAM,IAAI,KAAA,CAAM,OAAA,KAAY;AAC7C;AAED,SAAgB,gBAAgB,UAAU,YAAA,EAAqB;IAC7D,MAAM,IAAI,aAAa,SAAS;AACjC;;;;;;;GCID,MAAM,oBAAA,aAAA,GAAoB,IAAI;;;mBAQ9B,MAAM,OAAO,KAEZ,CAFkB;sBAuMP,OAAO,WAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAlKnB,IAAa,YAAb,MAAa,UAAwC;IAwBzC,YAAYC,GAAAA,CAAuD;2CAyS7E,IAAA,EA7TmB,WAAA,KAAA;2CA6TlB,IAAA,EAzTS,eAA6D,CAAE,CAAA;2CAyTvE,IAAA,EApTQ,cAA6C;2CAoTpD,IAAA,EAAA,qBA/J6B;QAxI9B,IAAA,OAAW,QAAQ,WACjB,CAAA,IAAA,CAAK,OAAA,GAAU,IAAI,QAAQ;aAE3B,IAAA,CAAK,OAAA,GAAU;QAMjB,MAAM,aAAa,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,CAAC,UAAU;YAE9C,MAAM,EAAE,WAAA,EAAa,GAAG,IAAA;YACxB,IAAA,CAAK,WAAA,GAAc;YACnB,IAAA,CAAK,UAAA,GAAa;gBAChB,QAAQ;gBACR;YACD;YAED,gBAAA,QAAA,gBAAA,KAAA,KAAA,YAAa,OAAA,CAAQ,CAAC,EAAE,OAAA,EAAS,KAAK;gBACpC,QAAQ,MAAM;YACf,EAAC;QACH,EAAC;QAGF,IAAI,WAAW,WACb,CAAA,WAAW,KAAA,CAAM,CAAC,WAAW;YAE3B,MAAM,EAAE,WAAA,EAAa,GAAG,IAAA;YACxB,IAAA,CAAK,WAAA,GAAc;YACnB,IAAA,CAAK,UAAA,GAAa;gBAChB,QAAQ;gBACR;YACD;YAED,gBAAA,QAAA,gBAAA,KAAA,KAAA,YAAa,OAAA,CAAQ,CAAC,EAAE,MAAA,EAAQ,KAAK;gBACnC,OAAO,OAAO;YACf,EAAC;QACH,EAAC;IAEL;;;;;;;;;;;;;;;;;;IAoBD,YAAkC;QAEhC,IAAIC;QACJ,IAAIC;QAEJ,MAAM,EAAE,UAAA,EAAY,GAAG,IAAA;QACvB,IAAI,eAAe,MAAM;YAEvB,IAAI,IAAA,CAAK,WAAA,KAAgB,KAEvB,CAAA,MAAM,IAAI,MAAM;YAElB,MAAM,aAAa,eAAkB;YACrC,IAAA,CAAK,WAAA,GAAc,eAAe,IAAA,CAAK,WAAA,EAAa,WAAW;YAC/D,UAAU,WAAW,OAAA;YACrB,cAAc,MAAM;gBAClB,IAAI,IAAA,CAAK,WAAA,KAAgB,KACvB,CAAA,IAAA,CAAK,WAAA,GAAc,kBAAkB,IAAA,CAAK,WAAA,EAAa,WAAW;YAErE;QACF,OAAM;YAEL,MAAM,EAAE,MAAA,EAAQ,GAAG;YACnB,IAAI,WAAW,YACb,CAAA,UAAU,QAAQ,OAAA,CAAQ,WAAW,KAAA,CAAM;iBAE3C,UAAU,QAAQ,MAAA,CAAO,WAAW,MAAA,CAAO;YAE7C,cAAc;QACf;QAGD,OAAO,OAAO,MAAA,CAAO,SAAS;YAAE;QAAa,EAAC;IAC/C;wEAID,KACEC,WAAAA,EAIAC,UAAAA,EAIwC;QACxC,MAAM,aAAa,IAAA,CAAK,SAAA,EAAW;QACnC,MAAM,EAAE,WAAA,EAAa,GAAG;QACxB,OAAO,OAAO,MAAA,CAAO,WAAW,IAAA,CAAK,aAAa,WAAW,EAAE;YAC7D;QACD,EAAC;IACH;IAED,MACEC,UAAAA,EAIgC;QAChC,MAAM,aAAa,IAAA,CAAK,SAAA,EAAW;QACnC,MAAM,EAAE,WAAA,EAAa,GAAG;QACxB,OAAO,OAAO,MAAA,CAAO,WAAW,KAAA,CAAM,WAAW,EAAE;YACjD;QACD,EAAC;IACH;IAED,QAAQC,SAAAA,EAAyD;QAC/D,MAAM,aAAa,IAAA,CAAK,SAAA,EAAW;QACnC,MAAM,EAAE,WAAA,EAAa,GAAG;QACxB,OAAO,OAAO,MAAA,CAAO,WAAW,OAAA,CAAQ,UAAU,EAAE;YAClD;QACD,EAAC;IACH;;yCAUD,OAAO,MAASC,OAAAA,EAA0C;QACxD,MAAM,SAAS,UAAU,sBAAA,CAAuB,QAAQ;QACxD,OAAA,OAAc,WAAW,cACrB,SACA,UAAU,yBAAA,CAA0B,QAAQ;IACjD;uEAGD,OAAiB,0BAA6BA,OAAAA,EAAyB;QACrE,MAAM,UAAU,IAAI,UAAa;QACjC,kBAAkB,GAAA,CAAI,SAAS,QAA8B;QAC7D,kBAAkB,GAAA,CAAI,SAAS,QAA8B;QAC7D,OAAO;IACR;iFAGD,OAAiB,uBAA0BA,OAAAA,EAAyB;QAClE,OAAO,kBAAkB,GAAA,CAAI,QAAQ;IACtC;;oEAMD,OAAO,QAAWC,KAAAA,EAA2B;QAC3C,MAAMD,UAAAA,OACG,UAAU,YACjB,UAAU,QACV,UAAU,SAAA,OACH,MAAM,IAAA,KAAS,aAClB,QACA,QAAQ,OAAA,CAAQ,MAAM;QAC5B,OAAO,UAAU,KAAA,CAAM,QAAQ,CAAC,SAAA,EAAW;IAG5C;IAQD,aAAa,IACXE,MAAAA,EACqB;QACrB,MAAM,cAAc,MAAM,OAAA,CAAQ,OAAO,GAAG,SAAS,CAAC;eAAG,MAAO;SAAA;QAChE,MAAM,qBAAqB,YAAY,GAAA,CAAI,UAAU,OAAA,CAAQ;QAC7D,IAAI;YACF,OAAO,MAAM,QAAQ,GAAA,CAAI,mBAAmB;QAC7C,SAAS;YACR,mBAAmB,OAAA,CAAQ,CAAC,EAAE,WAAA,EAAa,KAAK;gBAC9C,aAAa;YACd,EAAC;QACH;IACF;IAQD,aAAa,KACXA,MAAAA,EACqB;QACrB,MAAM,cAAc,MAAM,OAAA,CAAQ,OAAO,GAAG,SAAS,CAAC;eAAG,MAAO;SAAA;QAChE,MAAM,qBAAqB,YAAY,GAAA,CAAI,UAAU,OAAA,CAAQ;QAC7D,IAAI;YACF,OAAO,MAAM,QAAQ,IAAA,CAAK,mBAAmB;QAC9C,SAAS;YACR,mBAAmB,OAAA,CAAQ,CAAC,EAAE,WAAA,EAAa,KAAK;gBAC9C,aAAa;YACd,EAAC;QACH;IACF;;;;;;;;;;;;MAcD,aAAa,eACXC,QAAAA,EACA;QAEA,MAAM,eAAe,SAAS,GAAA,CAAI,iBAAiB;QAGnD,IAAI;YACF,OAAO,MAAM,QAAQ,IAAA,CAAK,aAAa;QACxC,SAAS;YACR,KAAK,MAAM,WAAW,aAEpB,QAAQ,WAAA,EAAa;QAExB;IACF;AACF;;;;;;KAQD,SAAgB,iBACdC,OAAAA,EACwC;IACxC,OAAO,UAAU,KAAA,CAAM,QAAQ,CAAC,IAAA,CAAK,IAAM;YAAC,OAAQ;SAAA,CAAU;AAC/D;sIAKD,SAAS,gBAA4C;IACnD,IAAIC;IACJ,IAAIC;IACJ,MAAM,UAAU,IAAI,QAAW,CAAC,UAAU,YAAY;QACpD,UAAU;QACV,SAAS;IACV;IACD,OAAO;QACL;QACA;QACA;IACD;AACF;iCAID,SAAS,eAAkBC,GAAAA,EAAmBC,MAAAA,EAAyB;IACrE,OAAO,CAAC;WAAG;QAAK,MAAO;KAAA;AACxB;AAED,SAAS,iBAAoBD,GAAAA,EAAmBE,KAAAA,EAAe;IAC7D,OAAO,CAAC;WAAG,IAAI,KAAA,CAAM,GAAG,MAAM,EAAE;WAAG,IAAI,KAAA,CAAM,QAAQ,EAAE,AAAC;KAAA;AACzD;AAED,SAAS,kBAAqBF,GAAAA,EAAmBG,MAAAA,EAAiB;IAChE,MAAM,QAAQ,IAAI,OAAA,CAAQ,OAAY;IACtC,IAAI,UAAU,CAAA,EACZ,CAAA,OAAO,iBAAiB,KAAK,MAAM;IAErC,OAAO;AACR;;;;ACzXD,CAAA,kBAAA,CAAA,UAAA,MAAA,EAAO,OAAA,MAAA,QAAA,oBAAA,KAAA,KAAA,CAAA,QAAA,OAAA,GAAY,QAAQ;AAI3B,CAAA,wBAAA,CAAA,WAAA,MAAA,EAAO,YAAA,MAAA,QAAA,0BAAA,KAAA,KAAA,CAAA,SAAA,YAAA,GAAiB,QAAQ;;;;;;;GAShC,SAAgB,aAAgBC,KAAAA,EAAUC,OAAAA,EAAqC;IAC7E,MAAM,KAAK;IAGX,MAAM,WAAW,EAAA,CAAG,OAAO,OAAA,CAAA;IAG3B,EAAA,CAAG,OAAO,OAAA,CAAA,GAAW,MAAM;QACzB,SAAS;QACT,aAAA,QAAA,aAAA,KAAA,KAAA,UAAY;IACb;IAED,OAAO;AACR;;;;;;;GASD,SAAgB,kBACdD,KAAAA,EACAE,OAAAA,EACqB;IACrB,MAAM,KAAK;IAGX,MAAM,WAAW,EAAA,CAAG,OAAO,YAAA,CAAA;IAG3B,EAAA,CAAG,OAAO,YAAA,CAAA,GAAgB,YAAY;QACpC,MAAM,SAAS;QACf,MAAA,CAAA,aAAA,QAAA,aAAA,KAAA,IAAA,KAAA,IAAM,UAAY;IACnB;IAED,OAAO;AACR;;;ACnDD,MAAa,+BAA+B,QAAQ;AAEpD,SAAgB,cAAcC,EAAAA,EAAY;IACxC,IAAIC,QAA8C;IAElD,OAAO,aACL;QACE,QAAQ;YACN,IAAI,MACF,CAAA,MAAM,IAAI,MAAM;YAGlB,MAAM,UAAU,IAAI,QAClB,CAAC,YAAY;gBACX,QAAQ,WAAW,IAAM,QAAQ,6BAA6B,EAAE,GAAG;YACpE;YAEH,OAAO;QACR;IACF,GACD,MAAM;QACJ,IAAI,MACF,CAAA,aAAa,MAAM;IAEtB,EACF;AACF;;;;;QC5BD,SAAS,YAAY;YACnB,IAAI,IAAI,cAAA,OAAqB,kBAAkB,kBAAkB,SAAUC,GAAAA,EAAGC,GAAAA,EAAG;gBAC7E,IAAIC,MAAI,OAAO;gBACf,OAAOA,IAAE,IAAA,GAAO,mBAAmBA,IAAE,KAAA,GAAQF,KAAGE,IAAE,UAAA,GAAaD,KAAGC;YACnE,GACD,IAAI,CAAE,GACN,IAAI,CAAE,CAAA;YACR,SAAS,MAAMF,GAAAA,EAAGC,GAAAA,EAAG;gBACnB,IAAI,QAAQA,KAAG;oBACb,IAAI,OAAOA,IAAE,KAAKA,IAAG,CAAA,MAAM,IAAI,UAAU;oBACzC,IAAID,KAAG,IAAI,IAAIC,GAAAA,CAAE,OAAO,YAAA,IAAgB,MAAA,CAAO,MAAA,CAAO,sBAAsB,CAAA;oBAC5E,IAAA,KAAS,MAAM,KAAA,CAAM,IAAIA,GAAAA,CAAE,OAAO,OAAA,IAAW,MAAA,CAAO,MAAA,CAAO,iBAAiB,CAAA,EAAGD,GAAAA,GAAI,IAAI,IAAI;oBAC3F,IAAI,cAAA,OAAqB,EAAG,CAAA,MAAM,IAAI,UAAU;oBAChD,KAAA,CAAM,IAAI,SAASG,MAAI;wBACrB,IAAI;4BACF,EAAE,IAAA,CAAKF,IAAE;wBACV,EAAA,OAAQD,KAAG;4BACV,OAAO,QAAQ,MAAA,CAAOA,IAAE;wBACzB;oBACF,CAAA,GAAG,EAAE,IAAA,CAAK;wBACT,GAAGC;wBACH,GAAG;wBACH,GAAGD;oBACJ,EAAC;gBACH,MAAM,CAAA,OAAK,EAAE,IAAA,CAAK;oBACjB,GAAGC;oBACH,GAAGD;gBACJ,EAAC;gBACF,OAAOC;YACR;YACD,OAAO;gBACF;gBACH,GAAG,MAAM,IAAA,CAAK,MAAA,CAAO,EAAE;gBACvB,GAAG,MAAM,IAAA,CAAK,MAAA,CAAO,EAAE;gBACvB,GAAG,SAAS,IAAI;oBACd,IAAI,GACF,IAAI,IAAA,CAAK,CAAA,EACT,IAAI;oBACN,SAAS,OAAO;wBACd,MAAO,IAAI,EAAE,GAAA,EAAK,EAAG,IAAI;4BACvB,IAAA,CAAK,EAAE,CAAA,IAAK,MAAM,EAAG,CAAA,OAAO,IAAI,GAAG,EAAE,IAAA,CAAK,EAAE,EAAE,QAAQ,OAAA,EAAS,CAAC,IAAA,CAAK,KAAK;4BAC1E,IAAI,EAAE,CAAA,EAAG;gCACP,IAAID,MAAI,EAAE,CAAA,CAAE,IAAA,CAAK,EAAE,CAAA,CAAE;gCACrB,IAAI,EAAE,CAAA,CAAG,CAAA,OAAO,KAAK,GAAG,QAAQ,OAAA,CAAQA,IAAE,CAAC,IAAA,CAAK,MAAM,IAAI;4BAC3D,MAAM,CAAA,KAAK;wBACb,EAAA,OAAQA,KAAG;4BACV,OAAO,IAAIA,IAAE;wBACd;wBACD,IAAI,MAAM,EAAG,CAAA,OAAO,MAAM,IAAI,QAAQ,MAAA,CAAO,EAAE,GAAG,QAAQ,OAAA,EAAS;wBACnE,IAAI,MAAM,EAAG,CAAA,MAAM;oBACpB;oBACD,SAAS,IAAIE,GAAAA,EAAG;wBACd,OAAO,IAAI,MAAM,IAAI,IAAI,EAAEA,KAAG,KAAKA,KAAG,MAAM;oBAC7C;oBACD,OAAO,MAAM;gBACd;YACF;QACF;QACD,OAAO,OAAA,GAAU,WAAW,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QC1DjG,SAAS,eAAe,CAAA,EAAG,CAAA,EAAG;YAC5B,IAAA,CAAK,CAAA,GAAI,GAAG,IAAA,CAAK,CAAA,GAAI;QACtB;QACD,OAAO,OAAA,GAAU,gBAAgB,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCHtG,IAAIE,kBAAAA;QACJ,SAASC,uBAAqB,CAAA,EAAG;YAC/B,OAAO,IAAID,gBAAc,GAAG;QAC7B;QACD,OAAO,OAAA,GAAUC,wBAAsB,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;QCJ5G,IAAIC,kBAAAA;QACJ,SAASC,sBAAoB,CAAA,EAAG;YAC9B,OAAO,WAAY;gBACjB,OAAO,IAAI,eAAe,EAAE,KAAA,CAAM,IAAA,EAAM,UAAU;YACnD;QACF;QACD,SAAS,eAAe,CAAA,EAAG;YACzB,IAAI,GAAG;YACP,SAAS,OAAOC,GAAAA,EAAGC,GAAAA,EAAG;gBACpB,IAAI;oBACF,IAAI,IAAI,CAAA,CAAED,IAAAA,CAAGC,IAAE,EACb,IAAI,EAAE,KAAA,EACN,IAAI,aAAaH;oBACnB,QAAQ,OAAA,CAAQ,IAAI,EAAE,CAAA,GAAI,EAAE,CAAC,IAAA,CAAK,SAAUG,GAAAA,EAAG;wBAC7C,IAAI,GAAG;4BACL,IAAI,IAAI,aAAaD,MAAI,WAAW;4BACpC,IAAA,CAAK,EAAE,CAAA,IAAKC,IAAE,IAAA,CAAM,CAAA,OAAO,OAAO,GAAGA,IAAE;4BACvC,MAAI,CAAA,CAAE,EAAA,CAAGA,IAAE,CAAC,KAAA;wBACb;wBACD,OAAO,EAAE,IAAA,GAAO,WAAW,UAAUA,IAAE;oBACxC,GAAE,SAAUC,GAAAA,EAAG;wBACd,OAAO,SAASA,IAAE;oBACnB,EAAC;gBACH,EAAA,OAAQA,KAAG;oBACV,OAAO,SAASA,IAAE;gBACnB;YACF;YACD,SAAS,OAAOA,GAAAA,EAAG,CAAA,EAAG;gBACpB,OAAQA,KAAR;oBACE,KAAK;wBACH,EAAE,OAAA,CAAQ;4BACR,OAAO;4BACP,MAAA,CAAO;wBACR,EAAC;wBACF;oBACF,KAAK;wBACH,EAAE,MAAA,CAAO,EAAE;wBACX;oBACF,QACE;wBAAA,EAAE,OAAA,CAAQ;4BACR,OAAO;4BACP,MAAA,CAAO;wBACR,EAAC;gBACL;gBACD,CAAC,IAAI,EAAE,IAAA,IAAQ,OAAO,EAAE,GAAA,EAAK,EAAE,GAAA,CAAI,GAAG,IAAI;YAC3C;YACD,IAAA,CAAK,OAAA,GAAU,SAAUA,GAAAA,EAAG,CAAA,EAAG;gBAC7B,OAAO,IAAI,QAAQ,SAAU,CAAA,EAAG,CAAA,EAAG;oBACjC,IAAI,IAAI;wBACN,KAAKA;wBACL,KAAK;wBACL,SAAS;wBACT,QAAQ;wBACR,MAAM;oBACP;oBACD,IAAI,IAAI,EAAE,IAAA,GAAO,IAAA,CAAK,IAAI,IAAI,GAAG,OAAOA,KAAG,EAAE;gBAC9C;YACF,GAAE,cAAA,OAAqB,CAAA,CAAE,SAAA,IAAA,CAAc,IAAA,CAAK,SAAA,GAAA,KAAiB,CAAA;QAC/D;QACD,eAAe,SAAA,CAAU,cAAA,OAAqB,UAAU,OAAO,aAAA,IAAiB,kBAAA,GAAqB,WAAY;YAC/G,OAAO,IAAA;QACR,GAAE,eAAe,SAAA,CAAU,IAAA,GAAO,SAAU,CAAA,EAAG;YAC9C,OAAO,IAAA,CAAK,OAAA,CAAQ,QAAQ,EAAE;QAC/B,GAAE,eAAe,SAAA,CAAU,QAAA,GAAW,SAAU,CAAA,EAAG;YAClD,OAAO,IAAA,CAAK,OAAA,CAAQ,SAAS,EAAE;QAChC,GAAE,eAAe,SAAA,CAAU,SAAA,GAAY,SAAU,CAAA,EAAG;YACnD,OAAO,IAAA,CAAK,OAAA,CAAQ,UAAU,EAAE;QACjC;QACD,OAAO,OAAA,GAAUH,uBAAqB,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;;AC/D3G,SAAgB,iBACdI,QAAAA,EACyD;IACzD,MAAM,WAAW,QAAA,CAAS,OAAO,aAAA,CAAA,EAAgB;IAIjD,IAAI,QAAA,CAAS,OAAO,YAAA,CAAA,CAClB,CAAA,OAAO;IAGT,OAAO,kBAAkB,UAAU,YAAY;;QAC7C,MAAA,CAAA,CAAA,mBAAM,SAAS,MAAA,MAAA,QAAA,qBAAA,KAAA,IAAA,KAAA,IAAT,iBAAA,IAAA,CAAA,SAAmB;IAC1B,EAAC;AACH;;;GAID,SAAuB,gBAAA,EAAA,EAAA,GAAA;kCAqEnB,IAAA,EAAA;;;0EApEFC,QAAAA,EACAC,IAAAA,EACmB;;;YACnB,MAAY,WAAA,YAAA,CAAA,CAAW,iBAAiB,SAAS;YAEjD,MAAM,QAAA,YAAA,CAAA,CAAQ,cAAc,KAAK,aAAA,CAAc;YAE/C,MAAM,eAAe,MAAM,KAAA,EAAO;YAGlC,IAAIC;YAEJ,MAAO,KAAM;gBACX,SAAA,MAAA,CAAA,GAAA,6BAAA,OAAA,EAAe,UAAU,IAAA,CAAK;oBAAC,SAAS,IAAA,EAAM;oBAAE,YAAa;iBAAA,CAAC;gBAC9D,IAAI,WAAW,6BAEb,CAAA,iBAAiB;gBAEnB,IAAI,OAAO,IAAA,CACT,CAAA,OAAO;gBAET,MAAM,OAAO,KAAA;gBAEb,SAAS;YACV;;;;;;IACF;kCA2CI,IAAA,EAAA;;;;;;GApCL,SAAuB,cAAA,GAAA,EAAA,GAAA;gCAoCjB,IAAA,EAAA;;;wEAnCJF,QAAAA,EACAG,IAAAA,EAImB;;;YACnB,MAAY,WAAA,WAAA,CAAA,CAAW,iBAAiB,SAAS;YAGjD,IAAID;YAEJ,MAAM,QAAA,WAAA,CAAA,CAAQ,cAAc,KAAK,aAAA,CAAc;YAE/C,IAAI,QAAQ,KAAK,KAAA;YAEjB,IAAI,eAAe,IAAI,QAA6C,KAEnE,CAFyE;YAI1E,MAAO,KAAM;gBACX,SAAA,MAAA,CAAA,GAAA,6BAAA,OAAA,EAAe,UAAU,IAAA,CAAK;oBAAC,SAAS,IAAA,EAAM;oBAAE,YAAa;iBAAA,CAAC;gBAC9D,IAAI,WAAW,6BACb,CAAA,iBAAiB;gBAEnB,IAAI,OAAO,IAAA,CACT,CAAA,OAAO,OAAO,KAAA;gBAEhB,MAAM,OAAO,KAAA;gBACb,IAAI,EAAE,UAAU,EACd,CAAA,eAAe,MAAM,KAAA,EAAO;gBAG9B,SAAS;YACV;;;;;;IACF;gCACM,IAAA,EAAA;;;;AC3FP,SAAgB,iBAAgC;IAC9C,IAAIE;IACJ,IAAIC;IACJ,MAAM,UAAU,IAAI,QAAgB,CAAC,KAAK,QAAQ;QAChD,UAAU;QACV,SAAS;IACV;IAED,OAAO;QAAE;QAAkB;QAAkB;IAAS;AACvD;;;;;;ACHD,SAAS,sBACPC,QAAAA,EACAC,QAAAA,EACA;IACA,MAAM,WAAW,QAAA,CAAS,OAAO,aAAA,CAAA,EAAgB;IACjD,IAAIC,QAAqC;IAEzC,SAAS,UAAU;QACjB,QAAQ;QACR,WAAW,KAEV,CAFgB;IAGlB;IAED,SAAS,OAAO;QACd,IAAI,UAAU,OACZ,CAAA;QAEF,QAAQ;QAER,MAAM,OAAO,SAAS,IAAA,EAAM;QAC5B,KACG,IAAA,CAAK,CAAC,WAAW;YAChB,IAAI,OAAO,IAAA,EAAM;gBACf,QAAQ;gBACR,SAAS;oBAAE,QAAQ;oBAAU,OAAO,OAAO,KAAA;gBAAO,EAAC;gBACnD,SAAS;gBACT;YACD;YACD,QAAQ;YACR,SAAS;gBAAE,QAAQ;gBAAS,OAAO,OAAO,KAAA;YAAO,EAAC;QACnD,EAAC,CACD,KAAA,CAAM,CAAC,UAAU;YAChB,SAAS;gBAAE,QAAQ;gBAAS,OAAO;YAAO,EAAC;YAC3C,SAAS;QACV,EAAC;IACL;IAED,OAAO;QACL;QACA,SAAS,YAAY;;YACnB,SAAS;YACT,MAAA,CAAA,CAAA,mBAAM,SAAS,MAAA,MAAA,QAAA,qBAAA,KAAA,IAAA,KAAA,IAAT,iBAAA,IAAA,CAAA,SAAmB;QAC1B;IACF;AACF;;;;;;;;;;;GAqBD,SAAgB,sBAA4D;IAC1E,IAAIA,QAAqC;IACzC,IAAI,cAAc,gBAAgB;;;IAKlC,MAAMC,YAAoD,CAAE,CAAA;;;IAI5D,MAAM,YAAA,aAAA,GAAY,IAAI;IAEtB,MAAMC,SAQF,CAAE,CAAA;IAEN,SAAS,aAAaC,QAAAA,EAAgD;QACpE,IAAI,UAAU,UAEZ,CAAA;QAEF,MAAM,WAAW,sBAAsB,UAAU,CAAC,WAAW;YAC3D,IAAI,UAAU,UAEZ,CAAA;YAEF,OAAQ,OAAO,MAAA,EAAf;gBACE,KAAK;oBACH,OAAO,IAAA,CAAK;wBAAC;wBAAU,MAAO;qBAAA,CAAC;oBAC/B;gBACF,KAAK;oBACH,UAAU,MAAA,CAAO,SAAS;oBAC1B;gBACF,KAAK;oBACH,OAAO,IAAA,CAAK;wBAAC;wBAAU,MAAO;qBAAA,CAAC;oBAC/B,UAAU,MAAA,CAAO,SAAS;oBAC1B;YACH;YACD,YAAY,OAAA,EAAS;QACtB,EAAC;QACF,UAAU,GAAA,CAAI,SAAS;QACvB,SAAS,IAAA,EAAM;IAChB;IAED,OAAO;QACL,KAAIA,QAAAA,EAAgD;YAClD,OAAQ,OAAR;gBACE,KAAK;oBACH,UAAU,IAAA,CAAK,SAAS;oBACxB;gBACF,KAAK;oBACH,aAAa,SAAS;oBACtB;gBACF,KAAK,OAEH;oBAAA;YAEH;QACF;QACD,CAAQ,OAAO,aAAA,CAAA;yEAAiB;;;oBAC9B,IAAI,UAAU,OACZ,CAAA,MAAM,IAAI,MAAM;oBAElB,QAAQ;oBAER,MAAY,WAAA,YAAA,CAAA,CAAW,kBAAkB,CAAE,GAAE,YAAY;wBACvD,QAAQ;wBAER,MAAMC,SAAoB,CAAE,CAAA;wBAC5B,MAAM,QAAQ,GAAA,CACZ,MAAM,IAAA,CAAK,UAAU,MAAA,EAAQ,CAAC,CAAC,GAAA,CAAI,OAAO,OAAO;4BAC/C,IAAI;gCACF,MAAM,GAAG,OAAA,EAAS;4BACnB,EAAA,OAAQ,OAAO;gCACd,OAAO,IAAA,CAAK,MAAM;4BACnB;wBACF,EAAC,CACH;wBACD,OAAO,MAAA,GAAS;wBAChB,UAAU,KAAA,EAAO;wBACjB,YAAY,OAAA,EAAS;wBAErB,IAAI,OAAO,MAAA,GAAS,EAClB,CAAA,MAAM,IAAI,eAAe;oBAE5B,EAAC;oBAEF,MAAO,UAAU,MAAA,GAAS,EAExB,aAAa,UAAU,KAAA,EAAO,CAAE;oBAGlC,MAAO,UAAU,IAAA,GAAO,EAAG;wBACzB,MAAA,CAAA,GAAA,6BAAA,OAAA,EAAM,YAAY,OAAA;wBAElB,MAAO,OAAO,MAAA,GAAS,EAAG;4BAExB,MAAM,CAAC,UAAU,OAAO,GAAG,OAAO,KAAA,EAAO;4BAEzC,OAAQ,OAAO,MAAA,EAAf;gCACE,KAAK;oCACH,MAAM,OAAO,KAAA;oCACb,SAAS,IAAA,EAAM;oCACf;gCACF,KAAK,QACH;oCAAA,MAAM,OAAO,KAAA;4BAChB;wBACF;wBACD,cAAc,gBAAgB;oBAC/B;;;;;;YACF;;IACF;AACF;;;;;;;;GC1LD,SAAgB,mBACdC,QAAAA,EACwB;IACxB,MAAM,WAAW,QAAA,CAAS,OAAO,aAAA,CAAA,EAAgB;IAEjD,OAAO,IAAI,eAAe;QACxB,MAAM,SAAS;;YACb,MAAA,CAAA,CAAA,mBAAM,SAAS,MAAA,MAAA,QAAA,qBAAA,KAAA,IAAA,KAAA,IAAT,iBAAA,IAAA,CAAA,SAAmB;QAC1B;QAED,MAAM,MAAK,UAAA,EAAY;YACrB,MAAM,SAAS,MAAM,SAAS,IAAA,EAAM;YAEpC,IAAI,OAAO,IAAA,EAAM;gBACf,WAAW,KAAA,EAAO;gBAClB;YACD;YAED,WAAW,OAAA,CAAQ,OAAO,KAAA,CAAM;QACjC;IACF;AACF;;;;;;ACvBD,MAAa,WAAW,OAAO,OAAO;;;;GAMtC,SAAuB,SAAA,EAAA,EAAA,GAAA;2BAqCnB,IAAA,EAAA;;;mEApCFC,QAAAA,EACAC,cAAAA,EAC0C;;;YAC1C,MAAY,WAAA,YAAA,CAAA,CAAW,iBAAiB,SAAS;YAGjD,IAAIC;YAKJ,IAAI,cAAc,SAAS,IAAA,EAAM;YAEjC,MAAO,KAAA,IAAA;;gBACL,MAAM,cAAA,WAAA,CAAA,CAAc,cAAc,eAAe;gBAEjD,SAAA,MAAA,CAAA,GAAA,6BAAA,OAAA,EAAe,UAAU,IAAA,CAAK;oBAAC;oBAAa,YAAY,KAAA,EAAO,AAAC;iBAAA,CAAC;gBAEjE,IAAI,WAAW,8BAA8B;oBAG3C,MAAM;oBACN;gBACD;gBAED,IAAI,OAAO,IAAA,CACT,CAAA,OAAO,OAAO,KAAA;gBAGhB,cAAc,SAAS,IAAA,EAAM;gBAC7B,MAAM,OAAO,KAAA;gBAGb,SAAS;;;;;;;;;;;IAEZ;2BACI,IAAA,EAAA;;;;;;QC/CL,SAASC,iBAAe,CAAA,EAAG;YACzB,IAAI,GACF,GACA,GACA,IAAI;YACN,IAAK,eAAA,OAAsB,UAAA,CAAW,IAAI,OAAO,aAAA,EAAe,IAAI,OAAO,QAAA,GAAW,KAAM;gBAC1F,IAAI,KAAK,QAAA,CAAS,IAAI,CAAA,CAAE,EAAA,EAAK,CAAA,OAAO,EAAE,IAAA,CAAK,EAAE;gBAC7C,IAAI,KAAK,QAAA,CAAS,IAAI,CAAA,CAAE,EAAA,EAAK,CAAA,OAAO,IAAI,sBAAsB,EAAE,IAAA,CAAK,EAAE;gBACvE,IAAI,mBAAmB,IAAI;YAC5B;YACD,MAAM,IAAI,UAAU;QACrB;QACD,SAAS,sBAAsB,CAAA,EAAG;YAChC,SAAS,kCAAkCC,GAAAA,EAAG;gBAC5C,IAAI,OAAOA,IAAE,KAAKA,IAAG,CAAA,OAAO,QAAQ,MAAA,CAAO,IAAI,UAAUA,MAAI,sBAAsB;gBACnF,IAAI,IAAIA,IAAE,IAAA;gBACV,OAAO,QAAQ,OAAA,CAAQA,IAAE,KAAA,CAAM,CAAC,IAAA,CAAK,SAAUA,GAAAA,EAAG;oBAChD,OAAO;wBACL,OAAOA;wBACP,MAAM;oBACP;gBACF,EAAC;YACH;YACD,OAAO,wBAAwB,SAASC,wBAAsBD,GAAAA,EAAG;gBAC/D,IAAA,CAAK,CAAA,GAAIA,KAAG,IAAA,CAAK,CAAA,GAAIA,IAAE,IAAA;YACxB,GAAE,sBAAsB,SAAA,GAAY;gBACnC,GAAG;gBACH,GAAG;gBACH,MAAM,SAAS,OAAO;oBACpB,OAAO,kCAAkC,IAAA,CAAK,CAAA,CAAE,KAAA,CAAM,IAAA,CAAK,CAAA,EAAG,UAAU,CAAC;gBAC1E;gBACD,UAAU,SAAS,QAAQA,GAAAA,EAAG;oBAC5B,IAAI,IAAI,IAAA,CAAK,CAAA,CAAE,SAAA;oBACf,OAAA,KAAY,MAAM,IAAI,QAAQ,OAAA,CAAQ;wBACpC,OAAOA;wBACP,MAAA,CAAO;oBACR,EAAC,GAAG,kCAAkC,EAAE,KAAA,CAAM,IAAA,CAAK,CAAA,EAAG,UAAU,CAAC;gBACnE;gBACD,SAAS,SAAS,OAAOA,GAAAA,EAAG;oBAC1B,IAAI,IAAI,IAAA,CAAK,CAAA,CAAE,SAAA;oBACf,OAAA,KAAY,MAAM,IAAI,QAAQ,MAAA,CAAOA,IAAE,GAAG,kCAAkC,EAAE,KAAA,CAAM,IAAA,CAAK,CAAA,EAAG,UAAU,CAAC;gBACxG;YACF,GAAE,IAAI,sBAAsB;QAC9B;QACD,OAAO,OAAA,GAAUD,kBAAgB,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;;;ACpBtG,SAAS,cAAcG,KAAAA,EAAkD;IACvE,OAAO,OAAO,SAAA,CAAU,QAAA,CAAS,IAAA,CAAK,MAAM,KAAK;AAClD;AAGD,MAAM,2BAA2B;AAEjC,MAAM,kCAAkC;AAGxC,MAAM,2BAA2B;AAEjC,MAAM,0BAA0B;AAGhC,MAAM,+BAA+B;AAErC,MAAM,8BAA8B;AAEpC,MAAM,8BAA8B;AAqDpC,SAAgB,UAAUA,KAAAA,EAA2C;IACnE,OAAA,4PACG,WAAA,EAAS,MAAM,QAAI,oQAAA,EAAW,MAAM,KAAA,OAAA,CAAA,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAC9B,KAAA,CAAQ,OAAA,MAAY,cAAA,OAAA,CAAA,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IACpB,KAAA,CAAQ,QAAA,MAAa;AAE/B;AAwBD,IAAM,gBAAN,cAA4B,MAAM;IAChC,YAAmBC,IAAAA,CAA2B;QAC5C,KAAA,CAAM,gCAAgC,KAAK,IAAA,CAAK,IAAI,CAAC;QADpC,IAAA,CAAA,IAAA,GAAA;IAElB;AACF;AAED,SAAgB,0BAAA,GAAA;4CAkfX,IAAA,EAAA;;;oFAjfHC,IAAAA,EACyD;QACzD,MAAM,EAAE,IAAA,EAAM,GAAG;QACjB,IAAI,UAAU;QACd,MAAM,cAAc;QAEpB,MAAM,kBAAkB,qBAAgC;QACxD,SAAS,cACPC,QAAAA,EACA;YACA,MAAM,MAAM;YAEZ,MAAMC,aAAW,SAAS,IAAI;YAC9B,gBAAgB,GAAA,CAAIA,WAAS;YAE7B,OAAO;QACR;QAED,SAAS,cAAcC,OAAAA,EAA2BJ,IAAAA,EAA2B;YAC3E,OAAO,cAAA,aAAA,GAAc;8EAAiB,GAAA,EAAK;oBACzC,MAAM,QAAQ,cAAc,KAAK;oBACjC,IAAI,OAAO;wBAET,QAAQ,KAAA,CAAM,CAAC,UAAU;;4BACvB,CAAA,gBAAA,KAAK,OAAA,MAAA,QAAA,kBAAA,KAAA,KAAL,cAAA,IAAA,CAAA,MAAe;gCAAE,OAAO;gCAAO;4BAAM,EAAC;wBACvC,EAAC;wBAEF,UAAU,QAAQ,MAAA,CAAO,MAAM;oBAChC;oBACD,IAAI;wBACF,MAAM,OAAA,MAAA,CAAA,GAAA,6BAAA,OAAA,EAAa;wBACnB,MAAM;4BAAC;4BAAK;4BAA0B,OAAO,MAAM,KAAK;yBAAC;oBAC1D,EAAA,OAAQ,OAAO;;wBACd,CAAA,iBAAA,KAAK,OAAA,MAAA,QAAA,mBAAA,KAAA,KAAL,eAAA,IAAA,CAAA,MAAe;4BAAE,OAAO;4BAAO;wBAAM,EAAC;wBACtC,MAAM;4BACJ;4BACA;iDACA,KAAK,WAAA,MAAA,QAAA,sBAAA,KAAA,IAAA,KAAA,IAAL,kBAAA,IAAA,CAAA,MAAmB;gCAAE,OAAO;gCAAO;4BAAM,EAAC;yBAC3C;oBACF;gBACF;;sCAycC,IAAA,EAAA;;gBAzcA;QACH;QACD,SAAS,oBACPK,UAAAA,EACAL,IAAAA,EACA;YACA,OAAO,cAAA,aAAA,GAAc;+EAAiB,GAAA,EAAK;;;wBACzC,MAAM,QAAQ,cAAc,KAAK;wBACjC,IAAI,MACF,CAAA,MAAM;wBAER,MAAY,WAAA,YAAA,CAAA,CAAW,iBAAiBG,WAAS;wBAEjD,IAAI;4BACF,MAAO,KAAM;gCACX,MAAM,OAAA,MAAA,CAAA,GAAA,6BAAA,OAAA,EAAa,SAAS,IAAA,EAAM;gCAClC,IAAI,KAAK,IAAA,EAAM;oCACb,MAAM;wCAAC;wCAAK;wCAA8B,OAAO,KAAK,KAAA,EAAO,KAAK;qCAAC;oCACnE;gCACD;gCACD,MAAM;oCAAC;oCAAK;oCAA6B,OAAO,KAAK,KAAA,EAAO,KAAK;iCAAC;4BACnE;wBACF,EAAA,OAAQ,OAAO;;4BACd,CAAA,iBAAA,KAAK,OAAA,MAAA,QAAA,mBAAA,KAAA,KAAL,eAAA,IAAA,CAAA,MAAe;gCAAE,OAAO;gCAAO;4BAAM,EAAC;4BAEtC,MAAM;gCACJ;gCACA;sDACA,KAAK,WAAA,MAAA,QAAA,uBAAA,KAAA,IAAA,KAAA,IAAL,mBAAA,IAAA,CAAA,MAAmB;oCAAE,OAAO;oCAAO;gCAAM,EAAC;6BAC3C;wBACF;;;;;;gBACF;;uCA0aE,IAAA,EAAA;;gBA1aD;QACH;QACD,SAAS,cAAcH,IAAAA,EAA2B;YAChD,IAAI,KAAK,QAAA,IAAY,KAAK,MAAA,GAAS,KAAK,QAAA,CACtC,CAAA,OAAO,IAAI,cAAc;YAE3B,OAAO;QACR;QACD,SAAS,YACPD,KAAAA,EACAC,IAAAA,EACoD;YACpD,IAAI,UAAU,MAAM,CAClB,CAAA,OAAO;gBAAC;gBAA0B,cAAc,OAAO,KAAK,AAAC;aAAA;YAE/D,+PAAI,kBAAA,EAAgB,MAAM,EAAE;gBAC1B,IAAI,KAAK,QAAA,IAAY,KAAK,MAAA,IAAU,KAAK,QAAA,CACvC,CAAA,MAAM,IAAI,MAAM;gBAElB,OAAO;oBACL;oBACA,oBAAoB,OAAO,KAAK,AACjC;iBAAA;YACF;YACD,OAAO;QACR;QACD,SAAS,OAAOD,KAAAA,EAAgBC,IAAAA,EAAyC;YACvE,IAAI,UAAA,KAAA,EACF,CAAA,OAAO;gBAAC,CAAE,CAAC;aAAA;YAEb,MAAM,MAAM,YAAY,OAAO,KAAK;YACpC,IAAI,IACF,CAAA,OAAO;gBAAC;oBAAC,WAAY;iBAAA;gBAAE;oBAAC,MAAM;uBAAG,GAAI;iBAAC;aAAA;YAGxC,IAAA,CAAK,cAAc,MAAM,CACvB,CAAA,OAAO;gBAAC;oBAAC,KAAM;iBAAC;aAAA;YAGlB,MAAMM,SAAkC,CAAE;YAC1C,MAAMC,cAAiC,CAAE,CAAA;YACzC,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,OAAO,OAAA,CAAQ,MAAM,CAAE;gBAC/C,MAAM,cAAc,YAAY,MAAM,CAAC;uBAAG;oBAAM,GAAI;iBAAA,CAAC;gBACrD,IAAA,CAAK,aAAa;oBAChB,MAAA,CAAO,IAAA,GAAO;oBACd;gBACD;gBACD,MAAA,CAAO,IAAA,GAAO;gBACd,YAAY,IAAA,CAAK;oBAAC,KAAK;uBAAG,WAAY;iBAAA,CAAC;YACxC;YACD,OAAO;gBAAC;oBAAC,MAAO;iBAAA,EAAE;mBAAG,WAAY;aAAA;QAClC;QAED,MAAMC,UAAgB,CAAE;QACxB,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,OAAO,OAAA,CAAQ,KAAK,CAC5C,OAAA,CAAQ,IAAA,GAAO,OAAO,MAAM;YAAC,GAAI;SAAA,CAAC;QAGpC,MAAM;QAEN,IAAIC,WACF;QACF,IAAI,KAAK,MAAA,CACP,CAAA,WAAW,SAAS,iBAAiB,KAAK,MAAA,CAAO;;;;;oEAGzB,WAAA,OAAA,4BAAA,CAAA,CAAA,QAAA,MAAA,CAAA,GAAA,6BAAA,OAAA,EAAA,UAAA,IAAA,GAAA,EAAA,IAAA,EAAA,4BAAA,MAAA;sBAAT,QAAA,MAAA,KAAA;gBACf,MAAM;;;;;;;;;;;;IAET;4CAqWO,IAAA,EAAA;;;;;GAhWR,SAAgB,oBAAoBR,IAAAA,EAA4B;IAC9D,IAAI,SAAS,mBAAmB,0BAA0B,KAAK,CAAC;IAEhE,MAAM,EAAE,SAAA,EAAW,GAAG;IACtB,IAAI,UACF,CAAA,SAAS,OAAO,WAAA,CACd,IAAI,gBAAgB;QAClB,WAAU,KAAA,EAAO,UAAA,EAAY;YAC3B,IAAI,UAAU,SACZ,CAAA,WAAW,OAAA,CAAQ,SAAS;iBAE5B,WAAW,OAAA,CAAQ,UAAU,MAAM,CAAC;QAEvC;IACF,GACF;IAGH,OAAO,OACJ,WAAA,CACC,IAAI,gBAAgB;QAClB,WAAU,KAAA,EAAO,UAAA,EAAY;YAC3B,IAAI,UAAU,SACZ,CAAA,WAAW,OAAA,CAAQ,IAAI;iBAEvB,WAAW,OAAA,CAAQ,KAAK,SAAA,CAAU,MAAM,GAAG,KAAK;QAEnD;IACF,GACF,CACA,WAAA,CAAY,IAAI,oBAAoB;AACxC;AAED,IAAM,aAAN,cAAyB,MAAM;IAC7B,YAA4BS,IAAAA,CAAe;QACzC,KAAA,CAAM,6BAA6B;QADT,IAAA,CAAA,IAAA,GAAA;IAE3B;AACF;AAGD,MAAM,4BAA4B,CAACC,WAAsC;IACvE,OAAO;QACL,YAAY;YACV,MAAM,SAAS,IAAI,eAA2B;gBAC5C,OAAM,UAAA,EAAY;oBAChB,OAAO,EAAA,CAAG,QAAQ,CAAC,UAAU;wBAC3B,WAAW,OAAA,CAAQ,MAAM;oBAC1B,EAAC;oBACF,OAAO,EAAA,CAAG,OAAO,MAAM;wBACrB,WAAW,KAAA,EAAO;oBACnB,EAAC;oBACF,OAAO,EAAA,CAAG,SAAS,CAAC,UAAU;wBAC5B,WAAW,KAAA,CAAM,MAAM;oBACxB,EAAC;gBACH;YACF;YACD,OAAO,OAAO,SAAA,EAAW;QAC1B;IACF;AACF;AAED,SAAS,sBACPC,IAAAA,EACA;IACA,MAAM,SACJ,eAAe,OACX,KAAK,SAAA,EAAW,GAChB,0BAA0B,KAAK,CAAC,SAAA,EAAW;IAEjD,IAAI,gBAAgB;IAEpB,OAAO,IAAI,eAAe;QACxB,MAAM,MAAK,UAAA,EAAY;YACrB,MAAM,EAAE,IAAA,EAAM,KAAA,EAAO,GAAG,MAAM,OAAO,IAAA,EAAM;YAE3C,IAAI,KACF,CAAA,WAAW,KAAA,EAAO;iBAElB,WAAW,OAAA,CAAQ,MAAM;QAE5B;QACD,SAAS;YACP,OAAO,OAAO,MAAA,EAAQ;QACvB;IACF,GACE,WAAA,CAAY,IAAI,oBAAoB,CACpC,WAAA,CACC,IAAI,gBAAgC;QAClC,WAAU,KAAA,EAAO,UAAA,EAAY;;YAC3B,iBAAiB;YACjB,MAAM,QAAQ,cAAc,KAAA,CAAM,KAAK;YACvC,gBAAA,CAAA,aAAgB,MAAM,GAAA,EAAK,MAAA,QAAA,eAAA,KAAA,IAAA,aAAI;YAC/B,KAAK,MAAM,QAAQ,MACjB,WAAW,OAAA,CAAQ,KAAK;QAE3B;IACF,GACF;AACJ;AACD,SAAS,qBACPA,IAAAA,EACA;IACA,MAAM,SAAS,sBAAsB,KAAK;IAE1C,IAAI,WAAW;IACf,OAAO,OAAO,WAAA,CACZ,IAAI,gBAA2C;QAC7C,WAAU,IAAA,EAAM,UAAA,EAAY;YAC1B,IAAA,CAAK,UAAU;gBACb,MAAM,OAAO,KAAK,KAAA,CAAM,KAAK;gBAC7B,WAAW,OAAA,CAAQ,KAAc;gBACjC,WAAW;YACZ,OAAM;gBACL,MAAMC,QAAmB,KAAK,KAAA,CAAM,KAAK;gBACzC,WAAW,OAAA,CAAQ,MAAM;YAC1B;QACF;IACF,GACF;AACF;;;GAKD,SAAS,qBAAqBC,eAAAA,EAAkC;IAC9D,MAAM,gBAAA,aAAA,GAAgB,IAAI;;;IAQ1B,SAAS,UAAU;QACjB,OAAO,MAAM,IAAA,CAAK,cAAc,MAAA,EAAQ,CAAC,CAAC,KAAA,CAAM,CAAC,IAAM,EAAE,MAAA,CAAO;IACjE;;;IAKD,SAAS,yBAAyB;QAChC,IAAIC;QACJ,MAAM,SAAS,IAAI,eAA0B;YAC3C,OAAM,UAAA,EAAY;gBAChB,qBAAqB;YACtB;QACF;QAED,MAAM,mBAAmB;YACvB,SAAS,CAACC,IAAiB,mBAAmB,OAAA,CAAQ,EAAE;YACxD,OAAO,MAAM;gBACX,mBAAmB,KAAA,EAAO;gBAE1B,OAAO;gBAEP,IAAI,SAAS,CACX,CAAA,gBAAgB,KAAA,EAAO;YAE1B;YACD,QAAQ;YACR,mBAAmB,MAAM;gBACvB,MAAM,SAAS,OAAO,SAAA,EAAW;gBAEjC,OAAO,aAAa,QAAQ,MAAM;oBAChC,OAAO,WAAA,EAAa;oBACpB,iBAAiB,KAAA,EAAO;gBACzB,EAAC;YACH;YACD,OAAO,CAACC,WAAoB;gBAC1B,mBAAmB,KAAA,CAAM,OAAO;gBAChC,OAAO;YACR;QACF;QACD,SAAS,QAAQ;YACf,OAAO,MAAA,CAAO,kBAAkB;gBAC9B,QAAQ;gBACR,OAAO,KAEN,CAFY;gBAGb,SAAS,KAER,CAFc;gBAGf,mBAAmB;gBACnB,OAAO,KAEN,CAFY;YAGd,EAAC;QACH;QAED,OAAO;IACR;;;IAKD,SAAS,YAAYC,OAAAA,EAAqB;QACxC,IAAI,IAAI,cAAc,GAAA,CAAI,QAAQ;QAClC,IAAA,CAAK,GAAG;YACN,IAAI,wBAAwB;YAC5B,cAAc,GAAA,CAAI,SAAS,EAAE;QAC9B;QACD,OAAO;IACR;;;IAKD,SAAS,UAAUD,MAAAA,EAAiB;QAClC,KAAK,MAAM,cAAc,cAAc,MAAA,EAAQ,CAC7C,WAAW,KAAA,CAAM,OAAO;IAE3B;IAED,OAAO;QACL;QACA;QACA;IACD;AACF;;;;GAMD,eAAsB,oBAA2BE,IAAAA,EAS9C;IACD,MAAM,EAAE,cAAc,CAAC,IAAM,CAAA,EAAG,GAAG;IAEnC,IAAI,SAAS,qBAA2B,KAAK,IAAA,CAAK;IAClD,IAAI,YACF,CAAA,SAAS,OAAO,WAAA,CACd,IAAI,gBAAgB;QAClB,WAAU,KAAA,EAAO,UAAA,EAAY;YAC3B,WAAW,OAAA,CAAQ,YAAY,MAAM,CAAC;QACvC;IACF,GACF;IAEH,IAAIC,eAAuC,gBAAgB;IAE3D,MAAM,gBAAgB,qBAAqB,KAAK,eAAA,CAAgB;IAEhE,SAAS,sBAAsBC,KAAAA,EAAwB;QACrD,MAAM,CAAC,OAAO,MAAM,QAAQ,GAAG;QAE/B,MAAM,aAAa,cAAc,WAAA,CAAY,QAAQ;QAErD,OAAQ,MAAR;YACE,KAAK,yBACH;gBAAA,kQAAO,MAAA,EAAI,YAAY;;;wBACrB,MAAM,SAAA,WAAA,CAAA,CAAS,WAAW,iBAAA,EAAmB;wBAE7C,MAAM,EAAE,OAAA,OAAA,EAAO,GAAG,MAAM,OAAO,IAAA,EAAM;wBACrC,MAAM,CAAC,UAAU,QAAQ,KAAK,GAAGC;wBACjC,OAAQ,QAAR;4BACE,KAAK,yBACH;gCAAA,OAAO,OAAO,KAAK;4BACrB,KAAK;;gCACH,MAAA,CAAA,qBAAA,CAAA,qBAAM,KAAK,WAAA,MAAA,QAAA,uBAAA,KAAA,IAAA,KAAA,IAAL,mBAAA,IAAA,CAAA,MAAmB;oCAAE,OAAO;gCAAM,EAAC,MAAA,QAAA,uBAAA,KAAA,IAAA,qBAAI,IAAI,WAAW;wBAC/D;;;;;;gBACF,EAAC;YAEJ,KAAK,gCACH;gBAAA,kQAAO,MAAA,EAAA,CAAA,GAAA,4BAAA,OAAA,EAAA,aAAuB;;;wBAC5B,MAAM,SAAA,WAAA,CAAA,CAAS,WAAW,iBAAA,EAAmB;wBAE7C,MAAO,KAAM;4BACX,MAAM,EAAE,OAAA,OAAA,EAAO,GAAA,MAAA,CAAA,GAAA,6BAAA,OAAA,EAAS,OAAO,IAAA,EAAM;4BAErC,MAAM,CAAC,UAAU,QAAQ,KAAK,GAAGA;4BAEjC,OAAQ,QAAR;gCACE,KAAK;oCACH,MAAM,OAAO,KAAK;oCAClB;gCACF,KAAK,6BACH;oCAAA,OAAO,OAAO,KAAK;gCACrB,KAAK;;oCACH,MAAA,CAAA,qBAAA,CAAA,qBACE,KAAK,WAAA,MAAA,QAAA,uBAAA,KAAA,IAAA,KAAA,IAAL,mBAAA,IAAA,CAAA,MAAmB;wCAAE,OAAO;oCAAM,EAAC,MAAA,QAAA,uBAAA,KAAA,IAAA,qBAAI,IAAI,WAAW;4BAE3D;wBACF;;;;;;gBACF,GAAC;QAEL;IACF;IAED,SAAS,OAAOC,KAAAA,EAA8B;QAC5C,MAAM,CAAC,CAAC,KAAK,EAAE,GAAG,WAAW,GAAG;QAEhC,KAAK,MAAMD,WAAS,WAAY;YAC9B,MAAM,CAAC,IAAI,GAAGA;YACd,MAAM,UAAU,sBAAsBA,QAAM;YAE5C,IAAI,QAAQ,KACV,CAAA,OAAO;YAGR,IAAA,CAAa,IAAA,GAAO;QACtB;QACD,OAAO;IACR;IAED,MAAM,eAAe,CAACL,WAAoB;QACxC,iBAAA,QAAA,iBAAA,KAAA,KAAA,aAAc,MAAA,CAAO,OAAO;QAC5B,cAAc,SAAA,CAAU,OAAO;IAChC;IACD,OACG,MAAA,CACC,IAAI,eAAe;QACjB,OAAM,WAAA,EAAa;YACjB,IAAI,cAAc;gBAChB,MAAM,OAAO;gBAEb,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAA,CAAQ,YAAY,CAAE;oBACtD,MAAM,SAAS,OAAO,MAAa;oBACnC,IAAA,CAAK,IAAA,GAAO;gBACb;gBACD,aAAa,OAAA,CAAQ,KAAc;gBACnC,eAAe;gBAEf;YACD;YACD,MAAM,QAAQ;YACd,MAAM,CAAC,IAAI,GAAG;YAEd,MAAM,aAAa,cAAc,WAAA,CAAY,IAAI;YACjD,WAAW,OAAA,CAAQ,MAAM;QAC1B;QACD,OAAO,IAAM,aAAa,IAAI,MAAM,iBAAiB;QACrD,OAAO;IACR,IACD;QACE,QAAQ,KAAK,eAAA,CAAgB,MAAA;IAC9B,EACF,CACA,KAAA,CAAM,CAAC,UAAU;;QAChB,CAAA,iBAAA,KAAK,OAAA,MAAA,QAAA,mBAAA,KAAA,KAAL,eAAA,IAAA,CAAA,MAAe;YAAE;QAAO,EAAC;QACzB,aAAa,MAAM;IACpB,EAAC;IAEJ,OAAO;QAAC,MAAM,aAAa,OAAA;QAAS,aAAc;KAAA;AACnD;;;;;QCrnBD,IAAI,gBAAA;QACJ,SAASO,0BAAwB,CAAA,EAAG;YAClC,IAAI,IAAI,CAAE,GACR,IAAA,CAAK;YACP,SAAS,KAAKC,GAAAA,EAAG,CAAA,EAAG;gBAClB,OAAO,IAAA,CAAK,GAAG,IAAI,IAAI,QAAQ,SAAUC,GAAAA,EAAG;oBAC1C,IAAE,CAAA,CAAED,IAAAA,CAAG,EAAE,CAAC;gBACX,IAAG;oBACF,MAAA,CAAO;oBACP,OAAO,IAAI,cAAc,GAAG;gBAC7B;YACF;YACD,OAAO,CAAA,CAAE,eAAA,OAAsB,UAAU,OAAO,QAAA,IAAY,aAAA,GAAgB,WAAY;gBACtF,OAAO,IAAA;YACR,GAAE,EAAE,IAAA,GAAO,SAAUE,GAAAA,EAAG;gBACvB,OAAO,IAAA,CAAK,IAAA,CAAK,GAAGA,GAAAA,IAAK,KAAK,QAAQA,IAAE;YACzC,GAAE,cAAA,OAAqB,CAAA,CAAE,QAAA,IAAA,CAAa,CAAA,CAAE,QAAA,GAAW,SAAUA,GAAAA,EAAG;gBAC/D,IAAI,EAAG,CAAA,MAAM,IAAA,CAAK,GAAGA;gBACrB,OAAO,KAAK,SAASA,IAAE;YACxB,CAAA,GAAG,cAAA,OAAqB,CAAA,CAAE,SAAA,IAAA,CAAc,CAAA,CAAE,SAAA,GAAY,SAAUA,GAAAA,EAAG;gBAClE,OAAO,IAAA,CAAK,IAAA,CAAK,GAAGA,GAAAA,IAAK,KAAK,UAAUA,IAAE;YAC3C,CAAA,GAAG;QACL;QACD,OAAO,OAAA,GAAUH,2BAAyB,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,OAAO,OAAA,CAAQ,UAAA,GAAa,OAAO,OAAA;;;;;;;;;;AC8C/G,MAAM,aAAa;AACnB,MAAM,yBAAyB;AAC/B,MAAM,kBAAkB;AACxB,MAAM,eAAe;;;;GAYrB,SAAgB,kBACdI,IAAAA,EACA;;IACA,MAAM,EAAE,YAAY,kQAAA,EAAU,GAAG;IAEjC,MAAMC,OAAiC;QACrC,SAAA,CAAA,qBAAA,CAAA,aAAS,KAAK,IAAA,MAAA,QAAA,eAAA,KAAA,IAAA,KAAA,IAAA,WAAM,OAAA,MAAA,QAAA,uBAAA,KAAA,IAAA,qBAAW;QAC/B,YAAA,CAAA,wBAAA,CAAA,cAAY,KAAK,IAAA,MAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAM,UAAA,MAAA,QAAA,0BAAA,KAAA,IAAA,wBAAc;IACtC;IACD,MAAMC,SAAAA,CAAAA,eAA2B,KAAK,MAAA,MAAA,QAAA,iBAAA,KAAA,IAAA,eAAU,CAAE;IAElD,IACE,KAAK,OAAA,IACL,OAAO,0BAAA,IACP,KAAK,UAAA,GAAa,OAAO,0BAAA,CAEzB,CAAA,MAAM,IAAI,MAAA,CACP,iHAAA,EAAmH,KAAK,UAAA,CAAW,oCAAA,EAAsC,OAAO,0BAAA,CAA2B,CAAA;IAIhN,SAAgB;gCAuWZ,IAAA,EAAA;;;2EAvWsD;YACxD,MAAM;gBACJ,OAAO;gBACP,MAAM,KAAK,SAAA,CAAU,OAAO;YAC7B;YAID,IAAIC,WAAoD,KAAK,IAAA;YAE7D,IAAI,KAAK,qBAAA,CACP,CAAA,WAAW,cAAc,UAAU;gBACjC,OAAO;gBACP,eAAe;YAChB,EAAC;YAGJ,IACE,KAAK,aAAA,IACL,KAAK,aAAA,GAAgB,KACrB,KAAK,aAAA,KAAkB,SAEvB,CAAA,WAAW,gBAAgB,UAAU;gBACnC,eAAe,KAAK,aAAA;YACrB,EAAC;YAGJ,IAAI,KAAK,OAAA,IAAW,KAAK,UAAA,KAAe,YAAY,KAAK,UAAA,GAAa,EACpE,CAAA,WAAW,SAAS,UAAU,KAAK,UAAA,CAAW;YAKhD,IAAIC;YACJ,IAAIC;;;;;sEAEgB,WAAA,OAAA,4BAAA,CAAA,CAAA,QAAA,MAAA,CAAA,GAAA,2BAAA,OAAA,EAAA,UAAA,IAAA,GAAA,EAAA,IAAA,EAAA,4BAAA,MAAA;oBAAT,QAAA,MAAA,KAAA;oBAAmB;wBAC5B,IAAI,UAAU,UAAU;4BACtB,MAAM;gCAAE,OAAO;gCAAY,MAAM;4BAAI;4BACrC;wBACD;wBAED,oQAAQ,qBAAA,EAAkB,MAAM,GAC5B;4BAAE,IAAI,KAAA,CAAM,EAAA;4BAAI,MAAM,KAAA,CAAM,EAAA;wBAAI,IAChC;4BAAE,MAAM;wBAAO;wBAEnB,MAAM,IAAA,GAAO,KAAK,SAAA,CAAU,UAAU,MAAM,IAAA,CAAK,CAAC;wBAElD,MAAM;wBAGN,QAAQ;wBACR,QAAQ;oBACT;;;;;;;;;;;;QACF;gCAiTI,IAAA,EAAA;;IA/SL,SAAgB;iDA+SV,IAAA,EAAA;;;4FA/SqE;YACzE,IAAI;gBACF,OAAA,CAAA,GAAA,8BAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAO,WAAW;gBAElB,MAAM;oBACJ,OAAO;oBACP,MAAM;gBACP;YACF,EAAA,OAAQ,OAAO;;gBACd,IAAI,aAAa,MAAM,CAErB,CAAA;gBAIF,MAAM,oQAAQ,2BAAA,EAAwB,MAAM;gBAC5C,MAAM,OAAA,CAAA,oBAAA,CAAA,qBAAO,KAAK,WAAA,MAAA,QAAA,uBAAA,KAAA,IAAA,KAAA,IAAL,mBAAA,IAAA,CAAA,MAAmB;oBAAE;gBAAO,EAAC,MAAA,QAAA,sBAAA,KAAA,IAAA,oBAAI;gBAC9C,MAAM;oBACJ,OAAO;oBACP,MAAM,KAAK,SAAA,CAAU,UAAU,KAAK,CAAC;gBACtC;YACF;QACF;iDAyRM,IAAA,EAAA;;IAvRP,MAAM,SAAS,mBAAmB,4BAA4B,CAAC;IAE/D,OAAO,OACJ,WAAA,CACC,IAAI,gBAAgB;QAClB,WAAU,KAAA,EAAOC,UAAAA,EAAsD;YACrE,IAAI,WAAW,MACb,CAAA,WAAW,OAAA,CAAA,CAAS,OAAA,EAAS,MAAM,KAAA,CAAM,EAAA,CAAA,CAAI;YAE/C,IAAI,UAAU,MACZ,CAAA,WAAW,OAAA,CAAA,CAAS,MAAA,EAAQ,MAAM,IAAA,CAAK,EAAA,CAAA,CAAI;YAE7C,IAAI,QAAQ,MACV,CAAA,WAAW,OAAA,CAAA,CAAS,IAAA,EAAM,MAAM,EAAA,CAAG,EAAA,CAAA,CAAI;YAEzC,IAAI,aAAa,MACf,CAAA,WAAW,OAAA,CAAA,CAAS,EAAA,EAAI,MAAM,OAAA,CAAQ,EAAA,CAAA,CAAI;YAE5C,WAAW,OAAA,CAAQ,OAAO;QAC3B;IACF,GACF,CACA,WAAA,CAAY,IAAI,oBAAoB;AACxC;AA+DD,eAAe,YAAeC,IAAAA,EAIf;;;QACb,MAAM,iBAAA,YAAA,CAAA,CAAiB,cAAc,KAAK,SAAA,CAAU;QACpD,MAAM,MAAM,MAAM,UAAU,IAAA,CAAK;YAAC,KAAK,OAAA;YAAS,eAAe,KAAA,EAAO,AAAC;SAAA,CAAC;QAExE,IAAI,QAAQ,6BACV,CAAA,OAAO,MAAM,KAAK,SAAA,EAAW;QAE/B,OAAO;;;;;;AACR;;;GAKD,SAAgB,kBACdC,IAAAA,EAC8C;IAC9C,MAAM,EAAE,cAAc,CAAC,IAAM,CAAA,EAAG,GAAG;IAEnC,IAAIC,gBAAkC,CAAE;IAExC,MAAM,SAAS,KAAK,MAAA;IAEpB,IAAIC,MAAmD;IAEvD,MAAM,eAAe,IACnB,IAAI,eAA8C;YAChD,MAAM,OAAM,UAAA,EAAY;gBACtB,MAAM,CAAC,KAAK,KAAK,GAAG,MAAM,QAAQ,GAAA,CAAI;oBAAC,KAAK,GAAA,EAAK;oBAAE,KAAK,IAAA,EAAM,AAAC;iBAAA,CAAC;gBAChE,MAAM,cAAe,MAAM,IAAI,KAAK,WAAA,CAClC,KACA;gBAGF,WAAW,OAAA,CAAQ;oBACjB,MAAM;oBACN,aAAa;oBACb,OAAO;gBACR,EAAC;gBAEF,YAAY,gBAAA,CAAiB,iBAAiB,CAAC,SAAS;oBACtD,MAAM,MAAM;oBAEZ,MAAMC,UAA4B,KAAK,KAAA,CAAM,IAAI,IAAA,CAAK;oBAEtD,gBAAgB;oBAChB,WAAW,OAAA,CAAQ;wBACjB,MAAM;wBACN;wBACA;oBACD,EAAC;gBACH,EAAC;gBAEF,YAAY,gBAAA,CAAiB,wBAAwB,CAAC,SAAS;oBAC7D,MAAM,MAAM;oBAEZ,WAAW,OAAA,CAAQ;wBACjB,MAAM;wBACN,OAAO,YAAY,KAAK,KAAA,CAAM,IAAI,IAAA,CAAK,CAAC;wBACxC;oBACD,EAAC;gBACH,EAAC;gBACF,YAAY,gBAAA,CAAiB,YAAY,MAAM;oBAC7C,WAAW,OAAA,CAAQ;wBACjB,MAAM;wBACN;oBACD,EAAC;gBACH,EAAC;gBACF,YAAY,gBAAA,CAAiB,cAAc,MAAM;oBAC/C,YAAY,KAAA,EAAO;oBACnB,WAAW,KAAA,EAAO;oBAClB,MAAM;gBACP,EAAC;gBACF,YAAY,gBAAA,CAAiB,SAAS,CAAC,UAAU;oBAC/C,IAAI,YAAY,UAAA,KAAe,YAAY,MAAA,CACzC,CAAA,WAAW,KAAA,CAAM,MAAM;yBAEvB,WAAW,OAAA,CAAQ;wBACjB,MAAM;wBACN;wBACA;oBACD,EAAC;gBAEL,EAAC;gBACF,YAAY,gBAAA,CAAiB,WAAW,CAAC,SAAS;oBAChD,MAAM,MAAM;oBAEZ,MAAM,QAAQ,YAAY,KAAK,KAAA,CAAM,IAAI,IAAA,CAAK,CAAC;oBAE/C,MAAMC,MAAe;wBACnB,MAAM;oBACP;oBACD,IAAI,IAAI,WAAA,CACN,CAAA,IAAI,EAAA,GAAK,IAAI,WAAA;oBAEf,WAAW,OAAA,CAAQ;wBACjB,MAAM;wBACN,MAAM;wBACN;oBACD,EAAC;gBACH,EAAC;gBAEF,MAAM,UAAU,MAAM;oBACpB,IAAI;wBACF,YAAY,KAAA,EAAO;wBACnB,WAAW,KAAA,EAAO;oBACnB,EAAA,OAAA,SAAO,CAEP;gBACF;gBACD,IAAI,OAAO,OAAA,CACT,CAAA,SAAS;qBAET,OAAO,gBAAA,CAAiB,SAAS,QAAQ;YAE5C;YACD,SAAS;gBACP,QAAA,QAAA,QAAA,KAAA,KAAA,IAAK,KAAA,EAAO;YACb;QACF;IAEH,MAAM,oBAAoB,MAAM;QAC9B,IAAI,SAAS,cAAc;QAC3B,IAAI,SAAS,OAAO,SAAA,EAAW;QAE/B,eAAe,UAAU;YACvB,MAAM,OAAO,MAAA,EAAQ;YACrB,MAAM;QACP;QAED,OAAO,kBACL;YACE,OAAO;gBACL,OAAO,OAAO,IAAA,EAAM;YACrB;YACD,MAAM,WAAW;gBACf,MAAM,SAAS;gBAEf,SAAS,cAAc;gBACvB,SAAS,OAAO,SAAA,EAAW;YAC5B;QACF,GACD,QACD;IACF;IAED,kQAAO,MAAA,EAAA,CAAA,GAAA,4BAAA,OAAA,EAAA,aAAuB;;;YAC5B,MAAY,SAAA,WAAA,CAAA,CAAS,mBAAmB;YAExC,MAAO,KAAM;gBACX,IAAI,UAAU,OAAO,IAAA,EAAM;gBAE3B,MAAM,YAAY,cAAc,0BAAA;gBAChC,IAAI,UACF,CAAA,UAAU,YAAY;oBACpB;oBACA;oBACA,WAAW,YAAY;wBACrB,MAAMC,MAA+B;4BACnC,OAAO;gCACL,MAAM;gCACN,IAAI;gCACJ,aAAa;4BACd;4BACD,MAAM;wBACP;wBAED,MAAM,OAAO,QAAA,EAAU;wBAEvB,OAAO;oBACR;gBACF,EAAC;gBAGJ,MAAM,SAAA,MAAA,CAAA,GAAA,2BAAA,OAAA,EAAe;gBAErB,IAAI,OAAO,IAAA,CACT,CAAA,OAAO,OAAO,KAAA;gBAEhB,MAAM,OAAO,KAAA;YACd;;;;;;IACF,GAAC;AACH;AAED,MAAa,aAAa;IACxB,gBAAgB;IAChB,iBAAiB;IACjB,qBAAqB;IACrB,YAAY;AACb;;;;;ACrbD,SAAS,qBAAqBC,GAAAA,EAAsC;IAClE,kQAAO,MAAA,EAAA,CAAA,GAAA,0BAAA,OAAA,EAAA,aAAuB;QAC5B,MAAM;IACP,GAAC;AACH;AAUD,MAAMC,2BAAiE;IACrE,UAAU;QAAC,MAAO;KAAA;IAClB,OAAO;QAAC,KAAM;KAAA;IACd,cAAc;QAAC,KAAM;KAAA;AACtB;AACD,MAAMC,gDAGF;IAEF,UAAU;QAAC,MAAO;KAAA;IAClB,OAAO;QAAC;QAAO,MAAO;KAAA;IACtB,cAAc;QAAC;QAAO,MAAO;KAAA;AAC9B;AAaD,SAAS,aAAkDC,QAAAA,EAUxD;;IACD,MAAM,EACJ,GAAA,EACA,IAAA,EACA,YAAA,EACA,iBAAA,EACA,SAAS,CAAE,CAAA,EACX,OAAA,EACD,GAAG;IAEJ,IAAI,SAAS,uRAAoB,oBAAA,EAAkB,kBAAkB,GAAG;IAExE,MAAM,kBAAA,CAAmB;IACzB,MAAM,OAAO,kBACT,CAAE,CAAA,GACF,MAAM,OAAA,CAAQ,kBAAkB,GAC9B,oBACA;QAAC,iBAAkB;KAAA;IAEzB,MAAM,OAAA,CAAA,gBAAA,iBAAA,QAAA,iBAAA,KAAA,IAAA,KAAA,IACJ,aAAe;QACb;QACA;QACA,OAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAO,KAAM,KAAA,CAAM,GAAA,CAAI,CAAC,OAAS,KAAK,IAAA,CAAK;QAC3C;QACA;QACA;QACA,MAAA,CAAA,wBAAA,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,mBACE,KAAM,KAAA,CAAM,IAAA,CAAK,CAAC,SAAS;;2CAAK,SAAA,MAAA,QAAA,oBAAA,KAAA,IAAA,KAAA,IAAA,gBAAW,IAAA,CAAK,IAAA;QAAI,EAAC,MAAA,QAAA,qBAAA,KAAA,KAAA,CAAA,mBAAA,iBAAE,SAAA,MAAA,QAAA,qBAAA,KAAA,IAAA,KAAA,IAAA,iBAAW,IAAA,CAC/D,IAAA,MAAA,QAAA,0BAAA,KAAA,IAAA,wBAAQ;IACd,EAAC,MAAA,QAAA,kBAAA,KAAA,IAAA,gBAAI,CAAE;IAEV,IAAI,KAAK,OAAA,EACP;YAAI,KAAK,OAAA,YAAmB,QAC1B,CAAA,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,KAAK,OAAA,CAAQ,OAAA,EAAS,CAC/C,QAAQ,MAAA,CAAO,KAAK,MAAM;;;KAM5B,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAA,CAAQ,KAAK,OAAA,CAAQ,CACrD,IAAI,MAAM,OAAA,CAAQ,MAAM,CACtB,CAAA,KAAK,MAAM,KAAK,MACd,QAAQ,MAAA,CAAO,KAAK,EAAE;wBAER,UAAU,SAC1B,CAAA,QAAQ,GAAA,CAAI,KAAK,MAAM;IAG5B;IAEH,IAAI,KAAK,MAAA,CACP,CAAA,SAAS,KAAK,MAAA;IAGhB,OAAO;QACL;IACD;AACF;AAED,SAAS,kBACPC,KAAAA,EACAC,SAAAA,EAUA;IACA,MAAM,EAAE,MAAA,EAAQ,GAAA,EAAK,OAAA,EAAS,GAAG,UAAU,IAAA;IAC3C,MAAM,YAAQ,mRAAA,EAAwB,MAAM;IAC5C,YAAA,QAAA,YAAA,KAAA,KAAA,QAAU;QACR;QACA,MAAM,UAAU,IAAA;QAChB,OAAO,UAAU,KAAA;QACjB,KAAK,UAAU,GAAA;QACf,MAAM,UAAU,IAAA;QAChB;IACD,EAAC;IACF,MAAM,oBAAoB;QACxB,0QAAO,gBAAA,EAAc;YACnB,QAAQ,OAAO,IAAA,CAAK,OAAA;YACpB;YACA,MAAM,UAAU,IAAA;YAChB,MAAM,UAAU,IAAA;YAChB,OAAO,UAAU,KAAA;YACjB,KAAK,UAAU,GAAA;QAChB,EAAC;IACH;IACD,MAAM,+QAAkB,wBAAA,EACtB,OAAO,IAAA,CAAK,OAAA,EACZ,kBACD;IACD,MAAM,OAAO,KAAK,SAAA,CAAU,gBAAgB;IAC5C,OAAO;QACL;QACA;QACA;IACD;AACF;;;;;GAOD,SAAS,aAAaC,CAAAA,EAAY;IAChC,IAAA,4PAAK,WAAA,EAAS,EAAE,CACd,CAAA,OAAO;IAGT,IAAI,6QAAA,EAAgB,EAAE,CACpB,CAAA,OAAO;IAGT,OACE,OAAO,MAAA,CAAO,EAAE,CAAC,IAAA,CAAK,UAAU,IAAI,OAAO,MAAA,CAAO,EAAE,CAAC,IAAA,CAAK,yQAAA,CAAgB;AAE7E;AAID,eAAsB,gBACpBC,IAAAA,EACmB;;IACnB,MAAM,EAAE,MAAA,EAAQ,GAAA,EAAK,GAAG;IACxB,MAAM,UAAU,IAAI,QAAQ;QAAC;YAAC;YAAQ,aAAc;SAAC;KAAA;IACrD,MAAM,SAAS,OAAO,IAAA,CAAK,OAAA;IAE3B,MAAM,MAAM,IAAI,IAAI,IAAI,GAAA;IAExB,IAAI,IAAI,MAAA,KAAW,OAEjB,CAAA,OAAO,IAAI,SAAS,MAAM;QACxB,QAAQ;IACT;IAGH,MAAM,gBAAA,CAAA,OAAA,CAAA,sBAAgB,KAAK,aAAA,MAAA,QAAA,wBAAA,KAAA,IAAA,sBAAA,CAAA,iBAAiB,KAAK,QAAA,MAAA,QAAA,mBAAA,KAAA,IAAA,KAAA,IAAA,eAAU,OAAA,MAAA,QAAA,SAAA,KAAA,IAAA,OAAW;IACtE,MAAM,sBAAA,CAAA,CAAA,wBACH,KAAK,mBAAA,MAAA,QAAA,0BAAA,KAAA,IAAA,wBAAuB,KAAA,KAAU,IAAI,MAAA,KAAW;IAIxD,MAAMC,YAA0C,MAAM,iQAAA,EAAI,YAAY;QACpE,IAAI;YACF,OAAO;gBAAA,KAAA;gBAEL,MAAM,eAAe;oBACnB;oBACA,MAAM,mBAAmB,KAAK,IAAA,CAAK;oBACnC;oBACA,cAAc,IAAI,YAAA;oBAClB,SAAS,KAAK,GAAA,CAAI,OAAA;oBAClB;gBACD,EAAC,AACH;aAAA;QACF,EAAA,OAAQ,OAAO;YACd,OAAO;6QAAC,0BAAA,EAAwB,MAAM;gBAAA,KAAA,CAAY;aAAA;QACnD;IACF,EAAC;IAOF,MAAMC,iBAA6B,6PAAA,EAAI,MAAM;QAC3C,IAAIC,SAAAA,KAAAA;QACJ,OAAO;YACL,kBAAkB,MAAM;gBACtB,IAAA,CAAK,OACH,CAAA,OAAA,KAAA;gBAEF,OAAO,MAAA,CAAO,EAAA;YACf;YACD,OAAO,MAAM;gBACX,MAAM,CAAC,KAAK,IAAI,GAAG;gBACnB,IAAI,IACF,CAAA,MAAM;gBAER,OAAO;YACR;YACD,QAAQ,OAAO,SAAS;gBACtB,IAAI,OACF,CAAA,MAAM,IAAI,MACR;gBAGJ,IAAI;oBACF,MAAM,MAAM,MAAM,KAAK,aAAA,CAAc;wBACnC;oBACD,EAAC;oBACF,SAAS;wBAAA,KAAA;wBAAY,GAAI;qBAAA;gBAC1B,EAAA,OAAQ,OAAO;oBACd,SAAS;oRAAC,2BAAA,EAAwB,MAAM;wBAAA,KAAA,CAAY;qBAAA;gBACrD;YACF;QACF;IACF,EAAC;IAEF,MAAM,eAAe,sBACjB,gDACA;;;IAKJ,MAAM,eAAe,IAAI,OAAA,CAAQ,GAAA,CAAI,cAAc,KAAK;IAExD,MAAM,kBAAA,CAAA,sBAAA,CAAA,cAAkB,OAAO,GAAA,MAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAK,OAAA,MAAA,QAAA,wBAAA,KAAA,IAAA,sBAAW;IAC/C,IAAI;QACF,MAAM,CAAC,WAAW,KAAK,GAAG;QAC1B,IAAI,UACF,CAAA,MAAM;QAER,IAAI,KAAK,WAAA,IAAA,CAAgB,cACvB,CAAA,MAAM,6PAAI,YAAA,CAAU;YAClB,MAAM;YACN,SAAA,CAAU,qCAAA,CAAA;QACX;8CAGH,IAAI,gBAAA,CAAiB,KAAK,WAAA,CACxB,CAAA,MAAM,IAAI,qQAAA,CAAU;YAClB,SAAA,CAAU,4DAAA,CAAA;YACV,MAAM;QACP;QAEH,MAAM,WAAW,MAAA,CAAO,KAAK;QAM7B,MAAM,WAAW,KAAK,KAAA,CAAM,GAAA,CAAI,OAAO,SAA6B;YAClE,MAAM,OAAO,KAAK,SAAA;YAClB,IAAI;gBACF,IAAI,KAAK,KAAA,CACP,CAAA,MAAM,KAAK,KAAA;gBAGb,IAAA,CAAK,KACH,CAAA,MAAM,4PAAI,aAAA,CAAU;oBAClB,MAAM;oBACN,SAAA,CAAU,4BAAA,EAA8B,KAAK,IAAA,CAAK,CAAA,CAAA;gBACnD;gBAGH,IAAA,CAAK,YAAA,CAAa,KAAK,IAAA,CAAK,IAAA,CAAA,CAAM,QAAA,CAAS,IAAI,MAAA,CAAsB,CACnE,CAAA,MAAM,6PAAI,YAAA,CAAU;oBAClB,MAAM;oBACN,SAAA,CAAU,YAAA,EAAc,IAAI,MAAA,CAAO,YAAA,EAAc,KAAK,IAAA,CAAK,IAAA,CAAK,oBAAA,EAAsB,KAAK,IAAA,CAAK,CAAA,CAAA;gBACjG;gBAGH,IAAI,KAAK,IAAA,CAAK,IAAA,KAAS,gBAErB;8DAAI,KAAK,WAAA,CACP,CAAA,MAAM,6PAAI,YAAA,CAAU;wBAClB,MAAM;wBACN,SAAA,CAAU,+BAAA,CAAA;oBACX;gBACF;gBAEH,MAAMC,OAAgB,MAAM,KAAK;oBAC/B,MAAM,KAAK,IAAA;oBACX,aAAa,KAAK,WAAA;oBAClB,KAAK,WAAW,KAAA,EAAO;oBACvB,MAAM,KAAK,IAAA,CAAK,IAAA;oBAChB,QAAQ,KAAK,GAAA,CAAI,MAAA;gBAClB,EAAC;gBACF,OAAO;oBAAA,KAAA;oBAAY;wBAAE;oBAAM,CAAC;iBAAA;YAC7B,EAAA,OAAQ,OAAO;;gBACd,MAAM,YAAQ,mRAAA,EAAwB,MAAM;gBAC5C,MAAM,QAAQ,KAAK,MAAA,EAAQ;gBAE3B,CAAA,gBAAA,KAAK,OAAA,MAAA,QAAA,kBAAA,KAAA,KAAL,cAAA,IAAA,CAAA,MAAe;oBACb;oBACA,MAAM,KAAK,IAAA;oBACX;oBACA,KAAK,WAAW,gBAAA,EAAkB;oBAClC,MAAA,CAAA,wBAAA,CAAA,mBAAM,KAAK,SAAA,MAAA,QAAA,qBAAA,KAAA,IAAA,KAAA,IAAA,iBAAW,IAAA,CAAK,IAAA,MAAA,QAAA,0BAAA,KAAA,IAAA,wBAAQ;oBACnC,KAAK,KAAK,GAAA;gBACX,EAAC;gBAEF,OAAO;oBAAC;oBAAA,KAAA,CAAiB;iBAAA;YAC1B;QACF,EAAC;QAGF,IAAA,CAAK,KAAK,WAAA,EAAa;YACrB,MAAM,CAAC,KAAK,GAAG,KAAK,KAAA;YACpB,MAAM,CAAC,OAAO,OAAO,GAAG,MAAM,QAAA,CAAS,EAAA;YAEvC,OAAQ,KAAK,IAAA,EAAb;gBACE,KAAK;gBACL,KAAK;gBACL,KAAK;oBAAS;wBAEZ,QAAQ,GAAA,CAAI,gBAAgB,mBAAmB;wBAE/C,IAAI,aAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAa,OAAQ,IAAA,CAAK,CAC5B,CAAA,MAAM,IAAI,qQAAA,CAAU;4BAClB,MAAM;4BACN,SACE;wBACH;wBAEH,MAAMC,MAAwD,QAC1D;4BACE,0QAAO,gBAAA,EAAc;gCACnB;gCACA,KAAK,WAAW,gBAAA,EAAkB;gCAClC;gCACA,OAAO,KAAM,MAAA,EAAQ;gCACrB,MAAM,KAAM,IAAA;gCACZ,MAAM,KAAK,IAAA;4BACZ,EAAC;wBACH,IACD;4BAAE,QAAQ;gCAAE,MAAM,OAAO,IAAA;4BAAM;wBAAE;wBAErC,MAAMC,iBAAe,aAAa;4BAChC,KAAK,WAAW,gBAAA,EAAkB;4BAClC;4BACA,cAAc,KAAK,YAAA;4BACnB,QAAQ,QAAQ;gCAAC,KAAM;6BAAA,GAAG,CAAE,CAAA;4BAC5B;4BACA,mBAAmB;gCAAC,GAAI;6BAAA;wBACzB,EAAC;wBACF,OAAO,IAAI,SACT,KAAK,SAAA,KAAU,iRAAA,EAAsB,QAAQ,IAAI,CAAC,EAClD;4BACE,QAAQA,eAAa,MAAA;4BACrB;wBACD;oBAEJ;gBACD,KAAK;oBAAgB;wBAGnB,MAAMC,sQAAmC,MAAA,EAAI,MAAM;4BACjD,IAAI,MACF,CAAA,OAAO,qBAAqB,MAAM;4BAEpC,IAAA,CAAK,gBACH,CAAA,OAAO,qBACL,6PAAI,YAAA,CAAU;gCACZ,MAAM;gCACN,SAAS;4BACV,GACF;4BAGH,IAAA,CAAK,+QAAA,EAAa,OAAO,IAAA,CAAK,IAAA,4PAAK,kBAAA,EAAgB,OAAO,IAAA,CAAK,CAC7D,CAAA,OAAO,qBACL,6PAAI,YAAA,CAAU;gCACZ,SAAA,CAAU,aAAA,EACR,KAAM,IAAA,CACP,iDAAA,CAAA;gCACD,MAAM;4BACP,GACF;4BAEH,MAAM,iRAAiB,eAAA,EAAa,OAAO,IAAA,CAAK,OAC5C,wRAAA,EAA0B,OAAO,IAAA,EAAM,KAAK,GAAA,CAAI,MAAA,CAAO,GACvD,OAAO,IAAA;4BACX,OAAO;wBACR,EAAC;wBAEF,MAAM,SAAS,kBAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACV,OAAO,GAAA,GAAA,CAAA,GAAA;4BACV,MAAM;4BACN,WAAW,CAAC,IAAM,OAAO,WAAA,CAAY,MAAA,CAAO,SAAA,CAAU,EAAE;4BACxD,aAAY,SAAA,EAAW;;gCACrB,MAAMC,uQAAQ,0BAAA,EAAwB,UAAU,KAAA,CAAM;gCACtD,MAAM,QAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAQ,KAAM,MAAA,EAAQ;gCAC5B,MAAM,OAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAO,KAAM,IAAA;gCACnB,MAAM,OAAA,CAAA,yBAAA,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,mBAAO,KAAM,SAAA,MAAA,QAAA,qBAAA,KAAA,IAAA,KAAA,IAAA,iBAAW,IAAA,CAAK,IAAA,MAAA,QAAA,2BAAA,KAAA,IAAA,yBAAQ;gCAE3C,CAAA,iBAAA,KAAK,OAAA,MAAA,QAAA,mBAAA,KAAA,KAAL,eAAA,IAAA,CAAA,MAAe;oCACb,OAAA;oCACA;oCACA;oCACA,KAAK,WAAW,gBAAA,EAAkB;oCAClC,KAAK,KAAK,GAAA;oCACV;gCACD,EAAC;gCAEF,MAAM,2QAAQ,gBAAA,EAAc;oCAC1B;oCACA,KAAK,WAAW,gBAAA,EAAkB;oCAClC,OAAA;oCACA;oCACA;oCACA;gCACD,EAAC;gCAEF,OAAO;4BACR;2BACD;wBACF,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAA,CAAQ,WAAW,CACnD,QAAQ,GAAA,CAAI,KAAK,MAAM;wBAGzB,MAAMF,iBAAe,aAAa;4BAChC,KAAK,WAAW,gBAAA,EAAkB;4BAClC;4BACA,cAAc,KAAK,YAAA;4BACnB,QAAQ,CAAE,CAAA;4BACV;4BACA,mBAAmB;wBACpB,EAAC;wBAEF,OAAO,IAAI,SAAS,QAAQ;4BAC1B;4BACA,QAAQA,eAAa,MAAA;wBACtB;oBACF;YACF;QACF;QAGD,IAAI,KAAK,MAAA,KAAW,qBAAqB;YAEvC,QAAQ,GAAA,CAAI,gBAAgB,mBAAmB;YAC/C,QAAQ,GAAA,CAAI,qBAAqB,UAAU;YAC3C,MAAMA,iBAAe,aAAa;gBAChC,KAAK,WAAW,gBAAA,EAAkB;gBAClC;gBACA,cAAc,KAAK,YAAA;gBACnB,QAAQ,CAAE,CAAA;gBACV;gBACA,mBAAmB;YACpB,EAAC;YACF,MAAM,SAAS,oBAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GAAA,qBAAA,OAAA,EAAA,CAAA,GACV,OAAO,KAAA,GAAA,CAAA,GAAA;gBAcV,UAAU;gBACV,MAAM,SAAS,GAAA,CAAI,OAAO,QAAQ;oBAChC,MAAM,CAAC,OAAO,OAAO,GAAG,MAAM;oBAE9B,MAAM,OAAO,KAAK,KAAA,CAAM,EAAA;oBAExB,IAAI,OAAO;;wBACT,OAAO;4BACL,0QAAO,gBAAA,EAAc;gCACnB;gCACA,KAAK,WAAW,gBAAA,EAAkB;gCAClC;gCACA,OAAO,KAAM,MAAA,EAAQ;gCACrB,MAAM,KAAM,IAAA;gCACZ,MAAA,CAAA,uBAAA,CAAA,aAAM,KAAM,SAAA,MAAA,QAAA,eAAA,KAAA,IAAA,KAAA,IAAA,WAAW,IAAA,CAAK,IAAA,MAAA,QAAA,yBAAA,KAAA,IAAA,uBAAQ;4BACrC,EAAC;wBACH;oBACF;;;;QAMD,MAAM,YAAW,8QAAA,EAAa,OAAO,IAAA,CAAK,mQACtC,4BAAA,EAA0B,OAAO,IAAA,EAAM,KAAK,GAAA,CAAI,MAAA,CAAO,GACvD,QAAQ,OAAA,CAAQ,OAAO,IAAA,CAAK;oBAChC,OAAO;wBACL,QAAQ,QAAQ,OAAA,CAAQ;4BACtB,MAAM;wBACP,EAAC;oBACH;gBACF,EAAC;gBACF,WAAW,OAAO,WAAA,CAAY,MAAA,CAAO,SAAA;gBACrC,SAAS,CAAC,UAAU;;oBAClB,CAAA,iBAAA,KAAK,OAAA,MAAA,QAAA,mBAAA,KAAA,KAAL,eAAA,IAAA,CAAA,MAAe;wBACb,oQAAO,0BAAA,EAAwB,MAAM;wBACrC,MAAA,KAAA;wBACA,OAAA,KAAA;wBACA,KAAK,WAAW,gBAAA,EAAkB;wBAClC,KAAK,KAAK,GAAA;wBACV,MAAA,CAAA,aAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAM,KAAM,IAAA,MAAA,QAAA,eAAA,KAAA,IAAA,aAAQ;oBACrB,EAAC;gBACH;gBAED,aAAY,SAAA,EAAW;;oBACrB,MAAM,OAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAO,KAAM,KAAA,CAAM,UAAU,IAAA,CAAK,EAAA,CAAA;oBAExC,MAAM,qQAAQ,0BAAA,EAAwB,UAAU,KAAA,CAAM;oBACtD,MAAM,QAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAQ,KAAM,MAAA,EAAQ;oBAC5B,MAAM,OAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAO,KAAM,IAAA;oBACnB,MAAM,OAAA,CAAA,yBAAA,SAAA,QAAA,SAAA,KAAA,KAAA,CAAA,mBAAO,KAAM,SAAA,MAAA,QAAA,qBAAA,KAAA,IAAA,KAAA,IAAA,iBAAW,IAAA,CAAK,IAAA,MAAA,QAAA,2BAAA,KAAA,IAAA,yBAAQ;oBAI3C,MAAM,2QAAQ,gBAAA,EAAc;wBAC1B;wBACA,KAAK,WAAW,gBAAA,EAAkB;wBAClC;wBACA;wBACA;wBACA;oBACD,EAAC;oBAEF,OAAO;gBACR;eACD;YAEF,OAAO,IAAI,SAAS,QAAQ;gBAC1B;gBACA,QAAQA,eAAa,MAAA;YACtB;QACF;;;;;;KASD,QAAQ,GAAA,CAAI,gBAAgB,mBAAmB;QAC/C,MAAMG,UAAuB,CAAC,MAAM,QAAQ,GAAA,CAAI,SAAS,EAAE,GAAA,CACzD,CAAC,QAAmB;YAClB,MAAM,CAAC,OAAO,OAAO,GAAG;YACxB,IAAI,MACF,CAAA,OAAO;YAGT,IAAI,aAAa,OAAO,IAAA,CAAK,CAC3B,CAAA,OAAO;gBACL,6PAAI,YAAA,CAAU;oBACZ,MAAM;oBACN,SACE;gBACH;gBAAA,KAAA,CAEF;aAAA;YAEH,OAAO;QACR,EACF;QACD,MAAM,sBAAsB,QAAQ,GAAA,CAClC,CACE,CAAC,OAAO,OAAO,EACf,UACqD;YACrD,MAAM,OAAO,KAAK,KAAA,CAAM,MAAA;YACxB,IAAI,OAAO;;gBACT,OAAO;oBACL,0QAAO,gBAAA,EAAc;wBACnB;wBACA,KAAK,WAAW,gBAAA,EAAkB;wBAClC;wBACA,OAAO,KAAK,MAAA,EAAQ;wBACpB,MAAM,KAAK,IAAA;wBACX,MAAA,CAAA,yBAAA,CAAA,mBAAM,KAAK,SAAA,MAAA,QAAA,qBAAA,KAAA,IAAA,KAAA,IAAA,iBAAW,IAAA,CAAK,IAAA,MAAA,QAAA,2BAAA,KAAA,IAAA,yBAAQ;oBACpC,EAAC;gBACH;YACF;YACD,OAAO;gBACL,QAAQ;oBAAE,MAAM,OAAO,IAAA;gBAAM;YAC9B;QACF,EACF;QAED,MAAM,SAAS,QACZ,GAAA,CAAI,CAAC,CAAC,MAAM,GAAK,MAAM,CACvB,MAAA,CAAO,QAAQ;QAElB,MAAM,eAAe,aAAa;YAChC,KAAK,WAAW,gBAAA,EAAkB;YAClC;YACA,cAAc,KAAK,YAAA;YACnB,mBAAmB;YACnB;YACA;QACD,EAAC;QAEF,OAAO,IAAI,SACT,KAAK,SAAA,EAAU,oRAAA,EAAsB,QAAQ,oBAAoB,CAAC,EAClE;YACE,QAAQ,aAAa,MAAA;YACrB;QACD;IAEJ,EAAA,OAAQ,OAAO;;QACd,MAAM,CAAC,YAAY,KAAK,GAAG;QAC3B,MAAM,MAAM,WAAW,gBAAA,EAAkB;QAQzC,MAAM,EAAE,KAAA,EAAO,iBAAA,EAAmB,IAAA,EAAM,GAAG,kBAAkB,OAAO;YAClE;YACA,KAAK,WAAW,gBAAA,EAAkB;YAClC,MAAA,CAAA,cAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAM,KAAM,IAAA,MAAA,QAAA,gBAAA,KAAA,IAAA,cAAQ;QACrB,EAAC;QAEF,MAAM,eAAe,aAAa;YAChC;YACA;YACA,cAAc,KAAK,YAAA;YACnB;YACA,QAAQ;gBAAC,KAAM;aAAA;YACf;QACD,EAAC;QAEF,OAAO,IAAI,SAAS,MAAM;YACxB,QAAQ,aAAa,MAAA;YACrB;QACD;IACF;AACF", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19], "debugId": null}}]}