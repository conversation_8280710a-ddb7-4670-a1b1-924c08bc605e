{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,ubAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,ubAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,ubAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,ubAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,ubAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,ubAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,ubAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,ubAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/src/app/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useUser } from '@clerk/nextjs'\nimport { redirect } from 'next/navigation'\nimport { Button } from \"@/components/ui/button\"\nimport { Card, CardContent } from \"@/components/ui/card\"\nimport { Bot, Search, Twitter, Zap, Target, TrendingUp } from \"lucide-react\"\nimport Link from \"next/link\"\n\nexport default function LandingPage() {\n  const { user, isLoaded } = useUser()\n  \n  // Redirect authenticated users to dashboard\n  if (isLoaded && user) {\n    redirect('/dashboard')\n  }\n\n  return (\n    <div className=\"min-h-screen bg-app-background text-app-headline\">\n      {/* Header */}\n      <header className=\"border-b border-app-stroke bg-app-card\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-2xl font-bold text-app-highlight\">BuddyChip</h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link href=\"/sign-in\">\n                <Button variant=\"outline\" className=\"border-app-stroke text-app-headline hover:bg-app-main hover:text-app-secondary\">\n                  Sign In\n                </Button>\n              </Link>\n              <Link href=\"/sign-up\">\n                <Button className=\"bg-app-main text-app-secondary hover:bg-app-highlight\">\n                  Get Started\n                </Button>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"py-20 px-4\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          <h1 className=\"text-5xl md:text-6xl font-bold text-app-headline mb-6\">\n            AI-Powered Social Media\n            <span className=\"block text-app-main\">Reply Assistant</span>\n          </h1>\n          <p className=\"text-xl text-app-headline opacity-80 mb-8 max-w-2xl mx-auto\">\n            Monitor mentions, generate intelligent responses, and grow your social presence with AI that understands your brand voice.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link href=\"/sign-up\">\n              <Button size=\"lg\" className=\"bg-app-main text-app-secondary hover:bg-app-highlight px-8 py-3 text-lg\">\n                Start Free Trial\n              </Button>\n            </Link>\n            <Link href=\"/sign-in\">\n              <Button variant=\"outline\" size=\"lg\" className=\"border-app-stroke text-app-headline hover:bg-app-main hover:text-app-secondary px-8 py-3 text-lg\">\n                Sign In\n              </Button>\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-20 px-4 bg-app-card\">\n        <div className=\"max-w-6xl mx-auto\">\n          <h2 className=\"text-3xl font-bold text-center text-app-headline mb-12\">\n            Everything you need to dominate social media\n          </h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <Card className=\"bg-app-background border-app-stroke\">\n              <CardContent className=\"p-6 text-center\">\n                <div className=\"w-12 h-12 bg-app-main/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <Bot className=\"w-6 h-6 text-app-main\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-app-headline mb-3\">AI Reply Generation</h3>\n                <p className=\"text-app-headline opacity-70\">\n                  Generate contextual, brand-appropriate replies to any mention or comment using advanced AI.\n                </p>\n              </CardContent>\n            </Card>\n            \n            <Card className=\"bg-app-background border-app-stroke\">\n              <CardContent className=\"p-6 text-center\">\n                <div className=\"w-12 h-12 bg-app-main/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <Search className=\"w-6 h-6 text-app-main\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-app-headline mb-3\">Smart Monitoring</h3>\n                <p className=\"text-app-headline opacity-70\">\n                  Track mentions across X/Twitter automatically and never miss an opportunity to engage.\n                </p>\n              </CardContent>\n            </Card>\n            \n            <Card className=\"bg-app-background border-app-stroke\">\n              <CardContent className=\"p-6 text-center\">\n                <div className=\"w-12 h-12 bg-app-main/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <TrendingUp className=\"w-6 h-6 text-app-main\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-app-headline mb-3\">Growth Analytics</h3>\n                <p className=\"text-app-headline opacity-70\">\n                  Track your engagement metrics and see how AI-powered replies boost your social presence.\n                </p>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 px-4\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          <h2 className=\"text-4xl font-bold text-app-headline mb-6\">\n            Ready to transform your social media?\n          </h2>\n          <p className=\"text-xl text-app-headline opacity-80 mb-8\">\n            Join thousands of creators and businesses using BuddyChip to scale their social presence.\n          </p>\n          <Link href=\"/sign-up\">\n            <Button size=\"lg\" className=\"bg-app-main text-app-secondary hover:bg-app-highlight px-8 py-3 text-lg\">\n              Get Started Free\n            </Button>\n          </Link>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"border-t border-app-stroke bg-app-card py-8\">\n        <div className=\"max-w-6xl mx-auto px-4 text-center\">\n          <p className=\"text-app-headline opacity-70\">\n            © 2025 BuddyChip. All rights reserved.\n          </p>\n        </div>\n      </footer>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,uSAAA,CAAA,UAAO,AAAD;IAEjC,4CAA4C;IAC5C,IAAI,YAAY,MAAM;QACpB,CAAA,GAAA,2UAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,qBACE,ubAAC;QAAI,WAAU;;0BAEb,ubAAC;gBAAO,WAAU;0BAChB,cAAA,ubAAC;oBAAI,WAAU;8BACb,cAAA,ubAAC;wBAAI,WAAU;;0CACb,ubAAC;gCAAI,WAAU;0CACb,cAAA,ubAAC;oCAAG,WAAU;8CAAwC;;;;;;;;;;;0CAExD,ubAAC;gCAAI,WAAU;;kDACb,ubAAC,qWAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,ubAAC,iJAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;sDAAiF;;;;;;;;;;;kDAIvH,ubAAC,qWAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,ubAAC,iJAAA,CAAA,SAAM;4CAAC,WAAU;sDAAwD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpF,ubAAC;gBAAQ,WAAU;0BACjB,cAAA,ubAAC;oBAAI,WAAU;;sCACb,ubAAC;4BAAG,WAAU;;gCAAwD;8CAEpE,ubAAC;oCAAK,WAAU;8CAAsB;;;;;;;;;;;;sCAExC,ubAAC;4BAAE,WAAU;sCAA8D;;;;;;sCAG3E,ubAAC;4BAAI,WAAU;;8CACb,ubAAC,qWAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,ubAAC,iJAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;kDAA0E;;;;;;;;;;;8CAIxG,ubAAC,qWAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,ubAAC,iJAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAmG;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASzJ,ubAAC;gBAAQ,WAAU;0BACjB,cAAA,ubAAC;oBAAI,WAAU;;sCACb,ubAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,ubAAC;4BAAI,WAAU;;8CACb,ubAAC,+IAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,ubAAC,+IAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,ubAAC;gDAAI,WAAU;0DACb,cAAA,ubAAC,oRAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;0DAEjB,ubAAC;gDAAG,WAAU;0DAA+C;;;;;;0DAC7D,ubAAC;gDAAE,WAAU;0DAA+B;;;;;;;;;;;;;;;;;8CAMhD,ubAAC,+IAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,ubAAC,+IAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,ubAAC;gDAAI,WAAU;0DACb,cAAA,ubAAC,0RAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,ubAAC;gDAAG,WAAU;0DAA+C;;;;;;0DAC7D,ubAAC;gDAAE,WAAU;0DAA+B;;;;;;;;;;;;;;;;;8CAMhD,ubAAC,+IAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,ubAAC,+IAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,ubAAC;gDAAI,WAAU;0DACb,cAAA,ubAAC,sSAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,ubAAC;gDAAG,WAAU;0DAA+C;;;;;;0DAC7D,ubAAC;gDAAE,WAAU;0DAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUtD,ubAAC;gBAAQ,WAAU;0BACjB,cAAA,ubAAC;oBAAI,WAAU;;sCACb,ubAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAG1D,ubAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAGzD,ubAAC,qWAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,ubAAC,iJAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,WAAU;0CAA0E;;;;;;;;;;;;;;;;;;;;;;0BAQ5G,ubAAC;gBAAO,WAAU;0BAChB,cAAA,ubAAC;oBAAI,WAAU;8BACb,cAAA,ubAAC;wBAAE,WAAU;kCAA+B;;;;;;;;;;;;;;;;;;;;;;AAOtD", "debugId": null}}]}