{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/node_modules__pnpm_04490921._.js", "server/edge/chunks/[root-of-the-server]__9f45d194._.js", "server/edge/chunks/apps_web_edge-wrapper_a462b325.js", "server/edge/chunks/_31b668e4._.js", "server/edge/chunks/68ae3_@clerk_shared_dist_e1abcbbc._.js", "server/edge/chunks/b3412_@clerk_backend_dist_334fdccb._.js", "server/edge/chunks/9aefa_@clerk_nextjs_dist_esm_33e6bb40._.js", "server/edge/chunks/node_modules__pnpm_fac582cc._.js", "server/edge/chunks/[root-of-the-server]__9ae05967._.js", "server/edge/chunks/apps_web_edge-wrapper_d40864d9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "mzIinH1YWNyMD7WtBtBW66vi+sBEHUpA1+aIXbmr4q8=", "__NEXT_PREVIEW_MODE_ID": "f5e26ace19b878f9b073d2d8c9184fa2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "acc98b5e34c349c6bd95541679fd3d744f577f24d4a8feb51807a1c785658745", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bdfbab2a43e5945efc528663a2b089290ac9bac3abda7a4f9d9cbd264eb209d2"}}}, "sortedMiddleware": ["/"], "functions": {}}