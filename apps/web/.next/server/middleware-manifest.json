{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/node_modules__pnpm_04490921._.js", "server/edge/chunks/[root-of-the-server]__9f45d194._.js", "server/edge/chunks/apps_web_edge-wrapper_a462b325.js", "server/edge/chunks/_31b668e4._.js", "server/edge/chunks/68ae3_@clerk_shared_dist_e1abcbbc._.js", "server/edge/chunks/b3412_@clerk_backend_dist_334fdccb._.js", "server/edge/chunks/9aefa_@clerk_nextjs_dist_esm_33e6bb40._.js", "server/edge/chunks/node_modules__pnpm_fac582cc._.js", "server/edge/chunks/[root-of-the-server]__9ae05967._.js", "server/edge/chunks/apps_web_edge-wrapper_d40864d9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "mzIinH1YWNyMD7WtBtBW66vi+sBEHUpA1+aIXbmr4q8=", "__NEXT_PREVIEW_MODE_ID": "69a597481b6be0469898802ef841e252", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4fb5215fff2a9b57d983c7d4bf7ef95c13a48b232b2828b04efa362a3b0600f9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9e246363256959914939d639049093041101b0f11c124c97960426c15afc1a10"}}}, "sortedMiddleware": ["/"], "functions": {}}