const CHUNK_PUBLIC_PATH = "server/app/page.js";
const runtime = require("../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/c4c98_next_dist_19f898be._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__4c58c055._.js");
runtime.loadChunk("server/chunks/ssr/apps_web_src_app_d623397e._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__pnpm_da0759eb._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__pnpm_7f5b007d._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__352b1894._.js");
runtime.loadChunk("server/chunks/ssr/apps_web_src_app_loading_tsx_fc413a50._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__pnpm_3c30f264._.js");
runtime.loadChunk("server/chunks/ssr/c4c98_next_dist_client_components_forbidden-error_06aa488f.js");
runtime.loadChunk("server/chunks/ssr/c4c98_next_dist_client_components_unauthorized-error_489de419.js");
runtime.loadChunk("server/chunks/ssr/_8ad70271._.js");
runtime.loadChunk("server/chunks/ssr/apps_web_src_app_9c53c1ef._.js");
runtime.getOrInstantiateRuntimeModule("[project]/apps/web/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.22.0_next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0_42d1807e423217c6736c669ff09b1861/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.22.0_next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0_42d1807e423217c6736c669ff09b1861/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.27.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { METADATA_0 => \"[project]/apps/web/src/app/icon.svg.mjs { IMAGE => \\\"[project]/apps/web/src/app/icon.svg (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/apps/web/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/apps/web/src/app/loading.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.27.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.27.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.27.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", METADATA_6 => \"[project]/apps/web/src/app/icon.svg.mjs { IMAGE => \\\"[project]/apps/web/src/app/icon.svg (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_7 => \"[project]/apps/web/src/app/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.27.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { METADATA_0 => \"[project]/apps/web/src/app/icon.svg.mjs { IMAGE => \\\"[project]/apps/web/src/app/icon.svg (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/apps/web/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/apps/web/src/app/loading.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.27.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.27.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.27.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", METADATA_6 => \"[project]/apps/web/src/app/icon.svg.mjs { IMAGE => \\\"[project]/apps/web/src/app/icon.svg (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_7 => \"[project]/apps/web/src/app/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
