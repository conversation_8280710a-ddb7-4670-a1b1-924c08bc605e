-- Update the existing Admin user to use the correct Clerk ID
-- This preserves any existing data (monitored accounts, etc.)

-- First, check if the Admin user exists
SELECT id, email, name FROM users WHERE id = 'Admin';

-- Update the Admin user ID to match your Clerk user ID
UPDATE users 
SET id = 'user_2y2xJLvi2ncFa8bEKWJmb94DPTZ'
WHERE id = 'Admin';

-- Update any related records that reference the old user ID
UPDATE monitored_accounts 
SET "userId" = 'user_2y2xJLvi2ncFa8bEKWJmb94DPTZ'
WHERE "userId" = 'Admin';

UPDATE usage_logs 
SET "userId" = 'user_2y2xJLvi2ncFa8bEKWJmb94DPTZ'
WHERE "userId" = 'Admin';

-- Verify the update
SELECT id, email, name, "planId" FROM users WHERE id = 'user_2y2xJLvi2ncFa8bEKWJmb94DPTZ';
