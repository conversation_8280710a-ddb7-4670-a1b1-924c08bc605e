{"name": "@trpc/server", "type": "module", "sideEffects": false, "version": "11.4.2", "description": "The tRPC server library", "author": "KATT", "license": "MIT", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.cts", "homepage": "https://trpc.io", "repository": {"type": "git", "url": "git+https://github.com/trpc/trpc.git", "directory": "packages/server"}, "scripts": {"build": "tsdown", "dev": "tsdown --watch", "benchmark": "tsc --project tsconfig.benchmark.json", "lint": "eslint --cache src", "ts-watch": "tsc --watch"}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./adapters/aws-lambda": {"import": {"types": "./dist/adapters/aws-lambda/index.d.mts", "default": "./dist/adapters/aws-lambda/index.mjs"}, "require": {"types": "./dist/adapters/aws-lambda/index.d.cts", "default": "./dist/adapters/aws-lambda/index.cjs"}}, "./adapters/express": {"import": {"types": "./dist/adapters/express.d.mts", "default": "./dist/adapters/express.mjs"}, "require": {"types": "./dist/adapters/express.d.cts", "default": "./dist/adapters/express.cjs"}}, "./adapters/fastify": {"import": {"types": "./dist/adapters/fastify/index.d.mts", "default": "./dist/adapters/fastify/index.mjs"}, "require": {"types": "./dist/adapters/fastify/index.d.cts", "default": "./dist/adapters/fastify/index.cjs"}}, "./adapters/fetch": {"import": {"types": "./dist/adapters/fetch/index.d.mts", "default": "./dist/adapters/fetch/index.mjs"}, "require": {"types": "./dist/adapters/fetch/index.d.cts", "default": "./dist/adapters/fetch/index.cjs"}}, "./adapters/next-app-dir": {"import": {"types": "./dist/adapters/next-app-dir.d.mts", "default": "./dist/adapters/next-app-dir.mjs"}, "require": {"types": "./dist/adapters/next-app-dir.d.cts", "default": "./dist/adapters/next-app-dir.cjs"}}, "./adapters/next": {"import": {"types": "./dist/adapters/next.d.mts", "default": "./dist/adapters/next.mjs"}, "require": {"types": "./dist/adapters/next.d.cts", "default": "./dist/adapters/next.cjs"}}, "./adapters/node-http": {"import": {"types": "./dist/adapters/node-http/index.d.mts", "default": "./dist/adapters/node-http/index.mjs"}, "require": {"types": "./dist/adapters/node-http/index.d.cts", "default": "./dist/adapters/node-http/index.cjs"}}, "./adapters/standalone": {"import": {"types": "./dist/adapters/standalone.d.mts", "default": "./dist/adapters/standalone.mjs"}, "require": {"types": "./dist/adapters/standalone.d.cts", "default": "./dist/adapters/standalone.cjs"}}, "./adapters/ws": {"import": {"types": "./dist/adapters/ws.d.mts", "default": "./dist/adapters/ws.mjs"}, "require": {"types": "./dist/adapters/ws.d.cts", "default": "./dist/adapters/ws.cjs"}}, "./http": {"import": {"types": "./dist/http.d.mts", "default": "./dist/http.mjs"}, "require": {"types": "./dist/http.d.cts", "default": "./dist/http.cjs"}}, "./observable": {"import": {"types": "./dist/observable/index.d.mts", "default": "./dist/observable/index.mjs"}, "require": {"types": "./dist/observable/index.d.cts", "default": "./dist/observable/index.cjs"}}, "./rpc": {"import": {"types": "./dist/rpc.d.mts", "default": "./dist/rpc.mjs"}, "require": {"types": "./dist/rpc.d.cts", "default": "./dist/rpc.cjs"}}, "./shared": {"import": {"types": "./dist/shared.d.mts", "default": "./dist/shared.mjs"}, "require": {"types": "./dist/shared.d.cts", "default": "./dist/shared.cjs"}}, "./unstable-core-do-not-import": {"import": {"types": "./dist/unstable-core-do-not-import.d.mts", "default": "./dist/unstable-core-do-not-import.mjs"}, "require": {"types": "./dist/unstable-core-do-not-import.d.cts", "default": "./dist/unstable-core-do-not-import.cjs"}}}, "files": ["dist", "src", "README.md", "package.json", "adapters", "http", "observable", "rpc", "shared", "unstable-core-do-not-import", "!**/*.test.*", "!**/__tests__"], "publishConfig": {"access": "public"}, "devDependencies": {"@fastify/websocket": "^10.0.1", "@oxc-project/runtime": "0.69.0", "@tanstack/react-query": "^5.80.3", "@types/aws-lambda": "^8.10.149", "@types/express": "^5.0.0", "@types/hash-sum": "^1.0.0", "@types/node": "^22.13.5", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "@types/ws": "^8.2.0", "devalue": "^5.0.0", "eslint": "^9.26.0", "express": "^5.0.0", "fastify": "^4.16.0", "fastify-plugin": "^5.0.0", "hash-sum": "^2.0.0", "myzod": "^1.3.1", "next": "^15.3.1", "react": "^19.1.0", "react-dom": "^19.1.0", "superjson": "^1.12.4", "superstruct": "^2.0.0", "tsdown": "0.12.7", "typescript": "^5.8.2", "valibot": "1.1.0", "ws": "^8.0.0", "yup": "^1.0.0", "zod": "^3.25.51"}, "funding": ["https://trpc.io/sponsor"], "peerDependencies": {"typescript": ">=5.7.2"}, "gitHead": "094e2ae3041bfb90c3d3bf20470ec7d4feefa381"}