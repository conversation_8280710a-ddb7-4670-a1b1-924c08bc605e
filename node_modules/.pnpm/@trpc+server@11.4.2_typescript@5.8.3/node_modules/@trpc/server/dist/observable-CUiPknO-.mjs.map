{"version": 3, "file": "observable-CUiPknO-.mjs", "names": ["project: (value: TValueBefore, index: number) => TValueAfter", "_opts?: ShareConfig", "subscription: Unsubscribable | null", "observers: <PERSON><PERSON><Observer<TV<PERSON>ue, TError>>[]", "observer: <PERSON><PERSON><Observer<TValue, TError>>", "compare: (a: TValue, b: TValue) => boolean", "lastValue: TValue | typeof distinctUnsetMarker", "a: T", "b: T", "initialValue: TValue", "value: TValue", "observerList: Observer<TValue, never>[]", "observer: Observer<TValue, never>", "nextValue: TValue"], "sources": ["../src/observable/operators.ts", "../src/observable/behaviorSubject.ts"], "sourcesContent": ["import { observable } from './observable';\nimport type {\n  MonoTypeOperatorFunction,\n  Observer,\n  OperatorFunction,\n  Unsubscribable,\n} from './types';\n\nexport function map<TValueBefore, TError, TValueAfter>(\n  project: (value: TValueBefore, index: number) => TValueAfter,\n): OperatorFunction<TValueBefore, TError, TValueAfter, TError> {\n  return (source) => {\n    return observable((destination) => {\n      let index = 0;\n      const subscription = source.subscribe({\n        next(value) {\n          destination.next(project(value, index++));\n        },\n        error(error) {\n          destination.error(error);\n        },\n        complete() {\n          destination.complete();\n        },\n      });\n      return subscription;\n    });\n  };\n}\n\ninterface ShareConfig {}\nexport function share<TValue, TError>(\n  _opts?: ShareConfig,\n): MonoTypeOperatorFunction<TValue, TError> {\n  return (source) => {\n    let refCount = 0;\n\n    let subscription: Unsubscribable | null = null;\n    const observers: Partial<Observer<TValue, TError>>[] = [];\n\n    function startIfNeeded() {\n      if (subscription) {\n        return;\n      }\n      subscription = source.subscribe({\n        next(value) {\n          for (const observer of observers) {\n            observer.next?.(value);\n          }\n        },\n        error(error) {\n          for (const observer of observers) {\n            observer.error?.(error);\n          }\n        },\n        complete() {\n          for (const observer of observers) {\n            observer.complete?.();\n          }\n        },\n      });\n    }\n    function resetIfNeeded() {\n      // \"resetOnRefCountZero\"\n      if (refCount === 0 && subscription) {\n        const _sub = subscription;\n        subscription = null;\n        _sub.unsubscribe();\n      }\n    }\n\n    return observable((subscriber) => {\n      refCount++;\n\n      observers.push(subscriber);\n      startIfNeeded();\n      return {\n        unsubscribe() {\n          refCount--;\n          resetIfNeeded();\n\n          const index = observers.findIndex((v) => v === subscriber);\n\n          if (index > -1) {\n            observers.splice(index, 1);\n          }\n        },\n      };\n    });\n  };\n}\n\nexport function tap<TValue, TError>(\n  observer: Partial<Observer<TValue, TError>>,\n): MonoTypeOperatorFunction<TValue, TError> {\n  return (source) => {\n    return observable((destination) => {\n      return source.subscribe({\n        next(value) {\n          observer.next?.(value);\n          destination.next(value);\n        },\n        error(error) {\n          observer.error?.(error);\n          destination.error(error);\n        },\n        complete() {\n          observer.complete?.();\n          destination.complete();\n        },\n      });\n    });\n  };\n}\n\nconst distinctUnsetMarker = Symbol();\nexport function distinctUntilChanged<TValue, TError>(\n  compare: (a: TValue, b: TValue) => boolean = (a, b) => a === b,\n): MonoTypeOperatorFunction<TValue, TError> {\n  return (source) => {\n    return observable((destination) => {\n      let lastValue: TValue | typeof distinctUnsetMarker = distinctUnsetMarker;\n\n      return source.subscribe({\n        next(value) {\n          if (lastValue !== distinctUnsetMarker && compare(lastValue, value)) {\n            return;\n          }\n          lastValue = value;\n          destination.next(value);\n        },\n        error(error) {\n          destination.error(error);\n        },\n        complete() {\n          destination.complete();\n        },\n      });\n    });\n  };\n}\n\nconst isDeepEqual = <T>(a: T, b: T): boolean => {\n  if (a === b) {\n    return true;\n  }\n  const bothAreObjects =\n    a && b && typeof a === 'object' && typeof b === 'object';\n\n  return (\n    !!bothAreObjects &&\n    Object.keys(a).length === Object.keys(b).length &&\n    Object.entries(a).every(([k, v]) => isDeepEqual(v, b[k as keyof T]))\n  );\n};\nexport function distinctUntilDeepChanged<\n  TValue,\n  TError,\n>(): MonoTypeOperatorFunction<TValue, TError> {\n  return distinctUntilChanged(isDeepEqual);\n}\n", "import { observable } from './observable';\nimport type { Observable, Observer } from './types';\n\nexport interface BehaviorSubject<TValue> extends Observable<TValue, never> {\n  observable: Observable<TValue, never>;\n  next: (value: TValue) => void;\n  get: () => TValue;\n}\n\nexport interface ReadonlyBehaviorSubject<TValue>\n  extends Omit<BehaviorSubject<TValue>, 'next'> {}\n\n/**\n * @internal\n * An observable that maintains and provides a \"current value\" to subscribers\n * @see https://www.learnrxjs.io/learn-rxjs/subjects/behaviorsubject\n */\nexport function behaviorSubject<TValue>(\n  initialValue: TValue,\n): BehaviorSubject<TValue> {\n  let value: TValue = initialValue;\n\n  const observerList: Observer<TValue, never>[] = [];\n\n  const addObserver = (observer: Observer<TValue, never>) => {\n    if (value !== undefined) {\n      observer.next(value);\n    }\n    observerList.push(observer);\n  };\n  const removeObserver = (observer: Observer<TValue, never>) => {\n    observerList.splice(observerList.indexOf(observer), 1);\n  };\n\n  const obs = observable<TValue, never>((observer) => {\n    addObserver(observer);\n    return () => {\n      removeObserver(observer);\n    };\n  }) as BehaviorSubject<TValue>;\n\n  obs.next = (nextValue: TValue) => {\n    if (value === nextValue) {\n      return;\n    }\n    value = nextValue;\n    for (const observer of observerList) {\n      observer.next(nextValue);\n    }\n  };\n\n  obs.get = () => value;\n\n  return obs;\n}\n"], "mappings": ";;;AAQA,SAAgB,IACdA,SAC6D;AAC7D,QAAO,CAAC,WAAW;AACjB,SAAO,WAAW,CAAC,gBAAgB;GACjC,IAAI,QAAQ;GACZ,MAAM,eAAe,OAAO,UAAU;IACpC,KAAK,OAAO;AACV,iBAAY,KAAK,QAAQ,OAAO,QAAQ,CAAC;IAC1C;IACD,MAAM,OAAO;AACX,iBAAY,MAAM,MAAM;IACzB;IACD,WAAW;AACT,iBAAY,UAAU;IACvB;GACF,EAAC;AACF,UAAO;EACR,EAAC;CACH;AACF;AAGD,SAAgB,MACdC,OAC0C;AAC1C,QAAO,CAAC,WAAW;EACjB,IAAI,WAAW;EAEf,IAAIC,eAAsC;EAC1C,MAAMC,YAAiD,CAAE;EAEzD,SAAS,gBAAgB;AACvB,OAAI,aACF;AAEF,kBAAe,OAAO,UAAU;IAC9B,KAAK,OAAO;AACV,UAAK,MAAM,YAAY,WAAW;;AAChC,iCAAS,+CAAT,8BAAgB,MAAM;KACvB;IACF;IACD,MAAM,OAAO;AACX,UAAK,MAAM,YAAY,WAAW;;AAChC,kCAAS,iDAAT,+BAAiB,MAAM;KACxB;IACF;IACD,WAAW;AACT,UAAK,MAAM,YAAY,WAAW;;AAChC,qCAAS,uDAAT,iCAAqB;KACtB;IACF;GACF,EAAC;EACH;EACD,SAAS,gBAAgB;AAEvB,OAAI,aAAa,KAAK,cAAc;IAClC,MAAM,OAAO;AACb,mBAAe;AACf,SAAK,aAAa;GACnB;EACF;AAED,SAAO,WAAW,CAAC,eAAe;AAChC;AAEA,aAAU,KAAK,WAAW;AAC1B,kBAAe;AACf,UAAO,EACL,cAAc;AACZ;AACA,mBAAe;IAEf,MAAM,QAAQ,UAAU,UAAU,CAAC,MAAM,MAAM,WAAW;AAE1D,QAAI,QAAQ,GACV,WAAU,OAAO,OAAO,EAAE;GAE7B,EACF;EACF,EAAC;CACH;AACF;AAED,SAAgB,IACdC,UAC0C;AAC1C,QAAO,CAAC,WAAW;AACjB,SAAO,WAAW,CAAC,gBAAgB;AACjC,UAAO,OAAO,UAAU;IACtB,KAAK,OAAO;;AACV,iCAAS,gDAAT,+BAAgB,MAAM;AACtB,iBAAY,KAAK,MAAM;IACxB;IACD,MAAM,OAAO;;AACX,kCAAS,kDAAT,gCAAiB,MAAM;AACvB,iBAAY,MAAM,MAAM;IACzB;IACD,WAAW;;AACT,qCAAS,wDAAT,kCAAqB;AACrB,iBAAY,UAAU;IACvB;GACF,EAAC;EACH,EAAC;CACH;AACF;AAED,MAAM,sBAAsB,QAAQ;AACpC,SAAgB,qBACdC,UAA6C,CAAC,GAAG,MAAM,MAAM,GACnB;AAC1C,QAAO,CAAC,WAAW;AACjB,SAAO,WAAW,CAAC,gBAAgB;GACjC,IAAIC,YAAiD;AAErD,UAAO,OAAO,UAAU;IACtB,KAAK,OAAO;AACV,SAAI,cAAc,uBAAuB,QAAQ,WAAW,MAAM,CAChE;AAEF,iBAAY;AACZ,iBAAY,KAAK,MAAM;IACxB;IACD,MAAM,OAAO;AACX,iBAAY,MAAM,MAAM;IACzB;IACD,WAAW;AACT,iBAAY,UAAU;IACvB;GACF,EAAC;EACH,EAAC;CACH;AACF;AAED,MAAM,cAAc,CAAIC,GAAMC,MAAkB;AAC9C,KAAI,MAAM,EACR,QAAO;CAET,MAAM,iBACJ,KAAK,YAAY,MAAM,mBAAmB,MAAM;AAElD,UACI,kBACF,OAAO,KAAK,EAAE,CAAC,WAAW,OAAO,KAAK,EAAE,CAAC,UACzC,OAAO,QAAQ,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,YAAY,GAAG,EAAE,GAAc,CAAC;AAEvE;AACD,SAAgB,2BAG8B;AAC5C,QAAO,qBAAqB,YAAY;AACzC;;;;;;;;;AC/ID,SAAgB,gBACdC,cACyB;CACzB,IAAIC,QAAgB;CAEpB,MAAMC,eAA0C,CAAE;CAElD,MAAM,cAAc,CAACC,aAAsC;AACzD,MAAI,iBACF,UAAS,KAAK,MAAM;AAEtB,eAAa,KAAK,SAAS;CAC5B;CACD,MAAM,iBAAiB,CAACA,aAAsC;AAC5D,eAAa,OAAO,aAAa,QAAQ,SAAS,EAAE,EAAE;CACvD;CAED,MAAM,MAAM,WAA0B,CAAC,aAAa;AAClD,cAAY,SAAS;AACrB,SAAO,MAAM;AACX,kBAAe,SAAS;EACzB;CACF,EAAC;AAEF,KAAI,OAAO,CAACC,cAAsB;AAChC,MAAI,UAAU,UACZ;AAEF,UAAQ;AACR,OAAK,MAAM,YAAY,aACrB,UAAS,KAAK,UAAU;CAE3B;AAED,KAAI,MAAM,MAAM;AAEhB,QAAO;AACR"}