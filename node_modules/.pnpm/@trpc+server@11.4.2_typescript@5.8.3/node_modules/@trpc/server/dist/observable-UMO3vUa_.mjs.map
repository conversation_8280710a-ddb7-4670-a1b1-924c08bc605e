{"version": 3, "file": "observable-UMO3vUa_.mjs", "names": ["x: unknown", "subscribe: (observer: Observer<TValue, TError>) => TeardownLogic", "self: Observable<TV<PERSON><PERSON>, TError>", "teardownRef: TeardownLogic | null", "prev: any", "fn: UnaryFunction<any, any>", "observable: Observable<TValue, unknown>", "signal: AbortSignal", "unsub: Unsubscribable | null", "observable", "iterator: AsyncIterator<TValue>"], "sources": ["../src/observable/observable.ts"], "sourcesContent": ["import type { Result } from '../unstable-core-do-not-import';\nimport type {\n  Observable,\n  Observer,\n  OperatorFunction,\n  TeardownLogic,\n  UnaryFunction,\n  Unsubscribable,\n} from './types';\n\n/** @public */\nexport type inferObservableValue<TObservable> =\n  TObservable extends Observable<infer TValue, unknown> ? TValue : never;\n\n/** @public */\nexport function isObservable(x: unknown): x is Observable<unknown, unknown> {\n  return typeof x === 'object' && x !== null && 'subscribe' in x;\n}\n\n/** @public */\nexport function observable<TValue, TError = unknown>(\n  subscribe: (observer: Observer<TValue, TError>) => TeardownLogic,\n): Observable<TValue, TError> {\n  const self: Observable<TValue, TError> = {\n    subscribe(observer) {\n      let teardownRef: TeardownLogic | null = null;\n      let isDone = false;\n      let unsubscribed = false;\n      let teardownImmediately = false;\n      function unsubscribe() {\n        if (teardownRef === null) {\n          teardownImmediately = true;\n          return;\n        }\n        if (unsubscribed) {\n          return;\n        }\n        unsubscribed = true;\n\n        if (typeof teardownRef === 'function') {\n          teardownRef();\n        } else if (teardownRef) {\n          teardownRef.unsubscribe();\n        }\n      }\n      teardownRef = subscribe({\n        next(value) {\n          if (isDone) {\n            return;\n          }\n          observer.next?.(value);\n        },\n        error(err) {\n          if (isDone) {\n            return;\n          }\n          isDone = true;\n          observer.error?.(err);\n          unsubscribe();\n        },\n        complete() {\n          if (isDone) {\n            return;\n          }\n          isDone = true;\n          observer.complete?.();\n          unsubscribe();\n        },\n      });\n      if (teardownImmediately) {\n        unsubscribe();\n      }\n      return {\n        unsubscribe,\n      };\n    },\n    pipe(\n      ...operations: OperatorFunction<any, any, any, any>[]\n    ): Observable<any, any> {\n      return operations.reduce(pipeReducer, self);\n    },\n  };\n  return self;\n}\n\nfunction pipeReducer(prev: any, fn: UnaryFunction<any, any>) {\n  return fn(prev);\n}\n\n/** @internal */\nexport function observableToPromise<TValue>(\n  observable: Observable<TValue, unknown>,\n) {\n  const ac = new AbortController();\n  const promise = new Promise<TValue>((resolve, reject) => {\n    let isDone = false;\n    function onDone() {\n      if (isDone) {\n        return;\n      }\n      isDone = true;\n      obs$.unsubscribe();\n    }\n    ac.signal.addEventListener('abort', () => {\n      reject(ac.signal.reason);\n    });\n    const obs$ = observable.subscribe({\n      next(data) {\n        isDone = true;\n        resolve(data);\n        onDone();\n      },\n      error(data) {\n        reject(data);\n      },\n      complete() {\n        ac.abort();\n        onDone();\n      },\n    });\n  });\n  return promise;\n}\n\n/**\n * @internal\n */\nfunction observableToReadableStream<TValue>(\n  observable: Observable<TValue, unknown>,\n  signal: AbortSignal,\n): ReadableStream<Result<TValue>> {\n  let unsub: Unsubscribable | null = null;\n\n  const onAbort = () => {\n    unsub?.unsubscribe();\n    unsub = null;\n    signal.removeEventListener('abort', onAbort);\n  };\n\n  return new ReadableStream<Result<TValue>>({\n    start(controller) {\n      unsub = observable.subscribe({\n        next(data) {\n          controller.enqueue({ ok: true, value: data });\n        },\n        error(error) {\n          controller.enqueue({ ok: false, error });\n          controller.close();\n        },\n        complete() {\n          controller.close();\n        },\n      });\n\n      if (signal.aborted) {\n        onAbort();\n      } else {\n        signal.addEventListener('abort', onAbort, { once: true });\n      }\n    },\n    cancel() {\n      onAbort();\n    },\n  });\n}\n\n/** @internal */\nexport function observableToAsyncIterable<TValue>(\n  observable: Observable<TValue, unknown>,\n  signal: AbortSignal,\n): AsyncIterable<TValue> {\n  const stream = observableToReadableStream(observable, signal);\n\n  const reader = stream.getReader();\n  const iterator: AsyncIterator<TValue> = {\n    async next() {\n      const value = await reader.read();\n      if (value.done) {\n        return {\n          value: undefined,\n          done: true,\n        };\n      }\n      const { value: result } = value;\n      if (!result.ok) {\n        throw result.error;\n      }\n      return {\n        value: result.value,\n        done: false,\n      };\n    },\n    async return() {\n      await reader.cancel();\n      return {\n        value: undefined,\n        done: true,\n      };\n    },\n  };\n  return {\n    [Symbol.asyncIterator]() {\n      return iterator;\n    },\n  };\n}\n"], "mappings": ";;AAeA,SAAgB,aAAaA,GAA+C;AAC1E,eAAc,MAAM,YAAY,MAAM,QAAQ,eAAe;AAC9D;;AAGD,SAAgB,WACdC,WAC4B;CAC5B,MAAMC,OAAmC;EACvC,UAAU,UAAU;GAClB,IAAIC,cAAoC;GACxC,IAAI,SAAS;GACb,IAAI,eAAe;GACnB,IAAI,sBAAsB;GAC1B,SAAS,cAAc;AACrB,QAAI,gBAAgB,MAAM;AACxB,2BAAsB;AACtB;IACD;AACD,QAAI,aACF;AAEF,mBAAe;AAEf,eAAW,gBAAgB,WACzB,cAAa;aACJ,YACT,aAAY,aAAa;GAE5B;AACD,iBAAc,UAAU;IACtB,KAAK,OAAO;;AACV,SAAI,OACF;AAEF,gCAAS,+CAAT,8BAAgB,MAAM;IACvB;IACD,MAAM,KAAK;;AACT,SAAI,OACF;AAEF,cAAS;AACT,iCAAS,iDAAT,+BAAiB,IAAI;AACrB,kBAAa;IACd;IACD,WAAW;;AACT,SAAI,OACF;AAEF,cAAS;AACT,oCAAS,uDAAT,iCAAqB;AACrB,kBAAa;IACd;GACF,EAAC;AACF,OAAI,oBACF,cAAa;AAEf,UAAO,EACL,YACD;EACF;EACD,KACE,GAAG,YACmB;AACtB,UAAO,WAAW,OAAO,aAAa,KAAK;EAC5C;CACF;AACD,QAAO;AACR;AAED,SAAS,YAAYC,MAAWC,IAA6B;AAC3D,QAAO,GAAG,KAAK;AAChB;;AAGD,SAAgB,oBACdC,cACA;CACA,MAAM,KAAK,IAAI;CACf,MAAM,UAAU,IAAI,QAAgB,CAAC,SAAS,WAAW;EACvD,IAAI,SAAS;EACb,SAAS,SAAS;AAChB,OAAI,OACF;AAEF,YAAS;AACT,QAAK,aAAa;EACnB;AACD,KAAG,OAAO,iBAAiB,SAAS,MAAM;AACxC,UAAO,GAAG,OAAO,OAAO;EACzB,EAAC;EACF,MAAM,OAAO,aAAW,UAAU;GAChC,KAAK,MAAM;AACT,aAAS;AACT,YAAQ,KAAK;AACb,YAAQ;GACT;GACD,MAAM,MAAM;AACV,WAAO,KAAK;GACb;GACD,WAAW;AACT,OAAG,OAAO;AACV,YAAQ;GACT;EACF,EAAC;CACH;AACD,QAAO;AACR;;;;AAKD,SAAS,2BACPA,cACAC,QACgC;CAChC,IAAIC,QAA+B;CAEnC,MAAM,UAAU,MAAM;AACpB,8CAAO,aAAa;AACpB,UAAQ;AACR,SAAO,oBAAoB,SAAS,QAAQ;CAC7C;AAED,QAAO,IAAI,eAA+B;EACxC,MAAM,YAAY;AAChB,WAAQ,aAAW,UAAU;IAC3B,KAAK,MAAM;AACT,gBAAW,QAAQ;MAAE,IAAI;MAAM,OAAO;KAAM,EAAC;IAC9C;IACD,MAAM,OAAO;AACX,gBAAW,QAAQ;MAAE,IAAI;MAAO;KAAO,EAAC;AACxC,gBAAW,OAAO;IACnB;IACD,WAAW;AACT,gBAAW,OAAO;IACnB;GACF,EAAC;AAEF,OAAI,OAAO,QACT,UAAS;OAET,QAAO,iBAAiB,SAAS,SAAS,EAAE,MAAM,KAAM,EAAC;EAE5D;EACD,SAAS;AACP,YAAS;EACV;CACF;AACF;;AAGD,SAAgB,0BACdF,cACAC,QACuB;CACvB,MAAM,SAAS,2BAA2BE,cAAY,OAAO;CAE7D,MAAM,SAAS,OAAO,WAAW;CACjC,MAAMC,WAAkC;EACtC,MAAM,OAAO;GACX,MAAM,QAAQ,MAAM,OAAO,MAAM;AACjC,OAAI,MAAM,KACR,QAAO;IACL;IACA,MAAM;GACP;GAEH,MAAM,EAAE,OAAO,QAAQ,GAAG;AAC1B,QAAK,OAAO,GACV,OAAM,OAAO;AAEf,UAAO;IACL,OAAO,OAAO;IACd,MAAM;GACP;EACF;EACD,MAAM,SAAS;AACb,SAAM,OAAO,QAAQ;AACrB,UAAO;IACL;IACA,MAAM;GACP;EACF;CACF;AACD,QAAO,EACL,CAAC,OAAO,iBAAiB;AACvB,SAAO;CACR,EACF;AACF"}