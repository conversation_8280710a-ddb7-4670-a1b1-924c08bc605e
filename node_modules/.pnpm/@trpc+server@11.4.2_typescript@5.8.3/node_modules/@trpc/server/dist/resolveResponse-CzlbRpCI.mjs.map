{"version": 3, "file": "resolveResponse-CzlbRpCI.mjs", "names": ["parsed: unknown", "str: string", "fn: () => Promise<TReturn>", "promise: Promise<TReturn> | null", "value: TReturn | typeof sym", "jsonContentTypeHandler: ContentTypeHandler", "inputs: unknown", "acc: InputRecord", "type: ProcedureType | 'unknown'", "info: TRPCRequestInfo", "formDataContentTypeHandler: ContentTypeHandler", "octetStreamContentTypeHandler: ContentTypeHandler", "req: Request", "handler", "opts: GetRequestInfoOptions", "error: unknown", "arg: Promise<T> | PromiseLike<T> | PromiseExecutor<T>", "promise: Promise<T>", "unsubscribe: () => void", "onfulfilled?:\n      | ((value: T) => TResult1 | PromiseLike<TResult1>)\n      | null", "onrejected?:\n      | ((reason: any) => TResult2 | PromiseLike<TResult2>)\n      | null", "onrejected?:\n      | ((reason: any) => TResult | PromiseLike<TResult>)\n      | null", "onfinally?: (() => void) | null", "promise: Promise<PERSON>ike<T>", "value: T | PromiseLike<T>", "values: Iterable<T | PromiseLike<T>>", "promises: readonly <PERSON><PERSON><PERSON><PERSON>[]", "promise: T<PERSON><PERSON><PERSON>", "resolve!: PromiseWithResolvers<T>[\"resolve\"]", "reject!: PromiseWithResolvers<T>[\"reject\"]", "arr: readonly T[]", "member: T", "index: number", "member: unknown", "thing: T", "dispose: () => void", "dispose: () => Promise<void>", "ms: number", "timer: ReturnType<typeof setTimeout> | null", "r", "e", "n", "o", "OverloadYield", "_awaitAsyncGenerator", "OverloadYield", "_wrapAsyncGenerator", "r", "t", "e", "iterable: AsyncIterable<T<PERSON>ield, TReturn, TNext>", "iterable: AsyncIterable<T>", "opts: { maxDurationMs: number }", "result: null | IteratorResult<T> | typeof disposablePromiseTimerResult", "opts: {\n    count: number;\n    gracePeriodMs: number;\n  }", "resolve: (value: TValue) => void", "reject: (error: unknown) => void", "iterable: AsyncIterable<TY<PERSON>, TReturn>", "onResult: (result: ManagedIteratorResult<TYield, TReturn>) => void", "state: 'idle' | 'pending' | 'done'", "iterables: AsyncIterable<TYield, void, unknown>[]", "buffer: <PERSON><PERSON>y<\n    [\n      iterator: ManagedIterator<TYield, void>,\n      result: Exclude<\n        ManagedIteratorResult<TYield, void>,\n        { status: 'return' }\n      >,\n    ]\n  >", "iterable: AsyncIterable<TYield, void, unknown>", "errors: unknown[]", "iterable: AsyncIterable<TYield, void>", "iterable: AsyncIterable<TValue>", "pingIntervalMs: number", "result:\n    | null\n    | IteratorResult<TValue>\n    | typeof disposablePromiseTimerResult", "_asyncIterator", "r", "AsyncFromSyncIterator", "value: unknown", "path: (string | number)[]", "opts: JSONLProducerOptions", "callback: (idx: ChunkIndex) => AsyncIterable<ChunkData, void>", "iterable", "promise: Promise<unknown>", "iterable: AsyncIterable<unknown>", "newObj: Record<string, unknown>", "asyncValues: ChunkDefinition[]", "newHead: Head", "iterable: AsyncIterable<ChunkData | typeof PING_SYM, void>", "data: unknown", "source: NodeJSReadableStreamEsque", "from: NodeJSReadableStreamEsque | WebReadableStreamEsque", "chunk: ChunkData", "abortController: AbortController", "originalController: ReadableStreamDefaultController<ChunkData>", "v: ChunkData", "reason: unknown", "chunkId: ChunkIndex", "opts: {\n  from: NodeJSReadableStreamEsque | WebReadableStreamEsque;\n  deserialize?: Deserialize;\n  onError?: ConsumerOnError;\n  formatError?: (opts: { error: unknown }) => Error;\n  /**\n   * This `AbortController` will be triggered when there are no more listeners to the stream.\n   */\n  abortController: AbortController;\n}", "headDeferred: null | Deferred<THead>", "value: ChunkDefinition", "value", "value: EncodedValue", "_asyncGeneratorDelegate", "e", "n", "t", "opts: SSEStreamProducerOptions<TValue>", "ping: Required<SSEPingOptions>", "client: SSEClientOptions", "iterable: AsyncIterable<TValue | typeof PING_SYM>", "value: null | TIteratorValue", "chunk: null | SSEvent", "controller: TransformStreamDefaultController<string>", "opts: {\n  promise: Promise<T>;\n  timeoutMs: number;\n  onTimeout: () => Promise<NoInfer<T>>;\n}", "opts: SSEStreamConsumerOptions<TConfig>", "clientOptions: SSEClientOptions", "_es: InstanceType<TConfig['EventSource']> | null", "options: SSEClientOptions", "def: <PERSON><PERSON><PERSON>", "res: Awaited<typeof promise>", "err: TRPCError", "TYPE_ACCEPTED_METHOD_MAP: Record<ProcedureType, HTTPMethods[]>", "TYPE_ACCEPTED_METHOD_MAP_WITH_METHOD_OVERRIDE: Record<\n  ProcedureType,\n  HTTPMethods[]\n>", "initOpts: {\n  ctx: inferRouterContext<TRouter> | undefined;\n  info: TRPCRequestInfo | undefined;\n  responseMeta?: HTTPBaseHandlerOptions<TRouter, TRequest>['responseMeta'];\n  untransformedJSON:\n    | TRPCResponse<unknown, inferRouterError<TRouter>>\n    | TRPCResponse<unknown, inferRouterError<TRouter>>[]\n    | null;\n  errors: TRPCError[];\n  headers: Headers;\n}", "cause: unknown", "errorOpts: {\n    opts: Pick<\n      ResolveHTTPRequestOptions<TRouter>,\n      'onError' | 'req' | 'router'\n    >;\n    ctx: inferRouterContext<TRouter> | undefined;\n    type: ProcedureType | 'unknown';\n    path?: string;\n    input?: unknown;\n  }", "v: unknown", "opts: ResolveHTTPRequestOptions<TRouter>", "infoTuple: ResultTuple<TRPCRequestInfo>", "ctxManager: ContextManager", "result: ResultTuple<$Context> | undefined", "data: unknown", "res: TRPCResponse<unknown, inferRouterError<TRouter>>", "headResponse", "iterable: AsyncIterable<unknown>", "error", "results: RPCResult[]"], "sources": ["../src/unstable-core-do-not-import/http/parseConnectionParams.ts", "../src/unstable-core-do-not-import/http/contentType.ts", "../src/unstable-core-do-not-import/http/abortError.ts", "../src/vendor/unpromise/unpromise.ts", "../src/unstable-core-do-not-import/stream/utils/disposable.ts", "../src/unstable-core-do-not-import/stream/utils/timerResource.ts", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/usingCtx.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/OverloadYield.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/awaitAsyncGenerator.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/wrapAsyncGenerator.js", "../src/unstable-core-do-not-import/stream/utils/asyncIterable.ts", "../src/unstable-core-do-not-import/stream/utils/createDeferred.ts", "../src/unstable-core-do-not-import/stream/utils/mergeAsyncIterables.ts", "../src/unstable-core-do-not-import/stream/utils/readableStreamFrom.ts", "../src/unstable-core-do-not-import/stream/utils/withPing.ts", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/asyncIterator.js", "../src/unstable-core-do-not-import/stream/jsonl.ts", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/asyncGeneratorDelegate.js", "../src/unstable-core-do-not-import/stream/sse.ts", "../src/unstable-core-do-not-import/http/resolveResponse.ts"], "sourcesContent": ["import { TRPCError } from '../error/TRPCError';\nimport { isObject } from '../utils';\nimport type { TRPCRequestInfo } from './types';\n\nexport function parseConnectionParamsFromUnknown(\n  parsed: unknown,\n): TRPCRequestInfo['connectionParams'] {\n  try {\n    if (parsed === null) {\n      return null;\n    }\n    if (!isObject(parsed)) {\n      throw new Error('Expected object');\n    }\n    const nonStringValues = Object.entries(parsed).filter(\n      ([_key, value]) => typeof value !== 'string',\n    );\n\n    if (nonStringValues.length > 0) {\n      throw new Error(\n        `Expected connectionParams to be string values. Got ${nonStringValues\n          .map(([key, value]) => `${key}: ${typeof value}`)\n          .join(', ')}`,\n      );\n    }\n    return parsed as Record<string, string>;\n  } catch (cause) {\n    throw new TRPCError({\n      code: 'PARSE_ERROR',\n      message: 'Invalid connection params shape',\n      cause,\n    });\n  }\n}\nexport function parseConnectionParamsFromString(\n  str: string,\n): TRPCRequestInfo['connectionParams'] {\n  let parsed: unknown;\n  try {\n    parsed = JSON.parse(str);\n  } catch (cause) {\n    throw new TRPCError({\n      code: 'PARSE_ERROR',\n      message: 'Not JSON-parsable query params',\n      cause,\n    });\n  }\n  return parseConnectionParamsFromUnknown(parsed);\n}\n", "import { TRPCError } from '../error/TRPCError';\nimport type { ProcedureType } from '../procedure';\nimport { getProcedureAtPath, type AnyRouter } from '../router';\nimport { isObject } from '../utils';\nimport { parseConnectionParamsFromString } from './parseConnectionParams';\nimport type { TRPCAcceptHeader, TRPCRequestInfo } from './types';\n\ntype GetRequestInfoOptions = {\n  path: string;\n  req: Request;\n  url: URL | null;\n  searchParams: URLSearchParams;\n  headers: Headers;\n  router: AnyRouter;\n};\n\ntype ContentTypeHandler = {\n  isMatch: (opts: Request) => boolean;\n  parse: (opts: GetRequestInfoOptions) => Promise<TRPCRequestInfo>;\n};\n\n/**\n * Memoize a function that takes no arguments\n * @internal\n */\nfunction memo<TReturn>(fn: () => Promise<TReturn>) {\n  let promise: Promise<TReturn> | null = null;\n  const sym = Symbol.for('@trpc/server/http/memo');\n  let value: TReturn | typeof sym = sym;\n  return {\n    /**\n     * Lazily read the value\n     */\n    read: async (): Promise<TReturn> => {\n      if (value !== sym) {\n        return value;\n      }\n\n      // dedupes promises and catches errors\n      promise ??= fn().catch((cause) => {\n        if (cause instanceof TRPCError) {\n          throw cause;\n        }\n        throw new TRPCError({\n          code: 'BAD_REQUEST',\n          message: cause instanceof Error ? cause.message : 'Invalid input',\n          cause,\n        });\n      });\n\n      value = await promise;\n      promise = null;\n\n      return value;\n    },\n    /**\n     * Get an already stored result\n     */\n    result: (): TReturn | undefined => {\n      return value !== sym ? value : undefined;\n    },\n  };\n}\n\nconst jsonContentTypeHandler: ContentTypeHandler = {\n  isMatch(req) {\n    return !!req.headers.get('content-type')?.startsWith('application/json');\n  },\n  async parse(opts) {\n    const { req } = opts;\n    const isBatchCall = opts.searchParams.get('batch') === '1';\n    const paths = isBatchCall ? opts.path.split(',') : [opts.path];\n\n    type InputRecord = Record<number, unknown>;\n    const getInputs = memo(async (): Promise<InputRecord> => {\n      let inputs: unknown = undefined;\n      if (req.method === 'GET') {\n        const queryInput = opts.searchParams.get('input');\n        if (queryInput) {\n          inputs = JSON.parse(queryInput);\n        }\n      } else {\n        inputs = await req.json();\n      }\n      if (inputs === undefined) {\n        return {};\n      }\n\n      if (!isBatchCall) {\n        return {\n          0: opts.router._def._config.transformer.input.deserialize(inputs),\n        };\n      }\n\n      if (!isObject(inputs)) {\n        throw new TRPCError({\n          code: 'BAD_REQUEST',\n          message: '\"input\" needs to be an object when doing a batch call',\n        });\n      }\n      const acc: InputRecord = {};\n      for (const index of paths.keys()) {\n        const input = inputs[index];\n        if (input !== undefined) {\n          acc[index] =\n            opts.router._def._config.transformer.input.deserialize(input);\n        }\n      }\n\n      return acc;\n    });\n\n    const calls = await Promise.all(\n      paths.map(\n        async (path, index): Promise<TRPCRequestInfo['calls'][number]> => {\n          const procedure = await getProcedureAtPath(opts.router, path);\n          return {\n            path,\n            procedure,\n            getRawInput: async () => {\n              const inputs = await getInputs.read();\n              let input = inputs[index];\n\n              if (procedure?._def.type === 'subscription') {\n                const lastEventId =\n                  opts.headers.get('last-event-id') ??\n                  opts.searchParams.get('lastEventId') ??\n                  opts.searchParams.get('Last-Event-Id');\n\n                if (lastEventId) {\n                  if (isObject(input)) {\n                    input = {\n                      ...input,\n                      lastEventId: lastEventId,\n                    };\n                  } else {\n                    input ??= {\n                      lastEventId: lastEventId,\n                    };\n                  }\n                }\n              }\n              return input;\n            },\n            result: () => {\n              return getInputs.result()?.[index];\n            },\n          };\n        },\n      ),\n    );\n\n    const types = new Set(\n      calls.map((call) => call.procedure?._def.type).filter(Boolean),\n    );\n\n    /* istanbul ignore if -- @preserve */\n    if (types.size > 1) {\n      throw new TRPCError({\n        code: 'BAD_REQUEST',\n        message: `Cannot mix procedure types in call: ${Array.from(types).join(\n          ', ',\n        )}`,\n      });\n    }\n    const type: ProcedureType | 'unknown' =\n      types.values().next().value ?? 'unknown';\n\n    const connectionParamsStr = opts.searchParams.get('connectionParams');\n\n    const info: TRPCRequestInfo = {\n      isBatchCall,\n      accept: req.headers.get('trpc-accept') as TRPCAcceptHeader | null,\n      calls,\n      type,\n      connectionParams:\n        connectionParamsStr === null\n          ? null\n          : parseConnectionParamsFromString(connectionParamsStr),\n      signal: req.signal,\n      url: opts.url,\n    };\n    return info;\n  },\n};\n\nconst formDataContentTypeHandler: ContentTypeHandler = {\n  isMatch(req) {\n    return !!req.headers.get('content-type')?.startsWith('multipart/form-data');\n  },\n  async parse(opts) {\n    const { req } = opts;\n    if (req.method !== 'POST') {\n      throw new TRPCError({\n        code: 'METHOD_NOT_SUPPORTED',\n        message:\n          'Only POST requests are supported for multipart/form-data requests',\n      });\n    }\n    const getInputs = memo(async () => {\n      const fd = await req.formData();\n      return fd;\n    });\n    const procedure = await getProcedureAtPath(opts.router, opts.path);\n    return {\n      accept: null,\n      calls: [\n        {\n          path: opts.path,\n          getRawInput: getInputs.read,\n          result: getInputs.result,\n          procedure,\n        },\n      ],\n      isBatchCall: false,\n      type: 'mutation',\n      connectionParams: null,\n      signal: req.signal,\n      url: opts.url,\n    };\n  },\n};\n\nconst octetStreamContentTypeHandler: ContentTypeHandler = {\n  isMatch(req) {\n    return !!req.headers\n      .get('content-type')\n      ?.startsWith('application/octet-stream');\n  },\n  async parse(opts) {\n    const { req } = opts;\n    if (req.method !== 'POST') {\n      throw new TRPCError({\n        code: 'METHOD_NOT_SUPPORTED',\n        message:\n          'Only POST requests are supported for application/octet-stream requests',\n      });\n    }\n    const getInputs = memo(async () => {\n      return req.body;\n    });\n    return {\n      calls: [\n        {\n          path: opts.path,\n          getRawInput: getInputs.read,\n          result: getInputs.result,\n          procedure: await getProcedureAtPath(opts.router, opts.path),\n        },\n      ],\n      isBatchCall: false,\n      accept: null,\n      type: 'mutation',\n      connectionParams: null,\n      signal: req.signal,\n      url: opts.url,\n    };\n  },\n};\n\nconst handlers = [\n  jsonContentTypeHandler,\n  formDataContentTypeHandler,\n  octetStreamContentTypeHandler,\n];\n\nfunction getContentTypeHandler(req: Request): ContentTypeHandler {\n  const handler = handlers.find((handler) => handler.isMatch(req));\n  if (handler) {\n    return handler;\n  }\n\n  if (!handler && req.method === 'GET') {\n    // fallback to JSON for get requests so GET-requests can be opened in browser easily\n    return jsonContentTypeHandler;\n  }\n\n  throw new TRPCError({\n    code: 'UNSUPPORTED_MEDIA_TYPE',\n    message: req.headers.has('content-type')\n      ? `Unsupported content-type \"${req.headers.get('content-type')}`\n      : 'Missing content-type header',\n  });\n}\n\nexport async function getRequestInfo(\n  opts: GetRequestInfoOptions,\n): Promise<TRPCRequestInfo> {\n  const handler = getContentTypeHandler(opts.req);\n  return await handler.parse(opts);\n}\n", "import { isObject } from '../utils';\n\nexport function isAbortError(\n  error: unknown,\n): error is DOMException | Error | { name: 'AbortError' } {\n  return isObject(error) && error['name'] === 'AbortError';\n}\n\nexport function throwAbortError(message = 'AbortError'): never {\n  throw new DOMException(message, 'AbortError');\n}\n", "/* eslint-disable @typescript-eslint/unbound-method */\n \n \n\nimport type {\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  PromiseWithResolvers,\n  ProxyPromise,\n  SubscribedPromise,\n} from \"./types\";\n\n/** Memory safe (weakmapped) cache of the ProxyPromise for each Promise,\n * which is retained for the lifetime of the original Promise.\n */\nconst subscribableCache = new WeakMap<\n  PromiseLike<unknown>,\n  ProxyPromise<unknown>\n>();\n\n/** A NOOP function allowing a consistent interface for settled\n * SubscribedPromises (settled promises are not subscribed - they resolve\n * immediately). */\nconst NOOP = () => {\n  // noop\n};\n\n/**\n * Every `Promise<T>` can be shadowed by a single `ProxyPromise<T>`. It is\n * created once, cached and reused throughout the lifetime of the Promise. Get a\n * Promise's ProxyPromise using `Unpromise.proxy(promise)`.\n *\n * The `ProxyPromise<T>` attaches handlers to the original `Promise<T>`\n * `.then()` and `.catch()` just once. Promises derived from it use a\n * subscription- (and unsubscription-) based mechanism that monitors these\n * handlers.\n *\n * Every time you call `.subscribe()`, `.then()` `.catch()` or `.finally()` on a\n * `ProxyPromise<T>` it returns a `SubscribedPromise<T>` having an additional\n * `unsubscribe()` method. Calling `unsubscribe()` detaches reference chains\n * from the original, potentially long-lived Promise, eliminating memory leaks.\n *\n * This approach can eliminate the memory leaks that otherwise come about from\n * repeated `race()` or `any()` calls invoking `.then()` and `.catch()` multiple\n * times on the same long-lived native Promise (subscriptions which can never be\n * cleaned up).\n *\n * `Unpromise.race(promises)` is a reference implementation of `Promise.race`\n * avoiding memory leaks when using long-lived unsettled Promises.\n *\n * `Unpromise.any(promises)` is a reference implementation of `Promise.any`\n * avoiding memory leaks when using long-lived unsettled Promises.\n *\n * `Unpromise.resolve(promise)` returns an ephemeral `SubscribedPromise<T>` for\n * any given `Promise<T>` facilitating arbitrary async/await patterns. Behind\n * the scenes, `resolve` is implemented simply as\n * `Unpromise.proxy(promise).subscribe()`. Don't forget to call `.unsubscribe()`\n * to tidy up!\n *\n */\nexport class Unpromise<T> implements ProxyPromise<T> {\n  /** INSTANCE IMPLEMENTATION */\n\n  /** The promise shadowed by this Unpromise<T>  */\n  protected readonly promise: Promise<T> | PromiseLike<T>;\n\n  /** Promises expecting eventual settlement (unless unsubscribed first). This list is deleted\n   * after the original promise settles - no further notifications will be issued. */\n  protected subscribers: ReadonlyArray<PromiseWithResolvers<T>> | null = [];\n\n  /** The Promise's settlement (recorded when it fulfils or rejects). This is consulted when\n   * calling .subscribe() .then() .catch() .finally() to see if an immediately-resolving Promise\n   * can be returned, and therefore subscription can be bypassed. */\n  protected settlement: PromiseSettledResult<T> | null = null;\n\n  /** Constructor accepts a normal Promise executor function like `new\n   * Unpromise((resolve, reject) => {...})` or accepts a pre-existing Promise\n   * like `new Unpromise(existingPromise)`. Adds `.then()` and `.catch()`\n   * handlers to the Promise. These handlers pass fulfilment and rejection\n   * notifications to downstream subscribers and maintains records of value\n   * or error if the Promise ever settles. */\n  protected constructor(promise: Promise<T>);\n  protected constructor(promise: PromiseLike<T>);\n  protected constructor(executor: PromiseExecutor<T>);\n  protected constructor(arg: Promise<T> | PromiseLike<T> | PromiseExecutor<T>) {\n    // handle either a Promise or a Promise executor function\n    if (typeof arg === \"function\") {\n      this.promise = new Promise(arg);\n    } else {\n      this.promise = arg;\n    }\n\n    // subscribe for eventual fulfilment and rejection\n\n    // handle PromiseLike objects (that at least have .then)\n    const thenReturn = this.promise.then((value) => {\n      // atomically record fulfilment and detach subscriber list\n      const { subscribers } = this;\n      this.subscribers = null;\n      this.settlement = {\n        status: \"fulfilled\",\n        value,\n      };\n      // notify fulfilment to subscriber list\n      subscribers?.forEach(({ resolve }) => {\n        resolve(value);\n      });\n    });\n\n    // handle Promise (that also have a .catch behaviour)\n    if (\"catch\" in thenReturn) {\n      thenReturn.catch((reason) => {\n        // atomically record rejection and detach subscriber list\n        const { subscribers } = this;\n        this.subscribers = null;\n        this.settlement = {\n          status: \"rejected\",\n          reason,\n        };\n        // notify rejection to subscriber list\n        subscribers?.forEach(({ reject }) => {\n          reject(reason);\n        });\n      });\n    }\n  }\n\n  /** Create a promise that mitigates uncontrolled subscription to a long-lived\n   * Promise via .then() and .catch() - otherwise a source of memory leaks.\n   *\n   * The returned promise has an `unsubscribe()` method which can be called when\n   * the Promise is no longer being tracked by application logic, and which\n   * ensures that there is no reference chain from the original promise to the\n   * new one, and therefore no memory leak.\n   *\n   * If original promise has not yet settled, this adds a new unique promise\n   * that listens to then/catch events, along with an `unsubscribe()` method to\n   * detach it.\n   *\n   * If original promise has settled, then creates a new Promise.resolve() or\n   * Promise.reject() and provided unsubscribe is a noop.\n   *\n   * If you call `unsubscribe()` before the returned Promise has settled, it\n   * will never settle.\n   */\n  subscribe(): SubscribedPromise<T> {\n    // in all cases we will combine some promise with its unsubscribe function\n    let promise: Promise<T>;\n    let unsubscribe: () => void;\n\n    const { settlement } = this;\n    if (settlement === null) {\n      // not yet settled - subscribe new promise. Expect eventual settlement\n      if (this.subscribers === null) {\n        // invariant - it is not settled, so it must have subscribers\n        throw new Error(\"Unpromise settled but still has subscribers\");\n      }\n      const subscriber = withResolvers<T>();\n      this.subscribers = listWithMember(this.subscribers, subscriber);\n      promise = subscriber.promise;\n      unsubscribe = () => {\n        if (this.subscribers !== null) {\n          this.subscribers = listWithoutMember(this.subscribers, subscriber);\n        }\n      };\n    } else {\n      // settled - don't create subscribed promise. Just resolve or reject\n      const { status } = settlement;\n      if (status === \"fulfilled\") {\n        promise = Promise.resolve(settlement.value);\n      } else {\n        promise = Promise.reject(settlement.reason);\n      }\n      unsubscribe = NOOP;\n    }\n\n    // extend promise signature with the extra method\n    return Object.assign(promise, { unsubscribe });\n  }\n\n  /** STANDARD PROMISE METHODS (but returning a SubscribedPromise) */\n\n  then<TResult1 = T, TResult2 = never>(\n    onfulfilled?:\n      | ((value: T) => TResult1 | PromiseLike<TResult1>)\n      | null\n       ,\n    onrejected?:\n      | ((reason: any) => TResult2 | PromiseLike<TResult2>)\n      | null\n       \n  ): SubscribedPromise<TResult1 | TResult2> {\n    const subscribed = this.subscribe();\n    const { unsubscribe } = subscribed;\n    return Object.assign(subscribed.then(onfulfilled, onrejected), {\n      unsubscribe,\n    });\n  }\n\n  catch<TResult = never>(\n    onrejected?:\n      | ((reason: any) => TResult | PromiseLike<TResult>)\n      | null\n       \n  ): SubscribedPromise<T | TResult> {\n    const subscribed = this.subscribe();\n    const { unsubscribe } = subscribed;\n    return Object.assign(subscribed.catch(onrejected), {\n      unsubscribe,\n    });\n  }\n\n  finally(onfinally?: (() => void) | null  ): SubscribedPromise<T> {\n    const subscribed = this.subscribe();\n    const { unsubscribe } = subscribed;\n    return Object.assign(subscribed.finally(onfinally), {\n      unsubscribe,\n    });\n  }\n\n  /** TOSTRING SUPPORT */\n\n  readonly [Symbol.toStringTag] = \"Unpromise\";\n\n  /** Unpromise STATIC METHODS */\n\n  /** Create or Retrieve the proxy Unpromise (a re-used Unpromise for the VM lifetime\n   * of the provided Promise reference) */\n  static proxy<T>(promise: PromiseLike<T>): ProxyPromise<T> {\n    const cached = Unpromise.getSubscribablePromise(promise);\n    return typeof cached !== \"undefined\"\n      ? cached\n      : Unpromise.createSubscribablePromise(promise);\n  }\n\n  /** Create and store an Unpromise keyed by an original Promise. */\n  protected static createSubscribablePromise<T>(promise: PromiseLike<T>) {\n    const created = new Unpromise<T>(promise);\n    subscribableCache.set(promise, created as Unpromise<unknown>); // resolve promise to unpromise\n    subscribableCache.set(created, created as Unpromise<unknown>); // resolve the unpromise to itself\n    return created;\n  }\n\n  /** Retrieve a previously-created Unpromise keyed by an original Promise. */\n  protected static getSubscribablePromise<T>(promise: PromiseLike<T>) {\n    return subscribableCache.get(promise) as ProxyPromise<T> | undefined;\n  }\n\n  /** Promise STATIC METHODS */\n\n  /** Lookup the Unpromise for this promise, and derive a SubscribedPromise from\n   * it (that can be later unsubscribed to eliminate Memory leaks) */\n  static resolve<T>(value: T | PromiseLike<T>) {\n    const promise: PromiseLike<T> =\n      typeof value === \"object\" &&\n      value !== null &&\n      \"then\" in value &&\n      typeof value.then === \"function\"\n        ? value\n        : Promise.resolve(value);\n    return Unpromise.proxy(promise).subscribe() as SubscribedPromise<\n      Awaited<T>\n    >;\n  }\n\n  /** Perform Promise.any() via SubscribedPromises, then unsubscribe them.\n   * Equivalent to Promise.any but eliminates memory leaks from long-lived\n   * promises accumulating .then() and .catch() subscribers. */\n  static async any<T extends readonly unknown[] | []>(\n    values: T\n  ): Promise<Awaited<T[number]>>;\n  static async any<T>(\n    values: Iterable<T | PromiseLike<T>>\n  ): Promise<Awaited<T>> {\n    const valuesArray = Array.isArray(values) ? values : [...values];\n    const subscribedPromises = valuesArray.map(Unpromise.resolve);\n    try {\n      return await Promise.any(subscribedPromises);\n    } finally {\n      subscribedPromises.forEach(({ unsubscribe }) => {\n        unsubscribe();\n      });\n    }\n  }\n\n  /** Perform Promise.race via SubscribedPromises, then unsubscribe them.\n   * Equivalent to Promise.race but eliminates memory leaks from long-lived\n   * promises accumulating .then() and .catch() subscribers. */\n  static async race<T extends readonly unknown[] | []>(\n    values: T\n  ): Promise<Awaited<T[number]>>;\n  static async race<T>(\n    values: Iterable<T | PromiseLike<T>>\n  ): Promise<Awaited<T>> {\n    const valuesArray = Array.isArray(values) ? values : [...values];\n    const subscribedPromises = valuesArray.map(Unpromise.resolve);\n    try {\n      return await Promise.race(subscribedPromises);\n    } finally {\n      subscribedPromises.forEach(({ unsubscribe }) => {\n        unsubscribe();\n      });\n    }\n  }\n\n  /** Create a race of SubscribedPromises that will fulfil to a single winning\n   * Promise (in a 1-Tuple). Eliminates memory leaks from long-lived promises\n   * accumulating .then() and .catch() subscribers. Allows simple logic to\n   * consume the result, like...\n   * ```ts\n   * const [ winner ] = await Unpromise.race([ promiseA, promiseB ]);\n   * if(winner === promiseB){\n   *   const result = await promiseB;\n   *   // do the thing\n   * }\n   * ```\n   * */\n  static async raceReferences<TPromise extends Promise<unknown>>(\n    promises: readonly TPromise[]\n  ) {\n    // map each promise to an eventual 1-tuple containing itself\n    const selfPromises = promises.map(resolveSelfTuple);\n\n    // now race them. They will fulfil to a readonly [P] or reject.\n    try {\n      return await Promise.race(selfPromises);\n    } finally {\n      for (const promise of selfPromises) {\n        // unsubscribe proxy promises when the race is over to mitigate memory leaks\n        promise.unsubscribe();\n      }\n    }\n  }\n}\n\n/** Promises a 1-tuple containing the original promise when it resolves. Allows\n * awaiting the eventual Promise ***reference*** (easy to destructure and\n * exactly compare with ===). Avoids resolving to the Promise ***value*** (which\n * may be ambiguous and therefore hard to identify as the winner of a race).\n * You can call unsubscribe on the Promise to mitigate memory leaks.\n * */\nexport function resolveSelfTuple<TPromise extends Promise<unknown>>(\n  promise: TPromise\n): SubscribedPromise<readonly [TPromise]> {\n  return Unpromise.proxy(promise).then(() => [promise] as const);\n}\n\n/** VENDORED (Future) PROMISE UTILITIES */\n\n/** Reference implementation of https://github.com/tc39/proposal-promise-with-resolvers */\nfunction withResolvers<T>(): PromiseWithResolvers<T> {\n  let resolve!: PromiseWithResolvers<T>[\"resolve\"];\n  let reject!: PromiseWithResolvers<T>[\"reject\"];\n  const promise = new Promise<T>((_resolve, _reject) => {\n    resolve = _resolve;\n    reject = _reject;\n  });\n  return {\n    promise,\n    resolve,\n    reject,\n  };\n}\n\n/** IMMUTABLE LIST OPERATIONS */\n\nfunction listWithMember<T>(arr: readonly T[], member: T): readonly T[] {\n  return [...arr, member];\n}\n\nfunction listWithoutIndex<T>(arr: readonly T[], index: number) {\n  return [...arr.slice(0, index), ...arr.slice(index + 1)];\n}\n\nfunction listWithoutMember<T>(arr: readonly T[], member: unknown) {\n  const index = arr.indexOf(member as T);\n  if (index !== -1) {\n    return listWithoutIndex(arr, index);\n  }\n  return arr;\n}\n", "// @ts-expect-error - polyfilling symbol\n// eslint-disable-next-line no-restricted-syntax\nSymbol.dispose ??= Symbol();\n\n// @ts-expect-error - polyfilling symbol\n// eslint-disable-next-line no-restricted-syntax\nSymbol.asyncDispose ??= Symbol();\n\n/**\n * Takes a value and a dispose function and returns a new object that implements the Disposable interface.\n * The returned object is the original value augmented with a Symbol.dispose method.\n * @param thing The value to make disposable\n * @param dispose Function to call when disposing the resource\n * @returns The original value with Symbol.dispose method added\n */\nexport function makeResource<T>(thing: T, dispose: () => void): T & Disposable {\n  const it = thing as T & Partial<Disposable>;\n\n  // eslint-disable-next-line no-restricted-syntax\n  const existing = it[Symbol.dispose];\n\n  // eslint-disable-next-line no-restricted-syntax\n  it[Symbol.dispose] = () => {\n    dispose();\n    existing?.();\n  };\n\n  return it as T & Disposable;\n}\n\n/**\n * Takes a value and an async dispose function and returns a new object that implements the AsyncDisposable interface.\n * The returned object is the original value augmented with a Symbol.asyncDispose method.\n * @param thing The value to make async disposable\n * @param dispose Async function to call when disposing the resource\n * @returns The original value with Symbol.asyncDispose method added\n */\nexport function makeAsyncResource<T>(\n  thing: T,\n  dispose: () => Promise<void>,\n): T & AsyncDisposable {\n  const it = thing as T & Partial<AsyncDisposable>;\n\n  // eslint-disable-next-line no-restricted-syntax\n  const existing = it[Symbol.asyncDispose];\n\n  // eslint-disable-next-line no-restricted-syntax\n  it[Symbol.asyncDispose] = async () => {\n    await dispose();\n    await existing?.();\n  };\n\n  return it as T & AsyncDisposable;\n}\n", "import { makeResource } from './disposable';\n\nexport const disposablePromiseTimerResult = Symbol();\n\nexport function timerResource(ms: number) {\n  let timer: ReturnType<typeof setTimeout> | null = null;\n\n  return makeResource(\n    {\n      start() {\n        if (timer) {\n          throw new Error('Timer already started');\n        }\n\n        const promise = new Promise<typeof disposablePromiseTimerResult>(\n          (resolve) => {\n            timer = setTimeout(() => resolve(disposablePromiseTimerResult), ms);\n          },\n        );\n        return promise;\n      },\n    },\n    () => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n    },\n  );\n}\n", "function _usingCtx() {\n  var r = \"function\" == typeof SuppressedError ? SuppressedError : function (r, e) {\n      var n = Error();\n      return n.name = \"SuppressedError\", n.error = r, n.suppressed = e, n;\n    },\n    e = {},\n    n = [];\n  function using(r, e) {\n    if (null != e) {\n      if (Object(e) !== e) throw new TypeError(\"using declarations can only be used with objects, functions, null, or undefined.\");\n      if (r) var o = e[Symbol.asyncDispose || Symbol[\"for\"](\"Symbol.asyncDispose\")];\n      if (void 0 === o && (o = e[Symbol.dispose || Symbol[\"for\"](\"Symbol.dispose\")], r)) var t = o;\n      if (\"function\" != typeof o) throw new TypeError(\"Object is not disposable.\");\n      t && (o = function o() {\n        try {\n          t.call(e);\n        } catch (r) {\n          return Promise.reject(r);\n        }\n      }), n.push({\n        v: e,\n        d: o,\n        a: r\n      });\n    } else r && n.push({\n      d: e,\n      a: r\n    });\n    return e;\n  }\n  return {\n    e: e,\n    u: using.bind(null, !1),\n    a: using.bind(null, !0),\n    d: function d() {\n      var o,\n        t = this.e,\n        s = 0;\n      function next() {\n        for (; o = n.pop();) try {\n          if (!o.a && 1 === s) return s = 0, n.push(o), Promise.resolve().then(next);\n          if (o.d) {\n            var r = o.d.call(o.v);\n            if (o.a) return s |= 2, Promise.resolve(r).then(next, err);\n          } else s |= 1;\n        } catch (r) {\n          return err(r);\n        }\n        if (1 === s) return t !== e ? Promise.reject(t) : Promise.resolve();\n        if (t !== e) throw t;\n      }\n      function err(n) {\n        return t = t !== e ? new r(n, t) : n, next();\n      }\n      return next();\n    }\n  };\n}\nmodule.exports = _usingCtx, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _OverloadYield(e, d) {\n  this.v = e, this.k = d;\n}\nmodule.exports = _OverloadYield, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var OverloadYield = require(\"./OverloadYield.js\");\nfunction _awaitAsyncGenerator(e) {\n  return new OverloadYield(e, 0);\n}\nmodule.exports = _awaitAsyncGenerator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var OverloadYield = require(\"./OverloadYield.js\");\nfunction _wrapAsyncGenerator(e) {\n  return function () {\n    return new AsyncGenerator(e.apply(this, arguments));\n  };\n}\nfunction AsyncGenerator(e) {\n  var r, t;\n  function resume(r, t) {\n    try {\n      var n = e[r](t),\n        o = n.value,\n        u = o instanceof OverloadYield;\n      Promise.resolve(u ? o.v : o).then(function (t) {\n        if (u) {\n          var i = \"return\" === r ? \"return\" : \"next\";\n          if (!o.k || t.done) return resume(i, t);\n          t = e[i](t).value;\n        }\n        settle(n.done ? \"return\" : \"normal\", t);\n      }, function (e) {\n        resume(\"throw\", e);\n      });\n    } catch (e) {\n      settle(\"throw\", e);\n    }\n  }\n  function settle(e, n) {\n    switch (e) {\n      case \"return\":\n        r.resolve({\n          value: n,\n          done: !0\n        });\n        break;\n      case \"throw\":\n        r.reject(n);\n        break;\n      default:\n        r.resolve({\n          value: n,\n          done: !1\n        });\n    }\n    (r = r.next) ? resume(r.key, r.arg) : t = null;\n  }\n  this._invoke = function (e, n) {\n    return new Promise(function (o, u) {\n      var i = {\n        key: e,\n        arg: n,\n        resolve: o,\n        reject: u,\n        next: null\n      };\n      t ? t = t.next = i : (r = t = i, resume(e, n));\n    });\n  }, \"function\" != typeof e[\"return\"] && (this[\"return\"] = void 0);\n}\nAsyncGenerator.prototype[\"function\" == typeof Symbol && Symbol.asyncIterator || \"@@asyncIterator\"] = function () {\n  return this;\n}, AsyncGenerator.prototype.next = function (e) {\n  return this._invoke(\"next\", e);\n}, AsyncGenerator.prototype[\"throw\"] = function (e) {\n  return this._invoke(\"throw\", e);\n}, AsyncGenerator.prototype[\"return\"] = function (e) {\n  return this._invoke(\"return\", e);\n};\nmodule.exports = _wrapAsyncGenerator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import { Unpromise } from '../../../vendor/unpromise';\nimport { throwAbortError } from '../../http/abortError';\nimport { makeAsyncResource } from './disposable';\nimport { disposablePromiseTimerResult, timerResource } from './timerResource';\n\nexport function iteratorResource<TYield, TReturn, TNext>(\n  iterable: AsyncIterable<TYield, TReturn, TNext>,\n): AsyncIterator<TYield, TReturn, TNext> & AsyncDisposable {\n  const iterator = iterable[Symbol.asyncIterator]();\n\n  // @ts-expect-error - this is added in node 24 which we don't officially support yet\n  // eslint-disable-next-line no-restricted-syntax\n  if (iterator[Symbol.asyncDispose]) {\n    return iterator as AsyncIterator<TYield, TReturn, TNext> & AsyncDisposable;\n  }\n\n  return makeAsyncResource(iterator, async () => {\n    await iterator.return?.();\n  });\n}\n/**\n * Derives a new {@link AsyncGenerator} based on {@link iterable}, that automatically aborts after the specified duration.\n */\nexport async function* withMaxDuration<T>(\n  iterable: AsyncIterable<T>,\n  opts: { maxDurationMs: number },\n): AsyncGenerator<T> {\n  await using iterator = iteratorResource(iterable);\n\n  using timer = timerResource(opts.maxDurationMs);\n\n  const timerPromise = timer.start();\n\n  // declaration outside the loop for garbage collection reasons\n  let result: null | IteratorResult<T> | typeof disposablePromiseTimerResult;\n\n  while (true) {\n    result = await Unpromise.race([iterator.next(), timerPromise]);\n    if (result === disposablePromiseTimerResult) {\n      // cancelled due to timeout\n      throwAbortError();\n    }\n    if (result.done) {\n      return result;\n    }\n    yield result.value;\n    // free up reference for garbage collection\n    result = null;\n  }\n}\n\n/**\n * Derives a new {@link AsyncGenerator} based of {@link iterable}, that yields its first\n * {@link count} values. Then, a grace period of {@link gracePeriodMs} is started in which further\n * values may still come through. After this period, the generator aborts.\n */\nexport async function* takeWithGrace<T>(\n  iterable: AsyncIterable<T>,\n  opts: {\n    count: number;\n    gracePeriodMs: number;\n  },\n): AsyncGenerator<T> {\n  await using iterator = iteratorResource(iterable);\n\n  // declaration outside the loop for garbage collection reasons\n  let result: null | IteratorResult<T> | typeof disposablePromiseTimerResult;\n\n  using timer = timerResource(opts.gracePeriodMs);\n\n  let count = opts.count;\n\n  let timerPromise = new Promise<typeof disposablePromiseTimerResult>(() => {\n    // never resolves\n  });\n\n  while (true) {\n    result = await Unpromise.race([iterator.next(), timerPromise]);\n    if (result === disposablePromiseTimerResult) {\n      throwAbortError();\n    }\n    if (result.done) {\n      return result.value;\n    }\n    yield result.value;\n    if (--count === 0) {\n      timerPromise = timer.start();\n    }\n    // free up reference for garbage collection\n    result = null;\n  }\n}\n", "/* eslint-disable @typescript-eslint/no-non-null-assertion */\nexport function createDeferred<TValue = void>() {\n  let resolve: (value: TValue) => void;\n  let reject: (error: unknown) => void;\n  const promise = new Promise<TValue>((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n\n  return { promise, resolve: resolve!, reject: reject! };\n}\nexport type Deferred<TValue> = ReturnType<typeof createDeferred<TValue>>;\n", "import { createDeferred } from './createDeferred';\nimport { makeAsyncResource } from './disposable';\n\ntype ManagedIteratorResult<TYield, TReturn> =\n  | { status: 'yield'; value: TYield }\n  | { status: 'return'; value: TReturn }\n  | { status: 'error'; error: unknown };\nfunction createManagedIterator<TYield, TReturn>(\n  iterable: AsyncIterable<TYield, TReturn>,\n  onResult: (result: ManagedIteratorResult<TYield, TReturn>) => void,\n) {\n  const iterator = iterable[Symbol.asyncIterator]();\n  let state: 'idle' | 'pending' | 'done' = 'idle';\n\n  function cleanup() {\n    state = 'done';\n    onResult = () => {\n      // noop\n    };\n  }\n\n  function pull() {\n    if (state !== 'idle') {\n      return;\n    }\n    state = 'pending';\n\n    const next = iterator.next();\n    next\n      .then((result) => {\n        if (result.done) {\n          state = 'done';\n          onResult({ status: 'return', value: result.value });\n          cleanup();\n          return;\n        }\n        state = 'idle';\n        onResult({ status: 'yield', value: result.value });\n      })\n      .catch((cause) => {\n        onResult({ status: 'error', error: cause });\n        cleanup();\n      });\n  }\n\n  return {\n    pull,\n    destroy: async () => {\n      cleanup();\n      await iterator.return?.();\n    },\n  };\n}\ntype ManagedIterator<TYield, TReturn> = ReturnType<\n  typeof createManagedIterator<TYield, TReturn>\n>;\n\ninterface MergedAsyncIterables<TYield>\n  extends AsyncIterable<TYield, void, unknown> {\n  add(iterable: AsyncIterable<TYield>): void;\n}\n\n/**\n * Creates a new async iterable that merges multiple async iterables into a single stream.\n * Values from the input iterables are yielded in the order they resolve, similar to Promise.race().\n *\n * New iterables can be added dynamically using the returned {@link MergedAsyncIterables.add} method, even after iteration has started.\n *\n * If any of the input iterables throws an error, that error will be propagated through the merged stream.\n * Other iterables will not continue to be processed.\n *\n * @template TYield The type of values yielded by the input iterables\n */\nexport function mergeAsyncIterables<TYield>(): MergedAsyncIterables<TYield> {\n  let state: 'idle' | 'pending' | 'done' = 'idle';\n  let flushSignal = createDeferred();\n\n  /**\n   * used while {@link state} is `idle`\n   */\n  const iterables: AsyncIterable<TYield, void, unknown>[] = [];\n  /**\n   * used while {@link state} is `pending`\n   */\n  const iterators = new Set<ManagedIterator<TYield, void>>();\n\n  const buffer: Array<\n    [\n      iterator: ManagedIterator<TYield, void>,\n      result: Exclude<\n        ManagedIteratorResult<TYield, void>,\n        { status: 'return' }\n      >,\n    ]\n  > = [];\n\n  function initIterable(iterable: AsyncIterable<TYield, void, unknown>) {\n    if (state !== 'pending') {\n      // shouldn't happen\n      return;\n    }\n    const iterator = createManagedIterator(iterable, (result) => {\n      if (state !== 'pending') {\n        // shouldn't happen\n        return;\n      }\n      switch (result.status) {\n        case 'yield':\n          buffer.push([iterator, result]);\n          break;\n        case 'return':\n          iterators.delete(iterator);\n          break;\n        case 'error':\n          buffer.push([iterator, result]);\n          iterators.delete(iterator);\n          break;\n      }\n      flushSignal.resolve();\n    });\n    iterators.add(iterator);\n    iterator.pull();\n  }\n\n  return {\n    add(iterable: AsyncIterable<TYield, void, unknown>) {\n      switch (state) {\n        case 'idle':\n          iterables.push(iterable);\n          break;\n        case 'pending':\n          initIterable(iterable);\n          break;\n        case 'done': {\n          // shouldn't happen\n          break;\n        }\n      }\n    },\n    async *[Symbol.asyncIterator]() {\n      if (state !== 'idle') {\n        throw new Error('Cannot iterate twice');\n      }\n      state = 'pending';\n\n      await using _finally = makeAsyncResource({}, async () => {\n        state = 'done';\n\n        const errors: unknown[] = [];\n        await Promise.all(\n          Array.from(iterators.values()).map(async (it) => {\n            try {\n              await it.destroy();\n            } catch (cause) {\n              errors.push(cause);\n            }\n          }),\n        );\n        buffer.length = 0;\n        iterators.clear();\n        flushSignal.resolve();\n\n        if (errors.length > 0) {\n          throw new AggregateError(errors);\n        }\n      });\n\n      while (iterables.length > 0) {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        initIterable(iterables.shift()!);\n      }\n\n      while (iterators.size > 0) {\n        await flushSignal.promise;\n\n        while (buffer.length > 0) {\n          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n          const [iterator, result] = buffer.shift()!;\n\n          switch (result.status) {\n            case 'yield':\n              yield result.value;\n              iterator.pull();\n              break;\n            case 'error':\n              throw result.error;\n          }\n        }\n        flushSignal = createDeferred();\n      }\n    },\n  };\n}\n", "/**\n * Creates a ReadableStream from an AsyncIterable.\n *\n * @param iterable - The source AsyncIterable to stream from\n * @returns A ReadableStream that yields values from the AsyncIterable\n */\nexport function readableStreamFrom<TYield>(\n  iterable: AsyncIterable<TYield, void>,\n): ReadableStream<TYield> {\n  const iterator = iterable[Symbol.asyncIterator]();\n\n  return new ReadableStream({\n    async cancel() {\n      await iterator.return?.();\n    },\n\n    async pull(controller) {\n      const result = await iterator.next();\n\n      if (result.done) {\n        controller.close();\n        return;\n      }\n\n      controller.enqueue(result.value);\n    },\n  });\n}\n", "import { Unpromise } from '../../../vendor/unpromise';\nimport { iteratorResource } from './asyncIterable';\nimport { disposablePromiseTimerResult, timerResource } from './timerResource';\n\nexport const PING_SYM = Symbol('ping');\n\n/**\n * Derives a new {@link AsyncGenerator} based of {@link iterable}, that yields {@link PING_SYM}\n * whenever no value has been yielded for {@link pingIntervalMs}.\n */\nexport async function* withPing<TValue>(\n  iterable: AsyncIterable<TValue>,\n  pingIntervalMs: number,\n): AsyncGenerator<TValue | typeof PING_SYM> {\n  await using iterator = iteratorResource(iterable);\n\n  // declaration outside the loop for garbage collection reasons\n  let result:\n    | null\n    | IteratorResult<TValue>\n    | typeof disposablePromiseTimerResult;\n\n  let nextPromise = iterator.next();\n\n  while (true) {\n    using pingPromise = timerResource(pingIntervalMs);\n\n    result = await Unpromise.race([nextPromise, pingPromise.start()]);\n\n    if (result === disposablePromiseTimerResult) {\n      // cancelled\n\n      yield PING_SYM;\n      continue;\n    }\n\n    if (result.done) {\n      return result.value;\n    }\n\n    nextPromise = iterator.next();\n    yield result.value;\n\n    // free up reference for garbage collection\n    result = null;\n  }\n}\n", "function _asyncIterator(r) {\n  var n,\n    t,\n    o,\n    e = 2;\n  for (\"undefined\" != typeof Symbol && (t = Symbol.asyncIterator, o = Symbol.iterator); e--;) {\n    if (t && null != (n = r[t])) return n.call(r);\n    if (o && null != (n = r[o])) return new AsyncFromSyncIterator(n.call(r));\n    t = \"@@asyncIterator\", o = \"@@iterator\";\n  }\n  throw new TypeError(\"Object is not async iterable\");\n}\nfunction AsyncFromSyncIterator(r) {\n  function AsyncFromSyncIteratorContinuation(r) {\n    if (Object(r) !== r) return Promise.reject(new TypeError(r + \" is not an object.\"));\n    var n = r.done;\n    return Promise.resolve(r.value).then(function (r) {\n      return {\n        value: r,\n        done: n\n      };\n    });\n  }\n  return AsyncFromSyncIterator = function AsyncFromSyncIterator(r) {\n    this.s = r, this.n = r.next;\n  }, AsyncFromSyncIterator.prototype = {\n    s: null,\n    n: null,\n    next: function next() {\n      return AsyncFromSyncIteratorContinuation(this.n.apply(this.s, arguments));\n    },\n    \"return\": function _return(r) {\n      var n = this.s[\"return\"];\n      return void 0 === n ? Promise.resolve({\n        value: r,\n        done: !0\n      }) : AsyncFromSyncIteratorContinuation(n.apply(this.s, arguments));\n    },\n    \"throw\": function _throw(r) {\n      var n = this.s[\"return\"];\n      return void 0 === n ? Promise.reject(r) : AsyncFromSyncIteratorContinuation(n.apply(this.s, arguments));\n    }\n  }, new AsyncFromSyncIterator(r);\n}\nmodule.exports = _asyncIterator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import { isAsyncIterable, isFunction, isObject, run } from '../utils';\nimport { iteratorResource } from './utils/asyncIterable';\nimport type { Deferred } from './utils/createDeferred';\nimport { createDeferred } from './utils/createDeferred';\nimport { makeResource } from './utils/disposable';\nimport { mergeAsyncIterables } from './utils/mergeAsyncIterables';\nimport { readableStreamFrom } from './utils/readableStreamFrom';\nimport { PING_SYM, withPing } from './utils/withPing';\n\n/**\n * A subset of the standard ReadableStream properties needed by tRPC internally.\n * @see ReadableStream from lib.dom.d.ts\n */\nexport type WebReadableStreamEsque = {\n  getReader: () => ReadableStreamDefaultReader<Uint8Array>;\n};\n\nexport type NodeJSReadableStreamEsque = {\n  on(\n    eventName: string | symbol,\n    listener: (...args: any[]) => void,\n  ): NodeJSReadableStreamEsque;\n};\n\nfunction isPlainObject(value: unknown): value is Record<string, unknown> {\n  return Object.prototype.toString.call(value) === '[object Object]';\n}\n\n// ---------- types\nconst CHUNK_VALUE_TYPE_PROMISE = 0;\ntype CHUNK_VALUE_TYPE_PROMISE = typeof CHUNK_VALUE_TYPE_PROMISE;\nconst CHUNK_VALUE_TYPE_ASYNC_ITERABLE = 1;\ntype CHUNK_VALUE_TYPE_ASYNC_ITERABLE = typeof CHUNK_VALUE_TYPE_ASYNC_ITERABLE;\n\nconst PROMISE_STATUS_FULFILLED = 0;\ntype PROMISE_STATUS_FULFILLED = typeof PROMISE_STATUS_FULFILLED;\nconst PROMISE_STATUS_REJECTED = 1;\ntype PROMISE_STATUS_REJECTED = typeof PROMISE_STATUS_REJECTED;\n\nconst ASYNC_ITERABLE_STATUS_RETURN = 0;\ntype ASYNC_ITERABLE_STATUS_RETURN = typeof ASYNC_ITERABLE_STATUS_RETURN;\nconst ASYNC_ITERABLE_STATUS_YIELD = 1;\ntype ASYNC_ITERABLE_STATUS_YIELD = typeof ASYNC_ITERABLE_STATUS_YIELD;\nconst ASYNC_ITERABLE_STATUS_ERROR = 2;\ntype ASYNC_ITERABLE_STATUS_ERROR = typeof ASYNC_ITERABLE_STATUS_ERROR;\n\ntype ChunkDefinitionKey =\n  // root should be replaced\n  | null\n  // at array path\n  | number\n  // at key path\n  | string;\n\ntype ChunkIndex = number & { __chunkIndex: true };\ntype ChunkValueType =\n  | CHUNK_VALUE_TYPE_PROMISE\n  | CHUNK_VALUE_TYPE_ASYNC_ITERABLE;\ntype ChunkDefinition = [\n  key: ChunkDefinitionKey,\n  type: ChunkValueType,\n  chunkId: ChunkIndex,\n];\ntype EncodedValue = [\n  // data\n  [unknown] | [],\n  // chunk descriptions\n  ...ChunkDefinition[],\n];\n\ntype Head = Record<string, EncodedValue>;\ntype PromiseChunk =\n  | [\n      chunkIndex: ChunkIndex,\n      status: PROMISE_STATUS_FULFILLED,\n      value: EncodedValue,\n    ]\n  | [chunkIndex: ChunkIndex, status: PROMISE_STATUS_REJECTED, error: unknown];\ntype IterableChunk =\n  | [\n      chunkIndex: ChunkIndex,\n      status: ASYNC_ITERABLE_STATUS_RETURN,\n      value: EncodedValue,\n    ]\n  | [\n      chunkIndex: ChunkIndex,\n      status: ASYNC_ITERABLE_STATUS_YIELD,\n      value: EncodedValue,\n    ]\n  | [\n      chunkIndex: ChunkIndex,\n      status: ASYNC_ITERABLE_STATUS_ERROR,\n      error: unknown,\n    ];\ntype ChunkData = PromiseChunk | IterableChunk;\ntype PlaceholderValue = 0 & { __placeholder: true };\nexport function isPromise(value: unknown): value is Promise<unknown> {\n  return (\n    (isObject(value) || isFunction(value)) &&\n    typeof value?.['then'] === 'function' &&\n    typeof value?.['catch'] === 'function'\n  );\n}\n\ntype Serialize = (value: any) => any;\ntype Deserialize = (value: any) => any;\n\ntype PathArray = readonly (string | number)[];\nexport type ProducerOnError = (opts: {\n  error: unknown;\n  path: PathArray;\n}) => void;\nexport interface JSONLProducerOptions {\n  serialize?: Serialize;\n  data: Record<string, unknown> | unknown[];\n  onError?: ProducerOnError;\n  formatError?: (opts: { error: unknown; path: PathArray }) => unknown;\n  maxDepth?: number;\n  /**\n   * Interval in milliseconds to send a ping to the client to keep the connection alive\n   * This will be sent as a whitespace character\n   * @default undefined\n   */\n  pingMs?: number;\n}\n\nclass MaxDepthError extends Error {\n  constructor(public path: (string | number)[]) {\n    super('Max depth reached at path: ' + path.join('.'));\n  }\n}\n\nasync function* createBatchStreamProducer(\n  opts: JSONLProducerOptions,\n): AsyncIterable<Head | ChunkData | typeof PING_SYM, void> {\n  const { data } = opts;\n  let counter = 0 as ChunkIndex;\n  const placeholder = 0 as PlaceholderValue;\n\n  const mergedIterables = mergeAsyncIterables<ChunkData>();\n  function registerAsync(\n    callback: (idx: ChunkIndex) => AsyncIterable<ChunkData, void>,\n  ) {\n    const idx = counter++ as ChunkIndex;\n\n    const iterable = callback(idx);\n    mergedIterables.add(iterable);\n\n    return idx;\n  }\n\n  function encodePromise(promise: Promise<unknown>, path: (string | number)[]) {\n    return registerAsync(async function* (idx) {\n      const error = checkMaxDepth(path);\n      if (error) {\n        // Catch any errors from the original promise to ensure they're reported\n        promise.catch((cause) => {\n          opts.onError?.({ error: cause, path });\n        });\n        // Replace the promise with a rejected one containing the max depth error\n        promise = Promise.reject(error);\n      }\n      try {\n        const next = await promise;\n        yield [idx, PROMISE_STATUS_FULFILLED, encode(next, path)];\n      } catch (cause) {\n        opts.onError?.({ error: cause, path });\n        yield [\n          idx,\n          PROMISE_STATUS_REJECTED,\n          opts.formatError?.({ error: cause, path }),\n        ];\n      }\n    });\n  }\n  function encodeAsyncIterable(\n    iterable: AsyncIterable<unknown>,\n    path: (string | number)[],\n  ) {\n    return registerAsync(async function* (idx) {\n      const error = checkMaxDepth(path);\n      if (error) {\n        throw error;\n      }\n      await using iterator = iteratorResource(iterable);\n\n      try {\n        while (true) {\n          const next = await iterator.next();\n          if (next.done) {\n            yield [idx, ASYNC_ITERABLE_STATUS_RETURN, encode(next.value, path)];\n            break;\n          }\n          yield [idx, ASYNC_ITERABLE_STATUS_YIELD, encode(next.value, path)];\n        }\n      } catch (cause) {\n        opts.onError?.({ error: cause, path });\n\n        yield [\n          idx,\n          ASYNC_ITERABLE_STATUS_ERROR,\n          opts.formatError?.({ error: cause, path }),\n        ];\n      }\n    });\n  }\n  function checkMaxDepth(path: (string | number)[]) {\n    if (opts.maxDepth && path.length > opts.maxDepth) {\n      return new MaxDepthError(path);\n    }\n    return null;\n  }\n  function encodeAsync(\n    value: unknown,\n    path: (string | number)[],\n  ): null | [type: ChunkValueType, chunkId: ChunkIndex] {\n    if (isPromise(value)) {\n      return [CHUNK_VALUE_TYPE_PROMISE, encodePromise(value, path)];\n    }\n    if (isAsyncIterable(value)) {\n      if (opts.maxDepth && path.length >= opts.maxDepth) {\n        throw new Error('Max depth reached');\n      }\n      return [\n        CHUNK_VALUE_TYPE_ASYNC_ITERABLE,\n        encodeAsyncIterable(value, path),\n      ];\n    }\n    return null;\n  }\n  function encode(value: unknown, path: (string | number)[]): EncodedValue {\n    if (value === undefined) {\n      return [[]];\n    }\n    const reg = encodeAsync(value, path);\n    if (reg) {\n      return [[placeholder], [null, ...reg]];\n    }\n\n    if (!isPlainObject(value)) {\n      return [[value]];\n    }\n\n    const newObj: Record<string, unknown> = {};\n    const asyncValues: ChunkDefinition[] = [];\n    for (const [key, item] of Object.entries(value)) {\n      const transformed = encodeAsync(item, [...path, key]);\n      if (!transformed) {\n        newObj[key] = item;\n        continue;\n      }\n      newObj[key] = placeholder;\n      asyncValues.push([key, ...transformed]);\n    }\n    return [[newObj], ...asyncValues];\n  }\n\n  const newHead: Head = {};\n  for (const [key, item] of Object.entries(data)) {\n    newHead[key] = encode(item, [key]);\n  }\n\n  yield newHead;\n\n  let iterable: AsyncIterable<ChunkData | typeof PING_SYM, void> =\n    mergedIterables;\n  if (opts.pingMs) {\n    iterable = withPing(mergedIterables, opts.pingMs);\n  }\n\n  for await (const value of iterable) {\n    yield value;\n  }\n}\n/**\n * JSON Lines stream producer\n * @see https://jsonlines.org/\n */\nexport function jsonlStreamProducer(opts: JSONLProducerOptions) {\n  let stream = readableStreamFrom(createBatchStreamProducer(opts));\n\n  const { serialize } = opts;\n  if (serialize) {\n    stream = stream.pipeThrough(\n      new TransformStream({\n        transform(chunk, controller) {\n          if (chunk === PING_SYM) {\n            controller.enqueue(PING_SYM);\n          } else {\n            controller.enqueue(serialize(chunk));\n          }\n        },\n      }),\n    );\n  }\n\n  return stream\n    .pipeThrough(\n      new TransformStream({\n        transform(chunk, controller) {\n          if (chunk === PING_SYM) {\n            controller.enqueue(' ');\n          } else {\n            controller.enqueue(JSON.stringify(chunk) + '\\n');\n          }\n        },\n      }),\n    )\n    .pipeThrough(new TextEncoderStream());\n}\n\nclass AsyncError extends Error {\n  constructor(public readonly data: unknown) {\n    super('Received error from server');\n  }\n}\nexport type ConsumerOnError = (opts: { error: unknown }) => void;\n\nconst nodeJsStreamToReaderEsque = (source: NodeJSReadableStreamEsque) => {\n  return {\n    getReader() {\n      const stream = new ReadableStream<Uint8Array>({\n        start(controller) {\n          source.on('data', (chunk) => {\n            controller.enqueue(chunk);\n          });\n          source.on('end', () => {\n            controller.close();\n          });\n          source.on('error', (error) => {\n            controller.error(error);\n          });\n        },\n      });\n      return stream.getReader();\n    },\n  };\n};\n\nfunction createLineAccumulator(\n  from: NodeJSReadableStreamEsque | WebReadableStreamEsque,\n) {\n  const reader =\n    'getReader' in from\n      ? from.getReader()\n      : nodeJsStreamToReaderEsque(from).getReader();\n\n  let lineAggregate = '';\n\n  return new ReadableStream({\n    async pull(controller) {\n      const { done, value } = await reader.read();\n\n      if (done) {\n        controller.close();\n      } else {\n        controller.enqueue(value);\n      }\n    },\n    cancel() {\n      return reader.cancel();\n    },\n  })\n    .pipeThrough(new TextDecoderStream())\n    .pipeThrough(\n      new TransformStream<string, string>({\n        transform(chunk, controller) {\n          lineAggregate += chunk;\n          const parts = lineAggregate.split('\\n');\n          lineAggregate = parts.pop() ?? '';\n          for (const part of parts) {\n            controller.enqueue(part);\n          }\n        },\n      }),\n    );\n}\nfunction createConsumerStream<THead>(\n  from: NodeJSReadableStreamEsque | WebReadableStreamEsque,\n) {\n  const stream = createLineAccumulator(from);\n\n  let sentHead = false;\n  return stream.pipeThrough(\n    new TransformStream<string, ChunkData | THead>({\n      transform(line, controller) {\n        if (!sentHead) {\n          const head = JSON.parse(line);\n          controller.enqueue(head as THead);\n          sentHead = true;\n        } else {\n          const chunk: ChunkData = JSON.parse(line);\n          controller.enqueue(chunk);\n        }\n      },\n    }),\n  );\n}\n\n/**\n * Creates a handler for managing stream controllers and their lifecycle\n */\nfunction createStreamsManager(abortController: AbortController) {\n  const controllerMap = new Map<\n    ChunkIndex,\n    ReturnType<typeof createStreamController>\n  >();\n\n  /**\n   * Checks if there are no pending controllers or deferred promises\n   */\n  function isEmpty() {\n    return Array.from(controllerMap.values()).every((c) => c.closed);\n  }\n\n  /**\n   * Creates a stream controller\n   */\n  function createStreamController() {\n    let originalController: ReadableStreamDefaultController<ChunkData>;\n    const stream = new ReadableStream<ChunkData>({\n      start(controller) {\n        originalController = controller;\n      },\n    });\n\n    const streamController = {\n      enqueue: (v: ChunkData) => originalController.enqueue(v),\n      close: () => {\n        originalController.close();\n\n        clear();\n\n        if (isEmpty()) {\n          abortController.abort();\n        }\n      },\n      closed: false,\n      getReaderResource: () => {\n        const reader = stream.getReader();\n\n        return makeResource(reader, () => {\n          reader.releaseLock();\n          streamController.close();\n        });\n      },\n      error: (reason: unknown) => {\n        originalController.error(reason);\n        clear();\n      },\n    };\n    function clear() {\n      Object.assign(streamController, {\n        closed: true,\n        close: () => {\n          // noop\n        },\n        enqueue: () => {\n          // noop\n        },\n        getReaderResource: null,\n        error: () => {\n          // noop\n        },\n      });\n    }\n\n    return streamController;\n  }\n\n  /**\n   * Gets or creates a stream controller\n   */\n  function getOrCreate(chunkId: ChunkIndex) {\n    let c = controllerMap.get(chunkId);\n    if (!c) {\n      c = createStreamController();\n      controllerMap.set(chunkId, c);\n    }\n    return c;\n  }\n\n  /**\n   * Cancels all pending controllers and rejects deferred promises\n   */\n  function cancelAll(reason: unknown) {\n    for (const controller of controllerMap.values()) {\n      controller.error(reason);\n    }\n  }\n\n  return {\n    getOrCreate,\n    isEmpty,\n    cancelAll,\n  };\n}\n\n/**\n * JSON Lines stream consumer\n * @see https://jsonlines.org/\n */\nexport async function jsonlStreamConsumer<THead>(opts: {\n  from: NodeJSReadableStreamEsque | WebReadableStreamEsque;\n  deserialize?: Deserialize;\n  onError?: ConsumerOnError;\n  formatError?: (opts: { error: unknown }) => Error;\n  /**\n   * This `AbortController` will be triggered when there are no more listeners to the stream.\n   */\n  abortController: AbortController;\n}) {\n  const { deserialize = (v) => v } = opts;\n\n  let source = createConsumerStream<Head>(opts.from);\n  if (deserialize) {\n    source = source.pipeThrough(\n      new TransformStream({\n        transform(chunk, controller) {\n          controller.enqueue(deserialize(chunk));\n        },\n      }),\n    );\n  }\n  let headDeferred: null | Deferred<THead> = createDeferred();\n\n  const streamManager = createStreamsManager(opts.abortController);\n\n  function decodeChunkDefinition(value: ChunkDefinition) {\n    const [_path, type, chunkId] = value;\n\n    const controller = streamManager.getOrCreate(chunkId);\n\n    switch (type) {\n      case CHUNK_VALUE_TYPE_PROMISE: {\n        return run(async () => {\n          using reader = controller.getReaderResource();\n\n          const { value } = await reader.read();\n          const [_chunkId, status, data] = value as PromiseChunk;\n          switch (status) {\n            case PROMISE_STATUS_FULFILLED:\n              return decode(data);\n            case PROMISE_STATUS_REJECTED:\n              throw opts.formatError?.({ error: data }) ?? new AsyncError(data);\n          }\n        });\n      }\n      case CHUNK_VALUE_TYPE_ASYNC_ITERABLE: {\n        return run(async function* () {\n          using reader = controller.getReaderResource();\n\n          while (true) {\n            const { value } = await reader.read();\n\n            const [_chunkId, status, data] = value as IterableChunk;\n\n            switch (status) {\n              case ASYNC_ITERABLE_STATUS_YIELD:\n                yield decode(data);\n                break;\n              case ASYNC_ITERABLE_STATUS_RETURN:\n                return decode(data);\n              case ASYNC_ITERABLE_STATUS_ERROR:\n                throw (\n                  opts.formatError?.({ error: data }) ?? new AsyncError(data)\n                );\n            }\n          }\n        });\n      }\n    }\n  }\n\n  function decode(value: EncodedValue): unknown {\n    const [[data], ...asyncProps] = value;\n\n    for (const value of asyncProps) {\n      const [key] = value;\n      const decoded = decodeChunkDefinition(value);\n\n      if (key === null) {\n        return decoded;\n      }\n\n      (data as any)[key] = decoded;\n    }\n    return data;\n  }\n\n  const closeOrAbort = (reason: unknown) => {\n    headDeferred?.reject(reason);\n    streamManager.cancelAll(reason);\n  };\n  source\n    .pipeTo(\n      new WritableStream({\n        write(chunkOrHead) {\n          if (headDeferred) {\n            const head = chunkOrHead as Record<number | string, unknown>;\n\n            for (const [key, value] of Object.entries(chunkOrHead)) {\n              const parsed = decode(value as any);\n              head[key] = parsed;\n            }\n            headDeferred.resolve(head as THead);\n            headDeferred = null;\n\n            return;\n          }\n          const chunk = chunkOrHead as ChunkData;\n          const [idx] = chunk;\n\n          const controller = streamManager.getOrCreate(idx);\n          controller.enqueue(chunk);\n        },\n        close: () => closeOrAbort(new Error('Stream closed')),\n        abort: closeOrAbort,\n      }),\n      {\n        signal: opts.abortController.signal,\n      },\n    )\n    .catch((error) => {\n      opts.onError?.({ error });\n      closeOrAbort(error);\n    });\n\n  return [await headDeferred.promise, streamManager] as const;\n}\n", "var OverloadYield = require(\"./OverloadYield.js\");\nfunction _asyncGeneratorDelegate(t) {\n  var e = {},\n    n = !1;\n  function pump(e, r) {\n    return n = !0, r = new Promise(function (n) {\n      n(t[e](r));\n    }), {\n      done: !1,\n      value: new OverloadYield(r, 1)\n    };\n  }\n  return e[\"undefined\" != typeof Symbol && Symbol.iterator || \"@@iterator\"] = function () {\n    return this;\n  }, e.next = function (t) {\n    return n ? (n = !1, t) : pump(\"next\", t);\n  }, \"function\" == typeof t[\"throw\"] && (e[\"throw\"] = function (t) {\n    if (n) throw n = !1, t;\n    return pump(\"throw\", t);\n  }), \"function\" == typeof t[\"return\"] && (e[\"return\"] = function (t) {\n    return n ? (n = !1, t) : pump(\"return\", t);\n  }), e;\n}\nmodule.exports = _asyncGeneratorDelegate, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import { Unpromise } from '../../vendor/unpromise';\nimport { getTRPCErrorFromUnknown } from '../error/TRPCError';\nimport { isAbortError } from '../http/abortError';\nimport type { MaybePromise } from '../types';\nimport { identity, run } from '../utils';\nimport type { EventSourceLike } from './sse.types';\nimport type { inferTrackedOutput } from './tracked';\nimport { isTrackedEnvelope } from './tracked';\nimport { takeWithGrace, withMaxDuration } from './utils/asyncIterable';\nimport { makeAsyncResource } from './utils/disposable';\nimport { readableStreamFrom } from './utils/readableStreamFrom';\nimport {\n  disposablePromiseTimerResult,\n  timerResource,\n} from './utils/timerResource';\nimport { PING_SYM, withPing } from './utils/withPing';\n\ntype Serialize = (value: any) => any;\ntype Deserialize = (value: any) => any;\n\n/**\n * @internal\n */\nexport interface SSEPingOptions {\n  /**\n   * Enable ping comments sent from the server\n   * @default false\n   */\n  enabled: boolean;\n  /**\n   * Interval in milliseconds\n   * @default 1000\n   */\n  intervalMs?: number;\n}\n\nexport interface SSEClientOptions {\n  /**\n   * Timeout and reconnect after inactivity in milliseconds\n   * @default undefined\n   */\n  reconnectAfterInactivityMs?: number;\n}\n\nexport interface SSEStreamProducerOptions<TValue = unknown> {\n  serialize?: Serialize;\n  data: AsyncIterable<TValue>;\n\n  maxDepth?: number;\n  ping?: SSEPingOptions;\n  /**\n   * Maximum duration in milliseconds for the request before ending the stream\n   * @default undefined\n   */\n  maxDurationMs?: number;\n  /**\n   * End the request immediately after data is sent\n   * Only useful for serverless runtimes that do not support streaming responses\n   * @default false\n   */\n  emitAndEndImmediately?: boolean;\n  formatError?: (opts: { error: unknown }) => unknown;\n  /**\n   * Client-specific options - these will be sent to the client as part of the first message\n   * @default {}\n   */\n  client?: SSEClientOptions;\n}\n\nconst PING_EVENT = 'ping';\nconst SERIALIZED_ERROR_EVENT = 'serialized-error';\nconst CONNECTED_EVENT = 'connected';\nconst RETURN_EVENT = 'return';\n\ninterface SSEvent {\n  id?: string;\n  data: unknown;\n  comment?: string;\n  event?: string;\n}\n/**\n *\n * @see https://html.spec.whatwg.org/multipage/server-sent-events.html\n */\nexport function sseStreamProducer<TValue = unknown>(\n  opts: SSEStreamProducerOptions<TValue>,\n) {\n  const { serialize = identity } = opts;\n\n  const ping: Required<SSEPingOptions> = {\n    enabled: opts.ping?.enabled ?? false,\n    intervalMs: opts.ping?.intervalMs ?? 1000,\n  };\n  const client: SSEClientOptions = opts.client ?? {};\n\n  if (\n    ping.enabled &&\n    client.reconnectAfterInactivityMs &&\n    ping.intervalMs > client.reconnectAfterInactivityMs\n  ) {\n    throw new Error(\n      `Ping interval must be less than client reconnect interval to prevent unnecessary reconnection - ping.intervalMs: ${ping.intervalMs} client.reconnectAfterInactivityMs: ${client.reconnectAfterInactivityMs}`,\n    );\n  }\n\n  async function* generator(): AsyncIterable<SSEvent, void> {\n    yield {\n      event: CONNECTED_EVENT,\n      data: JSON.stringify(client),\n    };\n\n    type TIteratorValue = Awaited<TValue> | typeof PING_SYM;\n\n    let iterable: AsyncIterable<TValue | typeof PING_SYM> = opts.data;\n\n    if (opts.emitAndEndImmediately) {\n      iterable = takeWithGrace(iterable, {\n        count: 1,\n        gracePeriodMs: 1,\n      });\n    }\n\n    if (\n      opts.maxDurationMs &&\n      opts.maxDurationMs > 0 &&\n      opts.maxDurationMs !== Infinity\n    ) {\n      iterable = withMaxDuration(iterable, {\n        maxDurationMs: opts.maxDurationMs,\n      });\n    }\n\n    if (ping.enabled && ping.intervalMs !== Infinity && ping.intervalMs > 0) {\n      iterable = withPing(iterable, ping.intervalMs);\n    }\n\n    // We need those declarations outside the loop for garbage collection reasons. If they were\n    // declared inside, they would not be freed until the next value is present.\n    let value: null | TIteratorValue;\n    let chunk: null | SSEvent;\n\n    for await (value of iterable) {\n      if (value === PING_SYM) {\n        yield { event: PING_EVENT, data: '' };\n        continue;\n      }\n\n      chunk = isTrackedEnvelope(value)\n        ? { id: value[0], data: value[1] }\n        : { data: value };\n\n      chunk.data = JSON.stringify(serialize(chunk.data));\n\n      yield chunk;\n\n      // free up references for garbage collection\n      value = null;\n      chunk = null;\n    }\n  }\n\n  async function* generatorWithErrorHandling(): AsyncIterable<SSEvent, void> {\n    try {\n      yield* generator();\n\n      yield {\n        event: RETURN_EVENT,\n        data: '',\n      };\n    } catch (cause) {\n      if (isAbortError(cause)) {\n        // ignore abort errors, send any other errors\n        return;\n      }\n      // `err` must be caused by `opts.data`, `JSON.stringify` or `serialize`.\n      // So, a user error in any case.\n      const error = getTRPCErrorFromUnknown(cause);\n      const data = opts.formatError?.({ error }) ?? null;\n      yield {\n        event: SERIALIZED_ERROR_EVENT,\n        data: JSON.stringify(serialize(data)),\n      };\n    }\n  }\n\n  const stream = readableStreamFrom(generatorWithErrorHandling());\n\n  return stream\n    .pipeThrough(\n      new TransformStream({\n        transform(chunk, controller: TransformStreamDefaultController<string>) {\n          if ('event' in chunk) {\n            controller.enqueue(`event: ${chunk.event}\\n`);\n          }\n          if ('data' in chunk) {\n            controller.enqueue(`data: ${chunk.data}\\n`);\n          }\n          if ('id' in chunk) {\n            controller.enqueue(`id: ${chunk.id}\\n`);\n          }\n          if ('comment' in chunk) {\n            controller.enqueue(`: ${chunk.comment}\\n`);\n          }\n          controller.enqueue('\\n\\n');\n        },\n      }),\n    )\n    .pipeThrough(new TextEncoderStream());\n}\n\ninterface ConsumerStreamResultBase<TConfig extends ConsumerConfig> {\n  eventSource: InstanceType<TConfig['EventSource']> | null;\n}\n\ninterface ConsumerStreamResultData<TConfig extends ConsumerConfig>\n  extends ConsumerStreamResultBase<TConfig> {\n  type: 'data';\n  data: inferTrackedOutput<TConfig['data']>;\n}\n\ninterface ConsumerStreamResultError<TConfig extends ConsumerConfig>\n  extends ConsumerStreamResultBase<TConfig> {\n  type: 'serialized-error';\n  error: TConfig['error'];\n}\n\ninterface ConsumerStreamResultConnecting<TConfig extends ConsumerConfig>\n  extends ConsumerStreamResultBase<TConfig> {\n  type: 'connecting';\n  event: EventSourceLike.EventOf<TConfig['EventSource']> | null;\n}\ninterface ConsumerStreamResultTimeout<TConfig extends ConsumerConfig>\n  extends ConsumerStreamResultBase<TConfig> {\n  type: 'timeout';\n  ms: number;\n}\ninterface ConsumerStreamResultPing<TConfig extends ConsumerConfig>\n  extends ConsumerStreamResultBase<TConfig> {\n  type: 'ping';\n}\n\ninterface ConsumerStreamResultConnected<TConfig extends ConsumerConfig>\n  extends ConsumerStreamResultBase<TConfig> {\n  type: 'connected';\n  options: SSEClientOptions;\n}\n\ntype ConsumerStreamResult<TConfig extends ConsumerConfig> =\n  | ConsumerStreamResultData<TConfig>\n  | ConsumerStreamResultError<TConfig>\n  | ConsumerStreamResultConnecting<TConfig>\n  | ConsumerStreamResultTimeout<TConfig>\n  | ConsumerStreamResultPing<TConfig>\n  | ConsumerStreamResultConnected<TConfig>;\n\nexport interface SSEStreamConsumerOptions<TConfig extends ConsumerConfig> {\n  url: () => MaybePromise<string>;\n  init: () =>\n    | MaybePromise<EventSourceLike.InitDictOf<TConfig['EventSource']>>\n    | undefined;\n  signal: AbortSignal;\n  deserialize?: Deserialize;\n  EventSource: TConfig['EventSource'];\n}\n\ninterface ConsumerConfig {\n  data: unknown;\n  error: unknown;\n  EventSource: EventSourceLike.AnyConstructor;\n}\n\nasync function withTimeout<T>(opts: {\n  promise: Promise<T>;\n  timeoutMs: number;\n  onTimeout: () => Promise<NoInfer<T>>;\n}): Promise<T> {\n  using timeoutPromise = timerResource(opts.timeoutMs);\n  const res = await Unpromise.race([opts.promise, timeoutPromise.start()]);\n\n  if (res === disposablePromiseTimerResult) {\n    return await opts.onTimeout();\n  }\n  return res;\n}\n\n/**\n * @see https://html.spec.whatwg.org/multipage/server-sent-events.html\n */\nexport function sseStreamConsumer<TConfig extends ConsumerConfig>(\n  opts: SSEStreamConsumerOptions<TConfig>,\n): AsyncIterable<ConsumerStreamResult<TConfig>> {\n  const { deserialize = (v) => v } = opts;\n\n  let clientOptions: SSEClientOptions = {};\n\n  const signal = opts.signal;\n\n  let _es: InstanceType<TConfig['EventSource']> | null = null;\n\n  const createStream = () =>\n    new ReadableStream<ConsumerStreamResult<TConfig>>({\n      async start(controller) {\n        const [url, init] = await Promise.all([opts.url(), opts.init()]);\n        const eventSource = (_es = new opts.EventSource(\n          url,\n          init,\n        ) as InstanceType<TConfig['EventSource']>);\n\n        controller.enqueue({\n          type: 'connecting',\n          eventSource: _es,\n          event: null,\n        });\n\n        eventSource.addEventListener(CONNECTED_EVENT, (_msg) => {\n          const msg = _msg as EventSourceLike.MessageEvent;\n\n          const options: SSEClientOptions = JSON.parse(msg.data);\n\n          clientOptions = options;\n          controller.enqueue({\n            type: 'connected',\n            options,\n            eventSource,\n          });\n        });\n\n        eventSource.addEventListener(SERIALIZED_ERROR_EVENT, (_msg) => {\n          const msg = _msg as EventSourceLike.MessageEvent;\n\n          controller.enqueue({\n            type: 'serialized-error',\n            error: deserialize(JSON.parse(msg.data)),\n            eventSource,\n          });\n        });\n        eventSource.addEventListener(PING_EVENT, () => {\n          controller.enqueue({\n            type: 'ping',\n            eventSource,\n          });\n        });\n        eventSource.addEventListener(RETURN_EVENT, () => {\n          eventSource.close();\n          controller.close();\n          _es = null;\n        });\n        eventSource.addEventListener('error', (event) => {\n          if (eventSource.readyState === eventSource.CLOSED) {\n            controller.error(event);\n          } else {\n            controller.enqueue({\n              type: 'connecting',\n              eventSource,\n              event,\n            });\n          }\n        });\n        eventSource.addEventListener('message', (_msg) => {\n          const msg = _msg as EventSourceLike.MessageEvent;\n\n          const chunk = deserialize(JSON.parse(msg.data));\n\n          const def: SSEvent = {\n            data: chunk,\n          };\n          if (msg.lastEventId) {\n            def.id = msg.lastEventId;\n          }\n          controller.enqueue({\n            type: 'data',\n            data: def as inferTrackedOutput<TConfig['data']>,\n            eventSource,\n          });\n        });\n\n        const onAbort = () => {\n          try {\n            eventSource.close();\n            controller.close();\n          } catch {\n            // ignore errors in case the controller is already closed\n          }\n        };\n        if (signal.aborted) {\n          onAbort();\n        } else {\n          signal.addEventListener('abort', onAbort);\n        }\n      },\n      cancel() {\n        _es?.close();\n      },\n    });\n\n  const getStreamResource = () => {\n    let stream = createStream();\n    let reader = stream.getReader();\n\n    async function dispose() {\n      await reader.cancel();\n      _es = null;\n    }\n\n    return makeAsyncResource(\n      {\n        read() {\n          return reader.read();\n        },\n        async recreate() {\n          await dispose();\n\n          stream = createStream();\n          reader = stream.getReader();\n        },\n      },\n      dispose,\n    );\n  };\n\n  return run(async function* () {\n    await using stream = getStreamResource();\n\n    while (true) {\n      let promise = stream.read();\n\n      const timeoutMs = clientOptions.reconnectAfterInactivityMs;\n      if (timeoutMs) {\n        promise = withTimeout({\n          promise,\n          timeoutMs,\n          onTimeout: async () => {\n            const res: Awaited<typeof promise> = {\n              value: {\n                type: 'timeout',\n                ms: timeoutMs,\n                eventSource: _es,\n              },\n              done: false,\n            };\n            // Close and release old reader\n            await stream.recreate();\n\n            return res;\n          },\n        });\n      }\n\n      const result = await promise;\n\n      if (result.done) {\n        return result.value;\n      }\n      yield result.value;\n    }\n  });\n}\n\nexport const sseHeaders = {\n  'Content-Type': 'text/event-stream',\n  'Cache-Control': 'no-cache, no-transform',\n  'X-Accel-Buffering': 'no',\n  Connection: 'keep-alive',\n} as const;\n", "/* eslint-disable @typescript-eslint/no-non-null-assertion */\nimport {\n  isObservable,\n  observableToAsyncIterable,\n} from '../../observable/observable';\nimport { getErrorShape } from '../error/getErrorShape';\nimport { getTRPCErrorFromUnknown, TRPCError } from '../error/TRPCError';\nimport type { ProcedureType } from '../procedure';\nimport {\n  type AnyRouter,\n  type inferRouterContext,\n  type inferRouterError,\n} from '../router';\nimport type { TRPCResponse } from '../rpc';\nimport { isPromise, jsonlStreamProducer } from '../stream/jsonl';\nimport { sseHeaders, sseStreamProducer } from '../stream/sse';\nimport { transformTRPCResponse } from '../transformer';\nimport { isAsyncIterable, isObject, run } from '../utils';\nimport { getRequestInfo } from './contentType';\nimport { getHTTPStatusCode } from './getHTTPStatusCode';\nimport type {\n  HTTPBaseHandlerOptions,\n  ResolveHTTPRequestOptionsContextFn,\n  TRPCRequestInfo,\n} from './types';\n\nfunction errorToAsyncIterable(err: TRPCError): AsyncIterable<never> {\n  return run(async function* () {\n    throw err;\n  });\n}\ntype HTTPMethods =\n  | 'GET'\n  | 'POST'\n  | 'HEAD'\n  | 'OPTIONS'\n  | 'PUT'\n  | 'DELETE'\n  | 'PATCH';\n\nconst TYPE_ACCEPTED_METHOD_MAP: Record<ProcedureType, HTTPMethods[]> = {\n  mutation: ['POST'],\n  query: ['GET'],\n  subscription: ['GET'],\n};\nconst TYPE_ACCEPTED_METHOD_MAP_WITH_METHOD_OVERRIDE: Record<\n  ProcedureType,\n  HTTPMethods[]\n> = {\n  // never allow GET to do a mutation\n  mutation: ['POST'],\n  query: ['GET', 'POST'],\n  subscription: ['GET', 'POST'],\n};\n\ninterface ResolveHTTPRequestOptions<TRouter extends AnyRouter>\n  extends HTTPBaseHandlerOptions<TRouter, Request> {\n  createContext: ResolveHTTPRequestOptionsContextFn<TRouter>;\n  req: Request;\n  path: string;\n  /**\n   * If the request had an issue before reaching the handler\n   */\n  error: TRPCError | null;\n}\n\nfunction initResponse<TRouter extends AnyRouter, TRequest>(initOpts: {\n  ctx: inferRouterContext<TRouter> | undefined;\n  info: TRPCRequestInfo | undefined;\n  responseMeta?: HTTPBaseHandlerOptions<TRouter, TRequest>['responseMeta'];\n  untransformedJSON:\n    | TRPCResponse<unknown, inferRouterError<TRouter>>\n    | TRPCResponse<unknown, inferRouterError<TRouter>>[]\n    | null;\n  errors: TRPCError[];\n  headers: Headers;\n}) {\n  const {\n    ctx,\n    info,\n    responseMeta,\n    untransformedJSON,\n    errors = [],\n    headers,\n  } = initOpts;\n\n  let status = untransformedJSON ? getHTTPStatusCode(untransformedJSON) : 200;\n\n  const eagerGeneration = !untransformedJSON;\n  const data = eagerGeneration\n    ? []\n    : Array.isArray(untransformedJSON)\n      ? untransformedJSON\n      : [untransformedJSON];\n\n  const meta =\n    responseMeta?.({\n      ctx,\n      info,\n      paths: info?.calls.map((call) => call.path),\n      data,\n      errors,\n      eagerGeneration,\n      type:\n        info?.calls.find((call) => call.procedure?._def.type)?.procedure?._def\n          .type ?? 'unknown',\n    }) ?? {};\n\n  if (meta.headers) {\n    if (meta.headers instanceof Headers) {\n      for (const [key, value] of meta.headers.entries()) {\n        headers.append(key, value);\n      }\n    } else {\n      /**\n       * @deprecated, delete in v12\n       */\n      for (const [key, value] of Object.entries(meta.headers)) {\n        if (Array.isArray(value)) {\n          for (const v of value) {\n            headers.append(key, v);\n          }\n        } else if (typeof value === 'string') {\n          headers.set(key, value);\n        }\n      }\n    }\n  }\n  if (meta.status) {\n    status = meta.status;\n  }\n\n  return {\n    status,\n  };\n}\n\nfunction caughtErrorToData<TRouter extends AnyRouter>(\n  cause: unknown,\n  errorOpts: {\n    opts: Pick<\n      ResolveHTTPRequestOptions<TRouter>,\n      'onError' | 'req' | 'router'\n    >;\n    ctx: inferRouterContext<TRouter> | undefined;\n    type: ProcedureType | 'unknown';\n    path?: string;\n    input?: unknown;\n  },\n) {\n  const { router, req, onError } = errorOpts.opts;\n  const error = getTRPCErrorFromUnknown(cause);\n  onError?.({\n    error,\n    path: errorOpts.path,\n    input: errorOpts.input,\n    ctx: errorOpts.ctx,\n    type: errorOpts.type,\n    req,\n  });\n  const untransformedJSON = {\n    error: getErrorShape({\n      config: router._def._config,\n      error,\n      type: errorOpts.type,\n      path: errorOpts.path,\n      input: errorOpts.input,\n      ctx: errorOpts.ctx,\n    }),\n  };\n  const transformedJSON = transformTRPCResponse(\n    router._def._config,\n    untransformedJSON,\n  );\n  const body = JSON.stringify(transformedJSON);\n  return {\n    error,\n    untransformedJSON,\n    body,\n  };\n}\n\n/**\n * Check if a value is a stream-like object\n * - if it's an async iterable\n * - if it's an object with async iterables or promises\n */\nfunction isDataStream(v: unknown) {\n  if (!isObject(v)) {\n    return false;\n  }\n\n  if (isAsyncIterable(v)) {\n    return true;\n  }\n\n  return (\n    Object.values(v).some(isPromise) || Object.values(v).some(isAsyncIterable)\n  );\n}\n\ntype ResultTuple<T> = [undefined, T] | [TRPCError, undefined];\n\nexport async function resolveResponse<TRouter extends AnyRouter>(\n  opts: ResolveHTTPRequestOptions<TRouter>,\n): Promise<Response> {\n  const { router, req } = opts;\n  const headers = new Headers([['vary', 'trpc-accept']]);\n  const config = router._def._config;\n\n  const url = new URL(req.url);\n\n  if (req.method === 'HEAD') {\n    // can be used for lambda warmup\n    return new Response(null, {\n      status: 204,\n    });\n  }\n\n  const allowBatching = opts.allowBatching ?? opts.batching?.enabled ?? true;\n  const allowMethodOverride =\n    (opts.allowMethodOverride ?? false) && req.method === 'POST';\n\n  type $Context = inferRouterContext<TRouter>;\n\n  const infoTuple: ResultTuple<TRPCRequestInfo> = await run(async () => {\n    try {\n      return [\n        undefined,\n        await getRequestInfo({\n          req,\n          path: decodeURIComponent(opts.path),\n          router,\n          searchParams: url.searchParams,\n          headers: opts.req.headers,\n          url,\n        }),\n      ];\n    } catch (cause) {\n      return [getTRPCErrorFromUnknown(cause), undefined];\n    }\n  });\n\n  interface ContextManager {\n    valueOrUndefined: () => $Context | undefined;\n    value: () => $Context;\n    create: (info: TRPCRequestInfo) => Promise<void>;\n  }\n  const ctxManager: ContextManager = run(() => {\n    let result: ResultTuple<$Context> | undefined = undefined;\n    return {\n      valueOrUndefined: () => {\n        if (!result) {\n          return undefined;\n        }\n        return result[1];\n      },\n      value: () => {\n        const [err, ctx] = result!;\n        if (err) {\n          throw err;\n        }\n        return ctx;\n      },\n      create: async (info) => {\n        if (result) {\n          throw new Error(\n            'This should only be called once - report a bug in tRPC',\n          );\n        }\n        try {\n          const ctx = await opts.createContext({\n            info,\n          });\n          result = [undefined, ctx];\n        } catch (cause) {\n          result = [getTRPCErrorFromUnknown(cause), undefined];\n        }\n      },\n    };\n  });\n\n  const methodMapper = allowMethodOverride\n    ? TYPE_ACCEPTED_METHOD_MAP_WITH_METHOD_OVERRIDE\n    : TYPE_ACCEPTED_METHOD_MAP;\n\n  /**\n   * @deprecated\n   */\n  const isStreamCall = req.headers.get('trpc-accept') === 'application/jsonl';\n\n  const experimentalSSE = config.sse?.enabled ?? true;\n  try {\n    const [infoError, info] = infoTuple;\n    if (infoError) {\n      throw infoError;\n    }\n    if (info.isBatchCall && !allowBatching) {\n      throw new TRPCError({\n        code: 'BAD_REQUEST',\n        message: `Batching is not enabled on the server`,\n      });\n    }\n    /* istanbul ignore if -- @preserve */\n    if (isStreamCall && !info.isBatchCall) {\n      throw new TRPCError({\n        message: `Streaming requests must be batched (you can do a batch of 1)`,\n        code: 'BAD_REQUEST',\n      });\n    }\n    await ctxManager.create(info);\n\n    interface RPCResultOk {\n      data: unknown;\n    }\n    type RPCResult = ResultTuple<RPCResultOk>;\n    const rpcCalls = info.calls.map(async (call): Promise<RPCResult> => {\n      const proc = call.procedure;\n      try {\n        if (opts.error) {\n          throw opts.error;\n        }\n\n        if (!proc) {\n          throw new TRPCError({\n            code: 'NOT_FOUND',\n            message: `No procedure found on path \"${call.path}\"`,\n          });\n        }\n\n        if (!methodMapper[proc._def.type].includes(req.method as HTTPMethods)) {\n          throw new TRPCError({\n            code: 'METHOD_NOT_SUPPORTED',\n            message: `Unsupported ${req.method}-request to ${proc._def.type} procedure at path \"${call.path}\"`,\n          });\n        }\n\n        if (proc._def.type === 'subscription') {\n          /* istanbul ignore if -- @preserve */\n          if (info.isBatchCall) {\n            throw new TRPCError({\n              code: 'BAD_REQUEST',\n              message: `Cannot batch subscription calls`,\n            });\n          }\n        }\n        const data: unknown = await proc({\n          path: call.path,\n          getRawInput: call.getRawInput,\n          ctx: ctxManager.value(),\n          type: proc._def.type,\n          signal: opts.req.signal,\n        });\n        return [undefined, { data }];\n      } catch (cause) {\n        const error = getTRPCErrorFromUnknown(cause);\n        const input = call.result();\n\n        opts.onError?.({\n          error,\n          path: call.path,\n          input,\n          ctx: ctxManager.valueOrUndefined(),\n          type: call.procedure?._def.type ?? 'unknown',\n          req: opts.req,\n        });\n\n        return [error, undefined];\n      }\n    });\n\n    // ----------- response handlers -----------\n    if (!info.isBatchCall) {\n      const [call] = info.calls;\n      const [error, result] = await rpcCalls[0]!;\n\n      switch (info.type) {\n        case 'unknown':\n        case 'mutation':\n        case 'query': {\n          // httpLink\n          headers.set('content-type', 'application/json');\n\n          if (isDataStream(result?.data)) {\n            throw new TRPCError({\n              code: 'UNSUPPORTED_MEDIA_TYPE',\n              message:\n                'Cannot use stream-like response in non-streaming request - use httpBatchStreamLink',\n            });\n          }\n          const res: TRPCResponse<unknown, inferRouterError<TRouter>> = error\n            ? {\n                error: getErrorShape({\n                  config,\n                  ctx: ctxManager.valueOrUndefined(),\n                  error,\n                  input: call!.result(),\n                  path: call!.path,\n                  type: info.type,\n                }),\n              }\n            : { result: { data: result.data } };\n\n          const headResponse = initResponse({\n            ctx: ctxManager.valueOrUndefined(),\n            info,\n            responseMeta: opts.responseMeta,\n            errors: error ? [error] : [],\n            headers,\n            untransformedJSON: [res],\n          });\n          return new Response(\n            JSON.stringify(transformTRPCResponse(config, res)),\n            {\n              status: headResponse.status,\n              headers,\n            },\n          );\n        }\n        case 'subscription': {\n          // httpSubscriptionLink\n\n          const iterable: AsyncIterable<unknown> = run(() => {\n            if (error) {\n              return errorToAsyncIterable(error);\n            }\n            if (!experimentalSSE) {\n              return errorToAsyncIterable(\n                new TRPCError({\n                  code: 'METHOD_NOT_SUPPORTED',\n                  message: 'Missing experimental flag \"sseSubscriptions\"',\n                }),\n              );\n            }\n\n            if (!isObservable(result.data) && !isAsyncIterable(result.data)) {\n              return errorToAsyncIterable(\n                new TRPCError({\n                  message: `Subscription ${\n                    call!.path\n                  } did not return an observable or a AsyncGenerator`,\n                  code: 'INTERNAL_SERVER_ERROR',\n                }),\n              );\n            }\n            const dataAsIterable = isObservable(result.data)\n              ? observableToAsyncIterable(result.data, opts.req.signal)\n              : result.data;\n            return dataAsIterable;\n          });\n\n          const stream = sseStreamProducer({\n            ...config.sse,\n            data: iterable,\n            serialize: (v) => config.transformer.output.serialize(v),\n            formatError(errorOpts) {\n              const error = getTRPCErrorFromUnknown(errorOpts.error);\n              const input = call?.result();\n              const path = call?.path;\n              const type = call?.procedure?._def.type ?? 'unknown';\n\n              opts.onError?.({\n                error,\n                path,\n                input,\n                ctx: ctxManager.valueOrUndefined(),\n                req: opts.req,\n                type,\n              });\n\n              const shape = getErrorShape({\n                config,\n                ctx: ctxManager.valueOrUndefined(),\n                error,\n                input,\n                path,\n                type,\n              });\n\n              return shape;\n            },\n          });\n          for (const [key, value] of Object.entries(sseHeaders)) {\n            headers.set(key, value);\n          }\n\n          const headResponse = initResponse({\n            ctx: ctxManager.valueOrUndefined(),\n            info,\n            responseMeta: opts.responseMeta,\n            errors: [],\n            headers,\n            untransformedJSON: null,\n          });\n\n          return new Response(stream, {\n            headers,\n            status: headResponse.status,\n          });\n        }\n      }\n    }\n\n    // batch response handlers\n    if (info.accept === 'application/jsonl') {\n      // httpBatchStreamLink\n      headers.set('content-type', 'application/json');\n      headers.set('transfer-encoding', 'chunked');\n      const headResponse = initResponse({\n        ctx: ctxManager.valueOrUndefined(),\n        info,\n        responseMeta: opts.responseMeta,\n        errors: [],\n        headers,\n        untransformedJSON: null,\n      });\n      const stream = jsonlStreamProducer({\n        ...config.jsonl,\n        /**\n         * Example structure for `maxDepth: 4`:\n         * {\n         *   // 1\n         *   0: {\n         *     // 2\n         *     result: {\n         *       // 3\n         *       data: // 4\n         *     }\n         *   }\n         * }\n         */\n        maxDepth: Infinity,\n        data: rpcCalls.map(async (res) => {\n          const [error, result] = await res;\n\n          const call = info.calls[0];\n\n          if (error) {\n            return {\n              error: getErrorShape({\n                config,\n                ctx: ctxManager.valueOrUndefined(),\n                error,\n                input: call!.result(),\n                path: call!.path,\n                type: call!.procedure?._def.type ?? 'unknown',\n              }),\n            };\n          }\n\n          /**\n           * Not very pretty, but we need to wrap nested data in promises\n           * Our stream producer will only resolve top-level async values or async values that are directly nested in another async value\n           */\n          const iterable = isObservable(result.data)\n            ? observableToAsyncIterable(result.data, opts.req.signal)\n            : Promise.resolve(result.data);\n          return {\n            result: Promise.resolve({\n              data: iterable,\n            }),\n          };\n        }),\n        serialize: config.transformer.output.serialize,\n        onError: (cause) => {\n          opts.onError?.({\n            error: getTRPCErrorFromUnknown(cause),\n            path: undefined,\n            input: undefined,\n            ctx: ctxManager.valueOrUndefined(),\n            req: opts.req,\n            type: info?.type ?? 'unknown',\n          });\n        },\n\n        formatError(errorOpts) {\n          const call = info?.calls[errorOpts.path[0] as any];\n\n          const error = getTRPCErrorFromUnknown(errorOpts.error);\n          const input = call?.result();\n          const path = call?.path;\n          const type = call?.procedure?._def.type ?? 'unknown';\n\n          // no need to call `onError` here as it will be propagated through the stream itself\n\n          const shape = getErrorShape({\n            config,\n            ctx: ctxManager.valueOrUndefined(),\n            error,\n            input,\n            path,\n            type,\n          });\n\n          return shape;\n        },\n      });\n\n      return new Response(stream, {\n        headers,\n        status: headResponse.status,\n      });\n    }\n\n    // httpBatchLink\n    /**\n     * Non-streaming response:\n     * - await all responses in parallel, blocking on the slowest one\n     * - create headers with known response body\n     * - return a complete HTTPResponse\n     */\n    headers.set('content-type', 'application/json');\n    const results: RPCResult[] = (await Promise.all(rpcCalls)).map(\n      (res): RPCResult => {\n        const [error, result] = res;\n        if (error) {\n          return res;\n        }\n\n        if (isDataStream(result.data)) {\n          return [\n            new TRPCError({\n              code: 'UNSUPPORTED_MEDIA_TYPE',\n              message:\n                'Cannot use stream-like response in non-streaming request - use httpBatchStreamLink',\n            }),\n            undefined,\n          ];\n        }\n        return res;\n      },\n    );\n    const resultAsRPCResponse = results.map(\n      (\n        [error, result],\n        index,\n      ): TRPCResponse<unknown, inferRouterError<TRouter>> => {\n        const call = info.calls[index]!;\n        if (error) {\n          return {\n            error: getErrorShape({\n              config,\n              ctx: ctxManager.valueOrUndefined(),\n              error,\n              input: call.result(),\n              path: call.path,\n              type: call.procedure?._def.type ?? 'unknown',\n            }),\n          };\n        }\n        return {\n          result: { data: result.data },\n        };\n      },\n    );\n\n    const errors = results\n      .map(([error]) => error)\n      .filter(Boolean) as TRPCError[];\n\n    const headResponse = initResponse({\n      ctx: ctxManager.valueOrUndefined(),\n      info,\n      responseMeta: opts.responseMeta,\n      untransformedJSON: resultAsRPCResponse,\n      errors,\n      headers,\n    });\n\n    return new Response(\n      JSON.stringify(transformTRPCResponse(config, resultAsRPCResponse)),\n      {\n        status: headResponse.status,\n        headers,\n      },\n    );\n  } catch (cause) {\n    const [_infoError, info] = infoTuple;\n    const ctx = ctxManager.valueOrUndefined();\n    // we get here if\n    // - batching is called when it's not enabled\n    // - `createContext()` throws\n    // - `router._def._config.transformer.output.serialize()` throws\n    // - post body is too large\n    // - input deserialization fails\n    // - `errorFormatter` return value is malformed\n    const { error, untransformedJSON, body } = caughtErrorToData(cause, {\n      opts,\n      ctx: ctxManager.valueOrUndefined(),\n      type: info?.type ?? 'unknown',\n    });\n\n    const headResponse = initResponse({\n      ctx,\n      info,\n      responseMeta: opts.responseMeta,\n      untransformedJSON,\n      errors: [error],\n      headers,\n    });\n\n    return new Response(body, {\n      status: headResponse.status,\n      headers,\n    });\n  }\n}\n"], "x_google_ignoreList": [6, 7, 8, 9, 15, 17], "mappings": ";;;;;;AAIA,SAAgB,iCACdA,QACqC;AACrC,KAAI;AACF,MAAI,WAAW,KACb,QAAO;AAET,OAAK,SAAS,OAAO,CACnB,OAAM,IAAI,MAAM;EAElB,MAAM,kBAAkB,OAAO,QAAQ,OAAO,CAAC,OAC7C,CAAC,CAAC,MAAM,MAAM,YAAY,UAAU,SACrC;AAED,MAAI,gBAAgB,SAAS,EAC3B,OAAM,IAAI,OACP,qDAAqD,gBACnD,IAAI,CAAC,CAAC,KAAK,MAAM,MAAM,EAAE,IAAI,WAAW,MAAM,EAAE,CAChD,KAAK,KAAK,CAAC;AAGlB,SAAO;CACR,SAAQ,OAAO;AACd,QAAM,IAAI,UAAU;GAClB,MAAM;GACN,SAAS;GACT;EACD;CACF;AACF;AACD,SAAgB,gCACdC,KACqC;CACrC,IAAID;AACJ,KAAI;AACF,WAAS,KAAK,MAAM,IAAI;CACzB,SAAQ,OAAO;AACd,QAAM,IAAI,UAAU;GAClB,MAAM;GACN,SAAS;GACT;EACD;CACF;AACD,QAAO,iCAAiC,OAAO;AAChD;;;;;;;;;ACvBD,SAAS,KAAcE,IAA4B;CACjD,IAAIC,UAAmC;CACvC,MAAM,MAAM,OAAO,IAAI,yBAAyB;CAChD,IAAIC,QAA8B;AAClC,QAAO;EAIL,MAAM,YAA8B;;AAClC,OAAI,UAAU,IACZ,QAAO;AAIT,sEAAY,IAAI,CAAC,MAAM,CAAC,UAAU;AAChC,QAAI,iBAAiB,UACnB,OAAM;AAER,UAAM,IAAI,UAAU;KAClB,MAAM;KACN,SAAS,iBAAiB,QAAQ,MAAM,UAAU;KAClD;IACD;GACF,EAAC;AAEF,WAAQ,MAAM;AACd,aAAU;AAEV,UAAO;EACR;EAID,QAAQ,MAA2B;AACjC,UAAO,UAAU,MAAM;EACxB;CACF;AACF;AAED,MAAMC,yBAA6C;CACjD,QAAQ,KAAK;;AACX,gCAAS,IAAI,QAAQ,IAAI,eAAe,qDAA/B,iBAAiC,WAAW,mBAAmB;CACzE;CACD,MAAM,MAAM,MAAM;;EAChB,MAAM,EAAE,KAAK,GAAG;EAChB,MAAM,cAAc,KAAK,aAAa,IAAI,QAAQ,KAAK;EACvD,MAAM,QAAQ,cAAc,KAAK,KAAK,MAAM,IAAI,GAAG,CAAC,KAAK,IAAK;EAG9D,MAAM,YAAY,KAAK,YAAkC;GACvD,IAAIC;AACJ,OAAI,IAAI,WAAW,OAAO;IACxB,MAAM,aAAa,KAAK,aAAa,IAAI,QAAQ;AACjD,QAAI,WACF,UAAS,KAAK,MAAM,WAAW;GAElC,MACC,UAAS,MAAM,IAAI,MAAM;AAE3B,OAAI,kBACF,QAAO,CAAE;AAGX,QAAK,YACH,QAAO,EACL,GAAG,KAAK,OAAO,KAAK,QAAQ,YAAY,MAAM,YAAY,OAAO,CAClE;AAGH,QAAK,SAAS,OAAO,CACnB,OAAM,IAAI,UAAU;IAClB,MAAM;IACN,SAAS;GACV;GAEH,MAAMC,MAAmB,CAAE;AAC3B,QAAK,MAAM,SAAS,MAAM,MAAM,EAAE;IAChC,MAAM,QAAQ,OAAO;AACrB,QAAI,iBACF,KAAI,SACF,KAAK,OAAO,KAAK,QAAQ,YAAY,MAAM,YAAY,MAAM;GAElE;AAED,UAAO;EACR,EAAC;EAEF,MAAM,QAAQ,MAAM,QAAQ,IAC1B,MAAM,IACJ,OAAO,MAAM,UAAqD;GAChE,MAAM,YAAY,MAAM,mBAAmB,KAAK,QAAQ,KAAK;AAC7D,UAAO;IACL;IACA;IACA,aAAa,YAAY;KACvB,MAAM,SAAS,MAAM,UAAU,MAAM;KACrC,IAAI,QAAQ,OAAO;AAEnB,gEAAI,UAAW,KAAK,UAAS,gBAAgB;;MAC3C,MAAM,2CACJ,KAAK,QAAQ,IAAI,gBAAgB,iEACjC,KAAK,aAAa,IAAI,cAAc,uCACpC,KAAK,aAAa,IAAI,gBAAgB;AAExC,UAAI,YACF,KAAI,SAAS,MAAM,CACjB,qFACK,cACU;WAEV;;AACL,kEAAU,EACK,YACd;MACF;KAEJ;AACD,YAAO;IACR;IACD,QAAQ,MAAM;;AACZ,iCAAO,UAAU,QAAQ,wEAAG;IAC7B;GACF;EACF,EACF,CACF;EAED,MAAM,QAAQ,IAAI,IAChB,MAAM,IAAI,CAAC,SAAS;;kCAAK,6EAAW,KAAK;EAAI,EAAC,CAAC,OAAO,QAAQ;;AAIhE,MAAI,MAAM,OAAO,EACf,OAAM,IAAI,UAAU;GAClB,MAAM;GACN,UAAU,sCAAsC,MAAM,KAAK,MAAM,CAAC,KAChE,KACD,CAAC;EACH;EAEH,MAAMC,gCACJ,MAAM,QAAQ,CAAC,MAAM,CAAC,8EAAS;EAEjC,MAAM,sBAAsB,KAAK,aAAa,IAAI,mBAAmB;EAErE,MAAMC,OAAwB;GAC5B;GACA,QAAQ,IAAI,QAAQ,IAAI,cAAc;GACtC;GACA;GACA,kBACE,wBAAwB,OACpB,OACA,gCAAgC,oBAAoB;GAC1D,QAAQ,IAAI;GACZ,KAAK,KAAK;EACX;AACD,SAAO;CACR;AACF;AAED,MAAMC,6BAAiD;CACrD,QAAQ,KAAK;;AACX,iCAAS,IAAI,QAAQ,IAAI,eAAe,sDAA/B,kBAAiC,WAAW,sBAAsB;CAC5E;CACD,MAAM,MAAM,MAAM;EAChB,MAAM,EAAE,KAAK,GAAG;AAChB,MAAI,IAAI,WAAW,OACjB,OAAM,IAAI,UAAU;GAClB,MAAM;GACN,SACE;EACH;EAEH,MAAM,YAAY,KAAK,YAAY;GACjC,MAAM,KAAK,MAAM,IAAI,UAAU;AAC/B,UAAO;EACR,EAAC;EACF,MAAM,YAAY,MAAM,mBAAmB,KAAK,QAAQ,KAAK,KAAK;AAClE,SAAO;GACL,QAAQ;GACR,OAAO,CACL;IACE,MAAM,KAAK;IACX,aAAa,UAAU;IACvB,QAAQ,UAAU;IAClB;GACD,CACF;GACD,aAAa;GACb,MAAM;GACN,kBAAkB;GAClB,QAAQ,IAAI;GACZ,KAAK,KAAK;EACX;CACF;AACF;AAED,MAAMC,gCAAoD;CACxD,QAAQ,KAAK;;AACX,iCAAS,IAAI,QACV,IAAI,eAAe,sDADb,kBAEL,WAAW,2BAA2B;CAC3C;CACD,MAAM,MAAM,MAAM;EAChB,MAAM,EAAE,KAAK,GAAG;AAChB,MAAI,IAAI,WAAW,OACjB,OAAM,IAAI,UAAU;GAClB,MAAM;GACN,SACE;EACH;EAEH,MAAM,YAAY,KAAK,YAAY;AACjC,UAAO,IAAI;EACZ,EAAC;AACF,SAAO;GACL,OAAO,CACL;IACE,MAAM,KAAK;IACX,aAAa,UAAU;IACvB,QAAQ,UAAU;IAClB,WAAW,MAAM,mBAAmB,KAAK,QAAQ,KAAK,KAAK;GAC5D,CACF;GACD,aAAa;GACb,QAAQ;GACR,MAAM;GACN,kBAAkB;GAClB,QAAQ,IAAI;GACZ,KAAK,KAAK;EACX;CACF;AACF;AAED,MAAM,WAAW;CACf;CACA;CACA;AACD;AAED,SAAS,sBAAsBC,KAAkC;CAC/D,MAAM,UAAU,SAAS,KAAK,CAACC,cAAY,UAAQ,QAAQ,IAAI,CAAC;AAChE,KAAI,QACF,QAAO;AAGT,MAAK,WAAW,IAAI,WAAW,MAE7B,QAAO;AAGT,OAAM,IAAI,UAAU;EAClB,MAAM;EACN,SAAS,IAAI,QAAQ,IAAI,eAAe,IACnC,4BAA4B,IAAI,QAAQ,IAAI,eAAe,CAAC,IAC7D;CACL;AACF;AAED,eAAsB,eACpBC,MAC0B;CAC1B,MAAM,UAAU,sBAAsB,KAAK,IAAI;AAC/C,QAAO,MAAM,QAAQ,MAAM,KAAK;AACjC;;;;AChSD,SAAgB,aACdC,OACwD;AACxD,QAAO,SAAS,MAAM,IAAI,MAAM,YAAY;AAC7C;AAED,SAAgB,gBAAgB,UAAU,cAAqB;AAC7D,OAAM,IAAI,aAAa,SAAS;AACjC;;;;;;;;;ACID,MAAM,oCAAoB,IAAI;;;;AAQ9B,MAAM,OAAO,MAAM,CAElB;sBAqMW,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAlKnB,IAAa,YAAb,MAAa,UAAwC;CAwBnD,AAAU,YAAYC,KAAuD;qCAyS7E,MA7TmB;qCA6TlB,MAzTS,eAA6D,CAAE;qCAyTvE,MApTQ,cAA6C;qCAoTpD,2BA/J6B;AAxI9B,aAAW,QAAQ,WACjB,MAAK,UAAU,IAAI,QAAQ;MAE3B,MAAK,UAAU;EAMjB,MAAM,aAAa,KAAK,QAAQ,KAAK,CAAC,UAAU;GAE9C,MAAM,EAAE,aAAa,GAAG;AACxB,QAAK,cAAc;AACnB,QAAK,aAAa;IAChB,QAAQ;IACR;GACD;AAED,iEAAa,QAAQ,CAAC,EAAE,SAAS,KAAK;AACpC,YAAQ,MAAM;GACf,EAAC;EACH,EAAC;AAGF,MAAI,WAAW,WACb,YAAW,MAAM,CAAC,WAAW;GAE3B,MAAM,EAAE,aAAa,GAAG;AACxB,QAAK,cAAc;AACnB,QAAK,aAAa;IAChB,QAAQ;IACR;GACD;AAED,iEAAa,QAAQ,CAAC,EAAE,QAAQ,KAAK;AACnC,WAAO,OAAO;GACf,EAAC;EACH,EAAC;CAEL;;;;;;;;;;;;;;;;;;;CAoBD,YAAkC;EAEhC,IAAIC;EACJ,IAAIC;EAEJ,MAAM,EAAE,YAAY,GAAG;AACvB,MAAI,eAAe,MAAM;AAEvB,OAAI,KAAK,gBAAgB,KAEvB,OAAM,IAAI,MAAM;GAElB,MAAM,aAAa,eAAkB;AACrC,QAAK,cAAc,eAAe,KAAK,aAAa,WAAW;AAC/D,aAAU,WAAW;AACrB,iBAAc,MAAM;AAClB,QAAI,KAAK,gBAAgB,KACvB,MAAK,cAAc,kBAAkB,KAAK,aAAa,WAAW;GAErE;EACF,OAAM;GAEL,MAAM,EAAE,QAAQ,GAAG;AACnB,OAAI,WAAW,YACb,WAAU,QAAQ,QAAQ,WAAW,MAAM;OAE3C,WAAU,QAAQ,OAAO,WAAW,OAAO;AAE7C,iBAAc;EACf;AAGD,SAAO,OAAO,OAAO,SAAS,EAAE,YAAa,EAAC;CAC/C;;CAID,KACEC,aAIAC,YAIwC;EACxC,MAAM,aAAa,KAAK,WAAW;EACnC,MAAM,EAAE,aAAa,GAAG;AACxB,SAAO,OAAO,OAAO,WAAW,KAAK,aAAa,WAAW,EAAE,EAC7D,YACD,EAAC;CACH;CAED,MACEC,YAIgC;EAChC,MAAM,aAAa,KAAK,WAAW;EACnC,MAAM,EAAE,aAAa,GAAG;AACxB,SAAO,OAAO,OAAO,WAAW,MAAM,WAAW,EAAE,EACjD,YACD,EAAC;CACH;CAED,QAAQC,WAAyD;EAC/D,MAAM,aAAa,KAAK,WAAW;EACnC,MAAM,EAAE,aAAa,GAAG;AACxB,SAAO,OAAO,OAAO,WAAW,QAAQ,UAAU,EAAE,EAClD,YACD,EAAC;CACH;;;;CAUD,OAAO,MAASC,SAA0C;EACxD,MAAM,SAAS,UAAU,uBAAuB,QAAQ;AACxD,gBAAc,WAAW,cACrB,SACA,UAAU,0BAA0B,QAAQ;CACjD;;CAGD,OAAiB,0BAA6BA,SAAyB;EACrE,MAAM,UAAU,IAAI,UAAa;AACjC,oBAAkB,IAAI,SAAS,QAA8B;AAC7D,oBAAkB,IAAI,SAAS,QAA8B;AAC7D,SAAO;CACR;;CAGD,OAAiB,uBAA0BA,SAAyB;AAClE,SAAO,kBAAkB,IAAI,QAAQ;CACtC;;;;CAMD,OAAO,QAAWC,OAA2B;EAC3C,MAAMD,iBACG,UAAU,YACjB,UAAU,QACV,UAAU,gBACH,MAAM,SAAS,aAClB,QACA,QAAQ,QAAQ,MAAM;AAC5B,SAAO,UAAU,MAAM,QAAQ,CAAC,WAAW;CAG5C;CAQD,aAAa,IACXE,QACqB;EACrB,MAAM,cAAc,MAAM,QAAQ,OAAO,GAAG,SAAS,CAAC,GAAG,MAAO;EAChE,MAAM,qBAAqB,YAAY,IAAI,UAAU,QAAQ;AAC7D,MAAI;AACF,UAAO,MAAM,QAAQ,IAAI,mBAAmB;EAC7C,UAAS;AACR,sBAAmB,QAAQ,CAAC,EAAE,aAAa,KAAK;AAC9C,iBAAa;GACd,EAAC;EACH;CACF;CAQD,aAAa,KACXA,QACqB;EACrB,MAAM,cAAc,MAAM,QAAQ,OAAO,GAAG,SAAS,CAAC,GAAG,MAAO;EAChE,MAAM,qBAAqB,YAAY,IAAI,UAAU,QAAQ;AAC7D,MAAI;AACF,UAAO,MAAM,QAAQ,KAAK,mBAAmB;EAC9C,UAAS;AACR,sBAAmB,QAAQ,CAAC,EAAE,aAAa,KAAK;AAC9C,iBAAa;GACd,EAAC;EACH;CACF;;;;;;;;;;;;;CAcD,aAAa,eACXC,UACA;EAEA,MAAM,eAAe,SAAS,IAAI,iBAAiB;AAGnD,MAAI;AACF,UAAO,MAAM,QAAQ,KAAK,aAAa;EACxC,UAAS;AACR,QAAK,MAAM,WAAW,aAEpB,SAAQ,aAAa;EAExB;CACF;AACF;;;;;;;AAQD,SAAgB,iBACdC,SACwC;AACxC,QAAO,UAAU,MAAM,QAAQ,CAAC,KAAK,MAAM,CAAC,OAAQ,EAAU;AAC/D;;;AAKD,SAAS,gBAA4C;CACnD,IAAIC;CACJ,IAAIC;CACJ,MAAM,UAAU,IAAI,QAAW,CAAC,UAAU,YAAY;AACpD,YAAU;AACV,WAAS;CACV;AACD,QAAO;EACL;EACA;EACA;CACD;AACF;;AAID,SAAS,eAAkBC,KAAmBC,QAAyB;AACrE,QAAO,CAAC,GAAG,KAAK,MAAO;AACxB;AAED,SAAS,iBAAoBD,KAAmBE,OAAe;AAC7D,QAAO,CAAC,GAAG,IAAI,MAAM,GAAG,MAAM,EAAE,GAAG,IAAI,MAAM,QAAQ,EAAE,AAAC;AACzD;AAED,SAAS,kBAAqBF,KAAmBG,QAAiB;CAChE,MAAM,QAAQ,IAAI,QAAQ,OAAY;AACtC,KAAI,UAAU,GACZ,QAAO,iBAAiB,KAAK,MAAM;AAErC,QAAO;AACR;;;;;ACzXD,sCAAO,sEAAY,QAAQ;AAI3B,6CAAO,uFAAiB,QAAQ;;;;;;;;AAShC,SAAgB,aAAgBC,OAAUC,SAAqC;CAC7E,MAAM,KAAK;CAGX,MAAM,WAAW,GAAG,OAAO;AAG3B,IAAG,OAAO,WAAW,MAAM;AACzB,WAAS;AACT,wDAAY;CACb;AAED,QAAO;AACR;;;;;;;;AASD,SAAgB,kBACdD,OACAE,SACqB;CACrB,MAAM,KAAK;CAGX,MAAM,WAAW,GAAG,OAAO;AAG3B,IAAG,OAAO,gBAAgB,YAAY;AACpC,QAAM,SAAS;AACf,6DAAM,UAAY;CACnB;AAED,QAAO;AACR;;;;ACnDD,MAAa,+BAA+B,QAAQ;AAEpD,SAAgB,cAAcC,IAAY;CACxC,IAAIC,QAA8C;AAElD,QAAO,aACL,EACE,QAAQ;AACN,MAAI,MACF,OAAM,IAAI,MAAM;EAGlB,MAAM,UAAU,IAAI,QAClB,CAAC,YAAY;AACX,WAAQ,WAAW,MAAM,QAAQ,6BAA6B,EAAE,GAAG;EACpE;AAEH,SAAO;CACR,EACF,GACD,MAAM;AACJ,MAAI,MACF,cAAa,MAAM;CAEtB,EACF;AACF;;;;;CC5BD,SAAS,YAAY;EACnB,IAAI,IAAI,qBAAqB,kBAAkB,kBAAkB,SAAUC,KAAGC,KAAG;GAC7E,IAAIC,MAAI,OAAO;AACf,UAAOA,IAAE,OAAO,mBAAmBA,IAAE,QAAQF,KAAGE,IAAE,aAAaD,KAAGC;EACnE,GACD,IAAI,CAAE,GACN,IAAI,CAAE;EACR,SAAS,MAAMF,KAAGC,KAAG;AACnB,OAAI,QAAQA,KAAG;AACb,QAAI,OAAOA,IAAE,KAAKA,IAAG,OAAM,IAAI,UAAU;AACzC,QAAID,KAAG,IAAI,IAAIC,IAAE,OAAO,gBAAgB,OAAO,OAAO,sBAAsB;AAC5E,aAAS,MAAM,MAAM,IAAIA,IAAE,OAAO,WAAW,OAAO,OAAO,iBAAiB,GAAGD,MAAI,IAAI,IAAI;AAC3F,QAAI,qBAAqB,EAAG,OAAM,IAAI,UAAU;AAChD,UAAM,IAAI,SAASG,MAAI;AACrB,SAAI;AACF,QAAE,KAAKF,IAAE;KACV,SAAQD,KAAG;AACV,aAAO,QAAQ,OAAOA,IAAE;KACzB;IACF,IAAG,EAAE,KAAK;KACT,GAAGC;KACH,GAAG;KACH,GAAGD;IACJ,EAAC;GACH,MAAM,QAAK,EAAE,KAAK;IACjB,GAAGC;IACH,GAAGD;GACJ,EAAC;AACF,UAAOC;EACR;AACD,SAAO;GACF;GACH,GAAG,MAAM,KAAK,OAAO,EAAE;GACvB,GAAG,MAAM,KAAK,OAAO,EAAE;GACvB,GAAG,SAAS,IAAI;IACd,IAAI,GACF,IAAI,KAAK,GACT,IAAI;IACN,SAAS,OAAO;AACd,YAAO,IAAI,EAAE,KAAK,EAAG,KAAI;AACvB,WAAK,EAAE,KAAK,MAAM,EAAG,QAAO,IAAI,GAAG,EAAE,KAAK,EAAE,EAAE,QAAQ,SAAS,CAAC,KAAK,KAAK;AAC1E,UAAI,EAAE,GAAG;OACP,IAAID,MAAI,EAAE,EAAE,KAAK,EAAE,EAAE;AACrB,WAAI,EAAE,EAAG,QAAO,KAAK,GAAG,QAAQ,QAAQA,IAAE,CAAC,KAAK,MAAM,IAAI;MAC3D,MAAM,MAAK;KACb,SAAQA,KAAG;AACV,aAAO,IAAIA,IAAE;KACd;AACD,SAAI,MAAM,EAAG,QAAO,MAAM,IAAI,QAAQ,OAAO,EAAE,GAAG,QAAQ,SAAS;AACnE,SAAI,MAAM,EAAG,OAAM;IACpB;IACD,SAAS,IAAIE,KAAG;AACd,YAAO,IAAI,MAAM,IAAI,IAAI,EAAEA,KAAG,KAAKA,KAAG,MAAM;IAC7C;AACD,WAAO,MAAM;GACd;EACF;CACF;AACD,QAAO,UAAU,WAAW,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CC1DjG,SAAS,eAAe,GAAG,GAAG;AAC5B,OAAK,IAAI,GAAG,KAAK,IAAI;CACtB;AACD,QAAO,UAAU,gBAAgB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CCHtG,IAAIE;CACJ,SAASC,uBAAqB,GAAG;AAC/B,SAAO,IAAID,gBAAc,GAAG;CAC7B;AACD,QAAO,UAAUC,wBAAsB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CCJ5G,IAAIC;CACJ,SAASC,sBAAoB,GAAG;AAC9B,SAAO,WAAY;AACjB,UAAO,IAAI,eAAe,EAAE,MAAM,MAAM,UAAU;EACnD;CACF;CACD,SAAS,eAAe,GAAG;EACzB,IAAI,GAAG;EACP,SAAS,OAAOC,KAAGC,KAAG;AACpB,OAAI;IACF,IAAI,IAAI,EAAED,KAAGC,IAAE,EACb,IAAI,EAAE,OACN,IAAI,aAAaH;AACnB,YAAQ,QAAQ,IAAI,EAAE,IAAI,EAAE,CAAC,KAAK,SAAUG,KAAG;AAC7C,SAAI,GAAG;MACL,IAAI,IAAI,aAAaD,MAAI,WAAW;AACpC,WAAK,EAAE,KAAKC,IAAE,KAAM,QAAO,OAAO,GAAGA,IAAE;AACvC,YAAI,EAAE,GAAGA,IAAE,CAAC;KACb;AACD,YAAO,EAAE,OAAO,WAAW,UAAUA,IAAE;IACxC,GAAE,SAAUC,KAAG;AACd,YAAO,SAASA,IAAE;IACnB,EAAC;GACH,SAAQA,KAAG;AACV,WAAO,SAASA,IAAE;GACnB;EACF;EACD,SAAS,OAAOA,KAAG,GAAG;AACpB,WAAQA,KAAR;IACE,KAAK;AACH,OAAE,QAAQ;MACR,OAAO;MACP,OAAO;KACR,EAAC;AACF;IACF,KAAK;AACH,OAAE,OAAO,EAAE;AACX;IACF,QACE,GAAE,QAAQ;KACR,OAAO;KACP,OAAO;IACR,EAAC;GACL;AACD,IAAC,IAAI,EAAE,QAAQ,OAAO,EAAE,KAAK,EAAE,IAAI,GAAG,IAAI;EAC3C;AACD,OAAK,UAAU,SAAUA,KAAG,GAAG;AAC7B,UAAO,IAAI,QAAQ,SAAU,GAAG,GAAG;IACjC,IAAI,IAAI;KACN,KAAKA;KACL,KAAK;KACL,SAAS;KACT,QAAQ;KACR,MAAM;IACP;AACD,QAAI,IAAI,EAAE,OAAO,KAAK,IAAI,IAAI,GAAG,OAAOA,KAAG,EAAE;GAC9C;EACF,GAAE,qBAAqB,EAAE,cAAc,KAAK,iBAAiB;CAC/D;AACD,gBAAe,UAAU,qBAAqB,UAAU,OAAO,iBAAiB,qBAAqB,WAAY;AAC/G,SAAO;CACR,GAAE,eAAe,UAAU,OAAO,SAAU,GAAG;AAC9C,SAAO,KAAK,QAAQ,QAAQ,EAAE;CAC/B,GAAE,eAAe,UAAU,WAAW,SAAU,GAAG;AAClD,SAAO,KAAK,QAAQ,SAAS,EAAE;CAChC,GAAE,eAAe,UAAU,YAAY,SAAU,GAAG;AACnD,SAAO,KAAK,QAAQ,UAAU,EAAE;CACjC;AACD,QAAO,UAAUH,uBAAqB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;;;AC/D3G,SAAgB,iBACdI,UACyD;CACzD,MAAM,WAAW,SAAS,OAAO,gBAAgB;AAIjD,KAAI,SAAS,OAAO,cAClB,QAAO;AAGT,QAAO,kBAAkB,UAAU,YAAY;;AAC7C,6BAAM,SAAS,2DAAT,+BAAmB;CAC1B,EAAC;AACH;;;;AAID,SAAuB;+BAqEnB;;;wEApEFC,UACAC,MACmB;;;GACnB,MAAY,yBAAW,iBAAiB,SAAS;GAEjD,MAAM,sBAAQ,cAAc,KAAK,cAAc;GAE/C,MAAM,eAAe,MAAM,OAAO;GAGlC,IAAIC;AAEJ,UAAO,MAAM;AACX,6DAAe,UAAU,KAAK,CAAC,SAAS,MAAM,EAAE,YAAa,EAAC;AAC9D,QAAI,WAAW,6BAEb,kBAAiB;AAEnB,QAAI,OAAO,KACT,QAAO;AAET,UAAM,OAAO;AAEb,aAAS;GACV;;;;;;CACF;+BA2CI;;;;;;;AApCL,SAAuB;6BAoCjB;;;sEAnCJF,UACAG,MAImB;;;GACnB,MAAY,wBAAW,iBAAiB,SAAS;GAGjD,IAAID;GAEJ,MAAM,qBAAQ,cAAc,KAAK,cAAc;GAE/C,IAAI,QAAQ,KAAK;GAEjB,IAAI,eAAe,IAAI,QAA6C,MAAM,CAEzE;AAED,UAAO,MAAM;AACX,6DAAe,UAAU,KAAK,CAAC,SAAS,MAAM,EAAE,YAAa,EAAC;AAC9D,QAAI,WAAW,6BACb,kBAAiB;AAEnB,QAAI,OAAO,KACT,QAAO,OAAO;AAEhB,UAAM,OAAO;AACb,QAAI,EAAE,UAAU,EACd,gBAAe,MAAM,OAAO;AAG9B,aAAS;GACV;;;;;;CACF;6BACM;;;;;AC3FP,SAAgB,iBAAgC;CAC9C,IAAIE;CACJ,IAAIC;CACJ,MAAM,UAAU,IAAI,QAAgB,CAAC,KAAK,QAAQ;AAChD,YAAU;AACV,WAAS;CACV;AAED,QAAO;EAAE;EAAkB;EAAkB;CAAS;AACvD;;;;;;;ACHD,SAAS,sBACPC,UACAC,UACA;CACA,MAAM,WAAW,SAAS,OAAO,gBAAgB;CACjD,IAAIC,QAAqC;CAEzC,SAAS,UAAU;AACjB,UAAQ;AACR,aAAW,MAAM,CAEhB;CACF;CAED,SAAS,OAAO;AACd,MAAI,UAAU,OACZ;AAEF,UAAQ;EAER,MAAM,OAAO,SAAS,MAAM;AAC5B,OACG,KAAK,CAAC,WAAW;AAChB,OAAI,OAAO,MAAM;AACf,YAAQ;AACR,aAAS;KAAE,QAAQ;KAAU,OAAO,OAAO;IAAO,EAAC;AACnD,aAAS;AACT;GACD;AACD,WAAQ;AACR,YAAS;IAAE,QAAQ;IAAS,OAAO,OAAO;GAAO,EAAC;EACnD,EAAC,CACD,MAAM,CAAC,UAAU;AAChB,YAAS;IAAE,QAAQ;IAAS,OAAO;GAAO,EAAC;AAC3C,YAAS;EACV,EAAC;CACL;AAED,QAAO;EACL;EACA,SAAS,YAAY;;AACnB,YAAS;AACT,8BAAM,SAAS,2DAAT,+BAAmB;EAC1B;CACF;AACF;;;;;;;;;;;;AAqBD,SAAgB,sBAA4D;CAC1E,IAAIA,QAAqC;CACzC,IAAI,cAAc,gBAAgB;;;;CAKlC,MAAMC,YAAoD,CAAE;;;;CAI5D,MAAM,4BAAY,IAAI;CAEtB,MAAMC,SAQF,CAAE;CAEN,SAAS,aAAaC,UAAgD;AACpE,MAAI,UAAU,UAEZ;EAEF,MAAM,WAAW,sBAAsB,UAAU,CAAC,WAAW;AAC3D,OAAI,UAAU,UAEZ;AAEF,WAAQ,OAAO,QAAf;IACE,KAAK;AACH,YAAO,KAAK,CAAC,UAAU,MAAO,EAAC;AAC/B;IACF,KAAK;AACH,eAAU,OAAO,SAAS;AAC1B;IACF,KAAK;AACH,YAAO,KAAK,CAAC,UAAU,MAAO,EAAC;AAC/B,eAAU,OAAO,SAAS;AAC1B;GACH;AACD,eAAY,SAAS;EACtB,EAAC;AACF,YAAU,IAAI,SAAS;AACvB,WAAS,MAAM;CAChB;AAED,QAAO;EACL,IAAIA,UAAgD;AAClD,WAAQ,OAAR;IACE,KAAK;AACH,eAAU,KAAK,SAAS;AACxB;IACF,KAAK;AACH,kBAAa,SAAS;AACtB;IACF,KAAK,OAEH;GAEH;EACF;EACD,CAAQ,OAAO;gEAAiB;;;AAC9B,SAAI,UAAU,OACZ,OAAM,IAAI,MAAM;AAElB,aAAQ;KAER,MAAY,yBAAW,kBAAkB,CAAE,GAAE,YAAY;AACvD,cAAQ;MAER,MAAMC,SAAoB,CAAE;AAC5B,YAAM,QAAQ,IACZ,MAAM,KAAK,UAAU,QAAQ,CAAC,CAAC,IAAI,OAAO,OAAO;AAC/C,WAAI;AACF,cAAM,GAAG,SAAS;OACnB,SAAQ,OAAO;AACd,eAAO,KAAK,MAAM;OACnB;MACF,EAAC,CACH;AACD,aAAO,SAAS;AAChB,gBAAU,OAAO;AACjB,kBAAY,SAAS;AAErB,UAAI,OAAO,SAAS,EAClB,OAAM,IAAI,eAAe;KAE5B,EAAC;AAEF,YAAO,UAAU,SAAS,EAExB,cAAa,UAAU,OAAO,CAAE;AAGlC,YAAO,UAAU,OAAO,GAAG;AACzB,sDAAM,YAAY;AAElB,aAAO,OAAO,SAAS,GAAG;OAExB,MAAM,CAAC,UAAU,OAAO,GAAG,OAAO,OAAO;AAEzC,eAAQ,OAAO,QAAf;QACE,KAAK;AACH,eAAM,OAAO;AACb,kBAAS,MAAM;AACf;QACF,KAAK,QACH,OAAM,OAAO;OAChB;MACF;AACD,oBAAc,gBAAgB;KAC/B;;;;;;GACF;;CACF;AACF;;;;;;;;;;AC1LD,SAAgB,mBACdC,UACwB;CACxB,MAAM,WAAW,SAAS,OAAO,gBAAgB;AAEjD,QAAO,IAAI,eAAe;EACxB,MAAM,SAAS;;AACb,8BAAM,SAAS,2DAAT,+BAAmB;EAC1B;EAED,MAAM,KAAK,YAAY;GACrB,MAAM,SAAS,MAAM,SAAS,MAAM;AAEpC,OAAI,OAAO,MAAM;AACf,eAAW,OAAO;AAClB;GACD;AAED,cAAW,QAAQ,OAAO,MAAM;EACjC;CACF;AACF;;;;;;;ACvBD,MAAa,WAAW,OAAO,OAAO;;;;;AAMtC,SAAuB;wBAqCnB;;;iEApCFC,UACAC,gBAC0C;;;GAC1C,MAAY,yBAAW,iBAAiB,SAAS;GAGjD,IAAIC;GAKJ,IAAI,cAAc,SAAS,MAAM;AAEjC,UAAO;;IACL,MAAM,2BAAc,cAAc,eAAe;AAEjD,6DAAe,UAAU,KAAK,CAAC,aAAa,YAAY,OAAO,AAAC,EAAC;AAEjE,QAAI,WAAW,8BAA8B;AAG3C,WAAM;AACN;IACD;AAED,QAAI,OAAO,KACT,QAAO,OAAO;AAGhB,kBAAc,SAAS,MAAM;AAC7B,UAAM,OAAO;AAGb,aAAS;;;;;;;;;;;CAEZ;wBACI;;;;;;CC/CL,SAASC,iBAAe,GAAG;EACzB,IAAI,GACF,GACA,GACA,IAAI;AACN,OAAK,sBAAsB,WAAW,IAAI,OAAO,eAAe,IAAI,OAAO,WAAW,MAAM;AAC1F,OAAI,KAAK,SAAS,IAAI,EAAE,IAAK,QAAO,EAAE,KAAK,EAAE;AAC7C,OAAI,KAAK,SAAS,IAAI,EAAE,IAAK,QAAO,IAAI,sBAAsB,EAAE,KAAK,EAAE;AACvE,OAAI,mBAAmB,IAAI;EAC5B;AACD,QAAM,IAAI,UAAU;CACrB;CACD,SAAS,sBAAsB,GAAG;EAChC,SAAS,kCAAkCC,KAAG;AAC5C,OAAI,OAAOA,IAAE,KAAKA,IAAG,QAAO,QAAQ,OAAO,IAAI,UAAUA,MAAI,sBAAsB;GACnF,IAAI,IAAIA,IAAE;AACV,UAAO,QAAQ,QAAQA,IAAE,MAAM,CAAC,KAAK,SAAUA,KAAG;AAChD,WAAO;KACL,OAAOA;KACP,MAAM;IACP;GACF,EAAC;EACH;AACD,SAAO,wBAAwB,SAASC,wBAAsBD,KAAG;AAC/D,QAAK,IAAIA,KAAG,KAAK,IAAIA,IAAE;EACxB,GAAE,sBAAsB,YAAY;GACnC,GAAG;GACH,GAAG;GACH,MAAM,SAAS,OAAO;AACpB,WAAO,kCAAkC,KAAK,EAAE,MAAM,KAAK,GAAG,UAAU,CAAC;GAC1E;GACD,UAAU,SAAS,QAAQA,KAAG;IAC5B,IAAI,IAAI,KAAK,EAAE;AACf,gBAAY,MAAM,IAAI,QAAQ,QAAQ;KACpC,OAAOA;KACP,OAAO;IACR,EAAC,GAAG,kCAAkC,EAAE,MAAM,KAAK,GAAG,UAAU,CAAC;GACnE;GACD,SAAS,SAAS,OAAOA,KAAG;IAC1B,IAAI,IAAI,KAAK,EAAE;AACf,gBAAY,MAAM,IAAI,QAAQ,OAAOA,IAAE,GAAG,kCAAkC,EAAE,MAAM,KAAK,GAAG,UAAU,CAAC;GACxG;EACF,GAAE,IAAI,sBAAsB;CAC9B;AACD,QAAO,UAAUD,kBAAgB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;;;;ACpBtG,SAAS,cAAcG,OAAkD;AACvE,QAAO,OAAO,UAAU,SAAS,KAAK,MAAM,KAAK;AAClD;AAGD,MAAM,2BAA2B;AAEjC,MAAM,kCAAkC;AAGxC,MAAM,2BAA2B;AAEjC,MAAM,0BAA0B;AAGhC,MAAM,+BAA+B;AAErC,MAAM,8BAA8B;AAEpC,MAAM,8BAA8B;AAqDpC,SAAgB,UAAUA,OAA2C;AACnE,SACG,SAAS,MAAM,IAAI,WAAW,MAAM,2DAC9B,MAAQ,aAAY,oEACpB,MAAQ,cAAa;AAE/B;AAwBD,IAAM,gBAAN,cAA4B,MAAM;CAChC,YAAmBC,MAA2B;AAC5C,QAAM,gCAAgC,KAAK,KAAK,IAAI,CAAC;EADpC;CAElB;AACF;AAED,SAAgB;yCAkfX;;;kFAjfHC,MACyD;EACzD,MAAM,EAAE,MAAM,GAAG;EACjB,IAAI,UAAU;EACd,MAAM,cAAc;EAEpB,MAAM,kBAAkB,qBAAgC;EACxD,SAAS,cACPC,UACA;GACA,MAAM,MAAM;GAEZ,MAAMC,aAAW,SAAS,IAAI;AAC9B,mBAAgB,IAAIA,WAAS;AAE7B,UAAO;EACR;EAED,SAAS,cAAcC,SAA2BJ,MAA2B;AAC3E,UAAO,8BAAc;mEAAiB,KAAK;KACzC,MAAM,QAAQ,cAAc,KAAK;AACjC,SAAI,OAAO;AAET,cAAQ,MAAM,CAAC,UAAU;;AACvB,6BAAK,iDAAL,yBAAe;QAAE,OAAO;QAAO;OAAM,EAAC;MACvC,EAAC;AAEF,gBAAU,QAAQ,OAAO,MAAM;KAChC;AACD,SAAI;MACF,MAAM,uDAAa;AACnB,YAAM;OAAC;OAAK;OAA0B,OAAO,MAAM,KAAK;MAAC;KAC1D,SAAQ,OAAO;;AACd,6BAAK,kDAAL,0BAAe;OAAE,OAAO;OAAO;MAAM,EAAC;AACtC,YAAM;OACJ;OACA;4BACA,KAAK,iEAAL,6BAAmB;QAAE,OAAO;QAAO;OAAM,EAAC;MAC3C;KACF;IACF;;uBAycC;;OAzcA;EACH;EACD,SAAS,oBACPK,YACAL,MACA;AACA,UAAO,8BAAc;oEAAiB,KAAK;;;MACzC,MAAM,QAAQ,cAAc,KAAK;AACjC,UAAI,MACF,OAAM;MAER,MAAY,yBAAW,iBAAiBG,WAAS;AAEjD,UAAI;AACF,cAAO,MAAM;QACX,MAAM,uDAAa,SAAS,MAAM;AAClC,YAAI,KAAK,MAAM;AACb,eAAM;UAAC;UAAK;UAA8B,OAAO,KAAK,OAAO,KAAK;SAAC;AACnE;QACD;AACD,cAAM;SAAC;SAAK;SAA6B,OAAO,KAAK,OAAO,KAAK;QAAC;OACnE;MACF,SAAQ,OAAO;;AACd,8BAAK,kDAAL,0BAAe;QAAE,OAAO;QAAO;OAAM,EAAC;AAEtC,aAAM;QACJ;QACA;8BACA,KAAK,kEAAL,8BAAmB;SAAE,OAAO;SAAO;QAAM,EAAC;OAC3C;MACF;;;;;;IACF;;wBA0aE;;OA1aD;EACH;EACD,SAAS,cAAcH,MAA2B;AAChD,OAAI,KAAK,YAAY,KAAK,SAAS,KAAK,SACtC,QAAO,IAAI,cAAc;AAE3B,UAAO;EACR;EACD,SAAS,YACPD,OACAC,MACoD;AACpD,OAAI,UAAU,MAAM,CAClB,QAAO,CAAC,0BAA0B,cAAc,OAAO,KAAK,AAAC;AAE/D,OAAI,gBAAgB,MAAM,EAAE;AAC1B,QAAI,KAAK,YAAY,KAAK,UAAU,KAAK,SACvC,OAAM,IAAI,MAAM;AAElB,WAAO,CACL,iCACA,oBAAoB,OAAO,KAAK,AACjC;GACF;AACD,UAAO;EACR;EACD,SAAS,OAAOD,OAAgBC,MAAyC;AACvE,OAAI,iBACF,QAAO,CAAC,CAAE,CAAC;GAEb,MAAM,MAAM,YAAY,OAAO,KAAK;AACpC,OAAI,IACF,QAAO,CAAC,CAAC,WAAY,GAAE,CAAC,MAAM,GAAG,GAAI,CAAC;AAGxC,QAAK,cAAc,MAAM,CACvB,QAAO,CAAC,CAAC,KAAM,CAAC;GAGlB,MAAMM,SAAkC,CAAE;GAC1C,MAAMC,cAAiC,CAAE;AACzC,QAAK,MAAM,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,MAAM,EAAE;IAC/C,MAAM,cAAc,YAAY,MAAM,CAAC,GAAG,MAAM,GAAI,EAAC;AACrD,SAAK,aAAa;AAChB,YAAO,OAAO;AACd;IACD;AACD,WAAO,OAAO;AACd,gBAAY,KAAK,CAAC,KAAK,GAAG,WAAY,EAAC;GACxC;AACD,UAAO,CAAC,CAAC,MAAO,GAAE,GAAG,WAAY;EAClC;EAED,MAAMC,UAAgB,CAAE;AACxB,OAAK,MAAM,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,CAC5C,SAAQ,OAAO,OAAO,MAAM,CAAC,GAAI,EAAC;AAGpC,QAAM;EAEN,IAAIC,WACF;AACF,MAAI,KAAK,OACP,YAAW,SAAS,iBAAiB,KAAK,OAAO;;;;;4DAGzB;UAAT;AACf,UAAM;;;;;;;;;;;;CAET;yCAqWO;;;;;;AAhWR,SAAgB,oBAAoBR,MAA4B;CAC9D,IAAI,SAAS,mBAAmB,0BAA0B,KAAK,CAAC;CAEhE,MAAM,EAAE,WAAW,GAAG;AACtB,KAAI,UACF,UAAS,OAAO,YACd,IAAI,gBAAgB,EAClB,UAAU,OAAO,YAAY;AAC3B,MAAI,UAAU,SACZ,YAAW,QAAQ,SAAS;MAE5B,YAAW,QAAQ,UAAU,MAAM,CAAC;CAEvC,EACF,GACF;AAGH,QAAO,OACJ,YACC,IAAI,gBAAgB,EAClB,UAAU,OAAO,YAAY;AAC3B,MAAI,UAAU,SACZ,YAAW,QAAQ,IAAI;MAEvB,YAAW,QAAQ,KAAK,UAAU,MAAM,GAAG,KAAK;CAEnD,EACF,GACF,CACA,YAAY,IAAI,oBAAoB;AACxC;AAED,IAAM,aAAN,cAAyB,MAAM;CAC7B,YAA4BS,MAAe;AACzC,QAAM,6BAA6B;EADT;CAE3B;AACF;AAGD,MAAM,4BAA4B,CAACC,WAAsC;AACvE,QAAO,EACL,YAAY;EACV,MAAM,SAAS,IAAI,eAA2B,EAC5C,MAAM,YAAY;AAChB,UAAO,GAAG,QAAQ,CAAC,UAAU;AAC3B,eAAW,QAAQ,MAAM;GAC1B,EAAC;AACF,UAAO,GAAG,OAAO,MAAM;AACrB,eAAW,OAAO;GACnB,EAAC;AACF,UAAO,GAAG,SAAS,CAAC,UAAU;AAC5B,eAAW,MAAM,MAAM;GACxB,EAAC;EACH,EACF;AACD,SAAO,OAAO,WAAW;CAC1B,EACF;AACF;AAED,SAAS,sBACPC,MACA;CACA,MAAM,SACJ,eAAe,OACX,KAAK,WAAW,GAChB,0BAA0B,KAAK,CAAC,WAAW;CAEjD,IAAI,gBAAgB;AAEpB,QAAO,IAAI,eAAe;EACxB,MAAM,KAAK,YAAY;GACrB,MAAM,EAAE,MAAM,OAAO,GAAG,MAAM,OAAO,MAAM;AAE3C,OAAI,KACF,YAAW,OAAO;OAElB,YAAW,QAAQ,MAAM;EAE5B;EACD,SAAS;AACP,UAAO,OAAO,QAAQ;EACvB;CACF,GACE,YAAY,IAAI,oBAAoB,CACpC,YACC,IAAI,gBAAgC,EAClC,UAAU,OAAO,YAAY;;AAC3B,mBAAiB;EACjB,MAAM,QAAQ,cAAc,MAAM,KAAK;AACvC,gCAAgB,MAAM,KAAK,mDAAI;AAC/B,OAAK,MAAM,QAAQ,MACjB,YAAW,QAAQ,KAAK;CAE3B,EACF,GACF;AACJ;AACD,SAAS,qBACPA,MACA;CACA,MAAM,SAAS,sBAAsB,KAAK;CAE1C,IAAI,WAAW;AACf,QAAO,OAAO,YACZ,IAAI,gBAA2C,EAC7C,UAAU,MAAM,YAAY;AAC1B,OAAK,UAAU;GACb,MAAM,OAAO,KAAK,MAAM,KAAK;AAC7B,cAAW,QAAQ,KAAc;AACjC,cAAW;EACZ,OAAM;GACL,MAAMC,QAAmB,KAAK,MAAM,KAAK;AACzC,cAAW,QAAQ,MAAM;EAC1B;CACF,EACF,GACF;AACF;;;;AAKD,SAAS,qBAAqBC,iBAAkC;CAC9D,MAAM,gCAAgB,IAAI;;;;CAQ1B,SAAS,UAAU;AACjB,SAAO,MAAM,KAAK,cAAc,QAAQ,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO;CACjE;;;;CAKD,SAAS,yBAAyB;EAChC,IAAIC;EACJ,MAAM,SAAS,IAAI,eAA0B,EAC3C,MAAM,YAAY;AAChB,wBAAqB;EACtB,EACF;EAED,MAAM,mBAAmB;GACvB,SAAS,CAACC,MAAiB,mBAAmB,QAAQ,EAAE;GACxD,OAAO,MAAM;AACX,uBAAmB,OAAO;AAE1B,WAAO;AAEP,QAAI,SAAS,CACX,iBAAgB,OAAO;GAE1B;GACD,QAAQ;GACR,mBAAmB,MAAM;IACvB,MAAM,SAAS,OAAO,WAAW;AAEjC,WAAO,aAAa,QAAQ,MAAM;AAChC,YAAO,aAAa;AACpB,sBAAiB,OAAO;IACzB,EAAC;GACH;GACD,OAAO,CAACC,WAAoB;AAC1B,uBAAmB,MAAM,OAAO;AAChC,WAAO;GACR;EACF;EACD,SAAS,QAAQ;AACf,UAAO,OAAO,kBAAkB;IAC9B,QAAQ;IACR,OAAO,MAAM,CAEZ;IACD,SAAS,MAAM,CAEd;IACD,mBAAmB;IACnB,OAAO,MAAM,CAEZ;GACF,EAAC;EACH;AAED,SAAO;CACR;;;;CAKD,SAAS,YAAYC,SAAqB;EACxC,IAAI,IAAI,cAAc,IAAI,QAAQ;AAClC,OAAK,GAAG;AACN,OAAI,wBAAwB;AAC5B,iBAAc,IAAI,SAAS,EAAE;EAC9B;AACD,SAAO;CACR;;;;CAKD,SAAS,UAAUD,QAAiB;AAClC,OAAK,MAAM,cAAc,cAAc,QAAQ,CAC7C,YAAW,MAAM,OAAO;CAE3B;AAED,QAAO;EACL;EACA;EACA;CACD;AACF;;;;;AAMD,eAAsB,oBAA2BE,MAS9C;CACD,MAAM,EAAE,cAAc,CAAC,MAAM,GAAG,GAAG;CAEnC,IAAI,SAAS,qBAA2B,KAAK,KAAK;AAClD,KAAI,YACF,UAAS,OAAO,YACd,IAAI,gBAAgB,EAClB,UAAU,OAAO,YAAY;AAC3B,aAAW,QAAQ,YAAY,MAAM,CAAC;CACvC,EACF,GACF;CAEH,IAAIC,eAAuC,gBAAgB;CAE3D,MAAM,gBAAgB,qBAAqB,KAAK,gBAAgB;CAEhE,SAAS,sBAAsBC,OAAwB;EACrD,MAAM,CAAC,OAAO,MAAM,QAAQ,GAAG;EAE/B,MAAM,aAAa,cAAc,YAAY,QAAQ;AAErD,UAAQ,MAAR;GACE,KAAK,yBACH,QAAO,IAAI,YAAY;;;KACrB,MAAM,sBAAS,WAAW,mBAAmB;KAE7C,MAAM,EAAE,gBAAO,GAAG,MAAM,OAAO,MAAM;KACrC,MAAM,CAAC,UAAU,QAAQ,KAAK,GAAGC;AACjC,aAAQ,QAAR;MACE,KAAK,yBACH,QAAO,OAAO,KAAK;MACrB,KAAK;;AACH,yDAAM,KAAK,kEAAL,8BAAmB,EAAE,OAAO,KAAM,EAAC,mEAAI,IAAI,WAAW;KAC/D;;;;;;GACF,EAAC;GAEJ,KAAK,gCACH,QAAO,0DAAuB;;;KAC5B,MAAM,sBAAS,WAAW,mBAAmB;AAE7C,YAAO,MAAM;MACX,MAAM,EAAE,gBAAO,mDAAS,OAAO,MAAM;MAErC,MAAM,CAAC,UAAU,QAAQ,KAAK,GAAGA;AAEjC,cAAQ,QAAR;OACE,KAAK;AACH,cAAM,OAAO,KAAK;AAClB;OACF,KAAK,6BACH,QAAO,OAAO,KAAK;OACrB,KAAK;;AACH,0DACE,KAAK,kEAAL,8BAAmB,EAAE,OAAO,KAAM,EAAC,mEAAI,IAAI,WAAW;MAE3D;KACF;;;;;;GACF,GAAC;EAEL;CACF;CAED,SAAS,OAAOC,OAA8B;EAC5C,MAAM,CAAC,CAAC,KAAK,EAAE,GAAG,WAAW,GAAG;AAEhC,OAAK,MAAMD,WAAS,YAAY;GAC9B,MAAM,CAAC,IAAI,GAAGA;GACd,MAAM,UAAU,sBAAsBA,QAAM;AAE5C,OAAI,QAAQ,KACV,QAAO;AAGT,GAAC,KAAa,OAAO;EACtB;AACD,SAAO;CACR;CAED,MAAM,eAAe,CAACL,WAAoB;AACxC,mEAAc,OAAO,OAAO;AAC5B,gBAAc,UAAU,OAAO;CAChC;AACD,QACG,OACC,IAAI,eAAe;EACjB,MAAM,aAAa;AACjB,OAAI,cAAc;IAChB,MAAM,OAAO;AAEb,SAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,QAAQ,YAAY,EAAE;KACtD,MAAM,SAAS,OAAO,MAAa;AACnC,UAAK,OAAO;IACb;AACD,iBAAa,QAAQ,KAAc;AACnC,mBAAe;AAEf;GACD;GACD,MAAM,QAAQ;GACd,MAAM,CAAC,IAAI,GAAG;GAEd,MAAM,aAAa,cAAc,YAAY,IAAI;AACjD,cAAW,QAAQ,MAAM;EAC1B;EACD,OAAO,MAAM,aAAa,IAAI,MAAM,iBAAiB;EACrD,OAAO;CACR,IACD,EACE,QAAQ,KAAK,gBAAgB,OAC9B,EACF,CACA,MAAM,CAAC,UAAU;;AAChB,yBAAK,kDAAL,0BAAe,EAAE,MAAO,EAAC;AACzB,eAAa,MAAM;CACpB,EAAC;AAEJ,QAAO,CAAC,MAAM,aAAa,SAAS,aAAc;AACnD;;;;;CCrnBD,IAAI;CACJ,SAASO,0BAAwB,GAAG;EAClC,IAAI,IAAI,CAAE,GACR,KAAK;EACP,SAAS,KAAKC,KAAG,GAAG;AAClB,UAAO,KAAK,GAAG,IAAI,IAAI,QAAQ,SAAUC,KAAG;AAC1C,QAAE,EAAED,KAAG,EAAE,CAAC;GACX,IAAG;IACF,OAAO;IACP,OAAO,IAAI,cAAc,GAAG;GAC7B;EACF;AACD,SAAO,EAAE,sBAAsB,UAAU,OAAO,YAAY,gBAAgB,WAAY;AACtF,UAAO;EACR,GAAE,EAAE,OAAO,SAAUE,KAAG;AACvB,UAAO,KAAK,KAAK,GAAGA,OAAK,KAAK,QAAQA,IAAE;EACzC,GAAE,qBAAqB,EAAE,aAAa,EAAE,WAAW,SAAUA,KAAG;AAC/D,OAAI,EAAG,OAAM,KAAK,GAAGA;AACrB,UAAO,KAAK,SAASA,IAAE;EACxB,IAAG,qBAAqB,EAAE,cAAc,EAAE,YAAY,SAAUA,KAAG;AAClE,UAAO,KAAK,KAAK,GAAGA,OAAK,KAAK,UAAUA,IAAE;EAC3C,IAAG;CACL;AACD,QAAO,UAAUH,2BAAyB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;;;;;AC8C/G,MAAM,aAAa;AACnB,MAAM,yBAAyB;AAC/B,MAAM,kBAAkB;AACxB,MAAM,eAAe;;;;;AAYrB,SAAgB,kBACdI,MACA;;CACA,MAAM,EAAE,YAAY,UAAU,GAAG;CAEjC,MAAMC,OAAiC;EACrC,6CAAS,KAAK,8DAAM,0EAAW;EAC/B,oDAAY,KAAK,gEAAM,mFAAc;CACtC;CACD,MAAMC,yBAA2B,KAAK,6DAAU,CAAE;AAElD,KACE,KAAK,WACL,OAAO,8BACP,KAAK,aAAa,OAAO,2BAEzB,OAAM,IAAI,OACP,mHAAmH,KAAK,WAAW,sCAAsC,OAAO,2BAA2B;CAIhN,SAAgB;0BAuWZ;;;qEAvWsD;AACxD,SAAM;IACJ,OAAO;IACP,MAAM,KAAK,UAAU,OAAO;GAC7B;GAID,IAAIC,WAAoD,KAAK;AAE7D,OAAI,KAAK,sBACP,YAAW,cAAc,UAAU;IACjC,OAAO;IACP,eAAe;GAChB,EAAC;AAGJ,OACE,KAAK,iBACL,KAAK,gBAAgB,KACrB,KAAK,kBAAkB,SAEvB,YAAW,gBAAgB,UAAU,EACnC,eAAe,KAAK,cACrB,EAAC;AAGJ,OAAI,KAAK,WAAW,KAAK,eAAe,YAAY,KAAK,aAAa,EACpE,YAAW,SAAS,UAAU,KAAK,WAAW;GAKhD,IAAIC;GACJ,IAAIC;;;;;2DAEgB;KAAT;KAAmB;AAC5B,UAAI,UAAU,UAAU;AACtB,aAAM;QAAE,OAAO;QAAY,MAAM;OAAI;AACrC;MACD;AAED,cAAQ,kBAAkB,MAAM,GAC5B;OAAE,IAAI,MAAM;OAAI,MAAM,MAAM;MAAI,IAChC,EAAE,MAAM,MAAO;AAEnB,YAAM,OAAO,KAAK,UAAU,UAAU,MAAM,KAAK,CAAC;AAElD,YAAM;AAGN,cAAQ;AACR,cAAQ;KACT;;;;;;;;;;;;EACF;0BAiTI;;CA/SL,SAAgB;2CA+SV;;;sFA/SqE;AACzE,OAAI;AACF,wFAAO,WAAW;AAElB,UAAM;KACJ,OAAO;KACP,MAAM;IACP;GACF,SAAQ,OAAO;;AACd,QAAI,aAAa,MAAM,CAErB;IAIF,MAAM,QAAQ,wBAAwB,MAAM;IAC5C,MAAM,kDAAO,KAAK,kEAAL,8BAAmB,EAAE,MAAO,EAAC,iEAAI;AAC9C,UAAM;KACJ,OAAO;KACP,MAAM,KAAK,UAAU,UAAU,KAAK,CAAC;IACtC;GACF;EACF;2CAyRM;;CAvRP,MAAM,SAAS,mBAAmB,4BAA4B,CAAC;AAE/D,QAAO,OACJ,YACC,IAAI,gBAAgB,EAClB,UAAU,OAAOC,YAAsD;AACrE,MAAI,WAAW,MACb,YAAW,SAAS,SAAS,MAAM,MAAM,IAAI;AAE/C,MAAI,UAAU,MACZ,YAAW,SAAS,QAAQ,MAAM,KAAK,IAAI;AAE7C,MAAI,QAAQ,MACV,YAAW,SAAS,MAAM,MAAM,GAAG,IAAI;AAEzC,MAAI,aAAa,MACf,YAAW,SAAS,IAAI,MAAM,QAAQ,IAAI;AAE5C,aAAW,QAAQ,OAAO;CAC3B,EACF,GACF,CACA,YAAY,IAAI,oBAAoB;AACxC;AA+DD,eAAe,YAAeC,MAIf;;;EACb,MAAM,+BAAiB,cAAc,KAAK,UAAU;EACpD,MAAM,MAAM,MAAM,UAAU,KAAK,CAAC,KAAK,SAAS,eAAe,OAAO,AAAC,EAAC;AAExE,MAAI,QAAQ,6BACV,QAAO,MAAM,KAAK,WAAW;AAE/B,SAAO;;;;;;AACR;;;;AAKD,SAAgB,kBACdC,MAC8C;CAC9C,MAAM,EAAE,cAAc,CAAC,MAAM,GAAG,GAAG;CAEnC,IAAIC,gBAAkC,CAAE;CAExC,MAAM,SAAS,KAAK;CAEpB,IAAIC,MAAmD;CAEvD,MAAM,eAAe,MACnB,IAAI,eAA8C;EAChD,MAAM,MAAM,YAAY;GACtB,MAAM,CAAC,KAAK,KAAK,GAAG,MAAM,QAAQ,IAAI,CAAC,KAAK,KAAK,EAAE,KAAK,MAAM,AAAC,EAAC;GAChE,MAAM,cAAe,MAAM,IAAI,KAAK,YAClC,KACA;AAGF,cAAW,QAAQ;IACjB,MAAM;IACN,aAAa;IACb,OAAO;GACR,EAAC;AAEF,eAAY,iBAAiB,iBAAiB,CAAC,SAAS;IACtD,MAAM,MAAM;IAEZ,MAAMC,UAA4B,KAAK,MAAM,IAAI,KAAK;AAEtD,oBAAgB;AAChB,eAAW,QAAQ;KACjB,MAAM;KACN;KACA;IACD,EAAC;GACH,EAAC;AAEF,eAAY,iBAAiB,wBAAwB,CAAC,SAAS;IAC7D,MAAM,MAAM;AAEZ,eAAW,QAAQ;KACjB,MAAM;KACN,OAAO,YAAY,KAAK,MAAM,IAAI,KAAK,CAAC;KACxC;IACD,EAAC;GACH,EAAC;AACF,eAAY,iBAAiB,YAAY,MAAM;AAC7C,eAAW,QAAQ;KACjB,MAAM;KACN;IACD,EAAC;GACH,EAAC;AACF,eAAY,iBAAiB,cAAc,MAAM;AAC/C,gBAAY,OAAO;AACnB,eAAW,OAAO;AAClB,UAAM;GACP,EAAC;AACF,eAAY,iBAAiB,SAAS,CAAC,UAAU;AAC/C,QAAI,YAAY,eAAe,YAAY,OACzC,YAAW,MAAM,MAAM;QAEvB,YAAW,QAAQ;KACjB,MAAM;KACN;KACA;IACD,EAAC;GAEL,EAAC;AACF,eAAY,iBAAiB,WAAW,CAAC,SAAS;IAChD,MAAM,MAAM;IAEZ,MAAM,QAAQ,YAAY,KAAK,MAAM,IAAI,KAAK,CAAC;IAE/C,MAAMC,MAAe,EACnB,MAAM,MACP;AACD,QAAI,IAAI,YACN,KAAI,KAAK,IAAI;AAEf,eAAW,QAAQ;KACjB,MAAM;KACN,MAAM;KACN;IACD,EAAC;GACH,EAAC;GAEF,MAAM,UAAU,MAAM;AACpB,QAAI;AACF,iBAAY,OAAO;AACnB,gBAAW,OAAO;IACnB,kBAAO,CAEP;GACF;AACD,OAAI,OAAO,QACT,UAAS;OAET,QAAO,iBAAiB,SAAS,QAAQ;EAE5C;EACD,SAAS;AACP,yCAAK,OAAO;EACb;CACF;CAEH,MAAM,oBAAoB,MAAM;EAC9B,IAAI,SAAS,cAAc;EAC3B,IAAI,SAAS,OAAO,WAAW;EAE/B,eAAe,UAAU;AACvB,SAAM,OAAO,QAAQ;AACrB,SAAM;EACP;AAED,SAAO,kBACL;GACE,OAAO;AACL,WAAO,OAAO,MAAM;GACrB;GACD,MAAM,WAAW;AACf,UAAM,SAAS;AAEf,aAAS,cAAc;AACvB,aAAS,OAAO,WAAW;GAC5B;EACF,GACD,QACD;CACF;AAED,QAAO,0DAAuB;;;GAC5B,MAAY,sBAAS,mBAAmB;AAExC,UAAO,MAAM;IACX,IAAI,UAAU,OAAO,MAAM;IAE3B,MAAM,YAAY,cAAc;AAChC,QAAI,UACF,WAAU,YAAY;KACpB;KACA;KACA,WAAW,YAAY;MACrB,MAAMC,MAA+B;OACnC,OAAO;QACL,MAAM;QACN,IAAI;QACJ,aAAa;OACd;OACD,MAAM;MACP;AAED,YAAM,OAAO,UAAU;AAEvB,aAAO;KACR;IACF,EAAC;IAGJ,MAAM,uDAAe;AAErB,QAAI,OAAO,KACT,QAAO,OAAO;AAEhB,UAAM,OAAO;GACd;;;;;;CACF,GAAC;AACH;AAED,MAAa,aAAa;CACxB,gBAAgB;CAChB,iBAAiB;CACjB,qBAAqB;CACrB,YAAY;AACb;;;;;;ACrbD,SAAS,qBAAqBC,KAAsC;AAClE,QAAO,wDAAuB;AAC5B,QAAM;CACP,GAAC;AACH;AAUD,MAAMC,2BAAiE;CACrE,UAAU,CAAC,MAAO;CAClB,OAAO,CAAC,KAAM;CACd,cAAc,CAAC,KAAM;AACtB;AACD,MAAMC,gDAGF;CAEF,UAAU,CAAC,MAAO;CAClB,OAAO,CAAC,OAAO,MAAO;CACtB,cAAc,CAAC,OAAO,MAAO;AAC9B;AAaD,SAAS,aAAkDC,UAUxD;;CACD,MAAM,EACJ,KACA,MACA,cACA,mBACA,SAAS,CAAE,GACX,SACD,GAAG;CAEJ,IAAI,SAAS,oBAAoB,kBAAkB,kBAAkB,GAAG;CAExE,MAAM,mBAAmB;CACzB,MAAM,OAAO,kBACT,CAAE,IACF,MAAM,QAAQ,kBAAkB,GAC9B,oBACA,CAAC,iBAAkB;CAEzB,MAAM,oFACJ,aAAe;EACb;EACA;EACA,mDAAO,KAAM,MAAM,IAAI,CAAC,SAAS,KAAK,KAAK;EAC3C;EACA;EACA;EACA,uFACE,KAAM,MAAM,KAAK,CAAC,SAAS;;kCAAK,6EAAW,KAAK;EAAI,EAAC,kFAAE,+EAAW,KAC/D,6EAAQ;CACd,EAAC,yDAAI,CAAE;AAEV,KAAI,KAAK,SACP;MAAI,KAAK,mBAAmB,QAC1B,MAAK,MAAM,CAAC,KAAK,MAAM,IAAI,KAAK,QAAQ,SAAS,CAC/C,SAAQ,OAAO,KAAK,MAAM;;;;;AAM5B,OAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,CACrD,KAAI,MAAM,QAAQ,MAAM,CACtB,MAAK,MAAM,KAAK,MACd,SAAQ,OAAO,KAAK,EAAE;kBAER,UAAU,SAC1B,SAAQ,IAAI,KAAK,MAAM;CAG5B;AAEH,KAAI,KAAK,OACP,UAAS,KAAK;AAGhB,QAAO,EACL,OACD;AACF;AAED,SAAS,kBACPC,OACAC,WAUA;CACA,MAAM,EAAE,QAAQ,KAAK,SAAS,GAAG,UAAU;CAC3C,MAAM,QAAQ,wBAAwB,MAAM;AAC5C,mDAAU;EACR;EACA,MAAM,UAAU;EAChB,OAAO,UAAU;EACjB,KAAK,UAAU;EACf,MAAM,UAAU;EAChB;CACD,EAAC;CACF,MAAM,oBAAoB,EACxB,OAAO,cAAc;EACnB,QAAQ,OAAO,KAAK;EACpB;EACA,MAAM,UAAU;EAChB,MAAM,UAAU;EAChB,OAAO,UAAU;EACjB,KAAK,UAAU;CAChB,EAAC,CACH;CACD,MAAM,kBAAkB,sBACtB,OAAO,KAAK,SACZ,kBACD;CACD,MAAM,OAAO,KAAK,UAAU,gBAAgB;AAC5C,QAAO;EACL;EACA;EACA;CACD;AACF;;;;;;AAOD,SAAS,aAAaC,GAAY;AAChC,MAAK,SAAS,EAAE,CACd,QAAO;AAGT,KAAI,gBAAgB,EAAE,CACpB,QAAO;AAGT,QACE,OAAO,OAAO,EAAE,CAAC,KAAK,UAAU,IAAI,OAAO,OAAO,EAAE,CAAC,KAAK,gBAAgB;AAE7E;AAID,eAAsB,gBACpBC,MACmB;;CACnB,MAAM,EAAE,QAAQ,KAAK,GAAG;CACxB,MAAM,UAAU,IAAI,QAAQ,CAAC,CAAC,QAAQ,aAAc,CAAC;CACrD,MAAM,SAAS,OAAO,KAAK;CAE3B,MAAM,MAAM,IAAI,IAAI,IAAI;AAExB,KAAI,IAAI,WAAW,OAEjB,QAAO,IAAI,SAAS,MAAM,EACxB,QAAQ,IACT;CAGH,MAAM,+CAAgB,KAAK,oGAAiB,KAAK,0EAAU,8CAAW;CACtE,MAAM,gDACH,KAAK,4FAAuB,UAAU,IAAI,WAAW;CAIxD,MAAMC,YAA0C,MAAM,IAAI,YAAY;AACpE,MAAI;AACF,UAAO,SAEL,MAAM,eAAe;IACnB;IACA,MAAM,mBAAmB,KAAK,KAAK;IACnC;IACA,cAAc,IAAI;IAClB,SAAS,KAAK,IAAI;IAClB;GACD,EAAC,AACH;EACF,SAAQ,OAAO;AACd,UAAO,CAAC,wBAAwB,MAAM,QAAY;EACnD;CACF,EAAC;CAOF,MAAMC,aAA6B,IAAI,MAAM;EAC3C,IAAIC;AACJ,SAAO;GACL,kBAAkB,MAAM;AACtB,SAAK,OACH;AAEF,WAAO,OAAO;GACf;GACD,OAAO,MAAM;IACX,MAAM,CAAC,KAAK,IAAI,GAAG;AACnB,QAAI,IACF,OAAM;AAER,WAAO;GACR;GACD,QAAQ,OAAO,SAAS;AACtB,QAAI,OACF,OAAM,IAAI,MACR;AAGJ,QAAI;KACF,MAAM,MAAM,MAAM,KAAK,cAAc,EACnC,KACD,EAAC;AACF,cAAS,SAAY,GAAI;IAC1B,SAAQ,OAAO;AACd,cAAS,CAAC,wBAAwB,MAAM,QAAY;IACrD;GACF;EACF;CACF,EAAC;CAEF,MAAM,eAAe,sBACjB,gDACA;;;;CAKJ,MAAM,eAAe,IAAI,QAAQ,IAAI,cAAc,KAAK;CAExD,MAAM,wDAAkB,OAAO,+DAAK,4EAAW;AAC/C,KAAI;EACF,MAAM,CAAC,WAAW,KAAK,GAAG;AAC1B,MAAI,UACF,OAAM;AAER,MAAI,KAAK,gBAAgB,cACvB,OAAM,IAAI,UAAU;GAClB,MAAM;GACN,UAAU;EACX;;AAGH,MAAI,iBAAiB,KAAK,YACxB,OAAM,IAAI,UAAU;GAClB,UAAU;GACV,MAAM;EACP;AAEH,QAAM,WAAW,OAAO,KAAK;EAM7B,MAAM,WAAW,KAAK,MAAM,IAAI,OAAO,SAA6B;GAClE,MAAM,OAAO,KAAK;AAClB,OAAI;AACF,QAAI,KAAK,MACP,OAAM,KAAK;AAGb,SAAK,KACH,OAAM,IAAI,UAAU;KAClB,MAAM;KACN,UAAU,8BAA8B,KAAK,KAAK;IACnD;AAGH,SAAK,aAAa,KAAK,KAAK,MAAM,SAAS,IAAI,OAAsB,CACnE,OAAM,IAAI,UAAU;KAClB,MAAM;KACN,UAAU,cAAc,IAAI,OAAO,cAAc,KAAK,KAAK,KAAK,sBAAsB,KAAK,KAAK;IACjG;AAGH,QAAI,KAAK,KAAK,SAAS,gBAErB;;SAAI,KAAK,YACP,OAAM,IAAI,UAAU;MAClB,MAAM;MACN,UAAU;KACX;IACF;IAEH,MAAMC,OAAgB,MAAM,KAAK;KAC/B,MAAM,KAAK;KACX,aAAa,KAAK;KAClB,KAAK,WAAW,OAAO;KACvB,MAAM,KAAK,KAAK;KAChB,QAAQ,KAAK,IAAI;IAClB,EAAC;AACF,WAAO,SAAY,EAAE,KAAM,CAAC;GAC7B,SAAQ,OAAO;;IACd,MAAM,QAAQ,wBAAwB,MAAM;IAC5C,MAAM,QAAQ,KAAK,QAAQ;AAE3B,0BAAK,iDAAL,yBAAe;KACb;KACA,MAAM,KAAK;KACX;KACA,KAAK,WAAW,kBAAkB;KAClC,mDAAM,KAAK,+EAAW,KAAK,6EAAQ;KACnC,KAAK,KAAK;IACX,EAAC;AAEF,WAAO,CAAC,aAAiB;GAC1B;EACF,EAAC;AAGF,OAAK,KAAK,aAAa;GACrB,MAAM,CAAC,KAAK,GAAG,KAAK;GACpB,MAAM,CAAC,OAAO,OAAO,GAAG,MAAM,SAAS;AAEvC,WAAQ,KAAK,MAAb;IACE,KAAK;IACL,KAAK;IACL,KAAK,SAAS;AAEZ,aAAQ,IAAI,gBAAgB,mBAAmB;AAE/C,SAAI,6DAAa,OAAQ,KAAK,CAC5B,OAAM,IAAI,UAAU;MAClB,MAAM;MACN,SACE;KACH;KAEH,MAAMC,MAAwD,QAC1D,EACE,OAAO,cAAc;MACnB;MACA,KAAK,WAAW,kBAAkB;MAClC;MACA,OAAO,KAAM,QAAQ;MACrB,MAAM,KAAM;MACZ,MAAM,KAAK;KACZ,EAAC,CACH,IACD,EAAE,QAAQ,EAAE,MAAM,OAAO,KAAM,EAAE;KAErC,MAAMC,iBAAe,aAAa;MAChC,KAAK,WAAW,kBAAkB;MAClC;MACA,cAAc,KAAK;MACnB,QAAQ,QAAQ,CAAC,KAAM,IAAG,CAAE;MAC5B;MACA,mBAAmB,CAAC,GAAI;KACzB,EAAC;AACF,YAAO,IAAI,SACT,KAAK,UAAU,sBAAsB,QAAQ,IAAI,CAAC,EAClD;MACE,QAAQA,eAAa;MACrB;KACD;IAEJ;IACD,KAAK,gBAAgB;KAGnB,MAAMC,WAAmC,IAAI,MAAM;AACjD,UAAI,MACF,QAAO,qBAAqB,MAAM;AAEpC,WAAK,gBACH,QAAO,qBACL,IAAI,UAAU;OACZ,MAAM;OACN,SAAS;MACV,GACF;AAGH,WAAK,aAAa,OAAO,KAAK,KAAK,gBAAgB,OAAO,KAAK,CAC7D,QAAO,qBACL,IAAI,UAAU;OACZ,UAAU,eACR,KAAM,KACP;OACD,MAAM;MACP,GACF;MAEH,MAAM,iBAAiB,aAAa,OAAO,KAAK,GAC5C,0BAA0B,OAAO,MAAM,KAAK,IAAI,OAAO,GACvD,OAAO;AACX,aAAO;KACR,EAAC;KAEF,MAAM,SAAS,0FACV,OAAO;MACV,MAAM;MACN,WAAW,CAAC,MAAM,OAAO,YAAY,OAAO,UAAU,EAAE;MACxD,YAAY,WAAW;;OACrB,MAAMC,UAAQ,wBAAwB,UAAU,MAAM;OACtD,MAAM,oDAAQ,KAAM,QAAQ;OAC5B,MAAM,mDAAO,KAAM;OACnB,MAAM,yFAAO,KAAM,+EAAW,KAAK,+EAAQ;AAE3C,8BAAK,kDAAL,0BAAe;QACb;QACA;QACA;QACA,KAAK,WAAW,kBAAkB;QAClC,KAAK,KAAK;QACV;OACD,EAAC;OAEF,MAAM,QAAQ,cAAc;QAC1B;QACA,KAAK,WAAW,kBAAkB;QAClC;QACA;QACA;QACA;OACD,EAAC;AAEF,cAAO;MACR;QACD;AACF,UAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,QAAQ,WAAW,CACnD,SAAQ,IAAI,KAAK,MAAM;KAGzB,MAAMF,iBAAe,aAAa;MAChC,KAAK,WAAW,kBAAkB;MAClC;MACA,cAAc,KAAK;MACnB,QAAQ,CAAE;MACV;MACA,mBAAmB;KACpB,EAAC;AAEF,YAAO,IAAI,SAAS,QAAQ;MAC1B;MACA,QAAQA,eAAa;KACtB;IACF;GACF;EACF;AAGD,MAAI,KAAK,WAAW,qBAAqB;AAEvC,WAAQ,IAAI,gBAAgB,mBAAmB;AAC/C,WAAQ,IAAI,qBAAqB,UAAU;GAC3C,MAAMA,iBAAe,aAAa;IAChC,KAAK,WAAW,kBAAkB;IAClC;IACA,cAAc,KAAK;IACnB,QAAQ,CAAE;IACV;IACA,mBAAmB;GACpB,EAAC;GACF,MAAM,SAAS,4FACV,OAAO;IAcV,UAAU;IACV,MAAM,SAAS,IAAI,OAAO,QAAQ;KAChC,MAAM,CAAC,OAAO,OAAO,GAAG,MAAM;KAE9B,MAAM,OAAO,KAAK,MAAM;AAExB,SAAI,OAAO;;AACT,aAAO,EACL,OAAO,cAAc;OACnB;OACA,KAAK,WAAW,kBAAkB;OAClC;OACA,OAAO,KAAM,QAAQ;OACrB,MAAM,KAAM;OACZ,4CAAM,KAAM,mEAAW,KAAK,2EAAQ;MACrC,EAAC,CACH;KACF;;;;;KAMD,MAAM,WAAW,aAAa,OAAO,KAAK,GACtC,0BAA0B,OAAO,MAAM,KAAK,IAAI,OAAO,GACvD,QAAQ,QAAQ,OAAO,KAAK;AAChC,YAAO,EACL,QAAQ,QAAQ,QAAQ,EACtB,MAAM,SACP,EAAC,CACH;IACF,EAAC;IACF,WAAW,OAAO,YAAY,OAAO;IACrC,SAAS,CAAC,UAAU;;AAClB,4BAAK,kDAAL,0BAAe;MACb,OAAO,wBAAwB,MAAM;MACrC;MACA;MACA,KAAK,WAAW,kBAAkB;MAClC,KAAK,KAAK;MACV,gEAAM,KAAM,uDAAQ;KACrB,EAAC;IACH;IAED,YAAY,WAAW;;KACrB,MAAM,mDAAO,KAAM,MAAM,UAAU,KAAK;KAExC,MAAM,QAAQ,wBAAwB,UAAU,MAAM;KACtD,MAAM,oDAAQ,KAAM,QAAQ;KAC5B,MAAM,mDAAO,KAAM;KACnB,MAAM,yFAAO,KAAM,+EAAW,KAAK,+EAAQ;KAI3C,MAAM,QAAQ,cAAc;MAC1B;MACA,KAAK,WAAW,kBAAkB;MAClC;MACA;MACA;MACA;KACD,EAAC;AAEF,YAAO;IACR;MACD;AAEF,UAAO,IAAI,SAAS,QAAQ;IAC1B;IACA,QAAQA,eAAa;GACtB;EACF;;;;;;;AASD,UAAQ,IAAI,gBAAgB,mBAAmB;EAC/C,MAAMG,UAAuB,CAAC,MAAM,QAAQ,IAAI,SAAS,EAAE,IACzD,CAAC,QAAmB;GAClB,MAAM,CAAC,OAAO,OAAO,GAAG;AACxB,OAAI,MACF,QAAO;AAGT,OAAI,aAAa,OAAO,KAAK,CAC3B,QAAO,CACL,IAAI,UAAU;IACZ,MAAM;IACN,SACE;GACH,UAEF;AAEH,UAAO;EACR,EACF;EACD,MAAM,sBAAsB,QAAQ,IAClC,CACE,CAAC,OAAO,OAAO,EACf,UACqD;GACrD,MAAM,OAAO,KAAK,MAAM;AACxB,OAAI,OAAO;;AACT,WAAO,EACL,OAAO,cAAc;KACnB;KACA,KAAK,WAAW,kBAAkB;KAClC;KACA,OAAO,KAAK,QAAQ;KACpB,MAAM,KAAK;KACX,oDAAM,KAAK,+EAAW,KAAK,+EAAQ;IACpC,EAAC,CACH;GACF;AACD,UAAO,EACL,QAAQ,EAAE,MAAM,OAAO,KAAM,EAC9B;EACF,EACF;EAED,MAAM,SAAS,QACZ,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CACvB,OAAO,QAAQ;EAElB,MAAM,eAAe,aAAa;GAChC,KAAK,WAAW,kBAAkB;GAClC;GACA,cAAc,KAAK;GACnB,mBAAmB;GACnB;GACA;EACD,EAAC;AAEF,SAAO,IAAI,SACT,KAAK,UAAU,sBAAsB,QAAQ,oBAAoB,CAAC,EAClE;GACE,QAAQ,aAAa;GACrB;EACD;CAEJ,SAAQ,OAAO;;EACd,MAAM,CAAC,YAAY,KAAK,GAAG;EAC3B,MAAM,MAAM,WAAW,kBAAkB;EAQzC,MAAM,EAAE,OAAO,mBAAmB,MAAM,GAAG,kBAAkB,OAAO;GAClE;GACA,KAAK,WAAW,kBAAkB;GAClC,iEAAM,KAAM,yDAAQ;EACrB,EAAC;EAEF,MAAM,eAAe,aAAa;GAChC;GACA;GACA,cAAc,KAAK;GACnB;GACA,QAAQ,CAAC,KAAM;GACf;EACD,EAAC;AAEF,SAAO,IAAI,SAAS,MAAM;GACxB,QAAQ,aAAa;GACrB;EACD;CACF;AACF"}