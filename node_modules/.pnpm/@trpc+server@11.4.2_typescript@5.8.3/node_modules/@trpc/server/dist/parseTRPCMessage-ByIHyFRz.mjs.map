{"version": 3, "file": "parseTRPCMessage-ByIHyFRz.mjs", "names": ["obj: unknown", "transformer: CombinedDataTransformer"], "sources": ["../src/unstable-core-do-not-import/procedure.ts", "../src/unstable-core-do-not-import/rpc/parseTRPCMessage.ts"], "sourcesContent": ["import type { TRPCError } from './error/TRPCError';\nimport type { Parser } from './parser';\nimport type { ProcedureCallOptions } from './procedureBuilder';\n\nexport const procedureTypes = ['query', 'mutation', 'subscription'] as const;\n/**\n * @public\n */\nexport type ProcedureType = (typeof procedureTypes)[number];\n\ninterface BuiltProcedureDef {\n  meta: unknown;\n  input: unknown;\n  output: unknown;\n}\n\n/**\n *\n * @internal\n */\nexport interface Procedure<\n  TType extends ProcedureType,\n  TDef extends BuiltProcedureDef,\n> {\n  _def: {\n    /**\n     * These are just types, they can't be used at runtime\n     * @internal\n     */\n    $types: {\n      input: TDef['input'];\n      output: TDef['output'];\n    };\n    procedure: true;\n    type: TType;\n    /**\n     * @internal\n     * Meta is not inferrable on individual procedures, only on the router\n     */\n    meta: unknown;\n    experimental_caller: boolean;\n    /**\n     * The input parsers for the procedure\n     */\n    inputs: Parser[];\n  };\n  meta: TDef['meta'];\n  /**\n   * @internal\n   */\n  (opts: ProcedureCallOptions<unknown>): Promise<TDef['output']>;\n}\n\nexport interface QueryProcedure<TDef extends BuiltProcedureDef>\n  extends Procedure<'query', TDef> {}\n\nexport interface MutationProcedure<TDef extends BuiltProcedureDef>\n  extends Procedure<'mutation', TDef> {}\n\nexport interface SubscriptionProcedure<TDef extends BuiltProcedureDef>\n  extends Procedure<'subscription', TDef> {}\n\n/**\n * @deprecated\n */\nexport interface LegacyObservableSubscriptionProcedure<\n  TDef extends BuiltProcedureDef,\n> extends SubscriptionProcedure<TDef> {\n  _observable: true;\n}\n\nexport type AnyQueryProcedure = QueryProcedure<any>;\nexport type AnyMutationProcedure = MutationProcedure<any>;\nexport type AnySubscriptionProcedure =\n  | SubscriptionProcedure<any>\n  | LegacyObservableSubscriptionProcedure<any>;\n\nexport type AnyProcedure =\n  | AnyQueryProcedure\n  | AnyMutationProcedure\n  | AnySubscriptionProcedure;\n\nexport type inferProcedureInput<TProcedure extends AnyProcedure> =\n  undefined extends inferProcedureParams<TProcedure>['$types']['input']\n    ? void | inferProcedureParams<TProcedure>['$types']['input']\n    : inferProcedureParams<TProcedure>['$types']['input'];\n\nexport type inferProcedureParams<TProcedure> = TProcedure extends AnyProcedure\n  ? TProcedure['_def']\n  : never;\nexport type inferProcedureOutput<TProcedure> =\n  inferProcedureParams<TProcedure>['$types']['output'];\n\n/**\n * @internal\n */\nexport interface ErrorHandlerOptions<TContext> {\n  error: TRPCError;\n  type: ProcedureType | 'unknown';\n  path: string | undefined;\n  input: unknown;\n  ctx: TContext | undefined;\n}\n", "import { procedureTypes, type ProcedureType } from '../procedure';\nimport type { CombinedDataTransformer } from '../transformer';\nimport { isObject } from '../utils';\nimport type { TRPCClientOutgoingMessage } from './envelopes';\n\n/* istanbul ignore next -- @preserve */\nfunction assertIsObject(obj: unknown): asserts obj is Record<string, unknown> {\n  if (!isObject(obj)) {\n    throw new Error('Not an object');\n  }\n}\n\n/* istanbul ignore next -- @preserve */\nfunction assertIsProcedureType(obj: unknown): asserts obj is ProcedureType {\n  if (!procedureTypes.includes(obj as any)) {\n    throw new Error('Invalid procedure type');\n  }\n}\n\n/* istanbul ignore next -- @preserve */\nfunction assertIsRequestId(\n  obj: unknown,\n): asserts obj is number | string | null {\n  if (\n    obj !== null &&\n    typeof obj === 'number' &&\n    isNaN(obj) &&\n    typeof obj !== 'string'\n  ) {\n    throw new Error('Invalid request id');\n  }\n}\n\n/* istanbul ignore next -- @preserve */\nfunction assertIsString(obj: unknown): asserts obj is string {\n  if (typeof obj !== 'string') {\n    throw new Error('Invalid string');\n  }\n}\n\n/* istanbul ignore next -- @preserve */\nfunction assertIsJSONRPC2OrUndefined(\n  obj: unknown,\n): asserts obj is '2.0' | undefined {\n  if (typeof obj !== 'undefined' && obj !== '2.0') {\n    throw new Error('Must be JSONRPC 2.0');\n  }\n}\n\n/** @public */\nexport function parseTRPCMessage(\n  obj: unknown,\n  transformer: CombinedDataTransformer,\n): TRPCClientOutgoingMessage {\n  assertIsObject(obj);\n\n  const { id, jsonrpc, method, params } = obj;\n  assertIsRequestId(id);\n  assertIsJSONRPC2OrUndefined(jsonrpc);\n\n  if (method === 'subscription.stop') {\n    return {\n      id,\n      jsonrpc,\n      method,\n    };\n  }\n  assertIsProcedureType(method);\n  assertIsObject(params);\n  const { input: rawInput, path, lastEventId } = params;\n\n  assertIsString(path);\n  if (lastEventId !== undefined) {\n    assertIsString(lastEventId);\n  }\n\n  const input = transformer.input.deserialize(rawInput);\n\n  return {\n    id,\n    jsonrpc,\n    method,\n    params: {\n      input,\n      path,\n      lastEventId,\n    },\n  };\n}\n"], "mappings": ";;;AAIA,MAAa,iBAAiB;CAAC;CAAS;CAAY;AAAe;;;;;ACEnE,SAAS,eAAeA,KAAsD;AAC5E,MAAK,SAAS,IAAI,CAChB,OAAM,IAAI,MAAM;AAEnB;;AAGD,SAAS,sBAAsBA,KAA4C;AACzE,MAAK,eAAe,SAAS,IAAW,CACtC,OAAM,IAAI,MAAM;AAEnB;;AAGD,SAAS,kBACPA,KACuC;AACvC,KACE,QAAQ,eACD,QAAQ,YACf,MAAM,IAAI,WACH,QAAQ,SAEf,OAAM,IAAI,MAAM;AAEnB;;AAGD,SAAS,eAAeA,KAAqC;AAC3D,YAAW,QAAQ,SACjB,OAAM,IAAI,MAAM;AAEnB;;AAGD,SAAS,4BACPA,KACkC;AAClC,YAAW,QAAQ,eAAe,QAAQ,MACxC,OAAM,IAAI,MAAM;AAEnB;;AAGD,SAAgB,iBACdA,KACAC,aAC2B;AAC3B,gBAAe,IAAI;CAEnB,MAAM,EAAE,IAAI,SAAS,QAAQ,QAAQ,GAAG;AACxC,mBAAkB,GAAG;AACrB,6BAA4B,QAAQ;AAEpC,KAAI,WAAW,oBACb,QAAO;EACL;EACA;EACA;CACD;AAEH,uBAAsB,OAAO;AAC7B,gBAAe,OAAO;CACtB,MAAM,EAAE,OAAO,UAAU,MAAM,aAAa,GAAG;AAE/C,gBAAe,KAAK;AACpB,KAAI,uBACF,gBAAe,YAAY;CAG7B,MAAM,QAAQ,YAAY,MAAM,YAAY,SAAS;AAErD,QAAO;EACL;EACA;EACA;EACA,QAAQ;GACN;GACA;GACA;EACD;CACF;AACF"}