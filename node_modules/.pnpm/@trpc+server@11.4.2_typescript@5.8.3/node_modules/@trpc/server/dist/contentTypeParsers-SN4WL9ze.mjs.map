{"version": 3, "file": "contentTypeParsers-SN4WL9ze.mjs", "names": ["octetInputParser: UtilityParser<OctetInput, ReadableStream>"], "sources": ["../src/unstable-core-do-not-import/http/contentTypeParsers.ts"], "sourcesContent": ["import type { ParserZodEsque } from '../parser';\n\ntype UtilityParser<TInput, TOutput> = ParserZodEsque<TInput, TOutput> & {\n  parse: (input: unknown) => TOutput;\n};\n\n// Should be the same possible types as packages/client/src/links/internals/contentTypes.ts isOctetType\n\n/**\n * File is only available from Node19+ but it always extends Blob so we can use that as a type until we eventually drop Node18\n */\ninterface FileLike extends Blob {\n  readonly name: string;\n}\n\ntype OctetInput = Blob | Uint8Array | FileLike;\nexport const octetInputParser: UtilityParser<OctetInput, ReadableStream> = {\n  _input: null as any as OctetInput,\n  _output: null as any as ReadableStream,\n  parse(input) {\n    if (input instanceof ReadableStream) {\n      return input;\n    }\n\n    throw new Error(\n      `Parsed input was expected to be a ReadableStream but was: ${typeof input}`,\n    );\n  },\n};\n"], "mappings": ";AAgBA,MAAaA,mBAA8D;CACzE,QAAQ;CACR,SAAS;CACT,MAAM,OAAO;AACX,MAAI,iBAAiB,eACnB,QAAO;AAGT,QAAM,IAAI,OACP,mEAAmE,MAAM;CAE7E;AACF"}