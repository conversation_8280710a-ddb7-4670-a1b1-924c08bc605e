{"version": 3, "file": "initTRPC-IT_6ZYJd.mjs", "names": ["middlewares: AnyMiddlewareFunction[]", "fn: MiddlewareFunction<\n      TContext,\n      TMeta,\n      object,\n      $ContextOverrides,\n      TInputOut\n    >", "parse: ParseFn<TInput>", "inputMiddleware: AnyMiddlewareFunction", "parsedInput: ReturnType<typeof parse>", "parse: ParseFn<TOutput>", "outputMiddleware: AnyMiddlewareFunction", "issues: ReadonlyArray<StandardSchemaV1.Issue>", "procedureParser: <PERSON><PERSON><PERSON>", "_objectWithoutProperties", "def1: AnyProcedureBuilderDef", "def2: Partial<AnyProcedureBuilderDef>", "initDef: Partial<AnyProcedureBuilderDef>", "_def: AnyProcedureBuilderDef", "builder: AnyProcedureBuilder", "output: <PERSON><PERSON><PERSON>", "builder", "resolver: ProcedureResolver<any, any, any, any, any, any>", "_defIn: AnyProcedureBuilderDef & { type: ProcedureType }", "resolver: AnyResolver", "_def: AnyProcedure['_def']", "index: number", "opts: ProcedureCallOptions<any>", "_nextOpts?: any", "opts: ProcedureCallOptions<unknown>", "isServerDefault: boolean", "opts?: ValidateShape<TOptions, RuntimeConfigOptions<TContext, TMeta>>", "config: RootConfig<$Root>", "isServer: boolean"], "sources": ["../src/unstable-core-do-not-import/middleware.ts", "../src/vendor/standard-schema-v1/error.ts", "../src/unstable-core-do-not-import/parser.ts", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectWithoutPropertiesLoose.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectWithoutProperties.js", "../src/unstable-core-do-not-import/procedureBuilder.ts", "../src/unstable-core-do-not-import/rootConfig.ts", "../src/unstable-core-do-not-import/initTRPC.ts"], "sourcesContent": ["import { TRPCError } from './error/TRPCError';\nimport type { ParseFn } from './parser';\nimport type { ProcedureType } from './procedure';\nimport type { GetRawInputFn, Overwrite, Simplify } from './types';\nimport { isObject } from './utils';\n\n/** @internal */\nexport const middlewareMarker = 'middlewareMarker' as 'middlewareMarker' & {\n  __brand: 'middlewareMarker';\n};\ntype MiddlewareMarker = typeof middlewareMarker;\n\ninterface MiddlewareResultBase {\n  /**\n   * All middlewares should pass through their `next()`'s output.\n   * Requiring this marker makes sure that can't be forgotten at compile-time.\n   */\n  readonly marker: MiddlewareMarker;\n}\n\ninterface MiddlewareOKResult<_TContextOverride> extends MiddlewareResultBase {\n  ok: true;\n  data: unknown;\n  // this could be extended with `input`/`rawInput` later\n}\n\ninterface MiddlewareErrorResult<_TContextOverride>\n  extends MiddlewareResultBase {\n  ok: false;\n  error: TRPCError;\n}\n\n/**\n * @internal\n */\nexport type MiddlewareResult<_TContextOverride> =\n  | MiddlewareErrorResult<_TContextOverride>\n  | MiddlewareOKResult<_TContextOverride>;\n\n/**\n * @internal\n */\nexport interface MiddlewareBuilder<\n  TContext,\n  TMeta,\n  TContextOverrides,\n  TInputOut,\n> {\n  /**\n   * Create a new builder based on the current middleware builder\n   */\n  unstable_pipe<$ContextOverridesOut>(\n    fn:\n      | MiddlewareFunction<\n          TContext,\n          TMeta,\n          TContextOverrides,\n          $ContextOverridesOut,\n          TInputOut\n        >\n      | MiddlewareBuilder<\n          Overwrite<TContext, TContextOverrides>,\n          TMeta,\n          $ContextOverridesOut,\n          TInputOut\n        >,\n  ): MiddlewareBuilder<\n    TContext,\n    TMeta,\n    Overwrite<TContextOverrides, $ContextOverridesOut>,\n    TInputOut\n  >;\n\n  /**\n   * List of middlewares within this middleware builder\n   */\n  _middlewares: MiddlewareFunction<\n    TContext,\n    TMeta,\n    TContextOverrides,\n    object,\n    TInputOut\n  >[];\n}\n\n/**\n * @internal\n */\nexport type MiddlewareFunction<\n  TContext,\n  TMeta,\n  TContextOverridesIn,\n  $ContextOverridesOut,\n  TInputOut,\n> = {\n  (opts: {\n    ctx: Simplify<Overwrite<TContext, TContextOverridesIn>>;\n    type: ProcedureType;\n    path: string;\n    input: TInputOut;\n    getRawInput: GetRawInputFn;\n    meta: TMeta | undefined;\n    signal: AbortSignal | undefined;\n    next: {\n      (): Promise<MiddlewareResult<TContextOverridesIn>>;\n      <$ContextOverride>(opts: {\n        ctx?: $ContextOverride;\n        input?: unknown;\n      }): Promise<MiddlewareResult<$ContextOverride>>;\n      (opts: {\n        getRawInput: GetRawInputFn;\n      }): Promise<MiddlewareResult<TContextOverridesIn>>;\n    };\n  }): Promise<MiddlewareResult<$ContextOverridesOut>>;\n  _type?: string | undefined;\n};\n\nexport type AnyMiddlewareFunction = MiddlewareFunction<any, any, any, any, any>;\nexport type AnyMiddlewareBuilder = MiddlewareBuilder<any, any, any, any>;\n/**\n * @internal\n */\nexport function createMiddlewareFactory<\n  TContext,\n  TMeta,\n  TInputOut = unknown,\n>() {\n  function createMiddlewareInner(\n    middlewares: AnyMiddlewareFunction[],\n  ): AnyMiddlewareBuilder {\n    return {\n      _middlewares: middlewares,\n      unstable_pipe(middlewareBuilderOrFn) {\n        const pipedMiddleware =\n          '_middlewares' in middlewareBuilderOrFn\n            ? middlewareBuilderOrFn._middlewares\n            : [middlewareBuilderOrFn];\n\n        return createMiddlewareInner([...middlewares, ...pipedMiddleware]);\n      },\n    };\n  }\n\n  function createMiddleware<$ContextOverrides>(\n    fn: MiddlewareFunction<\n      TContext,\n      TMeta,\n      object,\n      $ContextOverrides,\n      TInputOut\n    >,\n  ): MiddlewareBuilder<TContext, TMeta, $ContextOverrides, TInputOut> {\n    return createMiddlewareInner([fn]);\n  }\n\n  return createMiddleware;\n}\n\n/**\n * Create a standalone middleware\n * @see https://trpc.io/docs/v11/server/middlewares#experimental-standalone-middlewares\n * @deprecated use `.concat()` instead\n */\nexport const experimental_standaloneMiddleware = <\n  TCtx extends {\n    ctx?: object;\n    meta?: object;\n    input?: unknown;\n  },\n>() => ({\n  create: createMiddlewareFactory<\n    TCtx extends { ctx: infer T extends object } ? T : any,\n    TCtx extends { meta: infer T extends object } ? T : object,\n    TCtx extends { input: infer T } ? T : unknown\n  >(),\n});\n\n/**\n * @internal\n * Please note, `trpc-openapi` uses this function.\n */\nexport function createInputMiddleware<TInput>(parse: ParseFn<TInput>) {\n  const inputMiddleware: AnyMiddlewareFunction =\n    async function inputValidatorMiddleware(opts) {\n      let parsedInput: ReturnType<typeof parse>;\n\n      const rawInput = await opts.getRawInput();\n      try {\n        parsedInput = await parse(rawInput);\n      } catch (cause) {\n        throw new TRPCError({\n          code: 'BAD_REQUEST',\n          cause,\n        });\n      }\n\n      // Multiple input parsers\n      const combinedInput =\n        isObject(opts.input) && isObject(parsedInput)\n          ? {\n              ...opts.input,\n              ...parsedInput,\n            }\n          : parsedInput;\n\n      return opts.next({ input: combinedInput });\n    };\n  inputMiddleware._type = 'input';\n  return inputMiddleware;\n}\n\n/**\n * @internal\n */\nexport function createOutputMiddleware<TOutput>(parse: ParseFn<TOutput>) {\n  const outputMiddleware: AnyMiddlewareFunction =\n    async function outputValidatorMiddleware({ next }) {\n      const result = await next();\n      if (!result.ok) {\n        // pass through failures without validating\n        return result;\n      }\n      try {\n        const data = await parse(result.data);\n        return {\n          ...result,\n          data,\n        };\n      } catch (cause) {\n        throw new TRPCError({\n          message: 'Output validation failed',\n          code: 'INTERNAL_SERVER_ERROR',\n          cause,\n        });\n      }\n    };\n  outputMiddleware._type = 'output';\n  return outputMiddleware;\n}\n", "import type { StandardSchemaV1 } from \"./spec\";\n\n/** A schema error with useful information. */\n\nexport class StandardSchemaV1Error extends Error {\n  /** The schema issues. */\n  public readonly issues: ReadonlyArray<StandardSchemaV1.Issue>;\n\n  /**\n   * Creates a schema error with useful information.\n   *\n   * @param issues The schema issues.\n   */\n  constructor(issues: ReadonlyArray<StandardSchemaV1.Issue>) {\n    super(issues[0]?.message);\n    this.name = 'SchemaError';\n    this.issues = issues;\n  }\n}\n", "import { StandardSchemaV1Error } from '../vendor/standard-schema-v1/error';\nimport { type StandardSchemaV1 } from '../vendor/standard-schema-v1/spec';\n\n// zod / typeschema\nexport type ParserZodEsque<TInput, TParsedInput> = {\n  _input: TInput;\n  _output: TParsedInput;\n};\n\nexport type ParserValibotEsque<TInput, TParsedInput> = {\n  schema: {\n    _types?: {\n      input: TInput;\n      output: TParsedInput;\n    };\n  };\n};\n\nexport type ParserArkTypeEsque<TInput, TParsedInput> = {\n  inferIn: TInput;\n  infer: TParsedInput;\n};\n\nexport type ParserStandardSchemaEsque<TInput, TParsedInput> = StandardSchemaV1<\n  TInput,\n  TParsedInput\n>;\n\nexport type ParserMyZodEsque<TInput> = {\n  parse: (input: any) => TInput;\n};\n\nexport type ParserSuperstructEsque<TInput> = {\n  create: (input: unknown) => TInput;\n};\n\nexport type ParserCustomValidatorEsque<TInput> = (\n  input: unknown,\n) => Promise<TInput> | TInput;\n\nexport type ParserYupEsque<TInput> = {\n  validateSync: (input: unknown) => TInput;\n};\n\nexport type ParserScaleEsque<TInput> = {\n  assert(value: unknown): asserts value is TInput;\n};\n\nexport type ParserWithoutInput<TInput> =\n  | ParserCustomValidatorEsque<TInput>\n  | ParserMyZodEsque<TInput>\n  | ParserScaleEsque<TInput>\n  | ParserSuperstructEsque<TInput>\n  | ParserYupEsque<TInput>;\n\nexport type ParserWithInputOutput<TInput, TParsedInput> =\n  | ParserZodEsque<TInput, TParsedInput>\n  | ParserValibotEsque<TInput, TParsedInput>\n  | ParserArkTypeEsque<TInput, TParsedInput>\n  | ParserStandardSchemaEsque<TInput, TParsedInput>;\n\nexport type Parser = ParserWithInputOutput<any, any> | ParserWithoutInput<any>;\n\nexport type inferParser<TParser extends Parser> =\n  TParser extends ParserWithInputOutput<infer $TIn, infer $TOut>\n    ? {\n        in: $TIn;\n        out: $TOut;\n      }\n    : TParser extends ParserWithoutInput<infer $InOut>\n      ? {\n          in: $InOut;\n          out: $InOut;\n        }\n      : never;\n\nexport type ParseFn<TType> = (value: unknown) => Promise<TType> | TType;\n\nexport function getParseFn<TType>(procedureParser: Parser): ParseFn<TType> {\n  const parser = procedureParser as any;\n  const isStandardSchema = '~standard' in parser;\n\n  if (typeof parser === 'function' && typeof parser.assert === 'function') {\n    // ParserArkTypeEsque - arktype schemas shouldn't be called as a function because they return a union type instead of throwing\n    return parser.assert.bind(parser);\n  }\n\n  if (typeof parser === 'function' && !isStandardSchema) {\n    // ParserValibotEsque (>= v0.31.0)\n    // ParserCustomValidatorEsque - note the check for standard-schema conformance - some libraries like `effect` use function schemas which are *not* a \"parse\" function.\n    return parser;\n  }\n\n  if (typeof parser.parseAsync === 'function') {\n    // ParserZodEsque\n    return parser.parseAsync.bind(parser);\n  }\n\n  if (typeof parser.parse === 'function') {\n    // ParserZodEsque\n    // ParserValibotEsque (< v0.13.0)\n    return parser.parse.bind(parser);\n  }\n\n  if (typeof parser.validateSync === 'function') {\n    // ParserYupEsque\n    return parser.validateSync.bind(parser);\n  }\n\n  if (typeof parser.create === 'function') {\n    // ParserSuperstructEsque\n    return parser.create.bind(parser);\n  }\n\n  if (typeof parser.assert === 'function') {\n    // ParserScaleEsque\n    return (value) => {\n      parser.assert(value);\n      return value as TType;\n    };\n  }\n\n  if (isStandardSchema) {\n    // StandardSchemaEsque\n    return async (value) => {\n      const result = await parser['~standard'].validate(value);\n      if (result.issues) {\n        throw new StandardSchemaV1Error(result.issues);\n      }\n      return result.value;\n    };\n  }\n\n  throw new Error('Could not find a validator fn');\n}\n", "function _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (e.includes(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nmodule.exports = _objectWithoutPropertiesLoose, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var objectWithoutPropertiesLoose = require(\"./objectWithoutPropertiesLoose.js\");\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var s = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < s.length; r++) o = s[r], t.includes(o) || {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nmodule.exports = _objectWithoutProperties, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import type { inferObservableValue, Observable } from '../observable';\nimport { getTRPCErrorFromUnknown, TRPCError } from './error/TRPCError';\nimport type {\n  AnyMiddlewareFunction,\n  MiddlewareBuilder,\n  MiddlewareFunction,\n  MiddlewareResult,\n} from './middleware';\nimport {\n  createInputMiddleware,\n  createOutputMiddleware,\n  middlewareMarker,\n} from './middleware';\nimport type { inferParser, Parser } from './parser';\nimport { getParseFn } from './parser';\nimport type {\n  AnyMutationProcedure,\n  AnyProcedure,\n  AnyQueryProcedure,\n  LegacyObservableSubscriptionProcedure,\n  MutationProcedure,\n  ProcedureType,\n  QueryProcedure,\n  SubscriptionProcedure,\n} from './procedure';\nimport type { inferTrackedOutput } from './stream/tracked';\nimport type {\n  GetRawInputFn,\n  MaybePromise,\n  Overwrite,\n  Simplify,\n  TypeError,\n} from './types';\nimport type { UnsetMarker } from './utils';\nimport { mergeWithoutOverrides } from './utils';\n\ntype IntersectIfDefined<TType, TWith> = TType extends UnsetMarker\n  ? TWith\n  : TWith extends UnsetMarker\n    ? TType\n    : Simplify<TType & TWith>;\n\ntype DefaultValue<TValue, TFallback> = TValue extends UnsetMarker\n  ? TFallback\n  : TValue;\n\ntype inferAsyncIterable<TOutput> =\n  TOutput extends AsyncIterable<infer $Yield, infer $Return, infer $Next>\n    ? {\n        yield: $Yield;\n        return: $Return;\n        next: $Next;\n      }\n    : never;\ntype inferSubscriptionOutput<TOutput> =\n  TOutput extends AsyncIterable<any>\n    ? AsyncIterable<\n        inferTrackedOutput<inferAsyncIterable<TOutput>['yield']>,\n        inferAsyncIterable<TOutput>['return'],\n        inferAsyncIterable<TOutput>['next']\n      >\n    : TypeError<'Subscription output could not be inferred'>;\n\nexport type CallerOverride<TContext> = (opts: {\n  args: unknown[];\n  invoke: (opts: ProcedureCallOptions<TContext>) => Promise<unknown>;\n  _def: AnyProcedure['_def'];\n}) => Promise<unknown>;\ntype ProcedureBuilderDef<TMeta> = {\n  procedure: true;\n  inputs: Parser[];\n  output?: Parser;\n  meta?: TMeta;\n  resolver?: ProcedureBuilderResolver;\n  middlewares: AnyMiddlewareFunction[];\n  /**\n   * @deprecated use `type` instead\n   */\n  mutation?: boolean;\n  /**\n   * @deprecated use `type` instead\n   */\n  query?: boolean;\n  /**\n   * @deprecated use `type` instead\n   */\n  subscription?: boolean;\n  type?: ProcedureType;\n  caller?: CallerOverride<unknown>;\n};\n\ntype AnyProcedureBuilderDef = ProcedureBuilderDef<any>;\n\n/**\n * Procedure resolver options (what the `.query()`, `.mutation()`, and `.subscription()` functions receive)\n * @internal\n */\nexport interface ProcedureResolverOptions<\n  TContext,\n  _TMeta,\n  TContextOverridesIn,\n  TInputOut,\n> {\n  ctx: Simplify<Overwrite<TContext, TContextOverridesIn>>;\n  input: TInputOut extends UnsetMarker ? undefined : TInputOut;\n  /**\n   * The AbortSignal of the request\n   */\n  signal: AbortSignal | undefined;\n}\n\n/**\n * A procedure resolver\n */\ntype ProcedureResolver<\n  TContext,\n  TMeta,\n  TContextOverrides,\n  TInputOut,\n  TOutputParserIn,\n  $Output,\n> = (\n  opts: ProcedureResolverOptions<TContext, TMeta, TContextOverrides, TInputOut>,\n) => MaybePromise<\n  // If an output parser is defined, we need to return what the parser expects, otherwise we return the inferred type\n  DefaultValue<TOutputParserIn, $Output>\n>;\n\ntype AnyResolver = ProcedureResolver<any, any, any, any, any, any>;\nexport type AnyProcedureBuilder = ProcedureBuilder<\n  any,\n  any,\n  any,\n  any,\n  any,\n  any,\n  any,\n  any\n>;\n\n/**\n * Infer the context type from a procedure builder\n * Useful to create common helper functions for different procedures\n */\nexport type inferProcedureBuilderResolverOptions<\n  TProcedureBuilder extends AnyProcedureBuilder,\n> =\n  TProcedureBuilder extends ProcedureBuilder<\n    infer TContext,\n    infer TMeta,\n    infer TContextOverrides,\n    infer _TInputIn,\n    infer TInputOut,\n    infer _TOutputIn,\n    infer _TOutputOut,\n    infer _TCaller\n  >\n    ? ProcedureResolverOptions<\n        TContext,\n        TMeta,\n        TContextOverrides,\n        TInputOut extends UnsetMarker\n          ? // if input is not set, we don't want to infer it as `undefined` since a procedure further down the chain might have set an input\n            unknown\n          : TInputOut extends object\n            ? Simplify<\n                TInputOut & {\n                  /**\n                   * Extra input params might have been added by a `.input()` further down the chain\n                   */\n                  [keyAddedByInputCallFurtherDown: string]: unknown;\n                }\n              >\n            : TInputOut\n      >\n    : never;\n\nexport interface ProcedureBuilder<\n  TContext,\n  TMeta,\n  TContextOverrides,\n  TInputIn,\n  TInputOut,\n  TOutputIn,\n  TOutputOut,\n  TCaller extends boolean,\n> {\n  /**\n   * Add an input parser to the procedure.\n   * @see https://trpc.io/docs/v11/server/validators\n   */\n  input<$Parser extends Parser>(\n    schema: TInputOut extends UnsetMarker\n      ? $Parser\n      : inferParser<$Parser>['out'] extends Record<string, unknown> | undefined\n        ? TInputOut extends Record<string, unknown> | undefined\n          ? undefined extends inferParser<$Parser>['out'] // if current is optional the previous must be too\n            ? undefined extends TInputOut\n              ? $Parser\n              : TypeError<'Cannot chain an optional parser to a required parser'>\n            : $Parser\n          : TypeError<'All input parsers did not resolve to an object'>\n        : TypeError<'All input parsers did not resolve to an object'>,\n  ): ProcedureBuilder<\n    TContext,\n    TMeta,\n    TContextOverrides,\n    IntersectIfDefined<TInputIn, inferParser<$Parser>['in']>,\n    IntersectIfDefined<TInputOut, inferParser<$Parser>['out']>,\n    TOutputIn,\n    TOutputOut,\n    TCaller\n  >;\n  /**\n   * Add an output parser to the procedure.\n   * @see https://trpc.io/docs/v11/server/validators\n   */\n  output<$Parser extends Parser>(\n    schema: $Parser,\n  ): ProcedureBuilder<\n    TContext,\n    TMeta,\n    TContextOverrides,\n    TInputIn,\n    TInputOut,\n    IntersectIfDefined<TOutputIn, inferParser<$Parser>['in']>,\n    IntersectIfDefined<TOutputOut, inferParser<$Parser>['out']>,\n    TCaller\n  >;\n  /**\n   * Add a meta data to the procedure.\n   * @see https://trpc.io/docs/v11/server/metadata\n   */\n  meta(\n    meta: TMeta,\n  ): ProcedureBuilder<\n    TContext,\n    TMeta,\n    TContextOverrides,\n    TInputIn,\n    TInputOut,\n    TOutputIn,\n    TOutputOut,\n    TCaller\n  >;\n  /**\n   * Add a middleware to the procedure.\n   * @see https://trpc.io/docs/v11/server/middlewares\n   */\n  use<$ContextOverridesOut>(\n    fn:\n      | MiddlewareBuilder<\n          Overwrite<TContext, TContextOverrides>,\n          TMeta,\n          $ContextOverridesOut,\n          TInputOut\n        >\n      | MiddlewareFunction<\n          TContext,\n          TMeta,\n          TContextOverrides,\n          $ContextOverridesOut,\n          TInputOut\n        >,\n  ): ProcedureBuilder<\n    TContext,\n    TMeta,\n    Overwrite<TContextOverrides, $ContextOverridesOut>,\n    TInputIn,\n    TInputOut,\n    TOutputIn,\n    TOutputOut,\n    TCaller\n  >;\n\n  /**\n   * @deprecated use {@link concat} instead\n   */\n  unstable_concat<\n    $Context,\n    $Meta,\n    $ContextOverrides,\n    $InputIn,\n    $InputOut,\n    $OutputIn,\n    $OutputOut,\n  >(\n    builder: Overwrite<TContext, TContextOverrides> extends $Context\n      ? TMeta extends $Meta\n        ? ProcedureBuilder<\n            $Context,\n            $Meta,\n            $ContextOverrides,\n            $InputIn,\n            $InputOut,\n            $OutputIn,\n            $OutputOut,\n            TCaller\n          >\n        : TypeError<'Meta mismatch'>\n      : TypeError<'Context mismatch'>,\n  ): ProcedureBuilder<\n    TContext,\n    TMeta,\n    Overwrite<TContextOverrides, $ContextOverrides>,\n    IntersectIfDefined<TInputIn, $InputIn>,\n    IntersectIfDefined<TInputOut, $InputOut>,\n    IntersectIfDefined<TOutputIn, $OutputIn>,\n    IntersectIfDefined<TOutputOut, $OutputOut>,\n    TCaller\n  >;\n\n  /**\n   * Combine two procedure builders\n   */\n  concat<\n    $Context,\n    $Meta,\n    $ContextOverrides,\n    $InputIn,\n    $InputOut,\n    $OutputIn,\n    $OutputOut,\n  >(\n    builder: Overwrite<TContext, TContextOverrides> extends $Context\n      ? TMeta extends $Meta\n        ? ProcedureBuilder<\n            $Context,\n            $Meta,\n            $ContextOverrides,\n            $InputIn,\n            $InputOut,\n            $OutputIn,\n            $OutputOut,\n            TCaller\n          >\n        : TypeError<'Meta mismatch'>\n      : TypeError<'Context mismatch'>,\n  ): ProcedureBuilder<\n    TContext,\n    TMeta,\n    Overwrite<TContextOverrides, $ContextOverrides>,\n    IntersectIfDefined<TInputIn, $InputIn>,\n    IntersectIfDefined<TInputOut, $InputOut>,\n    IntersectIfDefined<TOutputIn, $OutputIn>,\n    IntersectIfDefined<TOutputOut, $OutputOut>,\n    TCaller\n  >;\n  /**\n   * Query procedure\n   * @see https://trpc.io/docs/v11/concepts#vocabulary\n   */\n  query<$Output>(\n    resolver: ProcedureResolver<\n      TContext,\n      TMeta,\n      TContextOverrides,\n      TInputOut,\n      TOutputIn,\n      $Output\n    >,\n  ): TCaller extends true\n    ? (\n        input: DefaultValue<TInputIn, void>,\n      ) => Promise<DefaultValue<TOutputOut, $Output>>\n    : QueryProcedure<{\n        input: DefaultValue<TInputIn, void>;\n        output: DefaultValue<TOutputOut, $Output>;\n        meta: TMeta;\n      }>;\n\n  /**\n   * Mutation procedure\n   * @see https://trpc.io/docs/v11/concepts#vocabulary\n   */\n  mutation<$Output>(\n    resolver: ProcedureResolver<\n      TContext,\n      TMeta,\n      TContextOverrides,\n      TInputOut,\n      TOutputIn,\n      $Output\n    >,\n  ): TCaller extends true\n    ? (\n        input: DefaultValue<TInputIn, void>,\n      ) => Promise<DefaultValue<TOutputOut, $Output>>\n    : MutationProcedure<{\n        input: DefaultValue<TInputIn, void>;\n        output: DefaultValue<TOutputOut, $Output>;\n        meta: TMeta;\n      }>;\n\n  /**\n   * Subscription procedure\n   * @see https://trpc.io/docs/v11/server/subscriptions\n   */\n  subscription<$Output extends AsyncIterable<any, void, any>>(\n    resolver: ProcedureResolver<\n      TContext,\n      TMeta,\n      TContextOverrides,\n      TInputOut,\n      TOutputIn,\n      $Output\n    >,\n  ): TCaller extends true\n    ? TypeError<'Not implemented'>\n    : SubscriptionProcedure<{\n        input: DefaultValue<TInputIn, void>;\n        output: inferSubscriptionOutput<DefaultValue<TOutputOut, $Output>>;\n        meta: TMeta;\n      }>;\n  /**\n   * @deprecated Using subscriptions with an observable is deprecated. Use an async generator instead.\n   * This feature will be removed in v12 of tRPC.\n   * @see https://trpc.io/docs/v11/server/subscriptions\n   */\n  subscription<$Output extends Observable<any, any>>(\n    resolver: ProcedureResolver<\n      TContext,\n      TMeta,\n      TContextOverrides,\n      TInputOut,\n      TOutputIn,\n      $Output\n    >,\n  ): TCaller extends true\n    ? TypeError<'Not implemented'>\n    : LegacyObservableSubscriptionProcedure<{\n        input: DefaultValue<TInputIn, void>;\n        output: inferObservableValue<DefaultValue<TOutputOut, $Output>>;\n        meta: TMeta;\n      }>;\n  /**\n   * Overrides the way a procedure is invoked\n   * Do not use this unless you know what you're doing - this is an experimental API\n   */\n  experimental_caller(\n    caller: CallerOverride<TContext>,\n  ): ProcedureBuilder<\n    TContext,\n    TMeta,\n    TContextOverrides,\n    TInputIn,\n    TInputOut,\n    TOutputIn,\n    TOutputOut,\n    true\n  >;\n  /**\n   * @internal\n   */\n  _def: ProcedureBuilderDef<TMeta>;\n}\n\ntype ProcedureBuilderResolver = (\n  opts: ProcedureResolverOptions<any, any, any, any>,\n) => Promise<unknown>;\n\nfunction createNewBuilder(\n  def1: AnyProcedureBuilderDef,\n  def2: Partial<AnyProcedureBuilderDef>,\n): AnyProcedureBuilder {\n  const { middlewares = [], inputs, meta, ...rest } = def2;\n\n  // TODO: maybe have a fn here to warn about calls\n  return createBuilder({\n    ...mergeWithoutOverrides(def1, rest),\n    inputs: [...def1.inputs, ...(inputs ?? [])],\n    middlewares: [...def1.middlewares, ...middlewares],\n    meta: def1.meta && meta ? { ...def1.meta, ...meta } : (meta ?? def1.meta),\n  });\n}\n\nexport function createBuilder<TContext, TMeta>(\n  initDef: Partial<AnyProcedureBuilderDef> = {},\n): ProcedureBuilder<\n  TContext,\n  TMeta,\n  object,\n  UnsetMarker,\n  UnsetMarker,\n  UnsetMarker,\n  UnsetMarker,\n  false\n> {\n  const _def: AnyProcedureBuilderDef = {\n    procedure: true,\n    inputs: [],\n    middlewares: [],\n    ...initDef,\n  };\n\n  const builder: AnyProcedureBuilder = {\n    _def,\n    input(input) {\n      const parser = getParseFn(input as Parser);\n      return createNewBuilder(_def, {\n        inputs: [input as Parser],\n        middlewares: [createInputMiddleware(parser)],\n      });\n    },\n    output(output: Parser) {\n      const parser = getParseFn(output);\n      return createNewBuilder(_def, {\n        output,\n        middlewares: [createOutputMiddleware(parser)],\n      });\n    },\n    meta(meta) {\n      return createNewBuilder(_def, {\n        meta,\n      });\n    },\n    use(middlewareBuilderOrFn) {\n      // Distinguish between a middleware builder and a middleware function\n      const middlewares =\n        '_middlewares' in middlewareBuilderOrFn\n          ? middlewareBuilderOrFn._middlewares\n          : [middlewareBuilderOrFn];\n\n      return createNewBuilder(_def, {\n        middlewares: middlewares,\n      });\n    },\n    unstable_concat(builder) {\n      return createNewBuilder(_def, (builder as AnyProcedureBuilder)._def);\n    },\n    concat(builder) {\n      return createNewBuilder(_def, (builder as AnyProcedureBuilder)._def);\n    },\n    query(resolver) {\n      return createResolver(\n        { ..._def, type: 'query' },\n        resolver,\n      ) as AnyQueryProcedure;\n    },\n    mutation(resolver) {\n      return createResolver(\n        { ..._def, type: 'mutation' },\n        resolver,\n      ) as AnyMutationProcedure;\n    },\n    subscription(resolver: ProcedureResolver<any, any, any, any, any, any>) {\n      return createResolver({ ..._def, type: 'subscription' }, resolver) as any;\n    },\n    experimental_caller(caller) {\n      return createNewBuilder(_def, {\n        caller,\n      }) as any;\n    },\n  };\n\n  return builder;\n}\n\nfunction createResolver(\n  _defIn: AnyProcedureBuilderDef & { type: ProcedureType },\n  resolver: AnyResolver,\n) {\n  const finalBuilder = createNewBuilder(_defIn, {\n    resolver,\n    middlewares: [\n      async function resolveMiddleware(opts) {\n        const data = await resolver(opts);\n        return {\n          marker: middlewareMarker,\n          ok: true,\n          data,\n          ctx: opts.ctx,\n        } as const;\n      },\n    ],\n  });\n  const _def: AnyProcedure['_def'] = {\n    ...finalBuilder._def,\n    type: _defIn.type,\n    experimental_caller: Boolean(finalBuilder._def.caller),\n    meta: finalBuilder._def.meta,\n    $types: null as any,\n  };\n\n  const invoke = createProcedureCaller(finalBuilder._def);\n  const callerOverride = finalBuilder._def.caller;\n  if (!callerOverride) {\n    return invoke;\n  }\n  const callerWrapper = async (...args: unknown[]) => {\n    return await callerOverride({\n      args,\n      invoke,\n      _def: _def,\n    });\n  };\n\n  callerWrapper._def = _def;\n\n  return callerWrapper;\n}\n\n/**\n * @internal\n */\nexport interface ProcedureCallOptions<TContext> {\n  ctx: TContext;\n  getRawInput: GetRawInputFn;\n  input?: unknown;\n  path: string;\n  type: ProcedureType;\n  signal: AbortSignal | undefined;\n}\n\nconst codeblock = `\nThis is a client-only function.\nIf you want to call this function on the server, see https://trpc.io/docs/v11/server/server-side-calls\n`.trim();\n\n// run the middlewares recursively with the resolver as the last one\nasync function callRecursive(\n  index: number,\n  _def: AnyProcedureBuilderDef,\n  opts: ProcedureCallOptions<any>,\n): Promise<MiddlewareResult<any>> {\n  try {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const middleware = _def.middlewares[index]!;\n    const result = await middleware({\n      ...opts,\n      meta: _def.meta,\n      input: opts.input,\n      next(_nextOpts?: any) {\n        const nextOpts = _nextOpts as\n          | {\n              ctx?: Record<string, unknown>;\n              input?: unknown;\n              getRawInput?: GetRawInputFn;\n            }\n          | undefined;\n\n        return callRecursive(index + 1, _def, {\n          ...opts,\n          ctx: nextOpts?.ctx ? { ...opts.ctx, ...nextOpts.ctx } : opts.ctx,\n          input: nextOpts && 'input' in nextOpts ? nextOpts.input : opts.input,\n          getRawInput: nextOpts?.getRawInput ?? opts.getRawInput,\n        });\n      },\n    });\n\n    return result;\n  } catch (cause) {\n    return {\n      ok: false,\n      error: getTRPCErrorFromUnknown(cause),\n      marker: middlewareMarker,\n    };\n  }\n}\n\nfunction createProcedureCaller(_def: AnyProcedureBuilderDef): AnyProcedure {\n  async function procedure(opts: ProcedureCallOptions<unknown>) {\n    // is direct server-side call\n    if (!opts || !('getRawInput' in opts)) {\n      throw new Error(codeblock);\n    }\n\n    // there's always at least one \"next\" since we wrap this.resolver in a middleware\n    const result = await callRecursive(0, _def, opts);\n\n    if (!result) {\n      throw new TRPCError({\n        code: 'INTERNAL_SERVER_ERROR',\n        message:\n          'No result from middlewares - did you forget to `return next()`?',\n      });\n    }\n    if (!result.ok) {\n      // re-throw original error\n      throw result.error;\n    }\n    return result.data;\n  }\n\n  procedure._def = _def;\n  procedure.procedure = true;\n  procedure.meta = _def.meta;\n\n  // FIXME typecast shouldn't be needed - fixittt\n  return procedure as unknown as AnyProcedure;\n}\n", "import type { CombinedDataTransformer } from '../unstable-core-do-not-import';\nimport type { DefaultErrorShape, ErrorFormatter } from './error/formatter';\nimport type { JSONLProducerOptions } from './stream/jsonl';\nimport type { SSEStreamProducerOptions } from './stream/sse';\n\n/**\n * The initial generics that are used in the init function\n * @internal\n */\nexport interface RootTypes {\n  ctx: object;\n  meta: object;\n  errorShape: DefaultErrorShape;\n  transformer: boolean;\n}\n\n/**\n * The default check to see if we're in a server\n */\nexport const isServerDefault: boolean =\n  typeof window === 'undefined' ||\n  'Deno' in window ||\n  // eslint-disable-next-line @typescript-eslint/dot-notation\n  globalThis.process?.env?.['NODE_ENV'] === 'test' ||\n  !!globalThis.process?.env?.['JEST_WORKER_ID'] ||\n  !!globalThis.process?.env?.['VITEST_WORKER_ID'];\n\n/**\n * The tRPC root config\n * @internal\n */\nexport interface RootConfig<TTypes extends RootTypes> {\n  /**\n   * The types that are used in the config\n   * @internal\n   */\n  $types: TTypes;\n  /**\n   * Use a data transformer\n   * @see https://trpc.io/docs/v11/data-transformers\n   */\n  transformer: CombinedDataTransformer;\n  /**\n   * Use custom error formatting\n   * @see https://trpc.io/docs/v11/error-formatting\n   */\n  errorFormatter: ErrorFormatter<TTypes['ctx'], TTypes['errorShape']>;\n  /**\n   * Allow `@trpc/server` to run in non-server environments\n   * @warning **Use with caution**, this should likely mainly be used within testing.\n   * @default false\n   */\n  allowOutsideOfServer: boolean;\n  /**\n   * Is this a server environment?\n   * @warning **Use with caution**, this should likely mainly be used within testing.\n   * @default typeof window === 'undefined' || 'Deno' in window || process.env.NODE_ENV === 'test'\n   */\n  isServer: boolean;\n  /**\n   * Is this development?\n   * Will be used to decide if the API should return stack traces\n   * @default process.env.NODE_ENV !== 'production'\n   */\n  isDev: boolean;\n\n  defaultMeta?: TTypes['meta'] extends object ? TTypes['meta'] : never;\n\n  /**\n   * Options for server-sent events (SSE) subscriptions\n   * @see https://trpc.io/docs/client/links/httpSubscriptionLink\n   */\n  sse?: {\n    /**\n     * Enable server-sent events (SSE) subscriptions\n     * @default true\n     */\n    enabled?: boolean;\n  } & Pick<\n    SSEStreamProducerOptions,\n    'ping' | 'emitAndEndImmediately' | 'maxDurationMs' | 'client'\n  >;\n\n  /**\n   * Options for batch stream\n   * @see https://trpc.io/docs/client/links/httpBatchStreamLink\n   */\n  jsonl?: Pick<JSONLProducerOptions, 'pingMs'>;\n  experimental?: {};\n}\n\n/**\n * @internal\n */\nexport type CreateRootTypes<TGenerics extends RootTypes> = TGenerics;\n\nexport type AnyRootTypes = CreateRootTypes<{\n  ctx: any;\n  meta: any;\n  errorShape: any;\n  transformer: any;\n}>;\n\ntype PartialIf<TCondition extends boolean, TType> = TCondition extends true\n  ? Partial<TType>\n  : TType;\n\n/**\n * Adds a `createContext` option with a given callback function\n * If context is the default value, then the `createContext` option is optional\n */\nexport type CreateContextCallback<\n  TContext,\n  TFunction extends (...args: any[]) => any,\n> = PartialIf<\n  object extends TContext ? true : false,\n  {\n    /**\n     * @see https://trpc.io/docs/v11/context\n     **/\n    createContext: TFunction;\n  }\n>;\n", "import {\n  defaultFormatter,\n  type DefaultErrorShape,\n  type ErrorFormatter,\n} from './error/formatter';\nimport type { MiddlewareBuilder, MiddlewareFunction } from './middleware';\nimport { createMiddlewareFactory } from './middleware';\nimport type { ProcedureBuilder } from './procedureBuilder';\nimport { createBuilder } from './procedureBuilder';\nimport type { AnyRootTypes, CreateRootTypes } from './rootConfig';\nimport { isServerDefault, type RootConfig } from './rootConfig';\nimport type {\n  AnyRouter,\n  MergeRouters,\n  RouterBuilder,\n  RouterCallerFactory,\n} from './router';\nimport {\n  createCallerFactory,\n  createRouterFactory,\n  mergeRouters,\n} from './router';\nimport type { DataTransformerOptions } from './transformer';\nimport { defaultTransformer, getDataTransformer } from './transformer';\nimport type { Unwrap, ValidateShape } from './types';\nimport type { UnsetMarker } from './utils';\n\ntype inferErrorFormatterShape<TType> =\n  TType extends ErrorFormatter<any, infer TShape> ? TShape : DefaultErrorShape;\n/** @internal */\nexport interface RuntimeConfigOptions<\n  TContext extends object,\n  TMeta extends object,\n> extends Partial<\n    Omit<\n      RootConfig<{\n        ctx: TContext;\n        meta: TMeta;\n        errorShape: any;\n        transformer: any;\n      }>,\n      '$types' | 'transformer'\n    >\n  > {\n  /**\n   * Use a data transformer\n   * @see https://trpc.io/docs/v11/data-transformers\n   */\n  transformer?: DataTransformerOptions;\n}\n\ntype ContextCallback = (...args: any[]) => object | Promise<object>;\n\nexport interface TRPCRootObject<\n  TContext extends object,\n  TMeta extends object,\n  TOptions extends RuntimeConfigOptions<TContext, TMeta>,\n  $Root extends AnyRootTypes = {\n    ctx: TContext;\n    meta: TMeta;\n    errorShape: undefined extends TOptions['errorFormatter']\n      ? DefaultErrorShape\n      : inferErrorFormatterShape<TOptions['errorFormatter']>;\n    transformer: undefined extends TOptions['transformer'] ? false : true;\n  },\n> {\n  /**\n   * Your router config\n   * @internal\n   */\n  _config: RootConfig<$Root>;\n\n  /**\n   * Builder object for creating procedures\n   * @see https://trpc.io/docs/v11/server/procedures\n   */\n  procedure: ProcedureBuilder<\n    TContext,\n    TMeta,\n    object,\n    UnsetMarker,\n    UnsetMarker,\n    UnsetMarker,\n    UnsetMarker,\n    false\n  >;\n\n  /**\n   * Create reusable middlewares\n   * @see https://trpc.io/docs/v11/server/middlewares\n   */\n  middleware: <$ContextOverrides>(\n    fn: MiddlewareFunction<TContext, TMeta, object, $ContextOverrides, unknown>,\n  ) => MiddlewareBuilder<TContext, TMeta, $ContextOverrides, unknown>;\n\n  /**\n   * Create a router\n   * @see https://trpc.io/docs/v11/server/routers\n   */\n  router: RouterBuilder<$Root>;\n\n  /**\n   * Merge Routers\n   * @see https://trpc.io/docs/v11/server/merging-routers\n   */\n  mergeRouters: <TRouters extends AnyRouter[]>(\n    ...routerList: [...TRouters]\n  ) => MergeRouters<TRouters>;\n\n  /**\n   * Create a server-side caller for a router\n   * @see https://trpc.io/docs/v11/server/server-side-calls\n   */\n  createCallerFactory: RouterCallerFactory<$Root>;\n}\n\nclass TRPCBuilder<TContext extends object, TMeta extends object> {\n  /**\n   * Add a context shape as a generic to the root object\n   * @see https://trpc.io/docs/v11/server/context\n   */\n  context<TNewContext extends object | ContextCallback>() {\n    return new TRPCBuilder<\n      TNewContext extends ContextCallback ? Unwrap<TNewContext> : TNewContext,\n      TMeta\n    >();\n  }\n\n  /**\n   * Add a meta shape as a generic to the root object\n   * @see https://trpc.io/docs/v11/quickstart\n   */\n  meta<TNewMeta extends object>() {\n    return new TRPCBuilder<TContext, TNewMeta>();\n  }\n\n  /**\n   * Create the root object\n   * @see https://trpc.io/docs/v11/server/routers#initialize-trpc\n   */\n  create<TOptions extends RuntimeConfigOptions<TContext, TMeta>>(\n    opts?: ValidateShape<TOptions, RuntimeConfigOptions<TContext, TMeta>>,\n  ): TRPCRootObject<TContext, TMeta, TOptions> {\n    type $Root = CreateRootTypes<{\n      ctx: TContext;\n      meta: TMeta;\n      errorShape: undefined extends TOptions['errorFormatter']\n        ? DefaultErrorShape\n        : inferErrorFormatterShape<TOptions['errorFormatter']>;\n      transformer: undefined extends TOptions['transformer'] ? false : true;\n    }>;\n\n    const config: RootConfig<$Root> = {\n      ...opts,\n      transformer: getDataTransformer(opts?.transformer ?? defaultTransformer),\n      isDev:\n        opts?.isDev ??\n        // eslint-disable-next-line @typescript-eslint/dot-notation\n        globalThis.process?.env['NODE_ENV'] !== 'production',\n      allowOutsideOfServer: opts?.allowOutsideOfServer ?? false,\n      errorFormatter: opts?.errorFormatter ?? defaultFormatter,\n      isServer: opts?.isServer ?? isServerDefault,\n      /**\n       * These are just types, they can't be used at runtime\n       * @internal\n       */\n      $types: null as any,\n    };\n\n    {\n      // Server check\n      const isServer: boolean = opts?.isServer ?? isServerDefault;\n\n      if (!isServer && opts?.allowOutsideOfServer !== true) {\n        throw new Error(\n          `You're trying to use @trpc/server in a non-server environment. This is not supported by default.`,\n        );\n      }\n    }\n    return {\n      /**\n       * Your router config\n       * @internal\n       */\n      _config: config,\n      /**\n       * Builder object for creating procedures\n       * @see https://trpc.io/docs/v11/server/procedures\n       */\n      procedure: createBuilder<$Root['ctx'], $Root['meta']>({\n        meta: opts?.defaultMeta,\n      }),\n      /**\n       * Create reusable middlewares\n       * @see https://trpc.io/docs/v11/server/middlewares\n       */\n      middleware: createMiddlewareFactory<$Root['ctx'], $Root['meta']>(),\n      /**\n       * Create a router\n       * @see https://trpc.io/docs/v11/server/routers\n       */\n      router: createRouterFactory<$Root>(config),\n      /**\n       * Merge Routers\n       * @see https://trpc.io/docs/v11/server/merging-routers\n       */\n      mergeRouters,\n      /**\n       * Create a server-side caller for a router\n       * @see https://trpc.io/docs/v11/server/server-side-calls\n       */\n      createCallerFactory: createCallerFactory<$Root>(),\n    };\n  }\n}\n\n/**\n * Builder to initialize the tRPC root object - use this exactly once per backend\n * @see https://trpc.io/docs/v11/quickstart\n */\nexport const initTRPC = new TRPCBuilder();\nexport type { TRPCBuilder };\n"], "x_google_ignoreList": [3, 4], "mappings": ";;;;;;;AAOA,MAAa,mBAAmB;;;;AAmHhC,SAAgB,0BAIZ;CACF,SAAS,sBACPA,aACsB;AACtB,SAAO;GACL,cAAc;GACd,cAAc,uBAAuB;IACnC,MAAM,kBACJ,kBAAkB,wBACd,sBAAsB,eACtB,CAAC,qBAAsB;AAE7B,WAAO,sBAAsB,CAAC,GAAG,aAAa,GAAG,eAAgB,EAAC;GACnE;EACF;CACF;CAED,SAAS,iBACPC,IAOkE;AAClE,SAAO,sBAAsB,CAAC,EAAG,EAAC;CACnC;AAED,QAAO;AACR;;;;;;AAOD,MAAa,oCAAoC,OAMzC,EACN,QAAQ,yBAIL,CACJ;;;;;AAMD,SAAgB,sBAA8BC,OAAwB;CACpE,MAAMC,kBACJ,eAAe,yBAAyB,MAAM;EAC5C,IAAIC;EAEJ,MAAM,WAAW,MAAM,KAAK,aAAa;AACzC,MAAI;AACF,iBAAc,MAAM,MAAM,SAAS;EACpC,SAAQ,OAAO;AACd,SAAM,IAAI,UAAU;IAClB,MAAM;IACN;GACD;EACF;EAGD,MAAM,gBACJ,SAAS,KAAK,MAAM,IAAI,SAAS,YAAY,+EAEpC,KAAK,QACL,eAEL;AAEN,SAAO,KAAK,KAAK,EAAE,OAAO,cAAe,EAAC;CAC3C;AACH,iBAAgB,QAAQ;AACxB,QAAO;AACR;;;;AAKD,SAAgB,uBAAgCC,OAAyB;CACvE,MAAMC,mBACJ,eAAe,0BAA0B,EAAE,MAAM,EAAE;EACjD,MAAM,SAAS,MAAM,MAAM;AAC3B,OAAK,OAAO,GAEV,QAAO;AAET,MAAI;GACF,MAAM,OAAO,MAAM,MAAM,OAAO,KAAK;AACrC,sFACK,eACH;EAEH,SAAQ,OAAO;AACd,SAAM,IAAI,UAAU;IAClB,SAAS;IACT,MAAM;IACN;GACD;EACF;CACF;AACH,kBAAiB,QAAQ;AACzB,QAAO;AACR;;;;;;AC1OD,IAAa,wBAAb,cAA2C,MAAM;;;;;;CAS/C,YAAYC,QAA+C;;AACzD,oBAAM,OAAO,wDAAI,QAAQ;qCAK3B,MAbgB;AASd,OAAK,OAAO;AACZ,OAAK,SAAS;CACf;AACF;;;;AC4DD,SAAgB,WAAkBC,iBAAyC;CACzE,MAAM,SAAS;CACf,MAAM,mBAAmB,eAAe;AAExC,YAAW,WAAW,qBAAqB,OAAO,WAAW,WAE3D,QAAO,OAAO,OAAO,KAAK,OAAO;AAGnC,YAAW,WAAW,eAAe,iBAGnC,QAAO;AAGT,YAAW,OAAO,eAAe,WAE/B,QAAO,OAAO,WAAW,KAAK,OAAO;AAGvC,YAAW,OAAO,UAAU,WAG1B,QAAO,OAAO,MAAM,KAAK,OAAO;AAGlC,YAAW,OAAO,iBAAiB,WAEjC,QAAO,OAAO,aAAa,KAAK,OAAO;AAGzC,YAAW,OAAO,WAAW,WAE3B,QAAO,OAAO,OAAO,KAAK,OAAO;AAGnC,YAAW,OAAO,WAAW,WAE3B,QAAO,CAAC,UAAU;AAChB,SAAO,OAAO,MAAM;AACpB,SAAO;CACR;AAGH,KAAI,iBAEF,QAAO,OAAO,UAAU;EACtB,MAAM,SAAS,MAAM,OAAO,aAAa,SAAS,MAAM;AACxD,MAAI,OAAO,OACT,OAAM,IAAI,sBAAsB,OAAO;AAEzC,SAAO,OAAO;CACf;AAGH,OAAM,IAAI,MAAM;AACjB;;;;;CCtID,SAAS,8BAA8B,GAAG,GAAG;AAC3C,MAAI,QAAQ,EAAG,QAAO,CAAE;EACxB,IAAI,IAAI,CAAE;AACV,OAAK,IAAI,KAAK,EAAG,KAAI,CAAE,EAAC,eAAe,KAAK,GAAG,EAAE,EAAE;AACjD,OAAI,EAAE,SAAS,EAAE,CAAE;AACnB,KAAE,KAAK,EAAE;EACV;AACD,SAAO;CACR;AACD,QAAO,UAAU,+BAA+B,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CCTrH,IAAI;CACJ,SAASC,2BAAyB,GAAG,GAAG;AACtC,MAAI,QAAQ,EAAG,QAAO,CAAE;EACxB,IAAI,GACF,GACA,IAAI,6BAA6B,GAAG,EAAE;AACxC,MAAI,OAAO,uBAAuB;GAChC,IAAI,IAAI,OAAO,sBAAsB,EAAE;AACvC,QAAK,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAK,KAAI,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAE,EAAC,qBAAqB,KAAK,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE;EAC3G;AACD,SAAO;CACR;AACD,QAAO,UAAUA,4BAA0B,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;;;CCqctG;CAAkB;CAAQ;;AAJpC,SAAS,iBACPC,MACAC,MACqB;CACrB,MAAM,EAAE,cAAc,CAAE,GAAE,QAAQ,MAAe,SAAN,mDAAS;AAGpD,QAAO,0FACF,sBAAsB,MAAM,KAAK;EACpC,QAAQ,CAAC,GAAG,KAAK,QAAQ,GAAI,gDAAU,CAAE,CAAE;EAC3C,aAAa,CAAC,GAAG,KAAK,aAAa,GAAG,WAAY;EAClD,MAAM,KAAK,QAAQ,mFAAY,KAAK,OAAS,QAAU,0CAAQ,KAAK;IACpE;AACH;AAED,SAAgB,cACdC,UAA2C,CAAE,GAU7C;CACA,MAAMC;EACJ,WAAW;EACX,QAAQ,CAAE;EACV,aAAa,CAAE;IACZ;CAGL,MAAMC,UAA+B;EACnC;EACA,MAAM,OAAO;GACX,MAAM,SAAS,WAAW,MAAgB;AAC1C,UAAO,iBAAiB,MAAM;IAC5B,QAAQ,CAAC,KAAgB;IACzB,aAAa,CAAC,sBAAsB,OAAO,AAAC;GAC7C,EAAC;EACH;EACD,OAAOC,QAAgB;GACrB,MAAM,SAAS,WAAW,OAAO;AACjC,UAAO,iBAAiB,MAAM;IAC5B;IACA,aAAa,CAAC,uBAAuB,OAAO,AAAC;GAC9C,EAAC;EACH;EACD,KAAK,MAAM;AACT,UAAO,iBAAiB,MAAM,EAC5B,KACD,EAAC;EACH;EACD,IAAI,uBAAuB;GAEzB,MAAM,cACJ,kBAAkB,wBACd,sBAAsB,eACtB,CAAC,qBAAsB;AAE7B,UAAO,iBAAiB,MAAM,EACf,YACd,EAAC;EACH;EACD,gBAAgBC,WAAS;AACvB,UAAO,iBAAiB,MAAOA,UAAgC,KAAK;EACrE;EACD,OAAOA,WAAS;AACd,UAAO,iBAAiB,MAAOA,UAAgC,KAAK;EACrE;EACD,MAAM,UAAU;AACd,UAAO,2FACA,aAAM,MAAM,YACjB,SACD;EACF;EACD,SAAS,UAAU;AACjB,UAAO,2FACA,aAAM,MAAM,eACjB,SACD;EACF;EACD,aAAaC,UAA2D;AACtE,UAAO,2FAAoB,aAAM,MAAM,mBAAkB,SAAS;EACnE;EACD,oBAAoB,QAAQ;AAC1B,UAAO,iBAAiB,MAAM,EAC5B,OACD,EAAC;EACH;CACF;AAED,QAAO;AACR;AAED,SAAS,eACPC,QACAC,UACA;CACA,MAAM,eAAe,iBAAiB,QAAQ;EAC5C;EACA,aAAa,CACX,eAAe,kBAAkB,MAAM;GACrC,MAAM,OAAO,MAAM,SAAS,KAAK;AACjC,UAAO;IACL,QAAQ;IACR,IAAI;IACJ;IACA,KAAK,KAAK;GACX;EACF,CACF;CACF,EAAC;CACF,MAAMC,mFACD,aAAa;EAChB,MAAM,OAAO;EACb,qBAAqB,QAAQ,aAAa,KAAK,OAAO;EACtD,MAAM,aAAa,KAAK;EACxB,QAAQ;;CAGV,MAAM,SAAS,sBAAsB,aAAa,KAAK;CACvD,MAAM,iBAAiB,aAAa,KAAK;AACzC,MAAK,eACH,QAAO;CAET,MAAM,gBAAgB,OAAO,GAAG,SAAoB;AAClD,SAAO,MAAM,eAAe;GAC1B;GACA;GACM;EACP,EAAC;CACH;AAED,eAAc,OAAO;AAErB,QAAO;AACR;AAcD,MAAM,YAAY,CAAC;;;EAGjB,MAAM;AAGR,eAAe,cACbC,OACAR,MACAS,MACgC;AAChC,KAAI;EAEF,MAAM,aAAa,KAAK,YAAY;EACpC,MAAM,SAAS,MAAM,uFAChB;GACH,MAAM,KAAK;GACX,OAAO,KAAK;GACZ,KAAKC,WAAiB;;IACpB,MAAM,WAAW;AAQjB,WAAO,cAAc,QAAQ,GAAG,kFAC3B;KACH,0DAAK,SAAU,mFAAW,KAAK,MAAQ,SAAS,OAAQ,KAAK;KAC7D,OAAO,YAAY,WAAW,WAAW,SAAS,QAAQ,KAAK;KAC/D,0FAAa,SAAU,oFAAe,KAAK;OAC3C;GACH;KACD;AAEF,SAAO;CACR,SAAQ,OAAO;AACd,SAAO;GACL,IAAI;GACJ,OAAO,wBAAwB,MAAM;GACrC,QAAQ;EACT;CACF;AACF;AAED,SAAS,sBAAsBV,MAA4C;CACzE,eAAe,UAAUW,MAAqC;AAE5D,OAAK,UAAU,iBAAiB,MAC9B,OAAM,IAAI,MAAM;EAIlB,MAAM,SAAS,MAAM,cAAc,GAAG,MAAM,KAAK;AAEjD,OAAK,OACH,OAAM,IAAI,UAAU;GAClB,MAAM;GACN,SACE;EACH;AAEH,OAAK,OAAO,GAEV,OAAM,OAAO;AAEf,SAAO,OAAO;CACf;AAED,WAAU,OAAO;AACjB,WAAU,YAAY;AACtB,WAAU,OAAO,KAAK;AAGtB,QAAO;AACR;;;;;;;;AC/pBD,MAAaC,yBACJ,WAAW,eAClB,UAAU,kCAEV,WAAW,kGAAS,+EAAM,iBAAgB,qCACxC,WAAW,qGAAS,iFAAM,iDAC1B,WAAW,qGAAS,iFAAM;;;;;AC2F9B,IAAM,cAAN,MAAM,YAA2D;;;;;CAK/D,UAAwD;AACtD,SAAO,IAAI;CAIZ;;;;;CAMD,OAAgC;AAC9B,SAAO,IAAI;CACZ;;;;;CAMD,OACEC,MAC2C;;EAU3C,MAAMC,iFACD;GACH,aAAa,oFAAmB,KAAM,4EAAe,mBAAmB;GACxE,kEACE,KAAM,oFAEN,WAAW,uFAAS,IAAI,iBAAgB;GAC1C,2FAAsB,KAAM,6FAAwB;GACpD,oFAAgB,KAAM,qFAAkB;GACxC,wEAAU,KAAM,mEAAY;GAK5B,QAAQ;;EAGV;;GAEE,MAAMC,0EAAoB,KAAM,qEAAY;AAE5C,QAAK,yDAAY,KAAM,0BAAyB,KAC9C,OAAM,IAAI,OACP;EAGN;AACD,SAAO;GAKL,SAAS;GAKT,WAAW,cAA2C,EACpD,kDAAM,KAAM,YACb,EAAC;GAKF,YAAY,yBAAsD;GAKlE,QAAQ,oBAA2B,OAAO;GAK1C;GAKA,qBAAqB,qBAA4B;EAClD;CACF;AACF;;;;;AAMD,MAAa,WAAW,IAAI"}