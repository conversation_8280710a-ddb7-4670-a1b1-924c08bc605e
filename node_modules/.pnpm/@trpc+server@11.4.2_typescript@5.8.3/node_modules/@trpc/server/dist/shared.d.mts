import "./index.d-D4qZxQJh.mjs";
import { createFlatProxy, getErrorShape, inferProcedureInput, inferProcedureOutput, inferTransformedProcedureOutput, inferTransformedSubscriptionOutput } from "./unstable-core-do-not-import.d-ptrxwuSa.mjs";
import "./index.d-vq_QHko2.mjs";
export { createFlatProxy, getErrorShape, inferProcedureInput, inferProcedureOutput, inferTransformedProcedureOutput, inferTransformedSubscriptionOutput };