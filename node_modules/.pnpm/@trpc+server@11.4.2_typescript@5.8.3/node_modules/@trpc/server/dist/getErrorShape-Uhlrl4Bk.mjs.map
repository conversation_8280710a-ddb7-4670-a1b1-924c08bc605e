{"version": 3, "file": "getErrorShape-Uhlrl4Bk.mjs", "names": ["obj: object", "callback: ProxyC<PERSON>back", "path: readonly string[]", "memo: Record<string, unknown>", "callback: (path: keyof TFaux) => any", "JSONRPC2_TO_HTTP_CODE: Record<\n  keyof typeof TRPC_ERROR_CODES_BY_KEY,\n  number\n>", "HTTP_CODE_TO_JSONRPC2: InvertKeyValue<\n  typeof JSONRPC2_TO_HTTP_CODE\n>", "code: keyof typeof TRPC_ERROR_CODES_BY_KEY", "code: keyof typeof HTTP_CODE_TO_JSONRPC2", "json: TRPCResponse | TRPCResponse[]", "error: TRPCError", "_typeof", "o", "_typeof", "toPrimitive", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "r", "opts: {\n  config: RootConfig<TRoot>;\n  error: TRPCError;\n  type: ProcedureType | 'unknown';\n  path: string | undefined;\n  input: unknown;\n  ctx: TRoot['ctx'] | undefined;\n}", "shape: DefaultErrorShape"], "sources": ["../src/unstable-core-do-not-import/createProxy.ts", "../src/unstable-core-do-not-import/http/getHTTPStatusCode.ts", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js", "../src/unstable-core-do-not-import/error/getErrorShape.ts"], "sourcesContent": ["interface ProxyCallbackOptions {\n  path: readonly string[];\n  args: readonly unknown[];\n}\ntype ProxyCallback = (opts: ProxyCallbackOptions) => unknown;\n\nconst noop = () => {\n  // noop\n};\n\nconst freezeIfAvailable = (obj: object) => {\n  if (Object.freeze) {\n    Object.freeze(obj);\n  }\n};\n\nfunction createInnerProxy(\n  callback: ProxyCallback,\n  path: readonly string[],\n  memo: Record<string, unknown>,\n) {\n  const cacheKey = path.join('.');\n\n  memo[cacheKey] ??= new Proxy(noop, {\n    get(_obj, key) {\n      if (typeof key !== 'string' || key === 'then') {\n        // special case for if the proxy is accidentally treated\n        // like a PromiseLike (like in `Promise.resolve(proxy)`)\n        return undefined;\n      }\n      return createInnerProxy(callback, [...path, key], memo);\n    },\n    apply(_1, _2, args) {\n      const lastOfPath = path[path.length - 1];\n\n      let opts = { args, path };\n      // special handling for e.g. `trpc.hello.call(this, 'there')` and `trpc.hello.apply(this, ['there'])\n      if (lastOfPath === 'call') {\n        opts = {\n          args: args.length >= 2 ? [args[1]] : [],\n          path: path.slice(0, -1),\n        };\n      } else if (lastOfPath === 'apply') {\n        opts = {\n          args: args.length >= 2 ? args[1] : [],\n          path: path.slice(0, -1),\n        };\n      }\n      freezeIfAvailable(opts.args);\n      freezeIfAvailable(opts.path);\n      return callback(opts);\n    },\n  });\n\n  return memo[cacheKey];\n}\n\n/**\n * Creates a proxy that calls the callback with the path and arguments\n *\n * @internal\n */\nexport const createRecursiveProxy = <TFaux = unknown>(\n  callback: ProxyCallback,\n): TFaux => createInnerProxy(callback, [], Object.create(null)) as TFaux;\n\n/**\n * Used in place of `new Proxy` where each handler will map 1 level deep to another value.\n *\n * @internal\n */\nexport const createFlatProxy = <TFaux>(\n  callback: (path: keyof TFaux) => any,\n): TFaux => {\n  return new Proxy(noop, {\n    get(_obj, name) {\n      if (name === 'then') {\n        // special case for if the proxy is accidentally treated\n        // like a PromiseLike (like in `Promise.resolve(proxy)`)\n        return undefined;\n      }\n      return callback(name as any);\n    },\n  }) as TFaux;\n};\n", "import type { TRPCError } from '../error/TRPCError';\nimport type { TRPC_ERROR_CODES_BY_KEY, TRPCResponse } from '../rpc';\nimport { TRPC_ERROR_CODES_BY_NUMBER } from '../rpc';\nimport type { InvertKeyValue, ValueOf } from '../types';\nimport { isObject } from '../utils';\n\nexport const JSONRPC2_TO_HTTP_CODE: Record<\n  keyof typeof TRPC_ERROR_CODES_BY_KEY,\n  number\n> = {\n  PARSE_ERROR: 400,\n  BAD_REQUEST: 400,\n  UNAUTHORIZED: 401,\n  PAYMENT_REQUIRED: 402,\n  FORBIDDEN: 403,\n  NOT_FOUND: 404,\n  METHOD_NOT_SUPPORTED: 405,\n  TIMEOUT: 408,\n  CONFLICT: 409,\n  PRECONDITION_FAILED: 412,\n  PAYLOAD_TOO_LARGE: 413,\n  UNSUPPORTED_MEDIA_TYPE: 415,\n  UNPROCESSABLE_CONTENT: 422,\n  TOO_MANY_REQUESTS: 429,\n  CLIENT_CLOSED_REQUEST: 499,\n  INTERNAL_SERVER_ERROR: 500,\n  NOT_IMPLEMENTED: 501,\n  BAD_GATEWAY: 502,\n  SERVICE_UNAVAILABLE: 503,\n  GATEWAY_TIMEOUT: 504,\n};\n\nexport const HTTP_CODE_TO_JSONRPC2: InvertKeyValue<\n  typeof JSONRPC2_TO_HTTP_CODE\n> = {\n  400: 'BAD_REQUEST',\n  401: 'UNAUTHORIZED',\n  402: 'PAYMENT_REQUIRED',\n  403: 'FORBIDDEN',\n  404: 'NOT_FOUND',\n  405: 'METHOD_NOT_SUPPORTED',\n  408: 'TIMEOUT',\n  409: 'CONFLICT',\n  412: 'PRECONDITION_FAILED',\n  413: 'PAYLOAD_TOO_LARGE',\n  415: 'UNSUPPORTED_MEDIA_TYPE',\n  422: 'UNPROCESSABLE_CONTENT',\n  429: 'TOO_MANY_REQUESTS',\n  499: 'CLIENT_CLOSED_REQUEST',\n  500: 'INTERNAL_SERVER_ERROR',\n  501: 'NOT_IMPLEMENTED',\n  502: 'BAD_GATEWAY',\n  503: 'SERVICE_UNAVAILABLE',\n  504: 'GATEWAY_TIMEOUT',\n} as const;\n\nexport function getStatusCodeFromKey(\n  code: keyof typeof TRPC_ERROR_CODES_BY_KEY,\n) {\n  return JSONRPC2_TO_HTTP_CODE[code] ?? 500;\n}\n\nexport function getStatusKeyFromCode(\n  code: keyof typeof HTTP_CODE_TO_JSONRPC2,\n): ValueOf<typeof HTTP_CODE_TO_JSONRPC2> {\n  return HTTP_CODE_TO_JSONRPC2[code] ?? 'INTERNAL_SERVER_ERROR';\n}\n\nexport function getHTTPStatusCode(json: TRPCResponse | TRPCResponse[]) {\n  const arr = Array.isArray(json) ? json : [json];\n  const httpStatuses = new Set<number>(\n    arr.map((res) => {\n      if ('error' in res && isObject(res.error.data)) {\n        if (typeof res.error.data?.['httpStatus'] === 'number') {\n          return res.error.data['httpStatus'];\n        }\n        const code = TRPC_ERROR_CODES_BY_NUMBER[res.error.code];\n        return getStatusCodeFromKey(code);\n      }\n      return 200;\n    }),\n  );\n\n  if (httpStatuses.size !== 1) {\n    return 207;\n  }\n\n  const httpStatus = httpStatuses.values().next().value;\n\n  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n  return httpStatus!;\n}\n\nexport function getHTTPStatusCodeFromError(error: TRPCError) {\n  return getStatusCodeFromKey(error.code);\n}\n", "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _typeof(o);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nmodule.exports = toPrimitive, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nvar toPrimitive = require(\"./toPrimitive.js\");\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nmodule.exports = toPropertyKey, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var defineProperty = require(\"./defineProperty.js\");\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nmodule.exports = _objectSpread2, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import { getHTTPStatusCodeFromError } from '../http/getHTTPStatusCode';\nimport type { ProcedureType } from '../procedure';\nimport type { AnyRootTypes, RootConfig } from '../rootConfig';\nimport { TRPC_ERROR_CODES_BY_KEY } from '../rpc';\nimport type { DefaultErrorShape } from './formatter';\nimport type { TRPCError } from './TRPCError';\n\n/**\n * @internal\n */\nexport function getErrorShape<TRoot extends AnyRootTypes>(opts: {\n  config: RootConfig<TRoot>;\n  error: TRPCError;\n  type: ProcedureType | 'unknown';\n  path: string | undefined;\n  input: unknown;\n  ctx: TRoot['ctx'] | undefined;\n}): TRoot['errorShape'] {\n  const { path, error, config } = opts;\n  const { code } = opts.error;\n  const shape: DefaultErrorShape = {\n    message: error.message,\n    code: TRPC_ERROR_CODES_BY_KEY[code],\n    data: {\n      code,\n      httpStatus: getHTTPStatusCodeFromError(error),\n    },\n  };\n  if (config.isDev && typeof opts.error.stack === 'string') {\n    shape.data.stack = opts.error.stack;\n  }\n  if (typeof path === 'string') {\n    shape.data.path = path;\n  }\n  return config.errorFormatter({ ...opts, shape });\n}\n"], "x_google_ignoreList": [2, 3, 4, 5, 6], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,MAAM,OAAO,MAAM,CAElB;AAED,MAAM,oBAAoB,CAACA,QAAgB;AACzC,KAAI,OAAO,OACT,QAAO,OAAO,IAAI;AAErB;AAED,SAAS,iBACPC,UACAC,MACAC,MACA;;CACA,MAAM,WAAW,KAAK,KAAK,IAAI;AAE/B,wBAAK,qDAAL,KAAK,YAAc,IAAI,MAAM,MAAM;EACjC,IAAI,MAAM,KAAK;AACb,cAAW,QAAQ,YAAY,QAAQ,OAGrC;AAEF,UAAO,iBAAiB,UAAU,CAAC,GAAG,MAAM,GAAI,GAAE,KAAK;EACxD;EACD,MAAM,IAAI,IAAI,MAAM;GAClB,MAAM,aAAa,KAAK,KAAK,SAAS;GAEtC,IAAI,OAAO;IAAE;IAAM;GAAM;AAEzB,OAAI,eAAe,OACjB,QAAO;IACL,MAAM,KAAK,UAAU,IAAI,CAAC,KAAK,EAAG,IAAG,CAAE;IACvC,MAAM,KAAK,MAAM,GAAG,GAAG;GACxB;YACQ,eAAe,QACxB,QAAO;IACL,MAAM,KAAK,UAAU,IAAI,KAAK,KAAK,CAAE;IACrC,MAAM,KAAK,MAAM,GAAG,GAAG;GACxB;AAEH,qBAAkB,KAAK,KAAK;AAC5B,qBAAkB,KAAK,KAAK;AAC5B,UAAO,SAAS,KAAK;EACtB;CACF;AAED,QAAO,KAAK;AACb;;;;;;AAOD,MAAa,uBAAuB,CAClCF,aACU,iBAAiB,UAAU,CAAE,GAAE,OAAO,OAAO,KAAK,CAAC;;;;;;AAO/D,MAAa,kBAAkB,CAC7BG,aACU;AACV,QAAO,IAAI,MAAM,MAAM,EACrB,IAAI,MAAM,MAAM;AACd,MAAI,SAAS,OAGX;AAEF,SAAO,SAAS,KAAY;CAC7B,EACF;AACF;;;;AC9ED,MAAaC,wBAGT;CACF,aAAa;CACb,aAAa;CACb,cAAc;CACd,kBAAkB;CAClB,WAAW;CACX,WAAW;CACX,sBAAsB;CACtB,SAAS;CACT,UAAU;CACV,qBAAqB;CACrB,mBAAmB;CACnB,wBAAwB;CACxB,uBAAuB;CACvB,mBAAmB;CACnB,uBAAuB;CACvB,uBAAuB;CACvB,iBAAiB;CACjB,aAAa;CACb,qBAAqB;CACrB,iBAAiB;AAClB;AAED,MAAaC,wBAET;CACF,KAAK;CACL,KAAK;CACL,KAAK;CACL,KAAK;CACL,KAAK;CACL,KAAK;CACL,KAAK;CACL,KAAK;CACL,KAAK;CACL,KAAK;CACL,KAAK;CACL,KAAK;CACL,KAAK;CACL,KAAK;CACL,KAAK;CACL,KAAK;CACL,KAAK;CACL,KAAK;CACL,KAAK;AACN;AAED,SAAgB,qBACdC,MACA;;AACA,iCAAO,sBAAsB,8EAAS;AACvC;AAED,SAAgB,qBACdC,MACuC;;AACvC,iCAAO,sBAAsB,8EAAS;AACvC;AAED,SAAgB,kBAAkBC,MAAqC;CACrE,MAAM,MAAM,MAAM,QAAQ,KAAK,GAAG,OAAO,CAAC,IAAK;CAC/C,MAAM,eAAe,IAAI,IACvB,IAAI,IAAI,CAAC,QAAQ;AACf,MAAI,WAAW,OAAO,SAAS,IAAI,MAAM,KAAK,EAAE;;AAC9C,kCAAW,IAAI,MAAM,wEAAO,mBAAkB,SAC5C,QAAO,IAAI,MAAM,KAAK;GAExB,MAAM,OAAO,2BAA2B,IAAI,MAAM;AAClD,UAAO,qBAAqB,KAAK;EAClC;AACD,SAAO;CACR,EAAC;AAGJ,KAAI,aAAa,SAAS,EACxB,QAAO;CAGT,MAAM,aAAa,aAAa,QAAQ,CAAC,MAAM,CAAC;AAGhD,QAAO;AACR;AAED,SAAgB,2BAA2BC,OAAkB;AAC3D,QAAO,qBAAqB,MAAM,KAAK;AACxC;;;;;CC/FD,SAASC,UAAQ,GAAG;AAClB;AAEA,SAAO,OAAO,UAAUA,YAAU,qBAAqB,UAAU,mBAAmB,OAAO,WAAW,SAAUC,KAAG;AACjH,iBAAcA;EACf,IAAG,SAAUA,KAAG;AACf,UAAOA,OAAK,qBAAqB,UAAUA,IAAE,gBAAgB,UAAUA,QAAM,OAAO,YAAY,kBAAkBA;EACnH,GAAE,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO,SAAS,UAAQ,EAAE;CAC5F;AACD,QAAO,UAAUD,WAAS,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CCT/F,IAAIE,6BAAiC;CACrC,SAASC,cAAY,GAAG,GAAG;AACzB,MAAI,YAAY,UAAQ,EAAE,KAAK,EAAG,QAAO;EACzC,IAAI,IAAI,EAAE,OAAO;AACjB,WAAS,MAAM,GAAG;GAChB,IAAI,IAAI,EAAE,KAAK,GAAG,KAAK,UAAU;AACjC,OAAI,YAAY,UAAQ,EAAE,CAAE,QAAO;AACnC,SAAM,IAAI,UAAU;EACrB;AACD,SAAO,CAAC,aAAa,IAAI,SAAS,QAAQ,EAAE;CAC7C;AACD,QAAO,UAAUA,eAAa,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CCXnG,IAAI,2BAAiC;CACrC,IAAI;CACJ,SAASC,gBAAc,GAAG;EACxB,IAAI,IAAI,YAAY,GAAG,SAAS;AAChC,SAAO,YAAY,QAAQ,EAAE,GAAG,IAAI,IAAI;CACzC;AACD,QAAO,UAAUA,iBAAe,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CCNrG,IAAI;CACJ,SAAS,gBAAgB,GAAG,GAAG,GAAG;AAChC,UAAQ,IAAI,cAAc,EAAE,KAAK,IAAI,OAAO,eAAe,GAAG,GAAG;GAC/D,OAAO;GACP,aAAa;GACb,eAAe;GACf,WAAW;EACZ,EAAC,GAAG,EAAE,KAAK,GAAG;CAChB;AACD,QAAO,UAAU,iBAAiB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CCTvG,IAAI;CACJ,SAAS,QAAQ,GAAG,GAAG;EACrB,IAAI,IAAI,OAAO,KAAK,EAAE;AACtB,MAAI,OAAO,uBAAuB;GAChC,IAAI,IAAI,OAAO,sBAAsB,EAAE;AACvC,SAAM,IAAI,EAAE,OAAO,SAAUC,KAAG;AAC9B,WAAO,OAAO,yBAAyB,GAAGA,IAAE,CAAC;GAC9C,EAAC,GAAG,EAAE,KAAK,MAAM,GAAG,EAAE;EACxB;AACD,SAAO;CACR;CACD,SAAS,eAAe,GAAG;AACzB,OAAK,IAAI,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;GACzC,IAAI,IAAI,QAAQ,UAAU,KAAK,UAAU,KAAK,CAAE;AAChD,OAAI,IAAI,QAAQ,OAAO,EAAE,GAAG,EAAE,CAAC,QAAQ,SAAUA,KAAG;AAClD,mBAAe,GAAGA,KAAG,EAAEA,KAAG;GAC3B,EAAC,GAAG,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,EAAE,CAAC,GAAG,QAAQ,OAAO,EAAE,CAAC,CAAC,QAAQ,SAAUA,KAAG;AAChJ,WAAO,eAAe,GAAGA,KAAG,OAAO,yBAAyB,GAAGA,IAAE,CAAC;GACnE,EAAC;EACH;AACD,SAAO;CACR;AACD,QAAO,UAAU,gBAAgB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;;;;ACZtG,SAAgB,cAA0CC,MAOlC;CACtB,MAAM,EAAE,MAAM,OAAO,QAAQ,GAAG;CAChC,MAAM,EAAE,MAAM,GAAG,KAAK;CACtB,MAAMC,QAA2B;EAC/B,SAAS,MAAM;EACf,MAAM,wBAAwB;EAC9B,MAAM;GACJ;GACA,YAAY,2BAA2B,MAAM;EAC9C;CACF;AACD,KAAI,OAAO,gBAAgB,KAAK,MAAM,UAAU,SAC9C,OAAM,KAAK,QAAQ,KAAK,MAAM;AAEhC,YAAW,SAAS,SAClB,OAAM,KAAK,OAAO;AAEpB,QAAO,OAAO,uFAAoB,aAAM,SAAQ;AACjD"}