import "./index.d-D4qZxQJh.mjs";
import { AnyClientTypes, AnyMiddlewareBuilder, AnyMiddlewareFunction, AnyMutationProcedure, AnyProcedure, AnyProcedureBuilder, AnyQueryProcedure, AnyRootTypes, AnyRouter, AnySubscriptionProcedure, BaseHandlerOptions, BuiltRouter, CallerOverride, CombinedDataTransformer, CombinedDataTransformerClient, ConsumerOnError, CreateContextCallback, CreateRootTypes, CreateRouterOptions, DataTransformer, DataTransformerOptions, DecorateCreateRouterOptions, DecorateRouterRecord, DeepPartial, DefaultErrorData, DefaultErrorShape, Deferred, Dict, DistributiveOmit, ErrorFormatter, ErrorHandlerOptions, ErrorSymbol, EventSourceLike, Filter, FilterKeys, GetInferenceHelpers, GetRawInputFn, HTTPBaseHandlerOptions, HTTPError<PERSON><PERSON><PERSON>, HTTP_CODE_TO_JSONRPC2, InferrableClientTypes, IntersectionError, InvertKeyValue, JSONLProducerOptions, JSONRPC2, JSONRPC2_TO_HTTP_CODE, KeyFromValue, Lazy, LegacyObservableSubscriptionProcedure, Maybe, MaybePromise, MergeRouters, MiddlewareBuilder, MiddlewareFunction, MiddlewareResult, MutationProcedure, NodeJSReadableStreamEsque, Overwrite, ParseFn, Parser, ParserArkTypeEsque, ParserCustomValidatorEsque, ParserMyZodEsque, ParserScaleEsque, ParserStandardSchemaEsque, ParserSuperstructEsque, ParserValibotEsque, ParserWithInputOutput, ParserWithoutInput, ParserYupEsque, ParserZodEsque, PickFirstDefined, Procedure, ProcedureBuilder, ProcedureCallOptions, ProcedureResolverOptions, ProcedureType, ProducerOnError, PromiseExecutor, PromiseWithResolvers, ProtectedIntersection, ProxyPromise, QueryProcedure, ResolveHTTPRequestOptionsContextFn, ResponseMeta, ResponseMetaFn, Result, RootConfig, RootTypes, Router, RouterBuilder, RouterCaller, RouterCallerErrorHandler, RouterCallerFactory, RouterDef, RouterRecord, RuntimeConfigOptions, SSEClientOptions, SSEPingOptions, SSEStreamConsumerOptions, SSEStreamProducerOptions, Serialize, SerializeObject, Simplify, StandardSchemaV1, StandardSchemaV1Error, SubscribedPromise, SubscriptionProcedure, TRPCAcceptHeader, TRPCBuilder, TRPCClientIncomingMessage, TRPCClientIncomingRequest, TRPCClientOutgoingMessage, TRPCClientOutgoingRequest, TRPCConnectionParamsMessage, TRPCError, TRPCErrorResponse, TRPCErrorShape, TRPCReconnectNotification, TRPCRequest, TRPCRequestInfo, TRPCRequestInfoProcedureCall, TRPCRequestMessage, TRPCResponse, TRPCResponseMessage, TRPCResult, TRPCResultMessage, TRPCRootObject, TRPCSubscriptionStopNotification, TRPCSuccessResponse, TRPC_ERROR_CODES_BY_KEY, TRPC_ERROR_CODES_BY_NUMBER, TRPC_ERROR_CODE_KEY, TRPC_ERROR_CODE_NUMBER, TrackedEnvelope, TypeError, Unpromise, UnsetMarker, Unwrap, ValidateShape, ValueOf, WebReadableStreamEsque, WithoutIndexSignature, abortSignalsAnyPonyfill, assert, callProcedure, coerceAsyncIterableToArray, createBuilder, createCallerFactory, createDeferred, createFlatProxy, createInputMiddleware, createMiddlewareFactory, createOutputMiddleware, createRecursiveProxy, createRouterFactory, defaultFormatter, defaultTransformer, experimental_standaloneMiddleware, formDataToObject, getCauseFromUnknown, getDataTransformer, getErrorShape, getHTTPStatusCode, getHTTPStatusCodeFromError, getParseFn, getProcedureAtPath, getRequestInfo, getStatusCodeFromKey, getStatusKeyFromCode, getTRPCErrorFromUnknown, identity, inferAsyncIterableYield, inferClientTypes, inferParser, inferProcedureBuilderResolverOptions, inferProcedureInput, inferProcedureOutput, inferProcedureParams, inferRouterContext, inferRouterError, inferRouterInputs, inferRouterMeta, inferRouterOutputs, inferRouterRootTypes, inferTrackedOutput, inferTransformedProcedureOutput, inferTransformedSubscriptionOutput, initTRPC, isAbortError, isAsyncIterable, isFunction, isObject, isPromise, isServerDefault, isTrackedEnvelope, iteratorResource, jsonlStreamConsumer, jsonlStreamProducer, lazy, makeAsyncResource, makeResource, mergeRouters, mergeWithoutOverrides, middlewareMarker, noop, octetInputParser, omitPrototype, parseConnectionParamsFromString, parseConnectionParamsFromUnknown, parseTRPCMessage, procedureTypes, resolveResponse, retryableRpcCodes, run, sleep, sse, sseHeaders, sseStreamConsumer, sseStreamProducer, takeWithGrace, throwAbortError, tracked, transformResult, transformTRPCResponse, withMaxDuration } from "./unstable-core-do-not-import.d-ptrxwuSa.mjs";
export { AnyClientTypes, AnyMiddlewareBuilder, AnyMiddlewareFunction, AnyMutationProcedure, AnyProcedure, AnyProcedureBuilder, AnyQueryProcedure, AnyRootTypes, AnyRouter, AnySubscriptionProcedure, BaseHandlerOptions, BuiltRouter, CallerOverride, CombinedDataTransformer, CombinedDataTransformerClient, ConsumerOnError, CreateContextCallback, CreateRootTypes, CreateRouterOptions, DataTransformer, DataTransformerOptions, DecorateCreateRouterOptions, DecorateRouterRecord, DeepPartial, DefaultErrorData, DefaultErrorShape, Deferred, Dict, DistributiveOmit, ErrorFormatter, ErrorHandlerOptions, ErrorSymbol, EventSourceLike, Filter, FilterKeys, GetInferenceHelpers, GetRawInputFn, HTTPBaseHandlerOptions, HTTPErrorHandler, HTTP_CODE_TO_JSONRPC2, InferrableClientTypes, IntersectionError, InvertKeyValue, JSONLProducerOptions, JSONRPC2, JSONRPC2_TO_HTTP_CODE, KeyFromValue, Lazy, LegacyObservableSubscriptionProcedure, Maybe, MaybePromise, MergeRouters, MiddlewareBuilder, MiddlewareFunction, MiddlewareResult, MutationProcedure, NodeJSReadableStreamEsque, Overwrite, ParseFn, Parser, ParserArkTypeEsque, ParserCustomValidatorEsque, ParserMyZodEsque, ParserScaleEsque, ParserStandardSchemaEsque, ParserSuperstructEsque, ParserValibotEsque, ParserWithInputOutput, ParserWithoutInput, ParserYupEsque, ParserZodEsque, PickFirstDefined, Procedure, ProcedureBuilder, ProcedureCallOptions, ProcedureResolverOptions, ProcedureType, ProducerOnError, PromiseExecutor, PromiseWithResolvers, ProtectedIntersection, ProxyPromise, QueryProcedure, ResolveHTTPRequestOptionsContextFn, ResponseMeta, ResponseMetaFn, Result, RootConfig, RootTypes, Router, RouterBuilder, RouterCaller, RouterCallerErrorHandler, RouterCallerFactory, RouterDef, RouterRecord, RuntimeConfigOptions, SSEClientOptions, SSEPingOptions, SSEStreamConsumerOptions, SSEStreamProducerOptions, Serialize, SerializeObject, Simplify, StandardSchemaV1, StandardSchemaV1Error, SubscribedPromise, SubscriptionProcedure, TRPCAcceptHeader, TRPCBuilder, TRPCClientIncomingMessage, TRPCClientIncomingRequest, TRPCClientOutgoingMessage, TRPCClientOutgoingRequest, TRPCConnectionParamsMessage, TRPCError, TRPCErrorResponse, TRPCErrorShape, TRPCReconnectNotification, TRPCRequest, TRPCRequestInfo, TRPCRequestInfoProcedureCall, TRPCRequestMessage, TRPCResponse, TRPCResponseMessage, TRPCResult, TRPCResultMessage, TRPCRootObject, TRPCSubscriptionStopNotification, TRPCSuccessResponse, TRPC_ERROR_CODES_BY_KEY, TRPC_ERROR_CODES_BY_NUMBER, TRPC_ERROR_CODE_KEY, TRPC_ERROR_CODE_NUMBER, TrackedEnvelope, TypeError, Unpromise, UnsetMarker, Unwrap, ValidateShape, ValueOf, WebReadableStreamEsque, WithoutIndexSignature, abortSignalsAnyPonyfill, assert, callProcedure, coerceAsyncIterableToArray, createBuilder, createCallerFactory, createDeferred, createFlatProxy, createInputMiddleware, createMiddlewareFactory, createOutputMiddleware, createRecursiveProxy, createRouterFactory, defaultFormatter, defaultTransformer, experimental_standaloneMiddleware, formDataToObject, getCauseFromUnknown, getDataTransformer, getErrorShape, getHTTPStatusCode, getHTTPStatusCodeFromError, getParseFn, getProcedureAtPath, getRequestInfo, getStatusCodeFromKey, getStatusKeyFromCode, getTRPCErrorFromUnknown, identity, inferAsyncIterableYield, inferClientTypes, inferParser, inferProcedureBuilderResolverOptions, inferProcedureInput, inferProcedureOutput, inferProcedureParams, inferRouterContext, inferRouterError, inferRouterInputs, inferRouterMeta, inferRouterOutputs, inferRouterRootTypes, inferTrackedOutput, inferTransformedProcedureOutput, inferTransformedSubscriptionOutput, initTRPC, isAbortError, isAsyncIterable, isFunction, isObject, isPromise, isServerDefault, isTrackedEnvelope, iteratorResource, jsonlStreamConsumer, jsonlStreamProducer, lazy, makeAsyncResource, makeResource, mergeRouters, mergeWithoutOverrides, middlewareMarker, noop, octetInputParser, omitPrototype, parseConnectionParamsFromString, parseConnectionParamsFromUnknown, parseTRPCMessage, procedureTypes, resolveResponse, retryableRpcCodes, run, sleep, sse, sseHeaders, sseStreamConsumer, sseStreamProducer, takeWithGrace, throwAbortError, tracked, transformResult, transformTRPCResponse, withMaxDuration };