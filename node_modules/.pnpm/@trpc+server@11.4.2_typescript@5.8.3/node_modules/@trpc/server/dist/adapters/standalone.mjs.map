{"version": 3, "file": "standalone.mjs", "names": ["opts: StandaloneHandlerOptions<TR<PERSON>er, TRequest, TResponse>", "opts: CreateHTTPHandlerOptions<TRouter>", "opts: CreateHTTP2HandlerOptions<AnyRouter>"], "sources": ["../../src/adapters/standalone.ts"], "sourcesContent": ["/**\n * If you're making an adapter for tRPC and looking at this file for reference, you should import types and functions from `@trpc/server` and `@trpc/server/http`\n *\n * @example\n * ```ts\n * import type { AnyTRPCRouter } from '@trpc/server'\n * import type { HTTPBaseHandlerOptions } from '@trpc/server/http'\n * ```\n */\n\nimport http from 'http';\n// --- http2 ---\nimport type * as http2 from 'http2';\n// @trpc/server\nimport { type AnyRouter } from '../@trpc/server';\n// eslint-disable-next-line no-restricted-imports\nimport { run } from '../unstable-core-do-not-import';\nimport type {\n  NodeHTTPCreateContextFnOptions,\n  NodeHTTPHandlerOptions,\n  NodeHTTPRequest,\n  NodeHTTPResponse,\n} from './node-http';\nimport {\n  createURL,\n  internal_exceptionHandler,\n  nodeHTTPRequestHandler,\n} from './node-http';\n\ntype StandaloneHandlerOptions<\n  TRouter extends AnyRouter,\n  TRequest extends NodeHTTPRequest,\n  TResponse extends NodeHTTPResponse,\n> = NodeHTTPHandlerOptions<TRouter, TRequest, TResponse> & {\n  /**\n   * The base path to handle requests for.\n   * This will be sliced from the beginning of the request path\n   * (Do not miss including the trailing slash)\n   * @default '/'\n   * @example '/trpc/'\n   * @example '/trpc/api/'\n   */\n  basePath?: string;\n};\n\n// --- http1 ---\nexport type CreateHTTPHandlerOptions<TRouter extends AnyRouter> =\n  StandaloneHandlerOptions<TRouter, http.IncomingMessage, http.ServerResponse>;\n\nexport type CreateHTTPContextOptions = NodeHTTPCreateContextFnOptions<\n  http.IncomingMessage,\n  http.ServerResponse\n>;\n\nfunction createHandler<\n  TRouter extends AnyRouter,\n  TRequest extends NodeHTTPRequest,\n  TResponse extends NodeHTTPResponse,\n>(\n  opts: StandaloneHandlerOptions<TRouter, TRequest, TResponse>,\n): (req: TRequest, res: TResponse) => void {\n  const basePath = opts.basePath ?? '/';\n  const sliceLength = basePath.length;\n\n  return (req, res) => {\n    let path = '';\n\n    run(async () => {\n      const url = createURL(req);\n\n      // get procedure(s) path and remove the leading slash\n      path = url.pathname.slice(sliceLength);\n\n      await nodeHTTPRequestHandler({\n        ...(opts as any),\n        req,\n        res,\n        path,\n      });\n    }).catch(\n      internal_exceptionHandler({\n        req,\n        res,\n        path,\n        ...opts,\n      }),\n    );\n  };\n}\n\n/**\n * @internal\n */\nexport function createHTTPHandler<TRouter extends AnyRouter>(\n  opts: CreateHTTPHandlerOptions<TRouter>,\n): http.RequestListener {\n  return createHandler(opts);\n}\n\nexport function createHTTPServer<TRouter extends AnyRouter>(\n  opts: CreateHTTPHandlerOptions<TRouter>,\n) {\n  return http.createServer(createHTTPHandler(opts));\n}\n\n// --- http2 ---\nexport type CreateHTTP2HandlerOptions<TRouter extends AnyRouter> =\n  StandaloneHandlerOptions<\n    TRouter,\n    http2.Http2ServerRequest,\n    http2.Http2ServerResponse\n  >;\n\nexport type CreateHTTP2ContextOptions = NodeHTTPCreateContextFnOptions<\n  http2.Http2ServerRequest,\n  http2.Http2ServerResponse\n>;\n\nexport function createHTTP2Handler(opts: CreateHTTP2HandlerOptions<AnyRouter>) {\n  return createHandler(opts);\n}\n"], "mappings": ";;;;;;;;;;;;;;AAsDA,SAAS,cAKPA,MACyC;;CACzC,MAAM,6BAAW,KAAK,mEAAY;CAClC,MAAM,cAAc,SAAS;AAE7B,QAAO,CAAC,KAAK,QAAQ;EACnB,IAAI,OAAO;AAEX,MAAI,YAAY;GACd,MAAM,MAAM,UAAU,IAAI;AAG1B,UAAO,IAAI,SAAS,MAAM,YAAY;AAEtC,SAAM,+FACA;IACJ;IACA;IACA;MACA;EACH,EAAC,CAAC,MACD;GACE;GACA;GACA;KACG,MACH,CACH;CACF;AACF;;;;AAKD,SAAgB,kBACdC,MACsB;AACtB,QAAO,cAAc,KAAK;AAC3B;AAED,SAAgB,iBACdA,MACA;AACA,QAAO,KAAK,aAAa,kBAAkB,KAAK,CAAC;AAClD;AAeD,SAAgB,mBAAmBC,MAA4C;AAC7E,QAAO,cAAc,KAAK;AAC3B"}