{"version": 3, "file": "next.mjs", "names": ["opts: NodeHTTPHandlerOptions<TRouter, NextApiRequest, NextApiResponse>"], "sources": ["../../src/adapters/next.ts"], "sourcesContent": ["/**\n * If you're making an adapter for tRPC and looking at this file for reference, you should import types and functions from `@trpc/server` and `@trpc/server/http`\n *\n * @example\n * ```ts\n * import type { AnyTRPCRouter } from '@trpc/server'\n * import type { HTTPBaseHandlerOptions } from '@trpc/server/http'\n * ```\n */\nimport type { NextApiHandler, NextApiRequest, NextApiResponse } from 'next';\n// @trpc/server\nimport type { AnyRouter } from '../@trpc/server';\n// @trpc/server\nimport { TRPCError } from '../@trpc/server';\n// eslint-disable-next-line no-restricted-imports\nimport { run } from '../unstable-core-do-not-import';\nimport type {\n  NodeHTTPCreateContextFnOptions,\n  NodeHTTPHandlerOptions,\n} from './node-http';\nimport { internal_exceptionHandler, nodeHTTPRequestHandler } from './node-http';\n\nexport type CreateNextContextOptions = NodeHTTPCreateContextFnOptions<\n  NextApiRequest,\n  NextApiResponse\n>;\n\n/**\n * Preventing \"TypeScript where it's tough not to get \"The inferred type of 'xxxx' cannot be named without a reference to [...]\"\n */\nexport type { NextApiHandler, NextApiRequest, NextApiResponse } from 'next';\n\nexport function createNextApiHandler<TRouter extends AnyRouter>(\n  opts: NodeHTTPHandlerOptions<TRouter, NextApiRequest, NextApiResponse>,\n): NextApiHandler {\n  return async (req, res) => {\n    let path = '';\n\n    await run(async () => {\n      path = run(() => {\n        if (typeof req.query['trpc'] === 'string') {\n          return req.query['trpc'];\n        }\n        if (Array.isArray(req.query['trpc'])) {\n          return req.query['trpc'].join('/');\n        }\n        throw new TRPCError({\n          message:\n            'Query \"trpc\" not found - is the file named `[trpc]`.ts or `[...trpc].ts`?',\n          code: 'INTERNAL_SERVER_ERROR',\n        });\n      });\n\n      await nodeHTTPRequestHandler({\n        ...(opts as any),\n        req,\n        res,\n        path,\n      });\n    }).catch(\n      internal_exceptionHandler({\n        req,\n        res,\n        path,\n        ...opts,\n      }),\n    );\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;AAgCA,SAAgB,qBACdA,MACgB;AAChB,QAAO,OAAO,KAAK,QAAQ;EACzB,IAAI,OAAO;AAEX,QAAM,IAAI,YAAY;AACpB,UAAO,IAAI,MAAM;AACf,eAAW,IAAI,MAAM,YAAY,SAC/B,QAAO,IAAI,MAAM;AAEnB,QAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAClC,QAAO,IAAI,MAAM,QAAQ,KAAK,IAAI;AAEpC,UAAM,IAAI,UAAU;KAClB,SACE;KACF,MAAM;IACP;GACF,EAAC;AAEF,SAAM,+FACA;IACJ;IACA;IACA;MACA;EACH,EAAC,CAAC,MACD;GACE;GACA;GACA;KACG,MACH,CACH;CACF;AACF"}