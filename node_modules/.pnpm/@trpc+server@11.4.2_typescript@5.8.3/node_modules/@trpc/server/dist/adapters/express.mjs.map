{"version": 3, "file": "express.mjs", "names": ["opts: NodeHTTPHandlerOptions<TRouter, express.Request, express.Response>"], "sources": ["../../src/adapters/express.ts"], "sourcesContent": ["/**\n * If you're making an adapter for tRPC and looking at this file for reference, you should import types and functions from `@trpc/server` and `@trpc/server/http`\n *\n * @example\n * ```ts\n * import type { AnyTRPCRouter } from '@trpc/server'\n * import type { HTTPBaseHandlerOptions } from '@trpc/server/http'\n * ```\n */\nimport type * as express from 'express';\nimport type { AnyRouter } from '../@trpc/server';\n// eslint-disable-next-line no-restricted-imports\nimport { run } from '../unstable-core-do-not-import';\nimport type {\n  NodeHTTPCreateContextFnOptions,\n  NodeHTTPHandlerOptions,\n} from './node-http';\nimport { internal_exceptionHandler, nodeHTTPRequestHandler } from './node-http';\n\nexport type CreateExpressContextOptions = NodeHTTPCreateContextFnOptions<\n  express.Request,\n  express.Response\n>;\n\nexport function createExpressMiddleware<TRouter extends AnyRouter>(\n  opts: NodeHTTPHandlerOptions<TRouter, express.Request, express.Response>,\n): express.Handler {\n  return (req, res) => {\n    let path = '';\n    run(async () => {\n      path = req.path.slice(req.path.lastIndexOf('/') + 1);\n\n      await nodeHTTPRequestHandler({\n        ...(opts as any),\n        req,\n        res,\n        path,\n      });\n    }).catch(\n      internal_exceptionHandler({\n        req,\n        res,\n        path,\n        ...opts,\n      }),\n    );\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;AAwBA,SAAgB,wBACdA,MACiB;AACjB,QAAO,CAAC,KAAK,QAAQ;EACnB,IAAI,OAAO;AACX,MAAI,YAAY;AACd,UAAO,IAAI,KAAK,MAAM,IAAI,KAAK,YAAY,IAAI,GAAG,EAAE;AAEpD,SAAM,+FACA;IACJ;IACA;IACA;MACA;EACH,EAAC,CAAC,MACD;GACE;GACA;GACA;KACG,MACH,CACH;CACF;AACF"}