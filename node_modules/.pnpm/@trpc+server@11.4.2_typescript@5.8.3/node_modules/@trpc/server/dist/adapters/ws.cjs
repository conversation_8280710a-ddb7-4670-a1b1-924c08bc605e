require('../getErrorShape-DKiEF6Zc.cjs');
require('../tracked-HoF8L_mq.cjs');
require('../utils-BhNVZA-c.cjs');
require('../parseTRPCMessage-snNQop7N.cjs');
require('../resolveResponse-CVGbakBm.cjs');
require('../contentTypeParsers-iAFF_pJG.cjs');
require('../unstable-core-do-not-import-DFQys1IC.cjs');
require('../observable-B1Nk6r1H.cjs');
require('../initTRPC-IT4M4lu3.cjs');
require('../http-DXy3XyhL.cjs');
require('../node-http-kIQEhZUH.cjs');
require('../observable-BVzLuBs6.cjs');
const require_ws = require('../ws-BhrWsMpl.cjs');

exports.applyWSSHandler = require_ws.applyWSSHandler;
exports.getWSConnectionHandler = require_ws.getWSConnectionHandler;
exports.handleKeepAlive = require_ws.handleKeepAlive;