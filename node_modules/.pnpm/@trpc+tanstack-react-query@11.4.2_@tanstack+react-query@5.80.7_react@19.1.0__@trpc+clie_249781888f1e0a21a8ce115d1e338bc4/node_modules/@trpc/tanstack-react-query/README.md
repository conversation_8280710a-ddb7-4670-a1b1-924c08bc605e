<p align="center">
  <a href="https://trpc.io/"><img src="https://assets.trpc.io/icons/svgs/blue-bg-rounded.svg" alt="tRPC" height="75"/></a>
</p>

<h3 align="center">tRPC</h3>

<p align="center">
  <strong>End-to-end typesafe APIs made easy</strong>
</p>

# `@trpc/tanstack-react-query`

> A tRPC wrapper around `@tanstack/react-query`.

> [!WARNING]
>
> 🚧 🚧 🚧 🚧 🚧 🚧 🚧 🚧 🚧 🚧 🚧 🚧 🚧 🚧 🚧 🚧 🚧 🚧 🚧
> This package is currently in beta as we stabilize the API. We might do breaking changes without respecting semver.

## Documentation

Full documentation can be found at https://trpc.io/docs/client/tanstack-react-query/setup.

## Installation

> Requires `@tanstack/react-query` v5.62.8 or higher

```bash
# npm
npm install @trpc/tanstack-react-query @tanstack/react-query

# Yarn
yarn add @trpc/tanstack-react-query @tanstack/react-query

# pnpm
pnpm add @trpc/tanstack-react-query @tanstack/react-query

# Bun
bun add @trpc/tanstack-react-query @tanstack/react-query
```
