{"version": 3, "file": "index.mjs", "names": ["_typeof", "o", "_typeof", "toPrimitive", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "r", "_asyncIterator", "r", "AsyncFromSyncIterator", "_objectWithoutProperties", "value: {\n  path: readonly string[];\n}", "queryKey: TRPCQueryKey", "opts: TOptions", "infiniteParams?: {\n    pageParam: any;\n    direction: 'forward' | 'backward';\n  }", "asyncIterable: AsyncIterable<unknown>", "queryClient: QueryClient", "aggregate: unknown[]", "path: readonly string[]", "input?: unknown", "type?: QueryType", "valueOrLazy: T | (() => T)", "args: {\n  input: unknown;\n  query: typeof TRPCUntypedClient.prototype.query;\n  queryClient: QueryClient | (() => QueryClient);\n  path: readonly string[];\n  queryKey: TRPCQueryKey;\n  opts: AnyTRPCInfiniteQueryOptionsIn;\n}", "queryFn: QueryFunction<unknown, TRP<PERSON><PERSON><PERSON><PERSON><PERSON>, unknown>", "args: {\n  mutate: typeof TRPCUntypedClient.prototype.mutation;\n  queryClient: QueryClient | (() => QueryClient);\n  path: readonly string[];\n  opts: AnyTRPCMutationOptionsIn;\n  overrides: MutationOptionsOverride | undefined;\n}", "mutationSuccessOverride: MutationOptionsOverride['onSuccess']", "mutationFn: MutationFunction", "args", "args: {\n  input: unknown;\n  query: typeof TRPCUntypedClient.prototype.query;\n  queryClient: QueryClient | (() => QueryClient);\n  path: readonly string[];\n  queryKey: TRPCQueryKey;\n  opts: AnyTRPCQueryOptionsIn;\n}", "queryFn: QueryFunction<unknown, TRPCQueryKey>", "query<PERSON><PERSON>", "args: {\n  subscribe: typeof TRPCUntypedClient.prototype.subscription;\n  path: readonly string[];\n  queryKey: TRPCQueryKey;\n  opts?: AnyTRPCSubscriptionOptionsIn;\n}", "_subscribe: ReturnType<TRPCSubscriptionOptions<any>>['subscribe']", "opts: TRPCSubscriptionOptionsOut<TOutput, TError>", "key: keyof $Result", "callback: (prevState: $Result) => $Result", "result: T", "onTrackResult: (key: keyof T) => void", "opts: TRPCOptionsProxyOptions<TRouter>", "type: TRPCProcedureType", "path: string", "input: unknown", "trpcOpts: TRPCRequestOptions", "contextMap: Record<UtilsMethods, () => unknown>", "props: <PERSON><PERSON><PERSON><{\n      children: React.ReactNode;\n      queryClient: QueryClient;\n      trpcClient: TRPCClient<TRouter>;\n    }>"], "sources": ["../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/asyncIterator.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectWithoutPropertiesLoose.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectWithoutProperties.js", "../src/internals/utils.ts", "../src/internals/infiniteQueryOptions.ts", "../src/internals/mutationOptions.ts", "../src/internals/queryOptions.ts", "../src/internals/subscriptionOptions.ts", "../src/internals/createOptionsProxy.ts", "../src/internals/Context.tsx"], "sourcesContent": ["function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _typeof(o);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nmodule.exports = toPrimitive, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nvar toPrimitive = require(\"./toPrimitive.js\");\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nmodule.exports = toPropertyKey, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var defineProperty = require(\"./defineProperty.js\");\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nmodule.exports = _objectSpread2, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _asyncIterator(r) {\n  var n,\n    t,\n    o,\n    e = 2;\n  for (\"undefined\" != typeof Symbol && (t = Symbol.asyncIterator, o = Symbol.iterator); e--;) {\n    if (t && null != (n = r[t])) return n.call(r);\n    if (o && null != (n = r[o])) return new AsyncFromSyncIterator(n.call(r));\n    t = \"@@asyncIterator\", o = \"@@iterator\";\n  }\n  throw new TypeError(\"Object is not async iterable\");\n}\nfunction AsyncFromSyncIterator(r) {\n  function AsyncFromSyncIteratorContinuation(r) {\n    if (Object(r) !== r) return Promise.reject(new TypeError(r + \" is not an object.\"));\n    var n = r.done;\n    return Promise.resolve(r.value).then(function (r) {\n      return {\n        value: r,\n        done: n\n      };\n    });\n  }\n  return AsyncFromSyncIterator = function AsyncFromSyncIterator(r) {\n    this.s = r, this.n = r.next;\n  }, AsyncFromSyncIterator.prototype = {\n    s: null,\n    n: null,\n    next: function next() {\n      return AsyncFromSyncIteratorContinuation(this.n.apply(this.s, arguments));\n    },\n    \"return\": function _return(r) {\n      var n = this.s[\"return\"];\n      return void 0 === n ? Promise.resolve({\n        value: r,\n        done: !0\n      }) : AsyncFromSyncIteratorContinuation(n.apply(this.s, arguments));\n    },\n    \"throw\": function _throw(r) {\n      var n = this.s[\"return\"];\n      return void 0 === n ? Promise.reject(r) : AsyncFromSyncIteratorContinuation(n.apply(this.s, arguments));\n    }\n  }, new AsyncFromSyncIterator(r);\n}\nmodule.exports = _asyncIterator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (e.includes(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nmodule.exports = _objectWithoutPropertiesLoose, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var objectWithoutPropertiesLoose = require(\"./objectWithoutPropertiesLoose.js\");\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var s = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < s.length; r++) o = s[r], t.includes(o) || {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nmodule.exports = _objectWithoutProperties, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import { skipToken, type QueryClient } from '@tanstack/react-query';\nimport { isFunction, isObject } from '@trpc/server/unstable-core-do-not-import';\nimport type {\n  QueryType,\n  TRPCMutationKey,\n  TRPCQueryKey,\n  TRPCQueryOptionsResult,\n} from './types';\n\n/**\n * @internal\n */\nexport function createTRPCOptionsResult(value: {\n  path: readonly string[];\n}): TRPCQueryOptionsResult['trpc'] {\n  const path = value.path.join('.');\n\n  return {\n    path,\n  };\n}\n\n/**\n * @internal\n */\nexport function getClientArgs<TOptions>(\n  queryKey: TRPCQueryKey,\n  opts: TOptions,\n  infiniteParams?: {\n    pageParam: any;\n    direction: 'forward' | 'backward';\n  },\n) {\n  const path = queryKey[0];\n  let input = queryKey[1]?.input;\n  if (infiniteParams) {\n    input = {\n      ...(input ?? {}),\n      ...(infiniteParams.pageParam !== undefined\n        ? { cursor: infiniteParams.pageParam }\n        : {}),\n      direction: infiniteParams.direction,\n    };\n  }\n  return [path.join('.'), input, (opts as any)?.trpc] as const;\n}\n\n/**\n * @internal\n */\nexport async function buildQueryFromAsyncIterable(\n  asyncIterable: AsyncIterable<unknown>,\n  queryClient: QueryClient,\n  queryKey: TRPCQueryKey,\n) {\n  const queryCache = queryClient.getQueryCache();\n\n  const query = queryCache.build(queryClient, {\n    queryKey,\n  });\n\n  query.setState({\n    data: [],\n    status: 'success',\n  });\n\n  const aggregate: unknown[] = [];\n  for await (const value of asyncIterable) {\n    aggregate.push(value);\n\n    query.setState({\n      data: [...aggregate],\n    });\n  }\n  return aggregate;\n}\n\n/**\n * To allow easy interactions with groups of related queries, such as\n * invalidating all queries of a router, we use an array as the path when\n * storing in tanstack query.\n *\n * @internal\n */\nexport function getQueryKeyInternal(\n  path: readonly string[],\n  input?: unknown,\n  type?: QueryType,\n): TRPCQueryKey {\n  // Construct a query key that is easy to destructure and flexible for\n  // partial selecting etc.\n  // https://github.com/trpc/trpc/issues/3128\n\n  // some parts of the path may be dot-separated, split them up\n  const splitPath = path.flatMap((part) => part.split('.'));\n\n  if (!input && (!type || type === 'any')) {\n    // this matches also all mutations (see `getMutationKeyInternal`)\n\n    // for `utils.invalidate()` to match all queries (including vanilla react-query)\n    // we don't want nested array if path is empty, i.e. `[]` instead of `[[]]`\n    return splitPath.length ? [splitPath] : ([] as unknown as TRPCQueryKey);\n  }\n\n  if (\n    type === 'infinite' &&\n    isObject(input) &&\n    ('direction' in input || 'cursor' in input)\n  ) {\n    const {\n      cursor: _,\n      direction: __,\n      ...inputWithoutCursorAndDirection\n    } = input;\n    return [\n      splitPath,\n      {\n        input: inputWithoutCursorAndDirection,\n        type: 'infinite',\n      },\n    ];\n  }\n\n  return [\n    splitPath,\n    {\n      ...(typeof input !== 'undefined' &&\n        input !== skipToken && { input: input }),\n      ...(type && type !== 'any' && { type: type }),\n    },\n  ];\n}\n\n/**\n * @internal\n */\nexport function getMutationKeyInternal(\n  path: readonly string[],\n): TRPCMutationKey {\n  // some parts of the path may be dot-separated, split them up\n  const splitPath = path.flatMap((part) => part.split('.'));\n\n  return splitPath.length ? [splitPath] : ([] as unknown as TRPCMutationKey);\n}\n\n/**\n * @internal\n */\nexport function unwrapLazyArg<T>(valueOrLazy: T | (() => T)): T {\n  return (isFunction(valueOrLazy) ? valueOrLazy() : valueOrLazy) as T;\n}\n", "import type {\n  DataTag,\n  DefinedInitialDataInfiniteOptions,\n  QueryClient,\n  QueryFunction,\n  SkipToken,\n  UndefinedInitialDataInfiniteOptions,\n  UnusedSkipTokenInfiniteOptions,\n} from '@tanstack/react-query';\nimport { infiniteQueryOptions, skipToken } from '@tanstack/react-query';\nimport type { TRPCClientErrorLike, TRPCUntypedClient } from '@trpc/client';\nimport type { DistributiveOmit } from '@trpc/server/unstable-core-do-not-import';\nimport type {\n  ExtractCursorType,\n  ResolverDef,\n  TRPCInfiniteData,\n  TRPCQueryBaseOptions,\n  TRPCQueryKey,\n  TRPCQueryOptionsResult,\n} from './types';\nimport { createTRPCOptionsResult, getClientArgs } from './utils';\n\ntype ReservedOptions =\n  | 'queryKey'\n  | 'queryFn'\n  | 'queryHashFn'\n  | 'queryHash'\n  | 'initialPageParam';\n\ninterface UndefinedTRPCInfiniteQueryOptionsIn<\n  TInput,\n  TQueryFnData,\n  TData,\n  TError,\n> extends DistributiveOmit<\n      UndefinedInitialDataInfiniteOptions<\n        TQueryFnData,\n        TError,\n        TRPCInfiniteData<TInput, TData>,\n        TRPCQueryKey,\n        NonNullable<ExtractCursorType<TInput>> | null\n      >,\n      ReservedOptions\n    >,\n    TRPCQueryBaseOptions {\n  initialCursor?: NonNullable<ExtractCursorType<TInput>> | null;\n}\n\ninterface UndefinedTRPCInfiniteQueryOptionsOut<\n  TInput,\n  TQueryFnData,\n  TData,\n  TError,\n> extends DistributiveOmit<\n      UndefinedInitialDataInfiniteOptions<\n        TQueryFnData,\n        TError,\n        TRPCInfiniteData<TInput, TData>,\n        TRPCQueryKey,\n        NonNullable<ExtractCursorType<TInput>> | null\n      >,\n      'initialPageParam'\n    >,\n    TRPCQueryOptionsResult {\n  queryKey: DataTag<TRPCQueryKey, TRPCInfiniteData<TInput, TData>, TError>;\n  initialPageParam: NonNullable<ExtractCursorType<TInput>> | null;\n}\n\ninterface DefinedTRPCInfiniteQueryOptionsIn<TInput, TQueryFnData, TData, TError>\n  extends DistributiveOmit<\n      DefinedInitialDataInfiniteOptions<\n        TQueryFnData,\n        TError,\n        TRPCInfiniteData<TInput, TData>,\n        TRPCQueryKey,\n        NonNullable<ExtractCursorType<TInput>> | null\n      >,\n      ReservedOptions\n    >,\n    TRPCQueryBaseOptions {\n  initialCursor?: NonNullable<ExtractCursorType<TInput>> | null;\n}\n\ninterface DefinedTRPCInfiniteQueryOptionsOut<\n  TInput,\n  TQueryFnData,\n  TData,\n  TError,\n> extends DistributiveOmit<\n      DefinedInitialDataInfiniteOptions<\n        TQueryFnData,\n        TError,\n        TRPCInfiniteData<TInput, TData>,\n        TRPCQueryKey,\n        NonNullable<ExtractCursorType<TInput>> | null\n      >,\n      'initialPageParam'\n    >,\n    TRPCQueryOptionsResult {\n  queryKey: DataTag<TRPCQueryKey, TRPCInfiniteData<TInput, TData>, TError>;\n  initialPageParam: NonNullable<ExtractCursorType<TInput>> | null;\n}\n\ninterface UnusedSkipTokenTRPCInfiniteQueryOptionsIn<\n  TInput,\n  TQueryFnData,\n  TData,\n  TError,\n> extends DistributiveOmit<\n      UnusedSkipTokenInfiniteOptions<\n        TQueryFnData,\n        TError,\n        TRPCInfiniteData<TInput, TData>,\n        TRPCQueryKey,\n        NonNullable<ExtractCursorType<TInput>> | null\n      >,\n      ReservedOptions\n    >,\n    TRPCQueryBaseOptions {\n  initialCursor?: NonNullable<ExtractCursorType<TInput>> | null;\n}\n\ninterface UnusedSkipTokenTRPCInfiniteQueryOptionsOut<\n  TInput,\n  TQueryFnData,\n  TData,\n  TError,\n> extends DistributiveOmit<\n      UnusedSkipTokenInfiniteOptions<\n        TQueryFnData,\n        TError,\n        TRPCInfiniteData<TInput, TData>,\n        TRPCQueryKey,\n        NonNullable<ExtractCursorType<TInput>> | null\n      >,\n      'initialPageParam'\n    >,\n    TRPCQueryOptionsResult {\n  queryKey: DataTag<TRPCQueryKey, TRPCInfiniteData<TInput, TData>, TError>;\n  initialPageParam: NonNullable<ExtractCursorType<TInput>> | null;\n}\n\nexport interface TRPCInfiniteQueryOptions<TDef extends ResolverDef> {\n  <TQueryFnData extends TDef['output'], TData = TQueryFnData>(\n    input: TDef['input'] | SkipToken,\n    opts: DefinedTRPCInfiniteQueryOptionsIn<\n      TDef['input'],\n      TQueryFnData,\n      TData,\n      TRPCClientErrorLike<{\n        transformer: TDef['transformer'];\n        errorShape: TDef['errorShape'];\n      }>\n    >,\n  ): DefinedTRPCInfiniteQueryOptionsOut<\n    TDef['input'],\n    TQueryFnData,\n    TData,\n    TRPCClientErrorLike<{\n      transformer: TDef['transformer'];\n      errorShape: TDef['errorShape'];\n    }>\n  >;\n  <TQueryFnData extends TDef['output'], TData = TQueryFnData>(\n    input: TDef['input'],\n    opts: UnusedSkipTokenTRPCInfiniteQueryOptionsIn<\n      TDef['input'],\n      TQueryFnData,\n      TData,\n      TRPCClientErrorLike<{\n        transformer: TDef['transformer'];\n        errorShape: TDef['errorShape'];\n      }>\n    >,\n  ): UnusedSkipTokenTRPCInfiniteQueryOptionsOut<\n    TDef['input'],\n    TQueryFnData,\n    TData,\n    TRPCClientErrorLike<{\n      transformer: TDef['transformer'];\n      errorShape: TDef['errorShape'];\n    }>\n  >;\n  <TQueryFnData extends TDef['output'], TData = TQueryFnData>(\n    input: TDef['input'] | SkipToken,\n    opts?: UndefinedTRPCInfiniteQueryOptionsIn<\n      TDef['input'],\n      TQueryFnData,\n      TData,\n      TRPCClientErrorLike<{\n        transformer: TDef['transformer'];\n        errorShape: TDef['errorShape'];\n      }>\n    >,\n  ): UndefinedTRPCInfiniteQueryOptionsOut<\n    TDef['input'],\n    TQueryFnData,\n    TData,\n    TRPCClientErrorLike<{\n      transformer: TDef['transformer'];\n      errorShape: TDef['errorShape'];\n    }>\n  >;\n}\n\ntype AnyTRPCInfiniteQueryOptionsIn =\n  | DefinedTRPCInfiniteQueryOptionsIn<any, any, any, any>\n  | UnusedSkipTokenTRPCInfiniteQueryOptionsIn<any, any, any, any>\n  | UndefinedTRPCInfiniteQueryOptionsIn<any, any, any, any>;\n\ntype AnyTRPCInfiniteQueryOptionsOut =\n  | DefinedTRPCInfiniteQueryOptionsOut<any, any, any, any>\n  | UnusedSkipTokenTRPCInfiniteQueryOptionsOut<any, any, any, any>\n  | UndefinedTRPCInfiniteQueryOptionsOut<any, any, any, any>;\n\nexport function trpcInfiniteQueryOptions(args: {\n  input: unknown;\n  query: typeof TRPCUntypedClient.prototype.query;\n  queryClient: QueryClient | (() => QueryClient);\n  path: readonly string[];\n  queryKey: TRPCQueryKey;\n  opts: AnyTRPCInfiniteQueryOptionsIn;\n}): AnyTRPCInfiniteQueryOptionsOut {\n  const { input, query, path, queryKey, opts } = args;\n  const inputIsSkipToken = input === skipToken;\n\n  const queryFn: QueryFunction<unknown, TRPCQueryKey, unknown> = async (\n    queryFnContext,\n  ) => {\n    const actualOpts = {\n      ...opts,\n      trpc: {\n        ...opts?.trpc,\n        ...(opts?.trpc?.abortOnUnmount\n          ? { signal: queryFnContext.signal }\n          : { signal: null }),\n      },\n    };\n\n    const result = await query(\n      ...getClientArgs(queryKey, actualOpts, {\n        direction: queryFnContext.direction,\n        pageParam: queryFnContext.pageParam,\n      }),\n    );\n\n    return result;\n  };\n\n  return Object.assign(\n    infiniteQueryOptions({\n      ...opts,\n      queryKey,\n      queryFn: inputIsSkipToken ? skipToken : queryFn,\n      initialPageParam: opts?.initialCursor ?? (input as any)?.cursor,\n    }),\n    { trpc: createTRPCOptionsResult({ path }) },\n  );\n}\n", "import type {\n  MutationFunction,\n  QueryClient,\n  UseMutationOptions,\n} from '@tanstack/react-query';\nimport type { TRPCClientErrorLike, TRPCUntypedClient } from '@trpc/client';\nimport type {\n  DistributiveOmit,\n  MaybePromise,\n} from '@trpc/server/unstable-core-do-not-import';\nimport type {\n  ResolverDef,\n  TRPCMutationKey,\n  TRPCQueryBaseOptions,\n  TRPCQueryOptionsResult,\n} from './types';\nimport {\n  createTRPCOptionsResult,\n  getClientArgs,\n  getMutationKeyInternal,\n  unwrapLazyArg,\n} from './utils';\n\ntype ReservedOptions = 'mutationKey' | 'mutationFn';\n\ninterface TRPCMutationOptionsIn<TInput, TError, TOutput, TContext>\n  extends DistributiveOmit<\n      UseMutationOptions<TOutput, TError, TInput, TContext>,\n      ReservedOptions\n    >,\n    TRPCQueryBaseOptions {}\n\ninterface TRPCMutationOptionsOut<TInput, TError, TOutput, TContext>\n  extends UseMutationOptions<TOutput, TError, TInput, TContext>,\n    TRPCQueryOptionsResult {\n  mutationKey: TRPCMutationKey;\n}\n\nexport interface TRPCMutationOptions<TDef extends ResolverDef> {\n  <TContext = unknown>(\n    opts?: TRPCMutationOptionsIn<\n      TDef['input'],\n      TRPCClientErrorLike<TDef>,\n      TDef['output'],\n      TContext\n    >,\n  ): TRPCMutationOptionsOut<\n    TDef['input'],\n    TRPCClientErrorLike<TDef>,\n    TDef['output'],\n    TContext\n  >;\n}\n\n/**\n * @internal\n */\nexport interface MutationOptionsOverride {\n  onSuccess: (opts: {\n    /**\n     * Calls the original function that was defined in the query's `onSuccess` option\n     */\n    originalFn: () => MaybePromise<void>;\n    queryClient: QueryClient;\n    /**\n     * Meta data passed in from the `useMutation()` hook\n     */\n    meta: Record<string, unknown>;\n  }) => MaybePromise<void>;\n}\n\ntype AnyTRPCMutationOptionsIn = TRPCMutationOptionsIn<\n  unknown,\n  unknown,\n  unknown,\n  unknown\n>;\n\ntype AnyTRPCMutationOptionsOut = TRPCMutationOptionsOut<\n  unknown,\n  unknown,\n  unknown,\n  unknown\n>;\n\n/**\n * @internal\n */\nexport function trpcMutationOptions(args: {\n  mutate: typeof TRPCUntypedClient.prototype.mutation;\n  queryClient: QueryClient | (() => QueryClient);\n  path: readonly string[];\n  opts: AnyTRPCMutationOptionsIn;\n  overrides: MutationOptionsOverride | undefined;\n}): AnyTRPCMutationOptionsOut {\n  const { mutate, path, opts, overrides } = args;\n  const queryClient = unwrapLazyArg(args.queryClient);\n\n  const mutationKey = getMutationKeyInternal(path);\n\n  const defaultOpts = queryClient.defaultMutationOptions(\n    queryClient.getMutationDefaults(mutationKey),\n  );\n\n  const mutationSuccessOverride: MutationOptionsOverride['onSuccess'] =\n    overrides?.onSuccess ?? ((options) => options.originalFn());\n\n  const mutationFn: MutationFunction = async (input) => {\n    const result = await mutate(...getClientArgs([path, { input }], opts));\n\n    return result;\n  };\n\n  return {\n    ...opts,\n    mutationKey: mutationKey,\n    mutationFn,\n    onSuccess(...args) {\n      const originalFn = () =>\n        opts?.onSuccess?.(...args) ?? defaultOpts?.onSuccess?.(...args);\n\n      return mutationSuccessOverride({\n        originalFn,\n        queryClient,\n        meta: opts?.meta ?? defaultOpts?.meta ?? {},\n      });\n    },\n    trpc: createTRPCOptionsResult({ path }),\n  };\n}\n", "import type {\n  DataTag,\n  DefinedInitialDataOptions,\n  QueryClient,\n  QueryFunction,\n  SkipToken,\n  UndefinedInitialDataOptions,\n  UnusedSkipTokenOptions,\n} from '@tanstack/react-query';\nimport { queryOptions, skipToken } from '@tanstack/react-query';\nimport type { TRPCClientErrorLike, TRPCUntypedClient } from '@trpc/client';\nimport type {\n  coerceAsyncIterableToArray,\n  DistributiveOmit,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { isAsyncIterable } from '@trpc/server/unstable-core-do-not-import';\nimport type {\n  ResolverDef,\n  TRPCQueryBaseOptions,\n  TRPCQueryKey,\n  TRPCQueryOptionsResult,\n} from './types';\nimport {\n  buildQueryFromAsyncIterable,\n  createTRPCOptionsResult,\n  getClientArgs,\n  unwrapLazyArg,\n} from './utils';\n\ntype ReservedOptions = 'queryKey' | 'queryFn' | 'queryHashFn' | 'queryHash';\n\ninterface UndefinedTRPCQueryOptionsIn<TQueryFnData, TData, TError>\n  extends DistributiveOmit<\n      UndefinedInitialDataOptions<\n        coerceAsyncIterableToArray<TQueryFnData>,\n        TError,\n        coerceAsyncIterableToArray<TData>,\n        TRPCQueryKey\n      >,\n      ReservedOptions\n    >,\n    TRPCQueryBaseOptions {}\n\ninterface UndefinedTRPCQueryOptionsOut<TQueryFnData, TOutput, TError>\n  extends UndefinedInitialDataOptions<\n      coerceAsyncIterableToArray<TQueryFnData>,\n      TError,\n      coerceAsyncIterableToArray<TOutput>,\n      TRPCQueryKey\n    >,\n    TRPCQueryOptionsResult {\n  queryKey: DataTag<TRPCQueryKey, coerceAsyncIterableToArray<TOutput>, TError>;\n}\n\ninterface DefinedTRPCQueryOptionsIn<TQueryFnData, TData, TError>\n  extends DistributiveOmit<\n      DefinedInitialDataOptions<\n        coerceAsyncIterableToArray<NoInfer<TQueryFnData>>,\n        TError,\n        coerceAsyncIterableToArray<TData>,\n        TRPCQueryKey\n      >,\n      ReservedOptions\n    >,\n    TRPCQueryBaseOptions {}\n\ninterface DefinedTRPCQueryOptionsOut<TQueryFnData, TData, TError>\n  extends DefinedInitialDataOptions<\n      coerceAsyncIterableToArray<TQueryFnData>,\n      TError,\n      coerceAsyncIterableToArray<TData>,\n      TRPCQueryKey\n    >,\n    TRPCQueryOptionsResult {\n  queryKey: DataTag<TRPCQueryKey, coerceAsyncIterableToArray<TData>, TError>;\n}\n\ninterface UnusedSkipTokenTRPCQueryOptionsIn<TQueryFnData, TData, TError>\n  extends DistributiveOmit<\n      UnusedSkipTokenOptions<\n        coerceAsyncIterableToArray<TQueryFnData>,\n        TError,\n        coerceAsyncIterableToArray<TData>,\n        TRPCQueryKey\n      >,\n      ReservedOptions\n    >,\n    TRPCQueryBaseOptions {}\n\ninterface UnusedSkipTokenTRPCQueryOptionsOut<TQueryFnData, TOutput, TError>\n  extends UnusedSkipTokenOptions<\n      coerceAsyncIterableToArray<TQueryFnData>,\n      TError,\n      coerceAsyncIterableToArray<TOutput>,\n      TRPCQueryKey\n    >,\n    TRPCQueryOptionsResult {\n  queryKey: DataTag<TRPCQueryKey, coerceAsyncIterableToArray<TOutput>, TError>;\n}\n\nexport interface TRPCQueryOptions<TDef extends ResolverDef> {\n  <TQueryFnData extends TDef['output'], TData = TQueryFnData>(\n    input: TDef['input'] | SkipToken,\n    opts: DefinedTRPCQueryOptionsIn<\n      TQueryFnData,\n      TData,\n      TRPCClientErrorLike<{\n        transformer: TDef['transformer'];\n        errorShape: TDef['errorShape'];\n      }>\n    >,\n  ): DefinedTRPCQueryOptionsOut<\n    TQueryFnData,\n    TData,\n    TRPCClientErrorLike<{\n      transformer: TDef['transformer'];\n      errorShape: TDef['errorShape'];\n    }>\n  >;\n  <TQueryFnData extends TDef['output'], TData = TQueryFnData>(\n    input: TDef['input'],\n    opts?: UnusedSkipTokenTRPCQueryOptionsIn<\n      TQueryFnData,\n      TData,\n      TRPCClientErrorLike<{\n        transformer: TDef['transformer'];\n        errorShape: TDef['errorShape'];\n      }>\n    >,\n  ): UnusedSkipTokenTRPCQueryOptionsOut<\n    TQueryFnData,\n    TData,\n    TRPCClientErrorLike<{\n      transformer: TDef['transformer'];\n      errorShape: TDef['errorShape'];\n    }>\n  >;\n  <TQueryFnData extends TDef['output'], TData = TQueryFnData>(\n    input: TDef['input'] | SkipToken,\n    opts?: UndefinedTRPCQueryOptionsIn<\n      TQueryFnData,\n      TData,\n      TRPCClientErrorLike<{\n        transformer: TDef['transformer'];\n        errorShape: TDef['errorShape'];\n      }>\n    >,\n  ): UndefinedTRPCQueryOptionsOut<\n    TQueryFnData,\n    TData,\n    TRPCClientErrorLike<{\n      transformer: TDef['transformer'];\n      errorShape: TDef['errorShape'];\n    }>\n  >;\n}\n\ntype AnyTRPCQueryOptionsIn =\n  | DefinedTRPCQueryOptionsIn<unknown, unknown, unknown>\n  | UnusedSkipTokenTRPCQueryOptionsIn<unknown, unknown, unknown>\n  | UndefinedTRPCQueryOptionsIn<unknown, unknown, unknown>;\n\ntype AnyTRPCQueryOptionsOut =\n  | DefinedTRPCQueryOptionsOut<unknown, unknown, unknown>\n  | UnusedSkipTokenTRPCQueryOptionsOut<unknown, unknown, unknown>\n  | UndefinedTRPCQueryOptionsOut<unknown, unknown, unknown>;\n\n/**\n * @internal\n */\nexport function trpcQueryOptions(args: {\n  input: unknown;\n  query: typeof TRPCUntypedClient.prototype.query;\n  queryClient: QueryClient | (() => QueryClient);\n  path: readonly string[];\n  queryKey: TRPCQueryKey;\n  opts: AnyTRPCQueryOptionsIn;\n}): AnyTRPCQueryOptionsOut {\n  const { input, query, path, queryKey, opts } = args;\n  const queryClient = unwrapLazyArg(args.queryClient);\n\n  const inputIsSkipToken = input === skipToken;\n\n  const queryFn: QueryFunction<unknown, TRPCQueryKey> = async (\n    queryFnContext,\n  ) => {\n    const actualOpts = {\n      ...opts,\n      trpc: {\n        ...opts?.trpc,\n        ...(opts?.trpc?.abortOnUnmount\n          ? { signal: queryFnContext.signal }\n          : { signal: null }),\n      },\n    };\n    const queryKey = queryFnContext.queryKey;\n\n    const result = await query(...getClientArgs(queryKey, actualOpts));\n\n    if (isAsyncIterable(result)) {\n      return buildQueryFromAsyncIterable(result, queryClient, queryKey);\n    }\n\n    return result;\n  };\n\n  return Object.assign(\n    queryOptions({\n      ...opts,\n      queryKey,\n      queryFn: inputIsSkipToken ? skipToken : queryFn,\n    }),\n    { trpc: createTRPCOptionsResult({ path }) },\n  );\n}\n", "import type { SkipToken } from '@tanstack/react-query';\nimport { hashKey, skipToken } from '@tanstack/react-query';\nimport type { TRPCClientErrorLike, TRPCUntypedClient } from '@trpc/client';\nimport type { TRPCConnectionState } from '@trpc/client/unstable-internals';\nimport type { Unsubscribable } from '@trpc/server/observable';\nimport type { inferAsyncIterableYield } from '@trpc/server/unstable-core-do-not-import';\nimport * as React from 'react';\nimport type {\n  ResolverDef,\n  TRPCQueryKey,\n  TRPCQueryOptionsResult,\n} from './types';\nimport { createTRPCOptionsResult } from './utils';\n\ninterface BaseTRPCSubscriptionOptionsIn<TOutput, TError> {\n  enabled?: boolean;\n  onStarted?: () => void;\n  onData?: (data: inferAsyncIterableYield<TOutput>) => void;\n  onError?: (err: TError) => void;\n  onConnectionStateChange?: (state: TRPCConnectionState<TError>) => void;\n}\n\ninterface UnusedSkipTokenTRPCSubscriptionOptionsIn<TOutput, TError> {\n  onStarted?: () => void;\n  onData?: (data: inferAsyncIterableYield<TOutput>) => void;\n  onError?: (err: TError) => void;\n  onConnectionStateChange?: (state: TRPCConnectionState<TError>) => void;\n}\n\ninterface TRPCSubscriptionOptionsOut<TOutput, TError>\n  extends UnusedSkipTokenTRPCSubscriptionOptionsIn<TOutput, TError>,\n    TRPCQueryOptionsResult {\n  enabled: boolean;\n  queryKey: TRPCQueryKey;\n  subscribe: (\n    innerOpts: UnusedSkipTokenTRPCSubscriptionOptionsIn<TOutput, TError>,\n  ) => Unsubscribable;\n}\n\nexport interface TRPCSubscriptionOptions<TDef extends ResolverDef> {\n  (\n    input: TDef['input'],\n    opts?: UnusedSkipTokenTRPCSubscriptionOptionsIn<\n      inferAsyncIterableYield<TDef['output']>,\n      TRPCClientErrorLike<TDef>\n    >,\n  ): TRPCSubscriptionOptionsOut<\n    inferAsyncIterableYield<TDef['output']>,\n    TRPCClientErrorLike<TDef>\n  >;\n  (\n    input: TDef['input'] | SkipToken,\n    opts?: BaseTRPCSubscriptionOptionsIn<\n      inferAsyncIterableYield<TDef['output']>,\n      TRPCClientErrorLike<TDef>\n    >,\n  ): TRPCSubscriptionOptionsOut<\n    inferAsyncIterableYield<TDef['output']>,\n    TRPCClientErrorLike<TDef>\n  >;\n}\nexport type TRPCSubscriptionStatus =\n  | 'idle'\n  | 'connecting'\n  | 'pending'\n  | 'error';\n\nexport interface TRPCSubscriptionBaseResult<TOutput, TError> {\n  status: TRPCSubscriptionStatus;\n  data: undefined | TOutput;\n  error: null | TError;\n  /**\n   * Reset the subscription\n   */\n  reset: () => void;\n}\n\nexport interface TRPCSubscriptionIdleResult<TOutput>\n  extends TRPCSubscriptionBaseResult<TOutput, null> {\n  status: 'idle';\n  data: undefined;\n  error: null;\n}\n\nexport interface TRPCSubscriptionConnectingResult<TOutput, TError>\n  extends TRPCSubscriptionBaseResult<TOutput, TError> {\n  status: 'connecting';\n  data: undefined | TOutput;\n  error: TError | null;\n}\n\nexport interface TRPCSubscriptionPendingResult<TOutput>\n  extends TRPCSubscriptionBaseResult<TOutput, undefined> {\n  status: 'pending';\n  data: TOutput | undefined;\n  error: null;\n}\n\nexport interface TRPCSubscriptionErrorResult<TOutput, TError>\n  extends TRPCSubscriptionBaseResult<TOutput, TError> {\n  status: 'error';\n  data: TOutput | undefined;\n  error: TError;\n}\n\nexport type TRPCSubscriptionResult<TOutput, TError> =\n  | TRPCSubscriptionIdleResult<TOutput>\n  | TRPCSubscriptionConnectingResult<TOutput, TError>\n  | TRPCSubscriptionErrorResult<TOutput, TError>\n  | TRPCSubscriptionPendingResult<TOutput>;\n\ntype AnyTRPCSubscriptionOptionsIn =\n  | BaseTRPCSubscriptionOptionsIn<unknown, unknown>\n  | UnusedSkipTokenTRPCSubscriptionOptionsIn<unknown, unknown>;\n\ntype AnyTRPCSubscriptionOptionsOut = TRPCSubscriptionOptionsOut<\n  unknown,\n  unknown\n>;\n\n/**\n * @internal\n */\nexport const trpcSubscriptionOptions = (args: {\n  subscribe: typeof TRPCUntypedClient.prototype.subscription;\n  path: readonly string[];\n  queryKey: TRPCQueryKey;\n  opts?: AnyTRPCSubscriptionOptionsIn;\n}): AnyTRPCSubscriptionOptionsOut => {\n  const { subscribe, path, queryKey, opts = {} } = args;\n  const input = queryKey[1]?.input;\n  const enabled = 'enabled' in opts ? !!opts.enabled : input !== skipToken;\n\n  const _subscribe: ReturnType<TRPCSubscriptionOptions<any>>['subscribe'] = (\n    innerOpts,\n  ) => {\n    return subscribe(path.join('.'), input ?? undefined, innerOpts);\n  };\n\n  return {\n    ...opts,\n    enabled,\n    subscribe: _subscribe,\n    queryKey,\n    trpc: createTRPCOptionsResult({ path }),\n  };\n};\n\nexport function useSubscription<TOutput, TError>(\n  opts: TRPCSubscriptionOptionsOut<TOutput, TError>,\n): TRPCSubscriptionResult<TOutput, TError> {\n  type $Result = TRPCSubscriptionResult<TOutput, TError>;\n\n  const optsRef = React.useRef(opts);\n  optsRef.current = opts;\n\n  const trackedProps = React.useRef(new Set<keyof $Result>([]));\n\n  const addTrackedProp = React.useCallback((key: keyof $Result) => {\n    trackedProps.current.add(key);\n  }, []);\n\n  type Unsubscribe = () => void;\n  const currentSubscriptionRef = React.useRef<Unsubscribe>(() => {\n    // noop\n  });\n\n  const reset = React.useCallback((): void => {\n    // unsubscribe from the previous subscription\n    currentSubscriptionRef.current?.();\n\n    updateState(getInitialState);\n    if (!opts.enabled) {\n      return;\n    }\n    const subscription = opts.subscribe({\n      onStarted: () => {\n        optsRef.current.onStarted?.();\n        updateState((prev) => ({\n          ...(prev as any),\n          status: 'pending',\n          error: null,\n        }));\n      },\n      onData: (data) => {\n        optsRef.current.onData?.(data);\n        updateState((prev) => ({\n          ...(prev as any),\n          status: 'pending',\n          data,\n          error: null,\n        }));\n      },\n      onError: (error) => {\n        optsRef.current.onError?.(error);\n        updateState((prev) => ({\n          ...(prev as any),\n          status: 'error',\n          error,\n        }));\n      },\n      onConnectionStateChange: (result) => {\n        optsRef.current.onConnectionStateChange?.(result);\n        updateState((prev) => {\n          switch (result.state) {\n            case 'connecting':\n              return {\n                ...prev,\n                status: 'connecting',\n                error: result.error,\n              };\n            case 'pending':\n              // handled in onStarted\n              return prev;\n            case 'idle':\n              return {\n                ...prev,\n                status: 'idle',\n                data: undefined,\n                error: null,\n              };\n          }\n        });\n      },\n    });\n\n    currentSubscriptionRef.current = () => {\n      subscription.unsubscribe();\n    };\n    // eslint-disable-next-line react-hooks/react-compiler\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [hashKey(opts.queryKey), opts.enabled]);\n\n  const getInitialState = React.useCallback((): $Result => {\n    return opts.enabled\n      ? {\n          data: undefined,\n          error: null,\n          status: 'connecting',\n          reset,\n        }\n      : {\n          data: undefined,\n          error: null,\n          status: 'idle',\n          reset,\n        };\n  }, [opts.enabled, reset]);\n\n  const resultRef = React.useRef<$Result>(getInitialState());\n\n  const [state, setState] = React.useState<$Result>(\n    trackResult(resultRef.current, addTrackedProp),\n  );\n\n  state.reset = reset;\n\n  const updateState = React.useCallback(\n    (callback: (prevState: $Result) => $Result) => {\n      const prev = resultRef.current;\n      const next = (resultRef.current = callback(prev));\n\n      let shouldUpdate = false;\n      for (const key of trackedProps.current) {\n        if (prev[key] !== next[key]) {\n          shouldUpdate = true;\n          break;\n        }\n      }\n      if (shouldUpdate) {\n        setState(trackResult(next, addTrackedProp));\n      }\n    },\n    [addTrackedProp],\n  );\n\n  React.useEffect(() => {\n    if (!opts.enabled) {\n      return;\n    }\n    reset();\n\n    return () => {\n      currentSubscriptionRef.current?.();\n    };\n  }, [reset, opts.enabled]);\n\n  return state;\n}\n\nfunction trackResult<T extends object>(\n  result: T,\n  onTrackResult: (key: keyof T) => void,\n): T {\n  const trackedResult = new Proxy(result, {\n    get(target, prop) {\n      onTrackResult(prop as keyof T);\n      return target[prop as keyof T];\n    },\n  });\n\n  return trackedResult;\n}\n", "import type { DataTag, QueryClient, QueryFilters } from '@tanstack/react-query';\nimport type {\n  TRPCClient,\n  TRPCClientErrorLike,\n  TRPCRequestOptions,\n} from '@trpc/client';\nimport { getUntypedClient, TRPCUntypedClient } from '@trpc/client';\nimport type {\n  AnyTRPCProcedure,\n  AnyTRPCRootTypes,\n  AnyTRPCRouter,\n  inferProcedureInput,\n  inferRouterContext,\n  inferTransformedProcedureOutput,\n  TRPCProcedureType,\n  TRPCRouterRecord,\n} from '@trpc/server';\nimport { callTRPCProcedure, createTRPCRecursiveProxy } from '@trpc/server';\nimport type { MaybePromise } from '@trpc/server/unstable-core-do-not-import';\nimport {\n  trpcInfiniteQueryOptions,\n  type TRPCInfiniteQueryOptions,\n} from './infiniteQueryOptions';\nimport type { MutationOptionsOverride } from './mutationOptions';\nimport {\n  trpcMutationOptions,\n  type TRPCMutationOptions,\n} from './mutationOptions';\nimport { trpcQueryOptions, type TRPCQueryOptions } from './queryOptions';\nimport {\n  trpcSubscriptionOptions,\n  type TRPCSubscriptionOptions,\n} from './subscriptionOptions';\nimport type {\n  OptionalCursorInput,\n  ResolverDef,\n  TRPCInfiniteData,\n  TRPCMutationKey,\n  TRPCQueryKey,\n  WithRequired,\n} from './types';\nimport {\n  getMutationKeyInternal,\n  getQueryKeyInternal,\n  unwrapLazyArg,\n} from './utils';\n\nexport interface DecorateRouterKeyable {\n  /**\n   * Calculate the TanStack Query Key for any path, could be used to invalidate every procedure beneath this path\n   *\n   * @see https://tanstack.com/query/latest/docs/framework/react/guides/query-keys\n   * @see https://trpc.io/docs/client/tanstack-react-query/usage#queryKey\n   */\n  pathKey: () => TRPCQueryKey;\n\n  /**\n   * Calculate a TanStack Query Filter for any path, could be used to manipulate every procedure beneath this path\n   *\n   * @see https://tanstack.com/query/latest/docs/framework/react/guides/filters\n   * @see https://trpc.io/docs/client/tanstack-react-query/usage#queryFilter\n   */\n  pathFilter: (\n    filters?: QueryFilters<TRPCQueryKey>,\n  ) => WithRequired<QueryFilters<TRPCQueryKey>, 'queryKey'>;\n}\n\ninterface TypeHelper<TDef extends ResolverDef> {\n  /**\n   * @internal prefer using inferInput and inferOutput to access types\n   */\n  '~types': {\n    input: TDef['input'];\n    output: TDef['output'];\n    errorShape: TDef['errorShape'];\n  };\n}\n\nexport type inferInput<\n  TProcedure extends\n    | DecorateInfiniteQueryProcedure<any>\n    | DecorateQueryProcedure<any>\n    | DecorateMutationProcedure<any>,\n> = TProcedure['~types']['input'];\n\nexport type inferOutput<\n  TProcedure extends\n    | DecorateInfiniteQueryProcedure<any>\n    | DecorateQueryProcedure<any>\n    | DecorateMutationProcedure<any>,\n> = TProcedure['~types']['output'];\n\nexport interface DecorateInfiniteQueryProcedure<TDef extends ResolverDef>\n  extends TypeHelper<TDef> {\n  /**\n   * Create a set of type-safe infinite query options that can be passed to `useInfiniteQuery`, `prefetchInfiniteQuery` etc.\n   *\n   * @see https://tanstack.com/query/latest/docs/framework/react/reference/infiniteQueryOptions#infinitequeryoptions\n   * @see https://trpc.io/docs/client/tanstack-react-query/usage#infiniteQueryOptions\n   */\n  infiniteQueryOptions: TRPCInfiniteQueryOptions<TDef>;\n\n  /**\n   * Calculate the TanStack Query Key for a Infinite Query Procedure\n   *\n   * @see https://tanstack.com/query/latest/docs/framework/react/guides/query-keys\n   * @see https://trpc.io/docs/client/tanstack-react-query/usage#queryKey\n   */\n  infiniteQueryKey: (input?: Partial<TDef['input']>) => DataTag<\n    TRPCQueryKey,\n    TRPCInfiniteData<TDef['input'], TDef['output']>,\n    TRPCClientErrorLike<{\n      transformer: TDef['transformer'];\n      errorShape: TDef['errorShape'];\n    }>\n  >;\n\n  /**\n   * Calculate a TanStack Query Filter for a Infinite Query Procedure\n   *\n   * @see https://tanstack.com/query/latest/docs/framework/react/guides/filters\n   * @see https://trpc.io/docs/client/tanstack-react-query/usage#queryFilter\n   */\n  infiniteQueryFilter: (\n    input?: Partial<TDef['input']>,\n    filters?: QueryFilters<\n      DataTag<\n        TRPCQueryKey,\n        TRPCInfiniteData<TDef['input'], TDef['output']>,\n        TRPCClientErrorLike<{\n          transformer: TDef['transformer'];\n          errorShape: TDef['errorShape'];\n        }>\n      >\n    >,\n  ) => WithRequired<\n    QueryFilters<\n      DataTag<\n        TRPCQueryKey,\n        TRPCInfiniteData<TDef['input'], TDef['output']>,\n        TRPCClientErrorLike<{\n          transformer: TDef['transformer'];\n          errorShape: TDef['errorShape'];\n        }>\n      >\n    >,\n    'queryKey'\n  >;\n}\nexport interface DecorateQueryProcedure<TDef extends ResolverDef>\n  extends TypeHelper<TDef>,\n    DecorateRouterKeyable {\n  /**\n   * Create a set of type-safe query options that can be passed to `useQuery`, `prefetchQuery` etc.\n   *\n   * @see https://tanstack.com/query/latest/docs/framework/react/reference/queryOptions#queryoptions\n   * @see https://trpc.io/docs/client/tanstack-react-query/usage#queryOptions\n   */\n  queryOptions: TRPCQueryOptions<TDef>;\n\n  /**\n   * Calculate the TanStack Query Key for a Query Procedure\n   *\n   * @see https://tanstack.com/query/latest/docs/framework/react/guides/query-keys\n   * @see https://trpc.io/docs/client/tanstack-react-query/usage#queryKey\n   */\n  queryKey: (input?: Partial<TDef['input']>) => DataTag<\n    TRPCQueryKey,\n    TDef['output'],\n    TRPCClientErrorLike<{\n      transformer: TDef['transformer'];\n      errorShape: TDef['errorShape'];\n    }>\n  >;\n\n  /**\n   * Calculate a TanStack Query Filter for a Query Procedure\n   *\n   * @see https://tanstack.com/query/latest/docs/framework/react/guides/filters\n   * @see https://trpc.io/docs/client/tanstack-react-query/usage#queryFilter\n   */\n  queryFilter: (\n    input?: Partial<TDef['input']>,\n    filters?: QueryFilters<\n      DataTag<\n        TRPCQueryKey,\n        TDef['output'],\n        TRPCClientErrorLike<{\n          transformer: TDef['transformer'];\n          errorShape: TDef['errorShape'];\n        }>\n      >\n    >,\n  ) => WithRequired<\n    QueryFilters<\n      DataTag<\n        TRPCQueryKey,\n        TDef['output'],\n        TRPCClientErrorLike<{\n          transformer: TDef['transformer'];\n          errorShape: TDef['errorShape'];\n        }>\n      >\n    >,\n    'queryKey'\n  >;\n}\n\nexport interface DecorateMutationProcedure<TDef extends ResolverDef>\n  extends TypeHelper<TDef> {\n  /**\n   * Create a set of type-safe mutation options that can be passed to `useMutation`\n   *\n   * @see https://trpc.io/docs/client/tanstack-react-query/usage#mutationOptions\n   */\n  mutationOptions: TRPCMutationOptions<TDef>;\n\n  /**\n   * Calculate the TanStack Mutation Key for a Mutation Procedure\n   *\n   * @see https://trpc.io/docs/client/tanstack-react-query/usage#mutationKey\n   */\n  mutationKey: () => TRPCMutationKey;\n}\n\nexport interface DecorateSubscriptionProcedure<TDef extends ResolverDef> {\n  /**\n   * Create a set of type-safe subscription options that can be passed to `useSubscription`\n   *\n   * @see https://trpc.io/docs/client/tanstack-react-query/usage#subscriptionOptions\n   */\n  subscriptionOptions: TRPCSubscriptionOptions<TDef>;\n}\n\nexport type DecorateProcedure<\n  TType extends TRPCProcedureType,\n  TDef extends ResolverDef,\n> = TType extends 'query'\n  ? DecorateQueryProcedure<TDef> &\n      (TDef['input'] extends OptionalCursorInput\n        ? DecorateInfiniteQueryProcedure<TDef>\n        : Record<string, never>)\n  : TType extends 'mutation'\n    ? DecorateMutationProcedure<TDef>\n    : TType extends 'subscription'\n      ? DecorateSubscriptionProcedure<TDef>\n      : never;\n\n/**\n * @internal\n */\nexport type DecoratedRouterRecord<\n  TRoot extends AnyTRPCRootTypes,\n  TRecord extends TRPCRouterRecord,\n> = {\n  [TKey in keyof TRecord]: TRecord[TKey] extends infer $Value\n    ? $Value extends TRPCRouterRecord\n      ? DecoratedRouterRecord<TRoot, $Value> & DecorateRouterKeyable\n      : $Value extends AnyTRPCProcedure\n        ? DecorateProcedure<\n            $Value['_def']['type'],\n            {\n              input: inferProcedureInput<$Value>;\n              output: inferTransformedProcedureOutput<TRoot, $Value>;\n              transformer: TRoot['transformer'];\n              errorShape: TRoot['errorShape'];\n            }\n          >\n        : never\n    : never;\n};\n\nexport type TRPCOptionsProxy<TRouter extends AnyTRPCRouter> =\n  DecoratedRouterRecord<\n    TRouter['_def']['_config']['$types'],\n    TRouter['_def']['record']\n  > &\n    DecorateRouterKeyable;\n\nexport interface TRPCOptionsProxyOptionsBase {\n  queryClient: QueryClient | (() => QueryClient);\n  overrides?: {\n    mutations?: MutationOptionsOverride;\n  };\n}\n\nexport interface TRPCOptionsProxyOptionsInternal<\n  TRouter extends AnyTRPCRouter,\n> {\n  router: TRouter;\n  ctx:\n    | inferRouterContext<TRouter>\n    | (() => MaybePromise<inferRouterContext<TRouter>>);\n}\n\nexport interface TRPCOptionsProxyOptionsExternal<\n  TRouter extends AnyTRPCRouter,\n> {\n  client: TRPCUntypedClient<TRouter> | TRPCClient<TRouter>;\n}\n\nexport type TRPCOptionsProxyOptions<TRouter extends AnyTRPCRouter> =\n  TRPCOptionsProxyOptionsBase &\n    (\n      | TRPCOptionsProxyOptionsInternal<TRouter>\n      | TRPCOptionsProxyOptionsExternal<TRouter>\n    );\n\ntype UtilsMethods =\n  | keyof DecorateQueryProcedure<any>\n  | keyof DecorateInfiniteQueryProcedure<any>\n  | keyof DecorateMutationProcedure<any>\n  | keyof DecorateSubscriptionProcedure<any>\n  | keyof DecorateRouterKeyable;\n\n/**\n * Create a typed proxy from your router types. Can also be used on the server.\n *\n * @see https://trpc.io/docs/client/tanstack-react-query/setup#3b-setup-without-react-context\n * @see https://trpc.io/docs/client/tanstack-react-query/server-components#5-create-a-trpc-caller-for-server-components\n */\nexport function createTRPCOptionsProxy<TRouter extends AnyTRPCRouter>(\n  opts: TRPCOptionsProxyOptions<TRouter>,\n): TRPCOptionsProxy<TRouter> {\n  const callIt = (type: TRPCProcedureType): any => {\n    return (path: string, input: unknown, trpcOpts: TRPCRequestOptions) => {\n      if ('router' in opts) {\n        return Promise.resolve(unwrapLazyArg(opts.ctx)).then((ctx) =>\n          callTRPCProcedure({\n            router: opts.router,\n            path: path,\n            getRawInput: async () => input,\n            ctx: ctx,\n            type: type,\n            signal: undefined,\n          }),\n        );\n      }\n\n      const untypedClient =\n        opts.client instanceof TRPCUntypedClient\n          ? opts.client\n          : getUntypedClient(opts.client);\n\n      return untypedClient[type](path, input, trpcOpts);\n    };\n  };\n\n  return createTRPCRecursiveProxy(({ args, path: _path }) => {\n    const path = [..._path];\n    const utilName = path.pop() as UtilsMethods;\n    const [arg1, arg2] = args as any[];\n\n    const contextMap: Record<UtilsMethods, () => unknown> = {\n      '~types': undefined as any,\n\n      pathKey: () => {\n        return getQueryKeyInternal(path);\n      },\n      pathFilter: (): QueryFilters => {\n        return {\n          ...arg1,\n          queryKey: getQueryKeyInternal(path),\n        };\n      },\n\n      queryOptions: () => {\n        return trpcQueryOptions({\n          input: arg1,\n          opts: arg2,\n          path,\n          queryClient: opts.queryClient,\n          queryKey: getQueryKeyInternal(path, arg1, 'query'),\n          query: callIt('query'),\n        });\n      },\n      queryKey: () => {\n        return getQueryKeyInternal(path, arg1, 'query');\n      },\n      queryFilter: (): QueryFilters => {\n        return {\n          ...arg2,\n          queryKey: getQueryKeyInternal(path, arg1, 'query'),\n        };\n      },\n\n      infiniteQueryOptions: () => {\n        return trpcInfiniteQueryOptions({\n          input: arg1,\n          opts: arg2,\n          path,\n          queryClient: opts.queryClient,\n          queryKey: getQueryKeyInternal(path, arg1, 'infinite'),\n          query: callIt('query'),\n        });\n      },\n      infiniteQueryKey: () => {\n        return getQueryKeyInternal(path, arg1, 'infinite');\n      },\n      infiniteQueryFilter: (): QueryFilters => {\n        return {\n          ...arg2,\n          queryKey: getQueryKeyInternal(path, arg1, 'infinite'),\n        };\n      },\n\n      mutationOptions: () => {\n        return trpcMutationOptions({\n          opts: arg1,\n          path,\n          queryClient: opts.queryClient,\n          mutate: callIt('mutation'),\n          overrides: opts.overrides?.mutations,\n        });\n      },\n      mutationKey: () => {\n        return getMutationKeyInternal(path);\n      },\n\n      subscriptionOptions: () => {\n        return trpcSubscriptionOptions({\n          opts: arg2,\n          path,\n          queryKey: getQueryKeyInternal(path, arg1, 'any'),\n          subscribe: callIt('subscription'),\n        });\n      },\n    };\n\n    return contextMap[utilName]();\n  });\n}\n", "import type { QueryClient } from '@tanstack/react-query';\nimport type { TRPCClient } from '@trpc/client';\nimport type { AnyTRPCRouter } from '@trpc/server';\nimport * as React from 'react';\nimport type { TRPCOptionsProxy } from './createOptionsProxy';\nimport { createTRPCOptionsProxy } from './createOptionsProxy';\n\nexport interface CreateTRPCContextResult<TRouter extends AnyTRPCRouter> {\n  TRPCProvider: React.FC<{\n    children: React.ReactNode;\n    queryClient: QueryClient;\n    trpcClient: TRPCClient<TRouter>;\n  }>;\n  useTRPC: () => TRPCOptionsProxy<TRouter>;\n  useTRPCClient: () => TRPCClient<TRouter>;\n}\n/**\n * Create a set of type-safe provider-consumers\n *\n * @see https://trpc.io/docs/client/tanstack-react-query/setup#3a-setup-the-trpc-context-provider\n */\nexport function createTRPCContext<\n  TRouter extends AnyTRPCRouter,\n>(): CreateTRPCContextResult<TRouter> {\n  const TRPCClientContext = React.createContext<TRPCClient<TRouter> | null>(\n    null,\n  );\n  const TRPCContext = React.createContext<TRPCOptionsProxy<TRouter> | null>(\n    null,\n  );\n\n  function TRPCProvider(\n    props: Readonly<{\n      children: React.ReactNode;\n      queryClient: QueryClient;\n      trpcClient: TRPCClient<TRouter>;\n    }>,\n  ) {\n    const value = React.useMemo(\n      () =>\n        createTRPCOptionsProxy({\n          client: props.trpcClient,\n          queryClient: props.queryClient,\n        }),\n      [props.trpcClient, props.queryClient],\n    );\n    return (\n      <TRPCClientContext.Provider value={props.trpcClient}>\n        <TRPCContext.Provider value={value}>\n          {props.children}\n        </TRPCContext.Provider>\n      </TRPCClientContext.Provider>\n    );\n  }\n\n  function useTRPC() {\n    const utils = React.useContext(TRPCContext);\n    if (!utils) {\n      throw new Error('useTRPC() can only be used inside of a <TRPCProvider>');\n    }\n    return utils;\n  }\n\n  function useTRPCClient() {\n    const client = React.useContext(TRPCClientContext);\n    if (!client) {\n      throw new Error(\n        'useTRPCClient() can only be used inside of a <TRPCProvider>',\n      );\n    }\n    return client;\n  }\n\n  return { TRPCProvider, useTRPC, useTRPCClient };\n}\n"], "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAAA,SAASA,UAAQ,GAAG;AAClB;AAEA,SAAO,OAAO,UAAUA,YAAU,qBAAqB,UAAU,mBAAmB,OAAO,WAAW,SAAUC,KAAG;AACjH,iBAAcA;EACf,IAAG,SAAUA,KAAG;AACf,UAAOA,OAAK,qBAAqB,UAAUA,IAAE,gBAAgB,UAAUA,QAAM,OAAO,YAAY,kBAAkBA;EACnH,GAAE,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO,SAAS,UAAQ,EAAE;CAC5F;AACD,QAAO,UAAUD,WAAS,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CCT/F,IAAIE,6BAAiC;CACrC,SAASC,cAAY,GAAG,GAAG;AACzB,MAAI,YAAY,UAAQ,EAAE,KAAK,EAAG,QAAO;EACzC,IAAI,IAAI,EAAE,OAAO;AACjB,WAAS,MAAM,GAAG;GAChB,IAAI,IAAI,EAAE,KAAK,GAAG,KAAK,UAAU;AACjC,OAAI,YAAY,UAAQ,EAAE,CAAE,QAAO;AACnC,SAAM,IAAI,UAAU;EACrB;AACD,SAAO,CAAC,aAAa,IAAI,SAAS,QAAQ,EAAE;CAC7C;AACD,QAAO,UAAUA,eAAa,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CCXnG,IAAI,2BAAiC;CACrC,IAAI;CACJ,SAASC,gBAAc,GAAG;EACxB,IAAI,IAAI,YAAY,GAAG,SAAS;AAChC,SAAO,YAAY,QAAQ,EAAE,GAAG,IAAI,IAAI;CACzC;AACD,QAAO,UAAUA,iBAAe,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CCNrG,IAAI;CACJ,SAAS,gBAAgB,GAAG,GAAG,GAAG;AAChC,UAAQ,IAAI,cAAc,EAAE,KAAK,IAAI,OAAO,eAAe,GAAG,GAAG;GAC/D,OAAO;GACP,aAAa;GACb,eAAe;GACf,WAAW;EACZ,EAAC,GAAG,EAAE,KAAK,GAAG;CAChB;AACD,QAAO,UAAU,iBAAiB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CCTvG,IAAI;CACJ,SAAS,QAAQ,GAAG,GAAG;EACrB,IAAI,IAAI,OAAO,KAAK,EAAE;AACtB,MAAI,OAAO,uBAAuB;GAChC,IAAI,IAAI,OAAO,sBAAsB,EAAE;AACvC,SAAM,IAAI,EAAE,OAAO,SAAUC,KAAG;AAC9B,WAAO,OAAO,yBAAyB,GAAGA,IAAE,CAAC;GAC9C,EAAC,GAAG,EAAE,KAAK,MAAM,GAAG,EAAE;EACxB;AACD,SAAO;CACR;CACD,SAAS,eAAe,GAAG;AACzB,OAAK,IAAI,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;GACzC,IAAI,IAAI,QAAQ,UAAU,KAAK,UAAU,KAAK,CAAE;AAChD,OAAI,IAAI,QAAQ,OAAO,EAAE,GAAG,EAAE,CAAC,QAAQ,SAAUA,KAAG;AAClD,mBAAe,GAAGA,KAAG,EAAEA,KAAG;GAC3B,EAAC,GAAG,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,EAAE,CAAC,GAAG,QAAQ,OAAO,EAAE,CAAC,CAAC,QAAQ,SAAUA,KAAG;AAChJ,WAAO,eAAe,GAAGA,KAAG,OAAO,yBAAyB,GAAGA,IAAE,CAAC;GACnE,EAAC;EACH;AACD,SAAO;CACR;AACD,QAAO,UAAU,gBAAgB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CCtBtG,SAASC,iBAAe,GAAG;EACzB,IAAI,GACF,GACA,GACA,IAAI;AACN,OAAK,sBAAsB,WAAW,IAAI,OAAO,eAAe,IAAI,OAAO,WAAW,MAAM;AAC1F,OAAI,KAAK,SAAS,IAAI,EAAE,IAAK,QAAO,EAAE,KAAK,EAAE;AAC7C,OAAI,KAAK,SAAS,IAAI,EAAE,IAAK,QAAO,IAAI,sBAAsB,EAAE,KAAK,EAAE;AACvE,OAAI,mBAAmB,IAAI;EAC5B;AACD,QAAM,IAAI,UAAU;CACrB;CACD,SAAS,sBAAsB,GAAG;EAChC,SAAS,kCAAkCC,KAAG;AAC5C,OAAI,OAAOA,IAAE,KAAKA,IAAG,QAAO,QAAQ,OAAO,IAAI,UAAUA,MAAI,sBAAsB;GACnF,IAAI,IAAIA,IAAE;AACV,UAAO,QAAQ,QAAQA,IAAE,MAAM,CAAC,KAAK,SAAUA,KAAG;AAChD,WAAO;KACL,OAAOA;KACP,MAAM;IACP;GACF,EAAC;EACH;AACD,SAAO,wBAAwB,SAASC,wBAAsBD,KAAG;AAC/D,QAAK,IAAIA,KAAG,KAAK,IAAIA,IAAE;EACxB,GAAE,sBAAsB,YAAY;GACnC,GAAG;GACH,GAAG;GACH,MAAM,SAAS,OAAO;AACpB,WAAO,kCAAkC,KAAK,EAAE,MAAM,KAAK,GAAG,UAAU,CAAC;GAC1E;GACD,UAAU,SAAS,QAAQA,KAAG;IAC5B,IAAI,IAAI,KAAK,EAAE;AACf,gBAAY,MAAM,IAAI,QAAQ,QAAQ;KACpC,OAAOA;KACP,OAAO;IACR,EAAC,GAAG,kCAAkC,EAAE,MAAM,KAAK,GAAG,UAAU,CAAC;GACnE;GACD,SAAS,SAAS,OAAOA,KAAG;IAC1B,IAAI,IAAI,KAAK,EAAE;AACf,gBAAY,MAAM,IAAI,QAAQ,OAAOA,IAAE,GAAG,kCAAkC,EAAE,MAAM,KAAK,GAAG,UAAU,CAAC;GACxG;EACF,GAAE,IAAI,sBAAsB;CAC9B;AACD,QAAO,UAAUD,kBAAgB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CC5CtG,SAAS,8BAA8B,GAAG,GAAG;AAC3C,MAAI,QAAQ,EAAG,QAAO,CAAE;EACxB,IAAI,IAAI,CAAE;AACV,OAAK,IAAI,KAAK,EAAG,KAAI,CAAE,EAAC,eAAe,KAAK,GAAG,EAAE,EAAE;AACjD,OAAI,EAAE,SAAS,EAAE,CAAE;AACnB,KAAE,KAAK,EAAE;EACV;AACD,SAAO;CACR;AACD,QAAO,UAAU,+BAA+B,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CCTrH,IAAI;CACJ,SAASG,2BAAyB,GAAG,GAAG;AACtC,MAAI,QAAQ,EAAG,QAAO,CAAE;EACxB,IAAI,GACF,GACA,IAAI,6BAA6B,GAAG,EAAE;AACxC,MAAI,OAAO,uBAAuB;GAChC,IAAI,IAAI,OAAO,sBAAsB,EAAE;AACvC,QAAK,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAK,KAAI,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAE,EAAC,qBAAqB,KAAK,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE;EAC3G;AACD,SAAO;CACR;AACD,QAAO,UAAUA,4BAA0B,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;;;mBCkG1G,UACA;;;;AAnGN,SAAgB,wBAAwBC,OAEL;CACjC,MAAM,OAAO,MAAM,KAAK,KAAK,IAAI;AAEjC,QAAO,EACL,KACD;AACF;;;;AAKD,SAAgB,cACdC,UACAC,MACAC,gBAIA;;CACA,MAAM,OAAO,SAAS;CACtB,IAAI,sBAAQ,SAAS,4DAAI;AACzB,KAAI,gBAAgB;;AAClB,oIACM,gDAAS,CAAE,IACX,eAAe,uBACf,EAAE,QAAQ,eAAe,UAAW,IACpC,CAAE,UACN,WAAW,eAAe;CAE7B;AACD,QAAO;EAAC,KAAK,KAAK,IAAI;EAAE;8CAAQ,KAAc;CAAK;AACpD;;;;AAKD,eAAsB,4BACpBC,eACAC,aACAJ,UACA;CACA,MAAM,aAAa,YAAY,eAAe;CAE9C,MAAM,QAAQ,WAAW,MAAM,aAAa,EAC1C,SACD,EAAC;AAEF,OAAM,SAAS;EACb,MAAM,CAAE;EACR,QAAQ;CACT,EAAC;CAEF,MAAMK,YAAuB,CAAE;;;;;yDACL;SAAT;GAAwB;AACvC,cAAU,KAAK,MAAM;AAErB,UAAM,SAAS,EACb,MAAM,CAAC,GAAG,SAAU,EACrB,EAAC;GACH;;;;;;;;;;;;AACD,QAAO;AACR;;;;;;;;AASD,SAAgB,oBACdC,MACAC,OACAC,MACc;CAMd,MAAM,YAAY,KAAK,QAAQ,CAAC,SAAS,KAAK,MAAM,IAAI,CAAC;AAEzD,MAAK,WAAW,QAAQ,SAAS,OAK/B,QAAO,UAAU,SAAS,CAAC,SAAU,IAAI,CAAE;AAG7C,KACE,SAAS,cACT,SAAS,MAAM,KACd,eAAe,SAAS,YAAY,QACrC;EACA,MAAM,EACJ,QAAQ,GACR,WAAW,IAEZ,UADI,6EACD;AACJ,SAAO,CACL,WACA;GACE,OAAO;GACP,MAAM;EACP,CACF;CACF;AAED,QAAO,CACL,8FAEa,UAAU,eACnB,UAAU,aAAa,EAAS,MAAO,IACrC,QAAQ,SAAS,SAAS,EAAQ,KAAM,EAE/C;AACF;;;;AAKD,SAAgB,uBACdF,MACiB;CAEjB,MAAM,YAAY,KAAK,QAAQ,CAAC,SAAS,KAAK,MAAM,IAAI,CAAC;AAEzD,QAAO,UAAU,SAAS,CAAC,SAAU,IAAI,CAAE;AAC5C;;;;AAKD,SAAgB,cAAiBG,aAA+B;AAC9D,QAAQ,WAAW,YAAY,GAAG,aAAa,GAAG;AACnD;;;;;ACiED,SAAgB,yBAAyBC,MAON;;CACjC,MAAM,EAAE,OAAO,OAAO,MAAM,UAAU,MAAM,GAAG;CAC/C,MAAM,mBAAmB,UAAU;CAEnC,MAAMC,UAAyD,OAC7D,mBACG;;EACH,MAAM,yFACD,aACH,8HACK,KAAM,0DACL,KAAM,8DAAM,kBACZ,EAAE,QAAQ,eAAe,OAAQ,IACjC,EAAE,QAAQ,KAAM;EAIxB,MAAM,SAAS,MAAM,MACnB,GAAG,cAAc,UAAU,YAAY;GACrC,WAAW,eAAe;GAC1B,WAAW,eAAe;EAC3B,EAAC,CACH;AAED,SAAO;CACR;AAED,QAAO,OAAO,OACZ,iGACK;EACH;EACA,SAAS,mBAAmB,YAAY;EACxC,qFAAkB,KAAM,gIAAkB,MAAe;IACzD,EACF,EAAE,MAAM,wBAAwB,EAAE,KAAM,EAAC,CAAE,EAC5C;AACF;;;;;;;;AC1KD,SAAgB,oBAAoBC,MAMN;;CAC5B,MAAM,EAAE,QAAQ,MAAM,MAAM,WAAW,GAAG;CAC1C,MAAM,cAAc,cAAc,KAAK,YAAY;CAEnD,MAAM,cAAc,uBAAuB,KAAK;CAEhD,MAAM,cAAc,YAAY,uBAC9B,YAAY,oBAAoB,YAAY,CAC7C;CAED,MAAMC,wGACJ,UAAW,gFAAc,CAAC,YAAY,QAAQ,YAAY;CAE5D,MAAMC,aAA+B,OAAO,UAAU;EACpD,MAAM,SAAS,MAAM,OAAO,GAAG,cAAc,CAAC,MAAM,EAAE,MAAO,CAAC,GAAE,KAAK,CAAC;AAEtE,SAAO;CACR;AAED,oFACK;EACU;EACb;EACA,UAAU,GAAGC,QAAM;;GACjB,MAAM,aAAa,MACjB;;2FAAM,8DAAN,4BAAkB,GAAGA,OAAK,wIAAI,YAAa,mEAAb,wCAAyB,GAAGA,OAAK;;AAEjE,UAAO,wBAAwB;IAC7B;IACA;IACA,wEAAM,KAAM,iHAAQ,YAAa,2CAAQ,CAAE;GAC5C,EAAC;EACH;EACD,MAAM,wBAAwB,EAAE,KAAM,EAAC;;AAE1C;;;;;;;;ACyCD,SAAgB,iBAAiBC,MAON;CACzB,MAAM,EAAE,OAAO,OAAO,MAAM,UAAU,MAAM,GAAG;CAC/C,MAAM,cAAc,cAAc,KAAK,YAAY;CAEnD,MAAM,mBAAmB,UAAU;CAEnC,MAAMC,UAAgD,OACpD,mBACG;;EACH,MAAM,yFACD,aACH,8HACK,KAAM,0DACL,KAAM,8DAAM,kBACZ,EAAE,QAAQ,eAAe,OAAQ,IACjC,EAAE,QAAQ,KAAM;EAGxB,MAAMC,aAAW,eAAe;EAEhC,MAAM,SAAS,MAAM,MAAM,GAAG,cAAcA,YAAU,WAAW,CAAC;AAElE,MAAI,gBAAgB,OAAO,CACzB,QAAO,4BAA4B,QAAQ,aAAaA,WAAS;AAGnE,SAAO;CACR;AAED,QAAO,OAAO,OACZ,yFACK;EACH;EACA,SAAS,mBAAmB,YAAY;IACxC,EACF,EAAE,MAAM,wBAAwB,EAAE,KAAM,EAAC,CAAE,EAC5C;AACF;;;;;;;;AC3FD,MAAa,0BAA0B,CAACC,SAKH;;CACnC,MAAM,EAAE,WAAW,MAAM,UAAU,OAAO,CAAE,GAAE,GAAG;CACjD,MAAM,sBAAQ,SAAS,4DAAI;CAC3B,MAAM,UAAU,aAAa,SAAS,KAAK,UAAU,UAAU;CAE/D,MAAMC,aAAoE,CACxE,cACG;AACH,SAAO,UAAU,KAAK,KAAK,IAAI,EAAE,qDAAoB,UAAU;CAChE;AAED,oFACK;EACH;EACA,WAAW;EACX;EACA,MAAM,wBAAwB,EAAE,KAAM,EAAC;;AAE1C;AAED,SAAgB,gBACdC,MACyC;CAGzC,MAAM,UAAU,QAAM,OAAO,KAAK;AAClC,SAAQ,UAAU;CAElB,MAAM,eAAe,QAAM,OAAO,IAAI,IAAmB,CAAE,GAAE;CAE7D,MAAM,iBAAiB,QAAM,YAAY,CAACC,QAAuB;AAC/D,eAAa,QAAQ,IAAI,IAAI;CAC9B,GAAE,CAAE,EAAC;CAGN,MAAM,yBAAyB,QAAM,OAAoB,MAAM,CAE9D,EAAC;CAEF,MAAM,QAAQ,QAAM,YAAY,MAAY;;AAE1C,kDAAuB,yDAAvB,kDAAkC;AAElC,cAAY,gBAAgB;AAC5B,OAAK,KAAK,QACR;EAEF,MAAM,eAAe,KAAK,UAAU;GAClC,WAAW,MAAM;;AACf,yDAAQ,SAAQ,2DAAhB,4CAA6B;AAC7B,gBAAY,CAAC,qFACP;KACJ,QAAQ;KACR,OAAO;OACN;GACJ;GACD,QAAQ,CAAC,SAAS;;AAChB,0DAAQ,SAAQ,wDAAhB,8CAAyB,KAAK;AAC9B,gBAAY,CAAC,qFACP;KACJ,QAAQ;KACR;KACA,OAAO;OACN;GACJ;GACD,SAAS,CAAC,UAAU;;AAClB,0DAAQ,SAAQ,yDAAhB,8CAA0B,MAAM;AAChC,gBAAY,CAAC,qFACP;KACJ,QAAQ;KACR;OACC;GACJ;GACD,yBAAyB,CAAC,WAAW;;AACnC,0DAAQ,SAAQ,yEAAhB,8CAA0C,OAAO;AACjD,gBAAY,CAAC,SAAS;AACpB,aAAQ,OAAO,OAAf;MACE,KAAK,aACH,oFACK;OACH,QAAQ;OACR,OAAO,OAAO;;MAElB,KAAK,UAEH,QAAO;MACT,KAAK,OACH,oFACK;OACH,QAAQ;OACR;OACA,OAAO;;KAEZ;IACF,EAAC;GACH;EACF,EAAC;AAEF,yBAAuB,UAAU,MAAM;AACrC,gBAAa,aAAa;EAC3B;CAGF,GAAE,CAAC,QAAQ,KAAK,SAAS,EAAE,KAAK,OAAQ,EAAC;CAE1C,MAAM,kBAAkB,QAAM,YAAY,MAAe;AACvD,SAAO,KAAK,UACR;GACE;GACA,OAAO;GACP,QAAQ;GACR;EACD,IACD;GACE;GACA,OAAO;GACP,QAAQ;GACR;EACD;CACN,GAAE,CAAC,KAAK,SAAS,KAAM,EAAC;CAEzB,MAAM,YAAY,QAAM,OAAgB,iBAAiB,CAAC;CAE1D,MAAM,CAAC,OAAO,SAAS,GAAG,QAAM,SAC9B,YAAY,UAAU,SAAS,eAAe,CAC/C;AAED,OAAM,QAAQ;CAEd,MAAM,cAAc,QAAM,YACxB,CAACC,aAA8C;EAC7C,MAAM,OAAO,UAAU;EACvB,MAAM,OAAQ,UAAU,UAAU,SAAS,KAAK;EAEhD,IAAI,eAAe;AACnB,OAAK,MAAM,OAAO,aAAa,QAC7B,KAAI,KAAK,SAAS,KAAK,MAAM;AAC3B,kBAAe;AACf;EACD;AAEH,MAAI,aACF,UAAS,YAAY,MAAM,eAAe,CAAC;CAE9C,GACD,CAAC,cAAe,EACjB;AAED,SAAM,UAAU,MAAM;AACpB,OAAK,KAAK,QACR;AAEF,SAAO;AAEP,SAAO,MAAM;;AACX,oDAAuB,0DAAvB,mDAAkC;EACnC;CACF,GAAE,CAAC,OAAO,KAAK,OAAQ,EAAC;AAEzB,QAAO;AACR;AAED,SAAS,YACPC,QACAC,eACG;CACH,MAAM,gBAAgB,IAAI,MAAM,QAAQ,EACtC,IAAI,QAAQ,MAAM;AAChB,gBAAc,KAAgB;AAC9B,SAAO,OAAO;CACf,EACF;AAED,QAAO;AACR;;;;;;;;;;;ACmBD,SAAgB,uBACdC,MAC2B;CAC3B,MAAM,SAAS,CAACC,SAAiC;AAC/C,SAAO,CAACC,MAAcC,OAAgBC,aAAiC;AACrE,OAAI,YAAY,KACd,QAAO,QAAQ,QAAQ,cAAc,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,QACpD,kBAAkB;IAChB,QAAQ,KAAK;IACP;IACN,aAAa,YAAY;IACpB;IACC;IACN;GACD,EAAC,CACH;GAGH,MAAM,gBACJ,KAAK,kBAAkB,oBACnB,KAAK,SACL,iBAAiB,KAAK,OAAO;AAEnC,UAAO,cAAc,MAAM,MAAM,OAAO,SAAS;EAClD;CACF;AAED,QAAO,yBAAyB,CAAC,EAAE,MAAM,MAAM,OAAO,KAAK;EACzD,MAAM,OAAO,CAAC,GAAG,KAAM;EACvB,MAAM,WAAW,KAAK,KAAK;EAC3B,MAAM,CAAC,MAAM,KAAK,GAAG;EAErB,MAAMC,aAAkD;GACtD;GAEA,SAAS,MAAM;AACb,WAAO,oBAAoB,KAAK;GACjC;GACD,YAAY,MAAoB;AAC9B,mFACK,aACH,UAAU,oBAAoB,KAAK;GAEtC;GAED,cAAc,MAAM;AAClB,WAAO,iBAAiB;KACtB,OAAO;KACP,MAAM;KACN;KACA,aAAa,KAAK;KAClB,UAAU,oBAAoB,MAAM,MAAM,QAAQ;KAClD,OAAO,OAAO,QAAQ;IACvB,EAAC;GACH;GACD,UAAU,MAAM;AACd,WAAO,oBAAoB,MAAM,MAAM,QAAQ;GAChD;GACD,aAAa,MAAoB;AAC/B,mFACK,aACH,UAAU,oBAAoB,MAAM,MAAM,QAAQ;GAErD;GAED,sBAAsB,MAAM;AAC1B,WAAO,yBAAyB;KAC9B,OAAO;KACP,MAAM;KACN;KACA,aAAa,KAAK;KAClB,UAAU,oBAAoB,MAAM,MAAM,WAAW;KACrD,OAAO,OAAO,QAAQ;IACvB,EAAC;GACH;GACD,kBAAkB,MAAM;AACtB,WAAO,oBAAoB,MAAM,MAAM,WAAW;GACnD;GACD,qBAAqB,MAAoB;AACvC,mFACK,aACH,UAAU,oBAAoB,MAAM,MAAM,WAAW;GAExD;GAED,iBAAiB,MAAM;;AACrB,WAAO,oBAAoB;KACzB,MAAM;KACN;KACA,aAAa,KAAK;KAClB,QAAQ,OAAO,WAAW;KAC1B,8BAAW,KAAK,6EAAW;IAC5B,EAAC;GACH;GACD,aAAa,MAAM;AACjB,WAAO,uBAAuB,KAAK;GACpC;GAED,qBAAqB,MAAM;AACzB,WAAO,wBAAwB;KAC7B,MAAM;KACN;KACA,UAAU,oBAAoB,MAAM,MAAM,MAAM;KAChD,WAAW,OAAO,eAAe;IAClC,EAAC;GACH;EACF;AAED,SAAO,WAAW,WAAW;CAC9B,EAAC;AACH;;;;;;;;;AC1ZD,SAAgB,oBAEsB;CACpC,MAAM,oBAAoB,MAAM,cAC9B,KACD;CACD,MAAM,cAAc,MAAM,cACxB,KACD;CAED,SAAS,aACPC,OAKA;EACA,MAAM,QAAQ,MAAM,QAClB,MACE,uBAAuB;GACrB,QAAQ,MAAM;GACd,aAAa,MAAM;EACpB,EAAC,EACJ,CAAC,MAAM,YAAY,MAAM,WAAY,EACtC;AACD,yBACE,IAAC,kBAAkB;GAAS,OAAO,MAAM;6BACvC,IAAC,YAAY;IAAgB;cAC1B,MAAM;KACc;IACI;CAEhC;CAED,SAAS,UAAU;EACjB,MAAM,QAAQ,MAAM,WAAW,YAAY;AAC3C,OAAK,MACH,OAAM,IAAI,MAAM;AAElB,SAAO;CACR;CAED,SAAS,gBAAgB;EACvB,MAAM,SAAS,MAAM,WAAW,kBAAkB;AAClD,OAAK,OACH,OAAM,IAAI,MACR;AAGJ,SAAO;CACR;AAED,QAAO;EAAE;EAAc;EAAS;CAAe;AAChD"}