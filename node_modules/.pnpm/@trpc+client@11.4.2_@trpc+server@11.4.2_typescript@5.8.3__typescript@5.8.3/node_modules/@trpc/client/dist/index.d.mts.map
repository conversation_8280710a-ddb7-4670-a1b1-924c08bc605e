{"version": 3, "file": "index.d.mts", "names": [], "sources": ["../src/internals/TRPCUntypedClient.ts", "../src/createTRPCUntypedClient.ts", "../src/createTRPCClient.ts", "../src/getFetch.ts", "../src/links/httpBatchStreamLink.ts", "../src/links/httpSubscriptionLink.ts", "../src/links/retryLink.ts", "../src/links/localLink.ts"], "sourcesContent": [], "mappings": ";;;;;;;;;;;;;;UAuBiB,kBAAA;;;;YAIL;WACD;;UAGM;;IARA,OAAA,EASc,gBATI,GAAA,SAAA;EAAA,CAAA,EAAA,GAAA,IAAA;EAAA,MAIvB,EAAA,CAAA,KAAA,EAMM,uBANN,CAM8B,MAN9B,CAAA,EAAA,GAAA,IAAA;EAAgB,OACjB,EAAA,CAAA,GAAA,EAMM,MANN,EAAA,GAAA,IAAA;EAAW,SAAA,EAAA,GAAA,GAAA,IAAA;EAGL,UAAA,EAAA,GAAA,GAAA,IAAA;EAAwB,uBAAA,EAAA,CAAA,KAAA,EAMN,mBANM,CAMc,MANd,CAAA,EAAA,GAAA,IAAA;;;AAEvB,KAQN,uBARM,CAAA,gBAQkC,qBARlC,CAAA,GAAA;EAAuB,KACxB,EAQR,QARQ,CAQC,OARD,CAAA,EAAA;EAAM,WAGgC,CAAA,EAMvC,SANuC,CAAA,qEAAA,CAAA;CAAM;AAAP,cASzC,iBATyC,CAAA,oBASH,qBATG,CAAA,CAAA;EAI1C,iBAAA,KAAA;EAAuB,SAAA,OAAA,EAOR,iBAPQ;EAAA,QAAiB,SAAA;EAAqB,WACvD,CAAA,IAAA,EASE,uBATF,CAS0B,WAT1B,CAAA;EAAO,QAAhB,QAAA;EAAQ,QACD,gBAAA;EAAS,KAAA,CAAA,IAAA,EAAA,MAAA,EAAA,KAAA,CAAA,EAAA,OAAA,EAAA,IAAA,CAAA,EAqD4B,kBArD5B,CAAA,EAqD8C,OArD9C,CAAA,OAAA,CAAA;EAGZ,QAAA,CAAA,IAAA,EAAA,MAAiB,EAAA,KAAA,CAAA,EAAA,OAAA,EAAA,IAAA,CAAA,EA2D0B,kBA3D1B,CAAA,EA2D4C,OA3D5C,CAAA,OAAA,CAAA;EAAA,YAAA,CAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,IAAA,EAuEpB,OAvEoB,CAwExB,wBAxEwB,CAAA,OAAA,EAwEU,eAxEV,CAwE0B,SAxE1B,CAAA,CAAA,CAAA,GA0ExB,kBA1EwB,CAAA,EA2EzB,cA3EyB;;;;;iBC1Cd,wCAAwC,iBAChD,wBAAwB,WAC7B,kBAAkB;;;;;;;KCoBT,kCAAkC,aAAa,WAAW;;;AFHtE;;AAIY,KEKA,gBFLA,CAAA,gBEKiC,SFLjC,CAAA,GEK8C,UFL9C,CEKyD,OFLzD,CAAA;cEON,mBFNK,EAAA,OAAA,MAAA;AAAW;AAGtB;;AAC+B,KEOnB,UFPmB,CAAA,gBEOQ,SFPR,CAAA,GEOqB,wBFPrB,CAAA;EAAgB,WACL,EEQzB,OFRyB,CAAA,MAAA,CAAA,CAAA,SAAA,CAAA,CAAA,QAAA,CAAA,CAAA,aAAA,CAAA;EAAM,UAA9B,EESF,OFTE,CAAA,MAAA,CAAA,CAAA,SAAA,CAAA,CAAA,QAAA,CAAA,CAAA,YAAA,CAAA;CAAuB,EEWvC,OFVe,CAAA,MAAA,CAAA,CAAA,QAAA,CAAA,CAAA,GAAA;EAAM,CEYpB,mBAAA,CFToD,EES9B,iBFT8B,CESZ,OFTY,CAAA;CAAM;AAAP,KEYjD,WAAA,GFZiD;EAI1C,KAAA,EAAA,GAAA;EAAuB,MAAA,EAAA,GAAA;EAAA,WAAiB,EAAA,OAAA;EAAqB,UACvD,EAAA,GAAA;CAAO;KEcpB,8BFbW,CAAA,CAAA,CAAA,GEcd,CFdc,SEcJ,cFdI,CAAA,KAAA,GAAA,EAAA,KAAA,QAAA,EAAA,KAAA,MAAA,CAAA,GEeV,aFfU,CEeI,EFfJ,EEeQ,OFfR,EEeiB,KFfjB,CAAA,GEgBV,CFhBU;AAAS;AAGZ,KEgBD,QFhBC,CAAA,aEgBqB,WFhBJ,CAAA,GAAA,CAAA,KAAA,EEiBrB,IFjBqB,CAAA,OAAA,CAAA,EAAA,IAAA,CAAA,EEkBrB,oBFlBqB,EAAA,GEmBzB,OFnByB,CEmBjB,8BFnBiB,CEmBc,IFnBd,CAAA,QAAA,CAAA,CAAA,CAAA;KEqBzB,oBFrByB,CAAA,aEqBS,WFrBT,CAAA,GAAA,CAAA,KAAA,EEsBrB,IFtBqB,CAAA,OAAA,CAAA,EAAA,IAAA,EEuBtB,OFvBsB,CEwB1B,wBFxB0B,CEwBD,IFxBC,CAAA,QAAA,CAAA,EEwBe,eFxBf,CEwB+B,IFxB/B,CAAA,CAAA,CAAA,GE0B1B,oBF1B0B,EAAA,GE2BzB,cF3ByB;KE6BzB,iBF7B8C,CAAA,cE8BnC,aF9BmC,EAAA,aE+BpC,WF/BoC,CAAA,GEgC/C,KFhC+C,SAAA,OAAA,GAAA;EAAqB,KAE7C,EEgCd,QFhCc,CEgCL,IFhCK,CAAA;CAAiB,GEkCxC,KF/BwC,SAAA,UAAA,GAAA;EAAW,MAAnC,EEiCJ,QFjCI,CEiCK,IFjCL,CAAA;CAAuB,GEmCrC,KFU+C,SAAA,cAAA,GAAA;EAAkB,SAAA,EERlD,oBFQkD,CER7B,IFQ6B,CAAA;CAAA,GAAA,KASf;;;;KEVnD,wBFuBC,CAAA,cEtBU,qBFsBV,EAAA,gBErBY,YFqBZ,CAAA,GAAA,WADI,MElBO,OFkBP,GElBiB,OFkBjB,CElByB,IFkBzB,CAAA,SAAA,KAAA,OAAA,GEjBJ,MFiBI,SEjBW,YFiBX,GEhBF,iBFgBE,CEfA,MFeA,CAAA,MAAA,CAAA,CAAA,MAAA,CAAA,EAAA;EAGJ,KAAA,EEhBa,mBFgBb,CEhBiC,MFgBjC,CAAA;EACD,MAAA,EEhBe,+BFgBf,CEfS,gBFeT,CEf0B,KFe1B,CAAA,EEdS,MFcT,CAAA;EAAc,UAAA,EEZK,gBFYL,CEZsB,KFYtB,CAAA,CAAA,YAAA,CAAA;eEXM,iBAAiB;KAGlC,eAAe,eACb,yBAAyB,OAAO;AD9G1C;AAAuC,cC6H1B,6BD7H0B,EAAA,CAAA,cAAA,EAAA,MAAA,EAAA,GC+HpC,aD/HoC;;;;AAElB,iBCoIL,qBDpIK,CAAA,gBCoIiC,SDpIjC,CAAA,CAAA,MAAA,ECqIX,iBDrIW,CCqIO,ODrIP,CAAA,CAAA,ECsIlB,UDtIkB,CCsIP,ODtIO,CAAA;AAAlB,iBCuJa,gBDvJb,CAAA,gBCuJ8C,SDvJ9C,CAAA,CAAA,IAAA,ECwJK,uBDxJL,CCwJ6B,ODxJ7B,CAAA,CAAA,ECyJA,UDzJA,CCyJW,ODzJX,CAAA;AAAiB;;;;ACoBR,iBA+II,gBA/Ia,CAAA,gBA+IoB,SA/IpB,CAAA,CAAA,MAAA,EAgJnB,UAhJmB,CAgJR,OAhJQ,CAAA,CAAA,EAiJ1B,iBAjJ0B,CAiJR,OAjJQ,CAAA;;;iBCpBb,QAAA,mBACI,aAAa,mBAC9B;;;;;;;;iBCca,oCAAoC,mBAC5C,qBAAqB,wCAC1B,SAAS;;;;cAqKC,qCAA4B;;;;KCrJpC,0CACW,qCACO,eAAA,CAAgB,wBAAwB;;;;gBAK/C;;;;uBAKV,eAAA,CAAgB,WAAW;QAErB;EL/BK,CAAA,EAAA,GKiCP,eAAA,CAAgB,ULjCS,CKiCE,YLjCF,CAAA,GKkCzB,OLlCyB,CKkCjB,eAAA,CAAgB,ULlCC,CKkCU,YLlCV,CAAA,CAAA,CAAA;CAAA,GKmC/B,kBLnC+B,CKmCZ,KLnCY,CAAA,GKoCjC,8BLpCiC;;;AAKb;AAGL,iBKiCD,oBLjCyB,CAAA,oBKkCnB,qBLlCmB,EAAA,qBKmClB,eAAA,CAAgB,cLnCE,CAAA,CAAA,IAAA,EKqCjC,2BLrCiC,CKsCrC,gBLtCqC,CKsCpB,WLtCoB,CAAA,EKuCrC,YLvCqC,CAAA,CAAA,EKyCtC,QLzCsC,CKyC7B,WLzC6B,CAAA;;;;AAEvB,cKmNL,6BLnNK,EAAA,OKmNwB,oBLnNxB;;;UMxBR,qCAAqC;;;;gBAI/B,eAAe;;;;;;UAOrB,mCAAmC;ENG5B;;;EAIW,EAAA,EMHtB,SNIK;EAAW;AAGtB;;EAAyC,KACV,EMJtB,eNIsB,CMJN,WNIM,CAAA;EAAgB;;;EAExB,QAGgC,EAAA,MAAA;;AAAD;AAItD;;AAAoD,iBMHpC,SNGoC,CAAA,oBMHN,qBNGM,CAAA,CAAA,IAAA,EMF5C,gBNE4C,CMF3B,WNE2B,CAAA,CAAA,EMDjD,QNCiD,CMDxC,WNCwC,CAAA;;;KObxC,iCAAiC;UACnC;uBACa,QAAQ,mBAAmB;mBAC/B,oBAAoB,mBAAmB;IACtD,mBAAmB,iBAAiB;;;;;;iBAOxB,mCAAmC,iBAC3C,iBAAiB,WACtB,SAAS;APlBZ;;;AAKW,cO4PE,sBP5PF,EAAA,OO4PiC,kBP5PjC;AAAW"}