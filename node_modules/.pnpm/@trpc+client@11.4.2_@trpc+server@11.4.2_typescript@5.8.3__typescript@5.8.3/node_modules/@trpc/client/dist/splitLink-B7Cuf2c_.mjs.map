{"version": 3, "file": "splitLink-B7Cuf2c_.mjs", "names": ["opts: {\n  links: OperationLink<TRouter, TInput, TOutput>[];\n  op: Operation<TInput>;\n}", "value: TType | TType[]", "opts: {\n  condition: (op: Operation) => boolean;\n  /**\n   * The link to execute next if the test function returns `true`.\n   */\n  true: TRPCLink<TRouter> | TRPCLink<TRouter>[];\n  /**\n   * The link to execute next if the test function returns `false`.\n   */\n  false: TRPCLink<TRouter> | TRPCLink<TRouter>[];\n}"], "sources": ["../src/links/internals/createChain.ts", "../src/links/splitLink.ts"], "sourcesContent": ["import { observable } from '@trpc/server/observable';\nimport type { AnyRouter } from '@trpc/server/unstable-core-do-not-import';\nimport type {\n  Operation,\n  OperationLink,\n  OperationResultObservable,\n} from '../types';\n\n/** @internal */\nexport function create<PERSON>hain<\n  TRouter extends AnyRouter,\n  TInput = unknown,\n  TOutput = unknown,\n>(opts: {\n  links: OperationLink<TRouter, TInput, TOutput>[];\n  op: Operation<TInput>;\n}): OperationResultObservable<TRouter, TOutput> {\n  return observable((observer) => {\n    function execute(index = 0, op = opts.op) {\n      const next = opts.links[index];\n      if (!next) {\n        throw new Error(\n          'No more links to execute - did you forget to add an ending link?',\n        );\n      }\n      const subscription = next({\n        op,\n        next(nextOp) {\n          const nextObserver = execute(index + 1, nextOp);\n\n          return nextObserver;\n        },\n      });\n      return subscription;\n    }\n\n    const obs$ = execute();\n    return obs$.subscribe(observer);\n  });\n}\n", "import { observable } from '@trpc/server/observable';\nimport type { AnyRouter } from '@trpc/server/unstable-core-do-not-import';\nimport { create<PERSON>hain } from './internals/createChain';\nimport type { Operation, TRPCLink } from './types';\n\nfunction asArray<TType>(value: TType | TType[]) {\n  return Array.isArray(value) ? value : [value];\n}\nexport function splitLink<TRouter extends AnyRouter = AnyRouter>(opts: {\n  condition: (op: Operation) => boolean;\n  /**\n   * The link to execute next if the test function returns `true`.\n   */\n  true: TRPCLink<TRouter> | TRPCLink<TRouter>[];\n  /**\n   * The link to execute next if the test function returns `false`.\n   */\n  false: TRPCLink<TRouter> | TRPCLink<TRouter>[];\n}): TRPCLink<TRouter> {\n  return (runtime) => {\n    const yes = asArray(opts.true).map((link) => link(runtime));\n    const no = asArray(opts.false).map((link) => link(runtime));\n    return (props) => {\n      return observable((observer) => {\n        const links = opts.condition(props.op) ? yes : no;\n        return createChain({ op: props.op, links }).subscribe(observer);\n      });\n    };\n  };\n}\n"], "mappings": ";;;;AASA,SAAgB,YAIdA,MAG8C;AAC9C,QAAO,WAAW,CAAC,aAAa;EAC9B,SAAS,QAAQ,QAAQ,GAAG,KAAK,KAAK,IAAI;GACxC,MAAM,OAAO,KAAK,MAAM;AACxB,QAAK,KACH,OAAM,IAAI,MACR;GAGJ,MAAM,eAAe,KAAK;IACxB;IACA,KAAK,QAAQ;KACX,MAAM,eAAe,QAAQ,QAAQ,GAAG,OAAO;AAE/C,YAAO;IACR;GACF,EAAC;AACF,UAAO;EACR;EAED,MAAM,OAAO,SAAS;AACtB,SAAO,KAAK,UAAU,SAAS;CAChC,EAAC;AACH;;;;AClCD,SAAS,QAAeC,OAAwB;AAC9C,QAAO,MAAM,QAAQ,MAAM,GAAG,QAAQ,CAAC,KAAM;AAC9C;AACD,SAAgB,UAAiDC,MAU3C;AACpB,QAAO,CAAC,YAAY;EAClB,MAAM,MAAM,QAAQ,KAAK,KAAK,CAAC,IAAI,CAAC,SAAS,KAAK,QAAQ,CAAC;EAC3D,MAAM,KAAK,QAAQ,KAAK,MAAM,CAAC,IAAI,CAAC,SAAS,KAAK,QAAQ,CAAC;AAC3D,SAAO,CAAC,UAAU;AAChB,UAAO,WAAW,CAAC,aAAa;IAC9B,MAAM,QAAQ,KAAK,UAAU,MAAM,GAAG,GAAG,MAAM;AAC/C,WAAO,YAAY;KAAE,IAAI,MAAM;KAAI;IAAO,EAAC,CAAC,UAAU,SAAS;GAChE,EAAC;EACH;CACF;AACF"}