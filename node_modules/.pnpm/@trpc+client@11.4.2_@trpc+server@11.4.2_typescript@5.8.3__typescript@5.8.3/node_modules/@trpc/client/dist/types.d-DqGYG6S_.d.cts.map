{"version": 3, "file": "types.d-DqGYG6S_.d.cts", "names": [], "sources": ["../src/internals/types.ts", "../src/TRPCClientError.ts", "../src/links/internals/contentTypes.ts", "../src/links/types.ts"], "sourcesContent": [], "mappings": ";;;;;;;;;;;AAOA;;AACS,KADG,UAAA,GACH,CAAA,KAAA,EAAA,WAAA,GAAc,GAAd,GAAA,MAAA,EAAA,IAAA,CAAA,EACA,WADA,GACc,gBADd,EAAA,GAEJ,OAFI,CAEI,aAFJ,CAAA;;;;;AAEJ,KAMO,gBAAA,GANP,CAAA,GAAA,EAOE,GAPF,GAAA,MAAA,EAAA,IAAA,CAAA,EAQI,yBARJ,EAAA,GASA,OATA,CASQ,aATR,CAAA;AAAO,UAWK,yBAAA,CAXL;EAMA,IAAA,CAAA,EAAA,MAAA;;;;;;AAGA;AAEZ;AAWA;AAAiC,UAAhB,gBAAA,CAAgB;EAAA;;;EAIoB,IAAG,CAAA,EAA/C,QAA+C,GAAA,MAAA,GAAA,IAAA,GAApB,UAAoB,GAAP,IAAO,GAAA,IAAA;EAAI;;AAetC;EAOV,OAAA,CAAA,EAAA,CAAA,MAAA,EAAA,MAAsB,CAAA,EAAA,GAjBD,MAiBC,CAAA,MAAA,EAAA,MAAA,CAAA;EAAA;;;EACY,MAAA,CAAA,EAAA,MAAA;EAGlC;AAWZ;;EAA8B,MACZ,CAAA,EAvBP,WAuBO,GAAA,SAAA;;;AAOD;AAMjB;;AAAoC,KA7BxB,sBAAA,GA6BwB;EAAK,SAAK,EAAA,GAAA,GA5B3B,2BA4B2B,CA5BC,UA4BD,CAAA;AAAK,CAAA;AAE9C,KA3BO,yBAAA,GA2Be;EAKV,EAAA,CAAA,SAAA,EAAA,MAAA,GAAoB,MAAA,EAAA,QAAA,EAAA,CAAA,GAAA,IAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,CAAA,EA5BhC,yBA4BgC;CAAA;;;AAKf;;UA1BL,aAAA;kBACC,4BAA4B;EC/DzC;;;;;AACa;EACD,IAAA,EAAA,EDoEP,OCpEO,CAAA,OAAmB,CAAA;;;;;AAGb,KDuEX,aCvEW,CAAA,KAAA,CAAA,GAAA,CDuEa,KCvEb,EAAA,GDuEuB,KCvEvB,EAAA,CAAA;KDyElB,aAAA,GAAgB,MCzEJ,CAAA,MAAA,EAAA,OAAA,CAAA;AAAK;AAEtB;;AAAoD,UD4EnC,oBAAA,CC5EmC;EAAqB;;;EACpD,OAAA,CAAA,ED+ET,aC/ES;EAEL,MAAA,CAAA,ED8EL,WC9EsB;;;;KAV5B,oCAAoC,yBACvC,iBAAiB;UACF,mCAAmC;;EDNxC,SAAA,KAAU,ECQJ,KDRI,CCQE,MDRF,CAAA;EAAA,SAAA,IAAA,ECSL,KDTK,CCSC,MDTD,CAAA,MAAA,CAAA,CAAA;;AACC,KCUX,mBDVW,CAAA,oBCU6B,qBDV7B,CAAA,GCWrB,mBDXqB,CCWD,eDXC,CCWe,WDXf,CAAA,CAAA;AACd,iBCYO,iBDZP,CAAA,oBCY6C,qBDZ7C,CAAA,CAAA,KAAA,EAAA,OAAA,CAAA,EAAA,KAAA,ICcG,eDdH,CCcmB,WDdnB,CAAA;AAAc,cCqCV,eDrCU,CAAA,2BCqCiC,qBDrCjC,CAAA,SCsCb,KAAA,YACG,mBDvCU,CCuCU,eDvCV,CCuC0B,kBDvC1B,CAAA,CAAA,CAAA;EAAgB,SAC1B,KAAA,EC0CmB,KD1CnB,GAAA,SAAA;EAAa,SAArB,KAAA,EC2CoB,KD3CpB,CC2C0B,eD3C1B,CC2C0C,kBD3C1C,CAAA,CAAA;EAAO,SAAA,IAAA,EC4CY,KD5CZ,CC4CkB,eD5ClB,CC4CkC,kBD5ClC,CAAA,CAAA,MAAA,CAAA,CAAA;EAMA;;;;EAEsB,IACrB,ECyCA,MDzCA,CAAA,MAAA,EAAA,OAAA,CAAA,GAAA,SAAA;EAAa,WAArB,CAAA,OAAA,EAAA,MAAA,EAAA,IAa4B,CAb5B,EAAA;IAAO,MAAA,CAAA,EC8CG,KD9CH,CC8CS,iBD9CT,CC8C2B,eD9C3B,CC8C2C,kBD9C3C,CAAA,CAAA,CAAA;IAEK,KAAA,CAAA,EC6CH,KD7CG;IAWA,IAAA,CAAA,ECmCJ,MDnCI,CAAA,MAAgB,EAAA,OAAA,CAAA;EAAA,CAAA;EAAA,OAIxB,IAAA,CAAA,2BCkDuC,qBDlDvC,CAAA,CAAA,MAAA,ECmDG,KDnDH,GCmDW,iBDnDX,CAAA,GAAA,CAAA,GAAA,MAAA,EAAA,IAAqC,CAArC,EAAA;IAA2B,IAAA,CAAA,ECoDjB,MDpDiB,CAAA,MAAA,EAAA,OAAA,CAAA;EAAU,CAAA,CAAA,ECqDzC,eDrD4C,CCqD5B,kBDrD4B,CAAA;;;;iBEpCjC,WAAA,2BAA0B,OAAA,WAAA;iBAQ1B,UAAA,2BAAyB;iBAIzB,qBAAA,2BAAoC,OAAA,WAAA,WAAA;;;;;;;;AFH3C,UGWQ,gBAAA,SAAyB,MHXjC,CAAA,MAAA,EAAA,OAAA,CAAA,CAAA;;;AACG;AAMA,KGSA,SHTA,CAAA,SAAgB,OAAA,CAAA,GAAA;EAAA,EAAA,EAAA,MAAA;EAAA,IACrB,EAAA,UAAA,GAAA,OAAA,GAAA,cAAA;EAAG,KACD,EGUA,MHVA;EAAyB,IACrB,EAAA,MAAA;EAAa,OAArB,EGWM,gBHXN;EAAO,MAAA,EGYF,KHZE,CGYI,WHZJ,CAAA;AAEZ,CAAA;AAWA,UGEU,gBAAA,CHFuB;EAAA,CAAA,MAAA,CAAA,QAAA,GAAA,EGGV,gBHHU,CAAA,CAAA,MAAA,EAAA,MAAA,CAAA,CAAA;;;;;AASA,KGArB,WAAA,GACR,gBHD6B,GGE7B,MHF6B,CAAA,MAAA,EAAA,MAAA,EAAA,GAAA,MAAA,GAAA,SAAA,CAAA;;AAUX;AAOtB;;AAC+C,KGVnC,SAAA,GHUmC,CAAA,GAAA,EAAA,MAAA,EAAA,OAAA,CAAA,EGRnC,WHQmC,EAAA,GGP1C,OHO0C,CGPlC,aHOkC,CAAA;AAA5B,UGLF,iBAAA,CHKE,CAA2B;AAG9C;AAWA;;AACkB,UGbD,uBHaC,CAAA,OAAA,EAAA,MAAA,CAAA,CAAA;EAAyB,MAAG,EGXxC,iBHWwC,CGXtB,OHWsB,CAAA,CAAA,QAAA,CAAA,GGVxC,mBHUwC,CGVpB,OHUoB,CAAA,CAAA,QAAA,CAAA,GGTxC,mBHSwC,CGTpB,MHSoB,CAAA;EAAsB,OAO1D,CAAA,EGfE,gBHeF;AAAO;AAMjB;;;AAA8C,KGflC,yBHekC,CAAA,oBGdxB,qBHcwB,EAAA,OAAA,CAAA,GGZ1C,UHY0C,CGX5C,uBHW4C,CGXpB,OHWoB,EGXX,eHWW,CGXK,WHWL,CAAA,CAAA,EGV5C,eHU4C,CGV5B,WHU4B,CAAA,CAAA;AAAK;AAAI;AAOvD;AAAqC,KGXzB,uBHWyB,CAAA,oBGVf,qBHUe,EAAA,OAAA,CAAA,GGRjC,QHQiC,CGPnC,uBHOmC,CGPX,OHOW,EGPF,eHOE,CGPc,WHOd,CAAA,CAAA,EGNnC,eHMmC,CGNnB,WHMmB,CAAA,CAAA;;;AAKf;KGLV,kCACU;MAIhB,UAAU;aAER,UAAU,YACX,0BAA0B,aAAa;AF7FI,CAAA,EAAA,GE8F5C,yBF5Fc,CE4FY,WF5FZ,EE4FyB,OF5FzB,CAAA;;;;AAClB,KEgGU,QFhGV,CAAA,oBEgGuC,qBFhGvC,CAAA,GAAA,CAAA,IAAA,EEiGM,iBFjGN,EAAA,GEkGG,aFlGH,CEkGiB,WFlGjB,CAAA;AAAgB"}