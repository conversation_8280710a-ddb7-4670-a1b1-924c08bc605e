{"version": 3, "file": "httpUtils-Bkv1johT.mjs", "names": ["fn: unknown", "customFetchImpl?: FetchEsque | NativeFetchEsque", "opts: HTTPLinkBaseOptions<AnyClientTypes>", "array: unknown[]", "dict: Record<number, unknown>", "opts: GetInputOptions", "getUrl: GetUrl", "queryParts: string[]", "getBody: GetBody", "jsonHttpRequester: Requester", "signal: Maybe<AbortSignal>", "opts: HTTPRequestOptions"], "sources": ["../src/getFetch.ts", "../src/links/internals/httpUtils.ts"], "sourcesContent": ["import type { FetchEsque, NativeFetchEsque } from './internals/types';\n\ntype AnyFn = (...args: any[]) => unknown;\n\nconst isFunction = (fn: unknown): fn is AnyFn => typeof fn === 'function';\n\nexport function getFetch(\n  customFetchImpl?: FetchEsque | NativeFetchEsque,\n): FetchEsque {\n  if (customFetchImpl) {\n    return customFetchImpl as FetchEsque;\n  }\n\n  if (typeof window !== 'undefined' && isFunction(window.fetch)) {\n    return window.fetch as FetchEsque;\n  }\n\n  if (typeof globalThis !== 'undefined' && isFunction(globalThis.fetch)) {\n    return globalThis.fetch as FetchEsque;\n  }\n\n  throw new Error('No fetch implementation found');\n}\n", "import type {\n  AnyClientTypes,\n  CombinedDataTransformer,\n  Maybe,\n  ProcedureType,\n  TRPCAcceptHeader,\n  TRPCResponse,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { getFetch } from '../../getFetch';\nimport type {\n  FetchEsque,\n  RequestInitEsque,\n  ResponseEsque,\n} from '../../internals/types';\nimport type { TransformerOptions } from '../../unstable-internals';\nimport { getTransformer } from '../../unstable-internals';\nimport type { HTTPHeaders } from '../types';\n\n/**\n * @internal\n */\nexport type HTTPLinkBaseOptions<\n  TRoot extends Pick<AnyClientTypes, 'transformer'>,\n> = {\n  url: string | URL;\n  /**\n   * Add ponyfill for fetch\n   */\n  fetch?: FetchEsque;\n  /**\n   * Send all requests `as POST`s requests regardless of the procedure type\n   * The HTTP handler must separately allow overriding the method. See:\n   * @see https://trpc.io/docs/rpc\n   */\n  methodOverride?: 'POST';\n} & TransformerOptions<TRoot>;\n\nexport interface ResolvedHTTPLinkOptions {\n  url: string;\n  fetch?: FetchEsque;\n  transformer: CombinedDataTransformer;\n  methodOverride?: 'POST';\n}\n\nexport function resolveHTTPLinkOptions(\n  opts: HTTPLinkBaseOptions<AnyClientTypes>,\n): ResolvedHTTPLinkOptions {\n  return {\n    url: opts.url.toString(),\n    fetch: opts.fetch,\n    transformer: getTransformer(opts.transformer),\n    methodOverride: opts.methodOverride,\n  };\n}\n\n// https://github.com/trpc/trpc/pull/669\nfunction arrayToDict(array: unknown[]) {\n  const dict: Record<number, unknown> = {};\n  for (let index = 0; index < array.length; index++) {\n    const element = array[index];\n    dict[index] = element;\n  }\n  return dict;\n}\n\nconst METHOD = {\n  query: 'GET',\n  mutation: 'POST',\n  subscription: 'PATCH',\n} as const;\n\nexport interface HTTPResult {\n  json: TRPCResponse;\n  meta: {\n    response: ResponseEsque;\n    responseJSON?: unknown;\n  };\n}\n\ntype GetInputOptions = {\n  transformer: CombinedDataTransformer;\n} & ({ input: unknown } | { inputs: unknown[] });\n\nexport function getInput(opts: GetInputOptions) {\n  return 'input' in opts\n    ? opts.transformer.input.serialize(opts.input)\n    : arrayToDict(\n        opts.inputs.map((_input) => opts.transformer.input.serialize(_input)),\n      );\n}\n\nexport type HTTPBaseRequestOptions = GetInputOptions &\n  ResolvedHTTPLinkOptions & {\n    type: ProcedureType;\n    path: string;\n    signal: Maybe<AbortSignal>;\n  };\n\ntype GetUrl = (opts: HTTPBaseRequestOptions) => string;\ntype GetBody = (opts: HTTPBaseRequestOptions) => RequestInitEsque['body'];\n\nexport type ContentOptions = {\n  trpcAcceptHeader?: TRPCAcceptHeader;\n  contentTypeHeader?: string;\n  getUrl: GetUrl;\n  getBody: GetBody;\n};\n\nexport const getUrl: GetUrl = (opts) => {\n  const parts = opts.url.split('?') as [string, string?];\n  const base = parts[0].replace(/\\/$/, ''); // Remove any trailing slashes\n\n  let url = base + '/' + opts.path;\n  const queryParts: string[] = [];\n\n  if (parts[1]) {\n    queryParts.push(parts[1]);\n  }\n  if ('inputs' in opts) {\n    queryParts.push('batch=1');\n  }\n  if (opts.type === 'query' || opts.type === 'subscription') {\n    const input = getInput(opts);\n    if (input !== undefined && opts.methodOverride !== 'POST') {\n      queryParts.push(`input=${encodeURIComponent(JSON.stringify(input))}`);\n    }\n  }\n  if (queryParts.length) {\n    url += '?' + queryParts.join('&');\n  }\n  return url;\n};\n\nexport const getBody: GetBody = (opts) => {\n  if (opts.type === 'query' && opts.methodOverride !== 'POST') {\n    return undefined;\n  }\n  const input = getInput(opts);\n  return input !== undefined ? JSON.stringify(input) : undefined;\n};\n\nexport type Requester = (\n  opts: HTTPBaseRequestOptions & {\n    headers: () => HTTPHeaders | Promise<HTTPHeaders>;\n  },\n) => Promise<HTTPResult>;\n\nexport const jsonHttpRequester: Requester = (opts) => {\n  return httpRequest({\n    ...opts,\n    contentTypeHeader: 'application/json',\n    getUrl,\n    getBody,\n  });\n};\n\n/**\n * Polyfill for DOMException with AbortError name\n */\nclass AbortError extends Error {\n  constructor() {\n    const name = 'AbortError';\n    super(name);\n    this.name = name;\n    this.message = name;\n  }\n}\n\nexport type HTTPRequestOptions = ContentOptions &\n  HTTPBaseRequestOptions & {\n    headers: () => HTTPHeaders | Promise<HTTPHeaders>;\n  };\n\n/**\n * Polyfill for `signal.throwIfAborted()`\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/throwIfAborted\n */\nconst throwIfAborted = (signal: Maybe<AbortSignal>) => {\n  if (!signal?.aborted) {\n    return;\n  }\n  // If available, use the native implementation\n  signal.throwIfAborted?.();\n\n  // If we have `DOMException`, use it\n  if (typeof DOMException !== 'undefined') {\n    throw new DOMException('AbortError', 'AbortError');\n  }\n\n  // Otherwise, use our own implementation\n  throw new AbortError();\n};\n\nexport async function fetchHTTPResponse(opts: HTTPRequestOptions) {\n  throwIfAborted(opts.signal);\n\n  const url = opts.getUrl(opts);\n  const body = opts.getBody(opts);\n  const { type } = opts;\n  const resolvedHeaders = await (async () => {\n    const heads = await opts.headers();\n    if (Symbol.iterator in heads) {\n      return Object.fromEntries(heads);\n    }\n    return heads;\n  })();\n  const headers = {\n    ...(opts.contentTypeHeader\n      ? { 'content-type': opts.contentTypeHeader }\n      : {}),\n    ...(opts.trpcAcceptHeader\n      ? { 'trpc-accept': opts.trpcAcceptHeader }\n      : undefined),\n    ...resolvedHeaders,\n  };\n\n  return getFetch(opts.fetch)(url, {\n    method: opts.methodOverride ?? METHOD[type],\n    signal: opts.signal,\n    body,\n    headers,\n  });\n}\n\nexport async function httpRequest(\n  opts: HTTPRequestOptions,\n): Promise<HTTPResult> {\n  const meta = {} as HTTPResult['meta'];\n\n  const res = await fetchHTTPResponse(opts);\n  meta.response = res;\n\n  const json = await res.json();\n\n  meta.responseJSON = json;\n\n  return {\n    json: json as TRPCResponse,\n    meta,\n  };\n}\n"], "mappings": ";;;;AAIA,MAAM,aAAa,CAACA,cAAoC,OAAO;AAE/D,SAAgB,SACdC,iBACY;AACZ,KAAI,gBACF,QAAO;AAGT,YAAW,WAAW,eAAe,WAAW,OAAO,MAAM,CAC3D,QAAO,OAAO;AAGhB,YAAW,eAAe,eAAe,WAAW,WAAW,MAAM,CACnE,QAAO,WAAW;AAGpB,OAAM,IAAI,MAAM;AACjB;;;;;ACsBD,SAAgB,uBACdC,MACyB;AACzB,QAAO;EACL,KAAK,KAAK,IAAI,UAAU;EACxB,OAAO,KAAK;EACZ,aAAa,eAAe,KAAK,YAAY;EAC7C,gBAAgB,KAAK;CACtB;AACF;AAGD,SAAS,YAAYC,OAAkB;CACrC,MAAMC,OAAgC,CAAE;AACxC,MAAK,IAAI,QAAQ,GAAG,QAAQ,MAAM,QAAQ,SAAS;EACjD,MAAM,UAAU,MAAM;AACtB,OAAK,SAAS;CACf;AACD,QAAO;AACR;AAED,MAAM,SAAS;CACb,OAAO;CACP,UAAU;CACV,cAAc;AACf;AAcD,SAAgB,SAASC,MAAuB;AAC9C,QAAO,WAAW,OACd,KAAK,YAAY,MAAM,UAAU,KAAK,MAAM,GAC5C,YACE,KAAK,OAAO,IAAI,CAAC,WAAW,KAAK,YAAY,MAAM,UAAU,OAAO,CAAC,CACtE;AACN;AAmBD,MAAaC,SAAiB,CAAC,SAAS;CACtC,MAAM,QAAQ,KAAK,IAAI,MAAM,IAAI;CACjC,MAAM,OAAO,MAAM,GAAG,QAAQ,OAAO,GAAG;CAExC,IAAI,MAAM,OAAO,MAAM,KAAK;CAC5B,MAAMC,aAAuB,CAAE;AAE/B,KAAI,MAAM,GACR,YAAW,KAAK,MAAM,GAAG;AAE3B,KAAI,YAAY,KACd,YAAW,KAAK,UAAU;AAE5B,KAAI,KAAK,SAAS,WAAW,KAAK,SAAS,gBAAgB;EACzD,MAAM,QAAQ,SAAS,KAAK;AAC5B,MAAI,oBAAuB,KAAK,mBAAmB,OACjD,YAAW,MAAM,QAAQ,mBAAmB,KAAK,UAAU,MAAM,CAAC,CAAC,EAAE;CAExE;AACD,KAAI,WAAW,OACb,QAAO,MAAM,WAAW,KAAK,IAAI;AAEnC,QAAO;AACR;AAED,MAAaC,UAAmB,CAAC,SAAS;AACxC,KAAI,KAAK,SAAS,WAAW,KAAK,mBAAmB,OACnD;CAEF,MAAM,QAAQ,SAAS,KAAK;AAC5B,QAAO,mBAAsB,KAAK,UAAU,MAAM;AACnD;AAQD,MAAaC,oBAA+B,CAAC,SAAS;AACpD,QAAO,oFACF;EACH,mBAAmB;EACnB;EACA;IACA;AACH;;;;AAKD,IAAM,aAAN,cAAyB,MAAM;CAC7B,cAAc;EACZ,MAAM,OAAO;AACb,QAAM,KAAK;AACX,OAAK,OAAO;AACZ,OAAK,UAAU;CAChB;AACF;;;;;;AAYD,MAAM,iBAAiB,CAACC,WAA+B;;AACrD,uDAAK,OAAQ,SACX;AAGF,iCAAO,gEAAP,kCAAyB;AAGzB,YAAW,iBAAiB,YAC1B,OAAM,IAAI,aAAa,cAAc;AAIvC,OAAM,IAAI;AACX;AAED,eAAsB,kBAAkBC,MAA0B;;AAChE,gBAAe,KAAK,OAAO;CAE3B,MAAM,MAAM,KAAK,OAAO,KAAK;CAC7B,MAAM,OAAO,KAAK,QAAQ,KAAK;CAC/B,MAAM,EAAE,MAAM,GAAG;CACjB,MAAM,kBAAkB,MAAM,CAAC,YAAY;EACzC,MAAM,QAAQ,MAAM,KAAK,SAAS;AAClC,MAAI,OAAO,YAAY,MACrB,QAAO,OAAO,YAAY,MAAM;AAElC,SAAO;CACR,IAAG;CACJ,MAAM,oHACA,KAAK,oBACL,EAAE,gBAAgB,KAAK,kBAAmB,IAC1C,CAAE,IACF,KAAK,mBACL,EAAE,eAAe,KAAK,iBAAkB,aAEzC;AAGL,QAAO,SAAS,KAAK,MAAM,CAAC,KAAK;EAC/B,gCAAQ,KAAK,qFAAkB,OAAO;EACtC,QAAQ,KAAK;EACb;EACA;CACD,EAAC;AACH;AAED,eAAsB,YACpBA,MACqB;CACrB,MAAM,OAAO,CAAE;CAEf,MAAM,MAAM,MAAM,kBAAkB,KAAK;AACzC,MAAK,WAAW;CAEhB,MAAM,OAAO,MAAM,IAAI,MAAM;AAE7B,MAAK,eAAe;AAEpB,QAAO;EACC;EACN;CACD;AACF"}