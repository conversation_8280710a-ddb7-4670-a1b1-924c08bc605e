{"version": 3, "file": "TRPCClientError-CjKyS10w.mjs", "names": ["cause: unknown", "obj: unknown", "err: unknown", "fallback: string", "message: string", "opts?: {\n      result?: Maybe<TRPCErrorResponse<inferErrorShape<TRouterOrProcedure>>>;\n      cause?: Error;\n      meta?: Record<string, unknown>;\n    }", "_cause: Error | TRPCErrorResponse<any> | object", "opts: { meta?: Record<string, unknown> }"], "sources": ["../src/TRPCClientError.ts"], "sourcesContent": ["import type {\n  inferClientTypes,\n  InferrableClientTypes,\n  Maybe,\n  TRPCErrorResponse,\n} from '@trpc/server/unstable-core-do-not-import';\nimport {\n  isObject,\n  type DefaultErrorShape,\n} from '@trpc/server/unstable-core-do-not-import';\n\ntype inferErrorShape<TInferrable extends InferrableClientTypes> =\n  inferClientTypes<TInferrable>['errorShape'];\nexport interface TRPCClientErrorBase<TShape extends DefaultErrorShape> {\n  readonly message: string;\n  readonly shape: Maybe<TShape>;\n  readonly data: Maybe<TShape['data']>;\n}\nexport type TRPCClientErrorLike<TInferrable extends InferrableClientTypes> =\n  TRPCClientErrorBase<inferErrorShape<TInferrable>>;\n\nexport function isTRPCClientError<TInferrable extends InferrableClientTypes>(\n  cause: unknown,\n): cause is TRPCClientError<TInferrable> {\n  return cause instanceof TRPCClientError;\n}\n\nfunction isTRPCErrorResponse(obj: unknown): obj is TRPCErrorResponse<any> {\n  return (\n    isObject(obj) &&\n    isObject(obj['error']) &&\n    typeof obj['error']['code'] === 'number' &&\n    typeof obj['error']['message'] === 'string'\n  );\n}\n\nfunction getMessageFromUnknownError(err: unknown, fallback: string): string {\n  if (typeof err === 'string') {\n    return err;\n  }\n  if (isObject(err) && typeof err['message'] === 'string') {\n    return err['message'];\n  }\n  return fallback;\n}\n\nexport class TRPCClientError<TRouterOrProcedure extends InferrableClientTypes>\n  extends Error\n  implements TRPCClientErrorBase<inferErrorShape<TRouterOrProcedure>>\n{\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore override doesn't work in all environments due to \"This member cannot have an 'override' modifier because it is not declared in the base class 'Error'\"\n  public override readonly cause;\n  public readonly shape: Maybe<inferErrorShape<TRouterOrProcedure>>;\n  public readonly data: Maybe<inferErrorShape<TRouterOrProcedure>['data']>;\n\n  /**\n   * Additional meta data about the error\n   * In the case of HTTP-errors, we'll have `response` and potentially `responseJSON` here\n   */\n  public meta;\n\n  constructor(\n    message: string,\n    opts?: {\n      result?: Maybe<TRPCErrorResponse<inferErrorShape<TRouterOrProcedure>>>;\n      cause?: Error;\n      meta?: Record<string, unknown>;\n    },\n  ) {\n    const cause = opts?.cause;\n\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore https://github.com/tc39/proposal-error-cause\n    super(message, { cause });\n\n    this.meta = opts?.meta;\n\n    this.cause = cause;\n    this.shape = opts?.result?.error;\n    this.data = opts?.result?.error.data;\n    this.name = 'TRPCClientError';\n\n    Object.setPrototypeOf(this, TRPCClientError.prototype);\n  }\n\n  public static from<TRouterOrProcedure extends InferrableClientTypes>(\n    _cause: Error | TRPCErrorResponse<any> | object,\n    opts: { meta?: Record<string, unknown> } = {},\n  ): TRPCClientError<TRouterOrProcedure> {\n    const cause = _cause as unknown;\n\n    if (isTRPCClientError(cause)) {\n      if (opts.meta) {\n        // Decorate with meta error data\n        cause.meta = {\n          ...cause.meta,\n          ...opts.meta,\n        };\n      }\n      return cause;\n    }\n    if (isTRPCErrorResponse(cause)) {\n      return new TRPCClientError(cause.error.message, {\n        ...opts,\n        result: cause,\n      });\n    }\n    return new TRPCClientError(\n      getMessageFromUnknownError(cause, 'Unknown error'),\n      {\n        ...opts,\n        cause: cause as any,\n      },\n    );\n  }\n}\n"], "mappings": ";;;;;;AAqBA,SAAgB,kBACdA,OACuC;AACvC,QAAO,iBAAiB;AACzB;AAED,SAAS,oBAAoBC,KAA6C;AACxE,QACE,SAAS,IAAI,IACb,SAAS,IAAI,SAAS,WACf,IAAI,SAAS,YAAY,mBACzB,IAAI,SAAS,eAAe;AAEtC;AAED,SAAS,2BAA2BC,KAAcC,UAA0B;AAC1E,YAAW,QAAQ,SACjB,QAAO;AAET,KAAI,SAAS,IAAI,WAAW,IAAI,eAAe,SAC7C,QAAO,IAAI;AAEb,QAAO;AACR;AAED,IAAa,kBAAb,MAAa,wBACH,MAEV;CAaE,YACEC,SACAC,MAKA;;EACA,MAAM,oDAAQ,KAAM;AAIpB,QAAM,SAAS,EAAE,MAAO,EAAC;qCA2C1B,MAjEwB;qCAiEvB,MAhEc;qCAgEb,MA/Da;qCA+DZ,MAzDG;AAgBL,OAAK,mDAAO,KAAM;AAElB,OAAK,QAAQ;AACb,OAAK,4DAAQ,KAAM,oEAAQ;AAC3B,OAAK,4DAAO,KAAM,sEAAQ,MAAM;AAChC,OAAK,OAAO;AAEZ,SAAO,eAAe,MAAM,gBAAgB,UAAU;CACvD;CAED,OAAc,KACZC,QACAC,OAA2C,CAAE,GACR;EACrC,MAAM,QAAQ;AAEd,MAAI,kBAAkB,MAAM,EAAE;AAC5B,OAAI,KAAK,KAEP,OAAM,+EACD,MAAM,OACN,KAAK;AAGZ,UAAO;EACR;AACD,MAAI,oBAAoB,MAAM,CAC5B,QAAO,IAAI,gBAAgB,MAAM,MAAM,iFAClC,aACH,QAAQ;AAGZ,SAAO,IAAI,gBACT,2BAA2B,OAAO,gBAAgB,0EAE7C,aACI;CAGZ;AACF"}