{"version": 3, "file": "httpLink-CYOcG9kQ.mjs", "names": ["input: unknown", "universalRequester: Requester", "opts: HTTPLinkOptions<TRouter['_def']['_config']['$types']>", "meta: HTTPResult['meta'] | undefined"], "sources": ["../src/links/internals/contentTypes.ts", "../src/links/httpLink.ts"], "sourcesContent": ["export function isOctetType(input: unknown) {\n  return (\n    input instanceof Uint8Array ||\n    // File extends from Blob but is only available in nodejs from v20\n    input instanceof Blob\n  );\n}\n\nexport function isFormData(input: unknown) {\n  return input instanceof FormData;\n}\n\nexport function isNonJsonSerializable(input: unknown) {\n  return isOctetType(input) || isFormData(input);\n}\n", "import { observable } from '@trpc/server/observable';\nimport type {\n  AnyClientTypes,\n  AnyRouter,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { transformResult } from '@trpc/server/unstable-core-do-not-import';\nimport { TRPCClientError } from '../TRPCClientError';\nimport type {\n  HTTPLinkBaseOptions,\n  HTTPResult,\n  Requester,\n} from './internals/httpUtils';\nimport {\n  getUrl,\n  httpRequest,\n  jsonHttpRequester,\n  resolveHTTPLinkOptions,\n} from './internals/httpUtils';\nimport {\n  isFormData,\n  isOctetType,\n  type HTTPHeaders,\n  type Operation,\n  type TRPCLink,\n} from './types';\n\nexport type HTTPLinkOptions<TRoot extends AnyClientTypes> =\n  HTTPLinkBaseOptions<TRoot> & {\n    /**\n     * Headers to be set on outgoing requests or a callback that of said headers\n     * @see http://trpc.io/docs/client/headers\n     */\n    headers?:\n      | HTTPHeaders\n      | ((opts: { op: Operation }) => HTTPHeaders | Promise<HTTPHeaders>);\n  };\n\nconst universalRequester: Requester = (opts) => {\n  if ('input' in opts) {\n    const { input } = opts;\n    if (isFormData(input)) {\n      if (opts.type !== 'mutation' && opts.methodOverride !== 'POST') {\n        throw new Error('FormData is only supported for mutations');\n      }\n\n      return httpRequest({\n        ...opts,\n        // The browser will set this automatically and include the boundary= in it\n        contentTypeHeader: undefined,\n        getUrl,\n        getBody: () => input,\n      });\n    }\n\n    if (isOctetType(input)) {\n      if (opts.type !== 'mutation' && opts.methodOverride !== 'POST') {\n        throw new Error('Octet type input is only supported for mutations');\n      }\n\n      return httpRequest({\n        ...opts,\n        contentTypeHeader: 'application/octet-stream',\n        getUrl,\n        getBody: () => input,\n      });\n    }\n  }\n\n  return jsonHttpRequester(opts);\n};\n\n/**\n * @see https://trpc.io/docs/client/links/httpLink\n */\nexport function httpLink<TRouter extends AnyRouter = AnyRouter>(\n  opts: HTTPLinkOptions<TRouter['_def']['_config']['$types']>,\n): TRPCLink<TRouter> {\n  const resolvedOpts = resolveHTTPLinkOptions(opts);\n  return () => {\n    return ({ op }) => {\n      return observable((observer) => {\n        const { path, input, type } = op;\n        /* istanbul ignore if -- @preserve */\n        if (type === 'subscription') {\n          throw new Error(\n            'Subscriptions are unsupported by `httpLink` - use `httpSubscriptionLink` or `wsLink`',\n          );\n        }\n\n        const request = universalRequester({\n          ...resolvedOpts,\n          type,\n          path,\n          input,\n          signal: op.signal,\n          headers() {\n            if (!opts.headers) {\n              return {};\n            }\n            if (typeof opts.headers === 'function') {\n              return opts.headers({\n                op,\n              });\n            }\n            return opts.headers;\n          },\n        });\n        let meta: HTTPResult['meta'] | undefined = undefined;\n        request\n          .then((res) => {\n            meta = res.meta;\n            const transformed = transformResult(\n              res.json,\n              resolvedOpts.transformer.output,\n            );\n\n            if (!transformed.ok) {\n              observer.error(\n                TRPCClientError.from(transformed.error, {\n                  meta,\n                }),\n              );\n              return;\n            }\n            observer.next({\n              context: res.meta,\n              result: transformed.result,\n            });\n            observer.complete();\n          })\n          .catch((cause) => {\n            observer.error(TRPCClientError.from(cause, { meta }));\n          });\n\n        return () => {\n          // noop\n        };\n      });\n    };\n  };\n}\n"], "mappings": ";;;;;;;AAAA,SAAgB,YAAYA,OAAgB;AAC1C,QACE,iBAAiB,cAEjB,iBAAiB;AAEpB;AAED,SAAgB,WAAWA,OAAgB;AACzC,QAAO,iBAAiB;AACzB;AAED,SAAgB,sBAAsBA,OAAgB;AACpD,QAAO,YAAY,MAAM,IAAI,WAAW,MAAM;AAC/C;;;;;ACuBD,MAAMC,qBAAgC,CAAC,SAAS;AAC9C,KAAI,WAAW,MAAM;EACnB,MAAM,EAAE,OAAO,GAAG;AAClB,MAAI,WAAW,MAAM,EAAE;AACrB,OAAI,KAAK,SAAS,cAAc,KAAK,mBAAmB,OACtD,OAAM,IAAI,MAAM;AAGlB,UAAO,oFACF;IAEH;IACA;IACA,SAAS,MAAM;MACf;EACH;AAED,MAAI,YAAY,MAAM,EAAE;AACtB,OAAI,KAAK,SAAS,cAAc,KAAK,mBAAmB,OACtD,OAAM,IAAI,MAAM;AAGlB,UAAO,oFACF;IACH,mBAAmB;IACnB;IACA,SAAS,MAAM;MACf;EACH;CACF;AAED,QAAO,kBAAkB,KAAK;AAC/B;;;;AAKD,SAAgB,SACdC,MACmB;CACnB,MAAM,eAAe,uBAAuB,KAAK;AACjD,QAAO,MAAM;AACX,SAAO,CAAC,EAAE,IAAI,KAAK;AACjB,UAAO,WAAW,CAAC,aAAa;IAC9B,MAAM,EAAE,MAAM,OAAO,MAAM,GAAG;;AAE9B,QAAI,SAAS,eACX,OAAM,IAAI,MACR;IAIJ,MAAM,UAAU,2FACX;KACH;KACA;KACA;KACA,QAAQ,GAAG;KACX,UAAU;AACR,WAAK,KAAK,QACR,QAAO,CAAE;AAEX,iBAAW,KAAK,YAAY,WAC1B,QAAO,KAAK,QAAQ,EAClB,GACD,EAAC;AAEJ,aAAO,KAAK;KACb;OACD;IACF,IAAIC;AACJ,YACG,KAAK,CAAC,QAAQ;AACb,YAAO,IAAI;KACX,MAAM,cAAc,gBAClB,IAAI,MACJ,aAAa,YAAY,OAC1B;AAED,UAAK,YAAY,IAAI;AACnB,eAAS,MACP,gBAAgB,KAAK,YAAY,OAAO,EACtC,KACD,EAAC,CACH;AACD;KACD;AACD,cAAS,KAAK;MACZ,SAAS,IAAI;MACb,QAAQ,YAAY;KACrB,EAAC;AACF,cAAS,UAAU;IACpB,EAAC,CACD,MAAM,CAAC,UAAU;AAChB,cAAS,MAAM,gBAAgB,KAAK,OAAO,EAAE,KAAM,EAAC,CAAC;IACtD,EAAC;AAEJ,WAAO,MAAM,CAEZ;GACF,EAAC;EACH;CACF;AACF"}