{"version": 3, "file": "unstable-internals-Bg7n9BBj.mjs", "names": ["transformer:\n    | TransformerOptions<{ transformer: false }>['transformer']\n    | TransformerOptions<{ transformer: true }>['transformer']\n    | undefined"], "sources": ["../src/internals/transformer.ts"], "sourcesContent": ["import type {\n  AnyClientTypes,\n  CombinedDataTransformer,\n  DataTransformerOptions,\n  TypeError,\n} from '@trpc/server/unstable-core-do-not-import';\n\n/**\n * @internal\n */\nexport type CoercedTransformerParameters = {\n  transformer?: DataTransformerOptions;\n};\n\ntype TransformerOptionYes = {\n  /**\n   * Data transformer\n   *\n   * You must use the same transformer on the backend and frontend\n   * @see https://trpc.io/docs/v11/data-transformers\n   **/\n  transformer: DataTransformerOptions;\n};\ntype TransformerOptionNo = {\n  /**\n   * Data transformer\n   *\n   * You must use the same transformer on the backend and frontend\n   * @see https://trpc.io/docs/v11/data-transformers\n   **/\n  transformer?: TypeError<'You must define a transformer on your your `initTRPC`-object first'>;\n};\n\n/**\n * @internal\n */\nexport type TransformerOptions<\n  TRoot extends Pick<AnyClientTypes, 'transformer'>,\n> = TRoot['transformer'] extends true\n  ? TransformerOptionYes\n  : TransformerOptionNo;\n/**\n * @internal\n */\n\n/**\n * @internal\n */\nexport function getTransformer(\n  transformer:\n    | TransformerOptions<{ transformer: false }>['transformer']\n    | TransformerOptions<{ transformer: true }>['transformer']\n    | undefined,\n): CombinedDataTransformer {\n  const _transformer =\n    transformer as CoercedTransformerParameters['transformer'];\n  if (!_transformer) {\n    return {\n      input: {\n        serialize: (data) => data,\n        deserialize: (data) => data,\n      },\n      output: {\n        serialize: (data) => data,\n        deserialize: (data) => data,\n      },\n    };\n  }\n  if ('input' in _transformer) {\n    return _transformer;\n  }\n  return {\n    input: _transformer,\n    output: _transformer,\n  };\n}\n"], "mappings": ";;;;;;;AAgDA,SAAgB,eACdA,aAIyB;CACzB,MAAM,eACJ;AACF,MAAK,aACH,QAAO;EACL,OAAO;GACL,WAAW,CAAC,SAAS;GACrB,aAAa,CAAC,SAAS;EACxB;EACD,QAAQ;GACN,WAAW,CAAC,SAAS;GACrB,aAAa,CAAC,SAAS;EACxB;CACF;AAEH,KAAI,WAAW,aACb,QAAO;AAET,QAAO;EACL,OAAO;EACP,QAAQ;CACT;AACF"}