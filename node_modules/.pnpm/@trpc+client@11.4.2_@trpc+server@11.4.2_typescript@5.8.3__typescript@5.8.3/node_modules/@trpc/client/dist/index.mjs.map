{"version": 3, "file": "index.mjs", "names": ["opts: CreateTRPCClientOptions<TInferrable>", "opts: {\n    type: TRPCType;\n    input: TInput;\n    path: string;\n    context?: OperationContext;\n    signal: Maybe<AbortSignal>;\n  }", "path: string", "input?: unknown", "opts?: TRPCRequestOptions", "input: unknown", "opts: Partial<\n      TRPCSubscriptionObserver<unknown, TRPCClientError<AnyRouter>>\n    > &\n      TRPCRequestOptions", "opts: CreateTRPCClientOptions<TRouter>", "clientCallTypeMap: Record<\n  keyof DecorateProcedure<any, any>,\n  ProcedureType\n>", "clientCallType: string", "client: TRPCUntypedClient<TRouter>", "opts: CreateTRPCClientOptions<TRouter>", "client: TRPCClient<TRouter>", "opts: HTTPBatchLinkOptions<TRouter['_def']['_config']['$types']>", "type: ProcedureType", "opts", "json: TRPCResponse", "input: unknown", "lastEventId: string | undefined", "_asyncIterator", "r", "AsyncFromSyncIterator", "opts: UrlOptionsWithConnectionParams", "opts: HTTPSubscriptionLinkOptions<\n    inferClientTypes<TInferrable>,\n    TEventSource\n  >", "lastEventId: string | undefined", "result: TRPCResult<unknown>", "opts: RetryLinkOptions<TInferrable>", "next$: Unsubscribable", "callNextTimeout: ReturnType<typeof setTimeout> | undefined", "lastEventId: string | undefined", "attempts: number", "r", "e", "n", "o", "OverloadYield", "_awaitAsyncGenerator", "_wrapAsyncGenerator", "r", "t", "e", "opts: LocalLinkOptions<TRouter>", "chunk: unknown", "ctx: inferRouterContext<TRouter> | undefined", "newInput: unknown", "cause: unknown", "lastEventId: string | undefined", "chunk: TRPCResult<unknown>", "experimental_localLink: typeof unstable_localLink"], "sources": ["../src/internals/TRPCUntypedClient.ts", "../src/createTRPCUntypedClient.ts", "../src/createTRPCClient.ts", "../src/links/httpBatchStreamLink.ts", "../src/internals/inputWithTrackedEventId.ts", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/asyncIterator.js", "../src/links/httpSubscriptionLink.ts", "../src/links/retryLink.ts", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/usingCtx.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/OverloadYield.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/awaitAsyncGenerator.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/wrapAsyncGenerator.js", "../src/links/localLink.ts"], "sourcesContent": ["import type {\n  inferObservableValue,\n  Unsubscribable,\n} from '@trpc/server/observable';\nimport { observableToPromise, share } from '@trpc/server/observable';\nimport type {\n  AnyRouter,\n  inferAsyncIterableYield,\n  InferrableClientTypes,\n  Maybe,\n  TypeError,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { createChain } from '../links/internals/createChain';\nimport type { TRPCConnectionState } from '../links/internals/subscriptions';\nimport type {\n  OperationContext,\n  OperationLink,\n  TRPCClientRuntime,\n  TRPCLink,\n} from '../links/types';\nimport { TRPCClientError } from '../TRPCClientError';\n\ntype TRPCType = 'mutation' | 'query' | 'subscription';\nexport interface TRPCRequestOptions {\n  /**\n   * Pass additional context to links\n   */\n  context?: OperationContext;\n  signal?: AbortSignal;\n}\n\nexport interface TRPCSubscriptionObserver<TValue, TError> {\n  onStarted: (opts: { context: OperationContext | undefined }) => void;\n  onData: (value: inferAsyncIterableYield<TValue>) => void;\n  onError: (err: TError) => void;\n  onStopped: () => void;\n  onComplete: () => void;\n  onConnectionStateChange: (state: TRPCConnectionState<TError>) => void;\n}\n\n/** @internal */\nexport type CreateTRPCClientOptions<TRouter extends InferrableClientTypes> = {\n  links: TRPCLink<TRouter>[];\n  transformer?: TypeError<'The transformer property has moved to httpLink/httpBatchLink/wsLink'>;\n};\n\nexport class TRPCUntypedClient<TInferrable extends InferrableClientTypes> {\n  private readonly links: OperationLink<TInferrable>[];\n  public readonly runtime: TRPCClientRuntime;\n  private requestId: number;\n\n  constructor(opts: CreateTRPCClientOptions<TInferrable>) {\n    this.requestId = 0;\n\n    this.runtime = {};\n\n    // Initialize the links\n    this.links = opts.links.map((link) => link(this.runtime));\n  }\n\n  private $request<TInput = unknown, TOutput = unknown>(opts: {\n    type: TRPCType;\n    input: TInput;\n    path: string;\n    context?: OperationContext;\n    signal: Maybe<AbortSignal>;\n  }) {\n    const chain$ = createChain<AnyRouter, TInput, TOutput>({\n      links: this.links as OperationLink<any, any, any>[],\n      op: {\n        ...opts,\n        context: opts.context ?? {},\n        id: ++this.requestId,\n      },\n    });\n    return chain$.pipe(share());\n  }\n\n  private async requestAsPromise<TInput = unknown, TOutput = unknown>(opts: {\n    type: TRPCType;\n    input: TInput;\n    path: string;\n    context?: OperationContext;\n    signal: Maybe<AbortSignal>;\n  }): Promise<TOutput> {\n    try {\n      const req$ = this.$request<TInput, TOutput>(opts);\n      type TValue = inferObservableValue<typeof req$>;\n\n      const envelope = await observableToPromise<TValue>(req$);\n      const data = (envelope.result as any).data;\n      return data;\n    } catch (err) {\n      throw TRPCClientError.from(err as Error);\n    }\n  }\n  public query(path: string, input?: unknown, opts?: TRPCRequestOptions) {\n    return this.requestAsPromise<unknown, unknown>({\n      type: 'query',\n      path,\n      input,\n      context: opts?.context,\n      signal: opts?.signal,\n    });\n  }\n  public mutation(path: string, input?: unknown, opts?: TRPCRequestOptions) {\n    return this.requestAsPromise<unknown, unknown>({\n      type: 'mutation',\n      path,\n      input,\n      context: opts?.context,\n      signal: opts?.signal,\n    });\n  }\n  public subscription(\n    path: string,\n    input: unknown,\n    opts: Partial<\n      TRPCSubscriptionObserver<unknown, TRPCClientError<AnyRouter>>\n    > &\n      TRPCRequestOptions,\n  ): Unsubscribable {\n    const observable$ = this.$request({\n      type: 'subscription',\n      path,\n      input,\n      context: opts.context,\n      signal: opts.signal,\n    });\n    return observable$.subscribe({\n      next(envelope) {\n        switch (envelope.result.type) {\n          case 'state': {\n            opts.onConnectionStateChange?.(envelope.result);\n            break;\n          }\n          case 'started': {\n            opts.onStarted?.({\n              context: envelope.context,\n            });\n            break;\n          }\n          case 'stopped': {\n            opts.onStopped?.();\n            break;\n          }\n          case 'data':\n          case undefined: {\n            opts.onData?.(envelope.result.data);\n            break;\n          }\n        }\n      },\n      error(err) {\n        opts.onError?.(err);\n      },\n      complete() {\n        opts.onComplete?.();\n      },\n    });\n  }\n}\n", "import type { AnyRouter } from '@trpc/server/unstable-core-do-not-import';\nimport type { CreateTRPCClientOptions } from './internals/TRPCUntypedClient';\nimport { TRPCUntypedClient } from './internals/TRPCUntypedClient';\n\nexport function createTRPCUntypedClient<TRouter extends AnyRouter>(\n  opts: CreateTRPCClientOptions<TRouter>,\n): TRPCUntypedClient<TRouter> {\n  return new TRPCUntypedClient(opts);\n}\n\nexport type {\n  CreateTRPCClientOptions,\n  TRPCRequestOptions,\n} from './internals/TRPCUntypedClient';\nexport { TRPCUntypedClient } from './internals/TRPCUntypedClient';\n", "/* eslint-disable @typescript-eslint/no-non-null-assertion */\nimport type { Unsubscribable } from '@trpc/server/observable';\nimport type {\n  AnyProcedure,\n  AnyRouter,\n  inferClientTypes,\n  inferProcedureInput,\n  InferrableClientTypes,\n  inferTransformedProcedureOutput,\n  ProcedureType,\n  RouterRecord,\n} from '@trpc/server/unstable-core-do-not-import';\nimport {\n  createFlatProxy,\n  createRecursiveProxy,\n} from '@trpc/server/unstable-core-do-not-import';\nimport type { CreateTRPCClientOptions } from './createTRPCUntypedClient';\nimport type { TRPCSubscriptionObserver } from './internals/TRPCUntypedClient';\nimport { TRPCUntypedClient } from './internals/TRPCUntypedClient';\nimport type { TRPCProcedureOptions } from './internals/types';\nimport type { TRPCClientError } from './TRPCClientError';\n\n/**\n * @public\n * @deprecated use {@link TRPCClient} instead, will be removed in v12\n **/\nexport type inferRouterClient<TRouter extends AnyRouter> = TRPCClient<TRouter>;\n\n/**\n * @public\n * @deprecated use {@link TRPCClient} instead, will be removed in v12\n **/\nexport type CreateTRPCClient<TRouter extends AnyRouter> = TRPCClient<TRouter>;\n\nconst untypedClientSymbol = Symbol.for('trpc_untypedClient');\n\n/**\n * @public\n **/\nexport type TRPCClient<TRouter extends AnyRouter> = DecoratedProcedureRecord<\n  {\n    transformer: TRouter['_def']['_config']['$types']['transformer'];\n    errorShape: TRouter['_def']['_config']['$types']['errorShape'];\n  },\n  TRouter['_def']['record']\n> & {\n  [untypedClientSymbol]: TRPCUntypedClient<TRouter>;\n};\n\ntype ResolverDef = {\n  input: any;\n  output: any;\n  transformer: boolean;\n  errorShape: any;\n};\n\ntype coerceAsyncGeneratorToIterable<T> =\n  T extends AsyncGenerator<infer $T, infer $Return, infer $Next>\n    ? AsyncIterable<$T, $Return, $Next>\n    : T;\n\n/** @internal */\nexport type Resolver<TDef extends ResolverDef> = (\n  input: TDef['input'],\n  opts?: TRPCProcedureOptions,\n) => Promise<coerceAsyncGeneratorToIterable<TDef['output']>>;\n\ntype SubscriptionResolver<TDef extends ResolverDef> = (\n  input: TDef['input'],\n  opts: Partial<\n    TRPCSubscriptionObserver<TDef['output'], TRPCClientError<TDef>>\n  > &\n    TRPCProcedureOptions,\n) => Unsubscribable;\n\ntype DecorateProcedure<\n  TType extends ProcedureType,\n  TDef extends ResolverDef,\n> = TType extends 'query'\n  ? {\n      query: Resolver<TDef>;\n    }\n  : TType extends 'mutation'\n    ? {\n        mutate: Resolver<TDef>;\n      }\n    : TType extends 'subscription'\n      ? {\n          subscribe: SubscriptionResolver<TDef>;\n        }\n      : never;\n\n/**\n * @internal\n */\ntype DecoratedProcedureRecord<\n  TRoot extends InferrableClientTypes,\n  TRecord extends RouterRecord,\n> = {\n  [TKey in keyof TRecord]: TRecord[TKey] extends infer $Value\n    ? $Value extends AnyProcedure\n      ? DecorateProcedure<\n          $Value['_def']['type'],\n          {\n            input: inferProcedureInput<$Value>;\n            output: inferTransformedProcedureOutput<\n              inferClientTypes<TRoot>,\n              $Value\n            >;\n            errorShape: inferClientTypes<TRoot>['errorShape'];\n            transformer: inferClientTypes<TRoot>['transformer'];\n          }\n        >\n      : $Value extends RouterRecord\n        ? DecoratedProcedureRecord<TRoot, $Value>\n        : never\n    : never;\n};\n\nconst clientCallTypeMap: Record<\n  keyof DecorateProcedure<any, any>,\n  ProcedureType\n> = {\n  query: 'query',\n  mutate: 'mutation',\n  subscribe: 'subscription',\n};\n\n/** @internal */\nexport const clientCallTypeToProcedureType = (\n  clientCallType: string,\n): ProcedureType => {\n  return clientCallTypeMap[clientCallType as keyof typeof clientCallTypeMap];\n};\n\n/**\n * @internal\n */\nexport function createTRPCClientProxy<TRouter extends AnyRouter>(\n  client: TRPCUntypedClient<TRouter>,\n): TRPCClient<TRouter> {\n  const proxy = createRecursiveProxy<TRPCClient<TRouter>>(({ path, args }) => {\n    const pathCopy = [...path];\n    const procedureType = clientCallTypeToProcedureType(pathCopy.pop()!);\n\n    const fullPath = pathCopy.join('.');\n\n    return (client[procedureType] as any)(fullPath, ...(args as any));\n  });\n  return createFlatProxy<TRPCClient<TRouter>>((key) => {\n    if (key === untypedClientSymbol) {\n      return client;\n    }\n    return proxy[key];\n  });\n}\n\nexport function createTRPCClient<TRouter extends AnyRouter>(\n  opts: CreateTRPCClientOptions<TRouter>,\n): TRPCClient<TRouter> {\n  const client = new TRPCUntypedClient(opts);\n  const proxy = createTRPCClientProxy<TRouter>(client);\n  return proxy;\n}\n\n/**\n * Get an untyped client from a proxy client\n * @internal\n */\nexport function getUntypedClient<TRouter extends AnyRouter>(\n  client: TRPCClient<TRouter>,\n): TRPCUntypedClient<TRouter> {\n  return client[untypedClientSymbol];\n}\n", "import type { AnyRouter, ProcedureType } from '@trpc/server';\nimport { observable } from '@trpc/server/observable';\nimport type { TRPCErrorShape, TRPCResponse } from '@trpc/server/rpc';\nimport { jsonlStreamConsumer } from '@trpc/server/unstable-core-do-not-import';\nimport type { BatchLoader } from '../internals/dataLoader';\nimport { dataLoader } from '../internals/dataLoader';\nimport { allAbortSignals, raceAbortSignals } from '../internals/signals';\nimport type { NonEmptyArray } from '../internals/types';\nimport { TRPCClientError } from '../TRPCClientError';\nimport type { HTTPBatchLinkOptions } from './HTTPBatchLinkOptions';\nimport type { HTTPResult } from './internals/httpUtils';\nimport {\n  fetchHTTPResponse,\n  getBody,\n  getUrl,\n  resolveHTTPLinkOptions,\n} from './internals/httpUtils';\nimport type { Operation, TRPCLink } from './types';\n\n/**\n * @see https://trpc.io/docs/client/links/httpBatchStreamLink\n */\nexport function httpBatchStreamLink<TRouter extends AnyRouter>(\n  opts: HTTPBatchLinkOptions<TRouter['_def']['_config']['$types']>,\n): TRPCLink<TRouter> {\n  const resolvedOpts = resolveHTTPLinkOptions(opts);\n  const maxURLLength = opts.maxURLLength ?? Infinity;\n  const maxItems = opts.maxItems ?? Infinity;\n\n  return () => {\n    const batchLoader = (\n      type: ProcedureType,\n    ): BatchLoader<Operation, HTTPResult> => {\n      return {\n        validate(batchOps) {\n          if (maxURLLength === Infinity && maxItems === Infinity) {\n            // escape hatch for quick calcs\n            return true;\n          }\n          if (batchOps.length > maxItems) {\n            return false;\n          }\n          const path = batchOps.map((op) => op.path).join(',');\n          const inputs = batchOps.map((op) => op.input);\n\n          const url = getUrl({\n            ...resolvedOpts,\n            type,\n            path,\n            inputs,\n            signal: null,\n          });\n\n          return url.length <= maxURLLength;\n        },\n        async fetch(batchOps) {\n          const path = batchOps.map((op) => op.path).join(',');\n          const inputs = batchOps.map((op) => op.input);\n\n          const batchSignals = allAbortSignals(\n            ...batchOps.map((op) => op.signal),\n          );\n          const abortController = new AbortController();\n\n          const responsePromise = fetchHTTPResponse({\n            ...resolvedOpts,\n            signal: raceAbortSignals(batchSignals, abortController.signal),\n            type,\n            contentTypeHeader: 'application/json',\n            trpcAcceptHeader: 'application/jsonl',\n            getUrl,\n            getBody,\n            inputs,\n            path,\n            headers() {\n              if (!opts.headers) {\n                return {};\n              }\n              if (typeof opts.headers === 'function') {\n                return opts.headers({\n                  opList: batchOps as NonEmptyArray<Operation>,\n                });\n              }\n              return opts.headers;\n            },\n          });\n\n          const res = await responsePromise;\n          const [head] = await jsonlStreamConsumer<\n            Record<string, Promise<any>>\n          >({\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            from: res.body!,\n            deserialize: resolvedOpts.transformer.output.deserialize,\n            // onError: console.error,\n            formatError(opts) {\n              const error = opts.error as TRPCErrorShape;\n              return TRPCClientError.from({\n                error,\n              });\n            },\n            abortController,\n          });\n          const promises = Object.keys(batchOps).map(\n            async (key): Promise<HTTPResult> => {\n              let json: TRPCResponse = await Promise.resolve(head[key]);\n\n              if ('result' in json) {\n                /**\n                 * Not very pretty, but we need to unwrap nested data as promises\n                 * Our stream producer will only resolve top-level async values or async values that are directly nested in another async value\n                 */\n                const result = await Promise.resolve(json.result);\n                json = {\n                  result: {\n                    data: await Promise.resolve(result.data),\n                  },\n                };\n              }\n\n              return {\n                json,\n                meta: {\n                  response: res,\n                },\n              };\n            },\n          );\n          return promises;\n        },\n      };\n    };\n\n    const query = dataLoader(batchLoader('query'));\n    const mutation = dataLoader(batchLoader('mutation'));\n\n    const loaders = { query, mutation };\n    return ({ op }) => {\n      return observable((observer) => {\n        /* istanbul ignore if -- @preserve */\n        if (op.type === 'subscription') {\n          throw new Error(\n            'Subscriptions are unsupported by `httpBatchStreamLink` - use `httpSubscriptionLink` or `wsLink`',\n          );\n        }\n        const loader = loaders[op.type];\n        const promise = loader.load(op);\n\n        let _res = undefined as HTTPResult | undefined;\n        promise\n          .then((res) => {\n            _res = res;\n            if ('error' in res.json) {\n              observer.error(\n                TRPCClientError.from(res.json, {\n                  meta: res.meta,\n                }),\n              );\n              return;\n            } else if ('result' in res.json) {\n              observer.next({\n                context: res.meta,\n                result: res.json.result,\n              });\n              observer.complete();\n              return;\n            }\n\n            observer.complete();\n          })\n          .catch((err) => {\n            observer.error(\n              TRPCClientError.from(err, {\n                meta: _res?.meta,\n              }),\n            );\n          });\n\n        return () => {\n          // noop\n        };\n      });\n    };\n  };\n}\n\n/**\n * @deprecated use {@link httpBatchStreamLink} instead\n */\nexport const unstable_httpBatchStreamLink = httpBatchStreamLink;\n", "export function inputWithTrackedEventId(\n  input: unknown,\n  lastEventId: string | undefined,\n) {\n  if (!lastEventId) {\n    return input;\n  }\n  if (input != null && typeof input !== 'object') {\n    return input;\n  }\n  return {\n    ...(input ?? {}),\n    lastEventId,\n  };\n}\n", "function _asyncIterator(r) {\n  var n,\n    t,\n    o,\n    e = 2;\n  for (\"undefined\" != typeof Symbol && (t = Symbol.asyncIterator, o = Symbol.iterator); e--;) {\n    if (t && null != (n = r[t])) return n.call(r);\n    if (o && null != (n = r[o])) return new AsyncFromSyncIterator(n.call(r));\n    t = \"@@asyncIterator\", o = \"@@iterator\";\n  }\n  throw new TypeError(\"Object is not async iterable\");\n}\nfunction AsyncFromSyncIterator(r) {\n  function AsyncFromSyncIteratorContinuation(r) {\n    if (Object(r) !== r) return Promise.reject(new TypeError(r + \" is not an object.\"));\n    var n = r.done;\n    return Promise.resolve(r.value).then(function (r) {\n      return {\n        value: r,\n        done: n\n      };\n    });\n  }\n  return AsyncFromSyncIterator = function AsyncFromSyncIterator(r) {\n    this.s = r, this.n = r.next;\n  }, AsyncFromSyncIterator.prototype = {\n    s: null,\n    n: null,\n    next: function next() {\n      return AsyncFromSyncIteratorContinuation(this.n.apply(this.s, arguments));\n    },\n    \"return\": function _return(r) {\n      var n = this.s[\"return\"];\n      return void 0 === n ? Promise.resolve({\n        value: r,\n        done: !0\n      }) : AsyncFromSyncIteratorContinuation(n.apply(this.s, arguments));\n    },\n    \"throw\": function _throw(r) {\n      var n = this.s[\"return\"];\n      return void 0 === n ? Promise.reject(r) : AsyncFromSyncIteratorContinuation(n.apply(this.s, arguments));\n    }\n  }, new AsyncFromSyncIterator(r);\n}\nmodule.exports = _asyncIterator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import { behaviorSubject, observable } from '@trpc/server/observable';\nimport type { TRPCErrorShape, TRPCResult } from '@trpc/server/rpc';\nimport type {\n  AnyClientTypes,\n  EventSourceLike,\n  inferClientTypes,\n  InferrableClientTypes,\n} from '@trpc/server/unstable-core-do-not-import';\nimport {\n  retryableRpcCodes,\n  run,\n  sseStreamConsumer,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { inputWithTrackedEventId } from '../internals/inputWithTrackedEventId';\nimport { raceAbortSignals } from '../internals/signals';\nimport { TRPCClientError } from '../TRPCClientError';\nimport type { TRPCConnectionState } from '../unstable-internals';\nimport { getTransformer, type TransformerOptions } from '../unstable-internals';\nimport { getUrl } from './internals/httpUtils';\nimport {\n  resultOf,\n  type UrlOptionsWithConnectionParams,\n} from './internals/urlWithConnectionParams';\nimport type { Operation, TRPCLink } from './types';\n\nasync function urlWithConnectionParams(\n  opts: UrlOptionsWithConnectionParams,\n): Promise<string> {\n  let url = await resultOf(opts.url);\n  if (opts.connectionParams) {\n    const params = await resultOf(opts.connectionParams);\n\n    const prefix = url.includes('?') ? '&' : '?';\n    url +=\n      prefix + 'connectionParams=' + encodeURIComponent(JSON.stringify(params));\n  }\n\n  return url;\n}\n\ntype HTTPSubscriptionLinkOptions<\n  TRoot extends AnyClientTypes,\n  TEventSource extends EventSourceLike.AnyConstructor = typeof EventSource,\n> = {\n  /**\n   * EventSource ponyfill\n   */\n  EventSource?: TEventSource;\n  /**\n   * EventSource options or a callback that returns them\n   */\n  eventSourceOptions?:\n    | EventSourceLike.InitDictOf<TEventSource>\n    | ((opts: {\n        op: Operation;\n      }) =>\n        | EventSourceLike.InitDictOf<TEventSource>\n        | Promise<EventSourceLike.InitDictOf<TEventSource>>);\n} & TransformerOptions<TRoot> &\n  UrlOptionsWithConnectionParams;\n\n/**\n * @see https://trpc.io/docs/client/links/httpSubscriptionLink\n */\nexport function httpSubscriptionLink<\n  TInferrable extends InferrableClientTypes,\n  TEventSource extends EventSourceLike.AnyConstructor,\n>(\n  opts: HTTPSubscriptionLinkOptions<\n    inferClientTypes<TInferrable>,\n    TEventSource\n  >,\n): TRPCLink<TInferrable> {\n  const transformer = getTransformer(opts.transformer);\n\n  return () => {\n    return ({ op }) => {\n      return observable((observer) => {\n        const { type, path, input } = op;\n\n        /* istanbul ignore if -- @preserve */\n        if (type !== 'subscription') {\n          throw new Error('httpSubscriptionLink only supports subscriptions');\n        }\n\n        let lastEventId: string | undefined = undefined;\n        const ac = new AbortController();\n        const signal = raceAbortSignals(op.signal, ac.signal);\n        const eventSourceStream = sseStreamConsumer<{\n          EventSource: TEventSource;\n          data: Partial<{\n            id?: string;\n            data: unknown;\n          }>;\n          error: TRPCErrorShape;\n        }>({\n          url: async () =>\n            getUrl({\n              transformer,\n              url: await urlWithConnectionParams(opts),\n              input: inputWithTrackedEventId(input, lastEventId),\n              path,\n              type,\n              signal: null,\n            }),\n          init: () => resultOf(opts.eventSourceOptions, { op }),\n          signal,\n          deserialize: transformer.output.deserialize,\n          EventSource:\n            opts.EventSource ??\n            (globalThis.EventSource as never as TEventSource),\n        });\n\n        const connectionState = behaviorSubject<\n          TRPCConnectionState<TRPCClientError<any>>\n        >({\n          type: 'state',\n          state: 'connecting',\n          error: null,\n        });\n\n        const connectionSub = connectionState.subscribe({\n          next(state) {\n            observer.next({\n              result: state,\n            });\n          },\n        });\n        run(async () => {\n          for await (const chunk of eventSourceStream) {\n            switch (chunk.type) {\n              case 'ping':\n                // do nothing\n                break;\n              case 'data':\n                const chunkData = chunk.data;\n\n                let result: TRPCResult<unknown>;\n                if (chunkData.id) {\n                  // if the `tracked()`-helper is used, we always have an `id` field\n                  lastEventId = chunkData.id;\n                  result = {\n                    id: chunkData.id,\n                    data: chunkData,\n                  };\n                } else {\n                  result = {\n                    data: chunkData.data,\n                  };\n                }\n\n                observer.next({\n                  result,\n                  context: {\n                    eventSource: chunk.eventSource,\n                  },\n                });\n                break;\n              case 'connected': {\n                observer.next({\n                  result: {\n                    type: 'started',\n                  },\n                  context: {\n                    eventSource: chunk.eventSource,\n                  },\n                });\n                connectionState.next({\n                  type: 'state',\n                  state: 'pending',\n                  error: null,\n                });\n                break;\n              }\n              case 'serialized-error': {\n                const error = TRPCClientError.from({ error: chunk.error });\n\n                if (retryableRpcCodes.includes(chunk.error.code)) {\n                  //\n                  connectionState.next({\n                    type: 'state',\n                    state: 'connecting',\n                    error,\n                  });\n                  break;\n                }\n                //\n                // non-retryable error, cancel the subscription\n                throw error;\n              }\n              case 'connecting': {\n                const lastState = connectionState.get();\n\n                const error = chunk.event && TRPCClientError.from(chunk.event);\n                if (!error && lastState.state === 'connecting') {\n                  break;\n                }\n\n                connectionState.next({\n                  type: 'state',\n                  state: 'connecting',\n                  error,\n                });\n                break;\n              }\n              case 'timeout': {\n                connectionState.next({\n                  type: 'state',\n                  state: 'connecting',\n                  error: new TRPCClientError(\n                    `Timeout of ${chunk.ms}ms reached while waiting for a response`,\n                  ),\n                });\n              }\n            }\n          }\n          observer.next({\n            result: {\n              type: 'stopped',\n            },\n          });\n          connectionState.next({\n            type: 'state',\n            state: 'idle',\n            error: null,\n          });\n          observer.complete();\n        }).catch((error) => {\n          observer.error(TRPCClientError.from(error));\n        });\n\n        return () => {\n          observer.complete();\n          ac.abort();\n          connectionSub.unsubscribe();\n        };\n      });\n    };\n  };\n}\n\n/**\n * @deprecated use {@link httpSubscriptionLink} instead\n */\nexport const unstable_httpSubscriptionLink = httpSubscriptionLink;\n", "/* istanbul ignore file -- @preserve */\n// We're not actually exporting this link\nimport type { Unsubscribable } from '@trpc/server/observable';\nimport { observable } from '@trpc/server/observable';\nimport type { InferrableClientTypes } from '@trpc/server/unstable-core-do-not-import';\nimport { inputWithTrackedEventId } from '../internals/inputWithTrackedEventId';\nimport type { TRPCClientError } from '../TRPCClientError';\nimport type { Operation, TRPCLink } from './types';\n\ninterface RetryLinkOptions<TInferrable extends InferrableClientTypes> {\n  /**\n   * The retry function\n   */\n  retry: (opts: RetryFnOptions<TInferrable>) => boolean;\n  /**\n   * The delay between retries in ms (defaults to 0)\n   */\n  retryDelayMs?: (attempt: number) => number;\n}\n\ninterface RetryFnOptions<TInferrable extends InferrableClientTypes> {\n  /**\n   * The operation that failed\n   */\n  op: Operation;\n  /**\n   * The error that occurred\n   */\n  error: TRPCClientError<TInferrable>;\n  /**\n   * The number of attempts that have been made (including the first call)\n   */\n  attempts: number;\n}\n\n/**\n * @see https://trpc.io/docs/v11/client/links/retryLink\n */\nexport function retryLink<TInferrable extends InferrableClientTypes>(\n  opts: RetryLinkOptions<TInferrable>,\n): TRPCLink<TInferrable> {\n  // initialized config\n  return () => {\n    // initialized in app\n    return (callOpts) => {\n      // initialized for request\n      return observable((observer) => {\n        let next$: Unsubscribable;\n        let callNextTimeout: ReturnType<typeof setTimeout> | undefined =\n          undefined;\n\n        let lastEventId: string | undefined = undefined;\n\n        attempt(1);\n\n        function opWithLastEventId() {\n          const op = callOpts.op;\n          if (!lastEventId) {\n            return op;\n          }\n\n          return {\n            ...op,\n            input: inputWithTrackedEventId(op.input, lastEventId),\n          };\n        }\n\n        function attempt(attempts: number) {\n          const op = opWithLastEventId();\n\n          next$ = callOpts.next(op).subscribe({\n            error(error) {\n              const shouldRetry = opts.retry({\n                op,\n                attempts,\n                error,\n              });\n              if (!shouldRetry) {\n                observer.error(error);\n                return;\n              }\n              const delayMs = opts.retryDelayMs?.(attempts) ?? 0;\n\n              if (delayMs <= 0) {\n                attempt(attempts + 1);\n                return;\n              }\n              callNextTimeout = setTimeout(\n                () => attempt(attempts + 1),\n                delayMs,\n              );\n            },\n            next(envelope) {\n              //\n              if (\n                (!envelope.result.type || envelope.result.type === 'data') &&\n                envelope.result.id\n              ) {\n                //\n                lastEventId = envelope.result.id;\n              }\n\n              observer.next(envelope);\n            },\n            complete() {\n              observer.complete();\n            },\n          });\n        }\n        return () => {\n          next$.unsubscribe();\n          clearTimeout(callNextTimeout);\n        };\n      });\n    };\n  };\n}\n", "function _usingCtx() {\n  var r = \"function\" == typeof SuppressedError ? SuppressedError : function (r, e) {\n      var n = Error();\n      return n.name = \"SuppressedError\", n.error = r, n.suppressed = e, n;\n    },\n    e = {},\n    n = [];\n  function using(r, e) {\n    if (null != e) {\n      if (Object(e) !== e) throw new TypeError(\"using declarations can only be used with objects, functions, null, or undefined.\");\n      if (r) var o = e[Symbol.asyncDispose || Symbol[\"for\"](\"Symbol.asyncDispose\")];\n      if (void 0 === o && (o = e[Symbol.dispose || Symbol[\"for\"](\"Symbol.dispose\")], r)) var t = o;\n      if (\"function\" != typeof o) throw new TypeError(\"Object is not disposable.\");\n      t && (o = function o() {\n        try {\n          t.call(e);\n        } catch (r) {\n          return Promise.reject(r);\n        }\n      }), n.push({\n        v: e,\n        d: o,\n        a: r\n      });\n    } else r && n.push({\n      d: e,\n      a: r\n    });\n    return e;\n  }\n  return {\n    e: e,\n    u: using.bind(null, !1),\n    a: using.bind(null, !0),\n    d: function d() {\n      var o,\n        t = this.e,\n        s = 0;\n      function next() {\n        for (; o = n.pop();) try {\n          if (!o.a && 1 === s) return s = 0, n.push(o), Promise.resolve().then(next);\n          if (o.d) {\n            var r = o.d.call(o.v);\n            if (o.a) return s |= 2, Promise.resolve(r).then(next, err);\n          } else s |= 1;\n        } catch (r) {\n          return err(r);\n        }\n        if (1 === s) return t !== e ? Promise.reject(t) : Promise.resolve();\n        if (t !== e) throw t;\n      }\n      function err(n) {\n        return t = t !== e ? new r(n, t) : n, next();\n      }\n      return next();\n    }\n  };\n}\nmodule.exports = _usingCtx, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _OverloadYield(e, d) {\n  this.v = e, this.k = d;\n}\nmodule.exports = _OverloadYield, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var OverloadYield = require(\"./OverloadYield.js\");\nfunction _awaitAsyncGenerator(e) {\n  return new OverloadYield(e, 0);\n}\nmodule.exports = _awaitAsyncGenerator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var OverloadYield = require(\"./OverloadYield.js\");\nfunction _wrapAsyncGenerator(e) {\n  return function () {\n    return new AsyncGenerator(e.apply(this, arguments));\n  };\n}\nfunction AsyncGenerator(e) {\n  var r, t;\n  function resume(r, t) {\n    try {\n      var n = e[r](t),\n        o = n.value,\n        u = o instanceof OverloadYield;\n      Promise.resolve(u ? o.v : o).then(function (t) {\n        if (u) {\n          var i = \"return\" === r ? \"return\" : \"next\";\n          if (!o.k || t.done) return resume(i, t);\n          t = e[i](t).value;\n        }\n        settle(n.done ? \"return\" : \"normal\", t);\n      }, function (e) {\n        resume(\"throw\", e);\n      });\n    } catch (e) {\n      settle(\"throw\", e);\n    }\n  }\n  function settle(e, n) {\n    switch (e) {\n      case \"return\":\n        r.resolve({\n          value: n,\n          done: !0\n        });\n        break;\n      case \"throw\":\n        r.reject(n);\n        break;\n      default:\n        r.resolve({\n          value: n,\n          done: !1\n        });\n    }\n    (r = r.next) ? resume(r.key, r.arg) : t = null;\n  }\n  this._invoke = function (e, n) {\n    return new Promise(function (o, u) {\n      var i = {\n        key: e,\n        arg: n,\n        resolve: o,\n        reject: u,\n        next: null\n      };\n      t ? t = t.next = i : (r = t = i, resume(e, n));\n    });\n  }, \"function\" != typeof e[\"return\"] && (this[\"return\"] = void 0);\n}\nAsyncGenerator.prototype[\"function\" == typeof Symbol && Symbol.asyncIterator || \"@@asyncIterator\"] = function () {\n  return this;\n}, AsyncGenerator.prototype.next = function (e) {\n  return this._invoke(\"next\", e);\n}, AsyncGenerator.prototype[\"throw\"] = function (e) {\n  return this._invoke(\"throw\", e);\n}, AsyncGenerator.prototype[\"return\"] = function (e) {\n  return this._invoke(\"return\", e);\n};\nmodule.exports = _wrapAsyncGenerator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import {\n  getTRPCErrorFromUnknown,\n  getTRPCErrorShape,\n  isTrackedEnvelope,\n} from '@trpc/server';\nimport { behaviorSubject, observable } from '@trpc/server/observable';\nimport { TRPC_ERROR_CODES_BY_KEY, type TRPCResult } from '@trpc/server/rpc';\nimport {\n  callProcedure,\n  isAbortError,\n  isAsyncIterable,\n  iteratorResource,\n  makeResource,\n  retryableRpcCodes,\n  run,\n  type AnyRouter,\n  type ErrorHandlerOptions,\n  type inferClientTypes,\n  type inferRouterContext,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { inputWithTrackedEventId } from '../internals/inputWithTrackedEventId';\nimport { abortSignalToPromise, raceAbortSignals } from '../internals/signals';\nimport { getTransformer } from '../internals/transformer';\nimport type { TransformerOptions } from '../internals/transformer';\nimport { isTR<PERSON><PERSON><PERSON>Error, TRPCClientError } from '../TRPCClientError';\nimport type { TRPCConnectionState } from './internals/subscriptions';\nimport type { TRPCLink } from './types';\n\nexport type LocalLinkOptions<TRouter extends AnyRouter> = {\n  router: TRouter;\n  createContext: () => Promise<inferRouterContext<TRouter>>;\n  onError?: (opts: ErrorHandlerOptions<inferRouterContext<TRouter>>) => void;\n} & TransformerOptions<inferClientTypes<TRouter>>;\n\n/**\n * localLink is a terminating link that allows you to make tRPC procedure calls directly in your application without going through HTTP.\n *\n * @see https://trpc.io/docs/links/localLink\n */\nexport function unstable_localLink<TRouter extends AnyRouter>(\n  opts: LocalLinkOptions<TRouter>,\n): TRPCLink<TRouter> {\n  const transformer = getTransformer(opts.transformer);\n\n  const transformChunk = (chunk: unknown) => {\n    if (opts.transformer) {\n      // assume transformer will do the right thing\n      return chunk;\n    }\n    // Special case for undefined, because `JSON.stringify(undefined)` throws\n    if (chunk === undefined) {\n      return chunk;\n    }\n    const serialized = JSON.stringify(transformer.input.serialize(chunk));\n    const deserialized = JSON.parse(transformer.output.deserialize(serialized));\n    return deserialized;\n  };\n\n  return () =>\n    ({ op }) =>\n      observable((observer) => {\n        let ctx: inferRouterContext<TRouter> | undefined = undefined;\n        const ac = new AbortController();\n\n        const signal = raceAbortSignals(op.signal, ac.signal);\n        const signalPromise = abortSignalToPromise(signal);\n\n        signalPromise.catch(() => {\n          // prevent unhandled rejection\n        });\n\n        let input = op.input;\n        async function runProcedure(newInput: unknown): Promise<unknown> {\n          input = newInput;\n\n          ctx = await opts.createContext();\n\n          return callProcedure({\n            router: opts.router,\n            path: op.path,\n            getRawInput: async () => newInput,\n            ctx,\n            type: op.type,\n            signal,\n          });\n        }\n\n        function onErrorCallback(cause: unknown) {\n          if (isAbortError(cause)) {\n            return;\n          }\n          opts.onError?.({\n            error: getTRPCErrorFromUnknown(cause),\n            type: op.type,\n            path: op.path,\n            input,\n            ctx,\n          });\n        }\n\n        function coerceToTRPCClientError(cause: unknown) {\n          if (isTRPCClientError<TRouter>(cause)) {\n            return cause;\n          }\n          const error = getTRPCErrorFromUnknown(cause);\n\n          const shape = getTRPCErrorShape({\n            config: opts.router._def._config,\n            ctx,\n            error,\n            input,\n            path: op.path,\n            type: op.type,\n          });\n          return TRPCClientError.from({\n            error: transformChunk(shape),\n          });\n        }\n\n        run(async () => {\n          switch (op.type) {\n            case 'query':\n            case 'mutation': {\n              const result = await runProcedure(op.input);\n              if (!isAsyncIterable(result)) {\n                observer.next({\n                  result: { data: transformChunk(result) },\n                });\n                observer.complete();\n                break;\n              }\n\n              observer.next({\n                result: {\n                  data: (async function* () {\n                    await using iterator = iteratorResource(result);\n                    using _finally = makeResource({}, () => {\n                      observer.complete();\n                    });\n                    try {\n                      while (true) {\n                        const res = await Promise.race([\n                          iterator.next(),\n                          signalPromise,\n                        ]);\n                        if (res.done) {\n                          return transformChunk(res.value);\n                        }\n                        yield transformChunk(res.value);\n                      }\n                    } catch (cause) {\n                      onErrorCallback(cause);\n                      throw coerceToTRPCClientError(cause);\n                    }\n                  })(),\n                },\n              });\n              break;\n            }\n            case 'subscription': {\n              const connectionState = behaviorSubject<\n                TRPCConnectionState<TRPCClientError<any>>\n              >({\n                type: 'state',\n                state: 'connecting',\n                error: null,\n              });\n\n              const connectionSub = connectionState.subscribe({\n                next(state) {\n                  observer.next({\n                    result: state,\n                  });\n                },\n              });\n              let lastEventId: string | undefined = undefined;\n\n              using _finally = makeResource({}, async () => {\n                observer.complete();\n\n                connectionState.next({\n                  type: 'state',\n                  state: 'idle',\n                  error: null,\n                });\n                connectionSub.unsubscribe();\n              });\n              while (true) {\n                const result = await runProcedure(\n                  inputWithTrackedEventId(op.input, lastEventId),\n                );\n                if (!isAsyncIterable(result)) {\n                  throw new Error('Expected an async iterable');\n                }\n                await using iterator = iteratorResource(result);\n\n                observer.next({\n                  result: {\n                    type: 'started',\n                  },\n                });\n                connectionState.next({\n                  type: 'state',\n                  state: 'pending',\n                  error: null,\n                });\n\n                // Use a while loop to handle errors and reconnects\n                while (true) {\n                  let res;\n                  try {\n                    res = await Promise.race([iterator.next(), signalPromise]);\n                  } catch (cause) {\n                    if (isAbortError(cause)) {\n                      return;\n                    }\n                    const error = getTRPCErrorFromUnknown(cause);\n\n                    if (\n                      !retryableRpcCodes.includes(\n                        TRPC_ERROR_CODES_BY_KEY[error.code],\n                      )\n                    ) {\n                      throw coerceToTRPCClientError(error);\n                    }\n\n                    onErrorCallback(error);\n                    connectionState.next({\n                      type: 'state',\n                      state: 'connecting',\n                      error: coerceToTRPCClientError(error),\n                    });\n\n                    break;\n                  }\n\n                  if (res.done) {\n                    return;\n                  }\n                  let chunk: TRPCResult<unknown>;\n                  if (isTrackedEnvelope(res.value)) {\n                    lastEventId = res.value[0];\n\n                    chunk = {\n                      id: res.value[0],\n                      data: {\n                        id: res.value[0],\n                        data: res.value[1],\n                      },\n                    };\n                  } else {\n                    chunk = {\n                      data: res.value,\n                    };\n                  }\n\n                  observer.next({\n                    result: {\n                      ...chunk,\n                      data: transformChunk(chunk.data),\n                    },\n                  });\n                }\n              }\n              break;\n            }\n          }\n        }).catch((cause) => {\n          onErrorCallback(cause);\n          observer.error(coerceToTRPCClientError(cause));\n        });\n\n        return () => {\n          ac.abort();\n        };\n      });\n}\n/**\n * @deprecated Renamed to `unstable_localLink`. This alias will be removed in a future major release.\n */\nexport const experimental_localLink: typeof unstable_localLink =\n  unstable_localLink;\n"], "x_google_ignoreList": [5, 8, 9, 10, 11], "mappings": ";;;;;;;;;;;;;;;;;AA8CA,IAAa,oBAAb,MAA0E;CAKxE,YAAYA,MAA4C;qCA+GvD,MAnHgB;qCAmHf,MAlHc;qCAkHb,MAjHK;AAGN,OAAK,YAAY;AAEjB,OAAK,UAAU,CAAE;AAGjB,OAAK,QAAQ,KAAK,MAAM,IAAI,CAAC,SAAS,KAAK,KAAK,QAAQ,CAAC;CAC1D;CAED,AAAQ,SAA8CC,MAMnD;;EACD,MAAM,SAAS,YAAwC;GACrD,OAAO,KAAK;GACZ,gFACK;IACH,0BAAS,KAAK,gEAAW,CAAE;IAC3B,IAAI,EAAE,KAAK;;EAEd,EAAC;AACF,SAAO,OAAO,KAAK,OAAO,CAAC;CAC5B;CAED,MAAc,iBAAsDA,MAM/C;cA8EjB;AA7EF,MAAI;GACF,MAAM,OAAO,MAAK,SAA0B,KAAK;GAGjD,MAAM,WAAW,MAAM,oBAA4B,KAAK;GACxD,MAAM,OAAQ,SAAS,OAAe;AACtC,UAAO;EACR,SAAQ,KAAK;AACZ,SAAM,gBAAgB,KAAK,IAAa;EACzC;CACF;CACD,AAAO,MAAMC,MAAcC,OAAiBC,MAA2B;AACrE,SAAO,KAAK,iBAAmC;GAC7C,MAAM;GACN;GACA;GACA,qDAAS,KAAM;GACf,oDAAQ,KAAM;EACf,EAAC;CACH;CACD,AAAO,SAASF,MAAcC,OAAiBC,MAA2B;AACxE,SAAO,KAAK,iBAAmC;GAC7C,MAAM;GACN;GACA;GACA,qDAAS,KAAM;GACf,oDAAQ,KAAM;EACf,EAAC;CACH;CACD,AAAO,aACLF,MACAG,OACAC,MAIgB;EAChB,MAAM,cAAc,KAAK,SAAS;GAChC,MAAM;GACN;GACA;GACA,SAAS,KAAK;GACd,QAAQ,KAAK;EACd,EAAC;AACF,SAAO,YAAY,UAAU;GAC3B,KAAK,UAAU;AACb,YAAQ,SAAS,OAAO,MAAxB;KACE,KAAK,SAAS;;AACZ,oCAAK,yEAAL,iCAA+B,SAAS,OAAO;AAC/C;KACD;KACD,KAAK,WAAW;;AACd,8BAAK,qDAAL,2BAAiB,EACf,SAAS,SAAS,QACnB,EAAC;AACF;KACD;KACD,KAAK,WAAW;;AACd,8BAAK,qDAAL,0BAAkB;AAClB;KACD;KACD,KAAK;KACL,aAAgB;;AACd,2BAAK,+CAAL,wBAAc,SAAS,OAAO,KAAK;AACnC;KACD;IACF;GACF;GACD,MAAM,KAAK;;AACT,0BAAK,iDAAL,yBAAe,IAAI;GACpB;GACD,WAAW;;AACT,6BAAK,uDAAL,2BAAmB;GACpB;EACF,EAAC;CACH;AACF;;;;AC7JD,SAAgB,wBACdC,MAC4B;AAC5B,QAAO,IAAI,kBAAkB;AAC9B;;;;AC0BD,MAAM,sBAAsB,OAAO,IAAI,qBAAqB;AAqF5D,MAAMC,oBAGF;CACF,OAAO;CACP,QAAQ;CACR,WAAW;AACZ;;AAGD,MAAa,gCAAgC,CAC3CC,mBACkB;AAClB,QAAO,kBAAkB;AAC1B;;;;AAKD,SAAgB,sBACdC,QACqB;CACrB,MAAM,QAAQ,qBAA0C,CAAC,EAAE,MAAM,MAAM,KAAK;EAC1E,MAAM,WAAW,CAAC,GAAG,IAAK;EAC1B,MAAM,gBAAgB,8BAA8B,SAAS,KAAK,CAAE;EAEpE,MAAM,WAAW,SAAS,KAAK,IAAI;AAEnC,SAAO,AAAC,OAAO,eAAuB,UAAU,GAAI,KAAa;CAClE,EAAC;AACF,QAAO,gBAAqC,CAAC,QAAQ;AACnD,MAAI,QAAQ,oBACV,QAAO;AAET,SAAO,MAAM;CACd,EAAC;AACH;AAED,SAAgB,iBACdC,MACqB;CACrB,MAAM,SAAS,IAAI,kBAAkB;CACrC,MAAM,QAAQ,sBAA+B,OAAO;AACpD,QAAO;AACR;;;;;AAMD,SAAgB,iBACdC,QAC4B;AAC5B,QAAO,OAAO;AACf;;;;;;;;ACvJD,SAAgB,oBACdC,MACmB;;CACnB,MAAM,eAAe,uBAAuB,KAAK;CACjD,MAAM,qCAAe,KAAK,+EAAgB;CAC1C,MAAM,6BAAW,KAAK,mEAAY;AAElC,QAAO,MAAM;EACX,MAAM,cAAc,CAClBC,SACuC;AACvC,UAAO;IACL,SAAS,UAAU;AACjB,SAAI,iBAAiB,YAAY,aAAa,SAE5C,QAAO;AAET,SAAI,SAAS,SAAS,SACpB,QAAO;KAET,MAAM,OAAO,SAAS,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,KAAK,IAAI;KACpD,MAAM,SAAS,SAAS,IAAI,CAAC,OAAO,GAAG,MAAM;KAE7C,MAAM,MAAM,mFACP;MACH;MACA;MACA;MACA,QAAQ;QACR;AAEF,YAAO,IAAI,UAAU;IACtB;IACD,MAAM,MAAM,UAAU;KACpB,MAAM,OAAO,SAAS,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,KAAK,IAAI;KACpD,MAAM,SAAS,SAAS,IAAI,CAAC,OAAO,GAAG,MAAM;KAE7C,MAAM,eAAe,gBACnB,GAAG,SAAS,IAAI,CAAC,OAAO,GAAG,OAAO,CACnC;KACD,MAAM,kBAAkB,IAAI;KAE5B,MAAM,kBAAkB,8FACnB;MACH,QAAQ,iBAAiB,cAAc,gBAAgB,OAAO;MAC9D;MACA,mBAAmB;MACnB,kBAAkB;MAClB;MACA;MACA;MACA;MACA,UAAU;AACR,YAAK,KAAK,QACR,QAAO,CAAE;AAEX,kBAAW,KAAK,YAAY,WAC1B,QAAO,KAAK,QAAQ,EAClB,QAAQ,SACT,EAAC;AAEJ,cAAO,KAAK;MACb;QACD;KAEF,MAAM,MAAM,MAAM;KAClB,MAAM,CAAC,KAAK,GAAG,MAAM,oBAEnB;MAEA,MAAM,IAAI;MACV,aAAa,aAAa,YAAY,OAAO;MAE7C,YAAYC,QAAM;OAChB,MAAM,QAAQA,OAAK;AACnB,cAAO,gBAAgB,KAAK,EAC1B,MACD,EAAC;MACH;MACD;KACD,EAAC;KACF,MAAM,WAAW,OAAO,KAAK,SAAS,CAAC,IACrC,OAAO,QAA6B;MAClC,IAAIC,OAAqB,MAAM,QAAQ,QAAQ,KAAK,KAAK;AAEzD,UAAI,YAAY,MAAM;;;;;OAKpB,MAAM,SAAS,MAAM,QAAQ,QAAQ,KAAK,OAAO;AACjD,cAAO,EACL,QAAQ,EACN,MAAM,MAAM,QAAQ,QAAQ,OAAO,KAAK,CACzC,EACF;MACF;AAED,aAAO;OACL;OACA,MAAM,EACJ,UAAU,IACX;MACF;KACF,EACF;AACD,YAAO;IACR;GACF;EACF;EAED,MAAM,QAAQ,WAAW,YAAY,QAAQ,CAAC;EAC9C,MAAM,WAAW,WAAW,YAAY,WAAW,CAAC;EAEpD,MAAM,UAAU;GAAE;GAAO;EAAU;AACnC,SAAO,CAAC,EAAE,IAAI,KAAK;AACjB,UAAO,WAAW,CAAC,aAAa;;AAE9B,QAAI,GAAG,SAAS,eACd,OAAM,IAAI,MACR;IAGJ,MAAM,SAAS,QAAQ,GAAG;IAC1B,MAAM,UAAU,OAAO,KAAK,GAAG;IAE/B,IAAI;AACJ,YACG,KAAK,CAAC,QAAQ;AACb,YAAO;AACP,SAAI,WAAW,IAAI,MAAM;AACvB,eAAS,MACP,gBAAgB,KAAK,IAAI,MAAM,EAC7B,MAAM,IAAI,KACX,EAAC,CACH;AACD;KACD,WAAU,YAAY,IAAI,MAAM;AAC/B,eAAS,KAAK;OACZ,SAAS,IAAI;OACb,QAAQ,IAAI,KAAK;MAClB,EAAC;AACF,eAAS,UAAU;AACnB;KACD;AAED,cAAS,UAAU;IACpB,EAAC,CACD,MAAM,CAAC,QAAQ;AACd,cAAS,MACP,gBAAgB,KAAK,KAAK,EACxB,kDAAM,KAAM,KACb,EAAC,CACH;IACF,EAAC;AAEJ,WAAO,MAAM,CAEZ;GACF,EAAC;EACH;CACF;AACF;;;;AAKD,MAAa,+BAA+B;;;;;AC7L5C,SAAgB,wBACdC,OACAC,aACA;AACA,MAAK,YACH,QAAO;AAET,KAAI,SAAS,eAAe,UAAU,SACpC,QAAO;AAET,oFACM,6CAAS,CAAE,UACf;AAEH;;;;;CCdD,SAASC,iBAAe,GAAG;EACzB,IAAI,GACF,GACA,GACA,IAAI;AACN,OAAK,sBAAsB,WAAW,IAAI,OAAO,eAAe,IAAI,OAAO,WAAW,MAAM;AAC1F,OAAI,KAAK,SAAS,IAAI,EAAE,IAAK,QAAO,EAAE,KAAK,EAAE;AAC7C,OAAI,KAAK,SAAS,IAAI,EAAE,IAAK,QAAO,IAAI,sBAAsB,EAAE,KAAK,EAAE;AACvE,OAAI,mBAAmB,IAAI;EAC5B;AACD,QAAM,IAAI,UAAU;CACrB;CACD,SAAS,sBAAsB,GAAG;EAChC,SAAS,kCAAkCC,KAAG;AAC5C,OAAI,OAAOA,IAAE,KAAKA,IAAG,QAAO,QAAQ,OAAO,IAAI,UAAUA,MAAI,sBAAsB;GACnF,IAAI,IAAIA,IAAE;AACV,UAAO,QAAQ,QAAQA,IAAE,MAAM,CAAC,KAAK,SAAUA,KAAG;AAChD,WAAO;KACL,OAAOA;KACP,MAAM;IACP;GACF,EAAC;EACH;AACD,SAAO,wBAAwB,SAASC,wBAAsBD,KAAG;AAC/D,QAAK,IAAIA,KAAG,KAAK,IAAIA,IAAE;EACxB,GAAE,sBAAsB,YAAY;GACnC,GAAG;GACH,GAAG;GACH,MAAM,SAAS,OAAO;AACpB,WAAO,kCAAkC,KAAK,EAAE,MAAM,KAAK,GAAG,UAAU,CAAC;GAC1E;GACD,UAAU,SAAS,QAAQA,KAAG;IAC5B,IAAI,IAAI,KAAK,EAAE;AACf,gBAAY,MAAM,IAAI,QAAQ,QAAQ;KACpC,OAAOA;KACP,OAAO;IACR,EAAC,GAAG,kCAAkC,EAAE,MAAM,KAAK,GAAG,UAAU,CAAC;GACnE;GACD,SAAS,SAAS,OAAOA,KAAG;IAC1B,IAAI,IAAI,KAAK,EAAE;AACf,gBAAY,MAAM,IAAI,QAAQ,OAAOA,IAAE,GAAG,kCAAkC,EAAE,MAAM,KAAK,GAAG,UAAU,CAAC;GACxG;EACF,GAAE,IAAI,sBAAsB;CAC9B;AACD,QAAO,UAAUD,kBAAgB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;ACnBtG,eAAe,wBACbG,MACiB;CACjB,IAAI,MAAM,MAAM,SAAS,KAAK,IAAI;AAClC,KAAI,KAAK,kBAAkB;EACzB,MAAM,SAAS,MAAM,SAAS,KAAK,iBAAiB;EAEpD,MAAM,SAAS,IAAI,SAAS,IAAI,GAAG,MAAM;AACzC,SACE,SAAS,sBAAsB,mBAAmB,KAAK,UAAU,OAAO,CAAC;CAC5E;AAED,QAAO;AACR;;;;AA0BD,SAAgB,qBAIdC,MAIuB;CACvB,MAAM,cAAc,eAAe,KAAK,YAAY;AAEpD,QAAO,MAAM;AACX,SAAO,CAAC,EAAE,IAAI,KAAK;AACjB,UAAO,WAAW,CAAC,aAAa;;IAC9B,MAAM,EAAE,MAAM,MAAM,OAAO,GAAG;;AAG9B,QAAI,SAAS,eACX,OAAM,IAAI,MAAM;IAGlB,IAAIC;IACJ,MAAM,KAAK,IAAI;IACf,MAAM,SAAS,iBAAiB,GAAG,QAAQ,GAAG,OAAO;IACrD,MAAM,oBAAoB,kBAOvB;KACD,KAAK,YACH,OAAO;MACL;MACA,KAAK,MAAM,wBAAwB,KAAK;MACxC,OAAO,wBAAwB,OAAO,YAAY;MAClD;MACA;MACA,QAAQ;KACT,EAAC;KACJ,MAAM,MAAM,SAAS,KAAK,oBAAoB,EAAE,GAAI,EAAC;KACrD;KACA,aAAa,YAAY,OAAO;KAChC,kCACE,KAAK,4EACJ,WAAW;IACf,EAAC;IAEF,MAAM,kBAAkB,gBAEtB;KACA,MAAM;KACN,OAAO;KACP,OAAO;IACR,EAAC;IAEF,MAAM,gBAAgB,gBAAgB,UAAU,EAC9C,KAAK,OAAO;AACV,cAAS,KAAK,EACZ,QAAQ,MACT,EAAC;IACH,EACF,EAAC;AACF,QAAI,YAAY;;;;;6DACY;aAAT;AACf,eAAQ,MAAM,MAAd;QACE,KAAK,OAEH;QACF,KAAK;SACH,MAAM,YAAY,MAAM;SAExB,IAAIC;AACJ,aAAI,UAAU,IAAI;AAEhB,wBAAc,UAAU;AACxB,mBAAS;WACP,IAAI,UAAU;WACd,MAAM;UACP;SACF,MACC,UAAS,EACP,MAAM,UAAU,KACjB;AAGH,kBAAS,KAAK;UACZ;UACA,SAAS,EACP,aAAa,MAAM,YACpB;SACF,EAAC;AACF;QACF,KAAK,aAAa;AAChB,kBAAS,KAAK;UACZ,QAAQ,EACN,MAAM,UACP;UACD,SAAS,EACP,aAAa,MAAM,YACpB;SACF,EAAC;AACF,yBAAgB,KAAK;UACnB,MAAM;UACN,OAAO;UACP,OAAO;SACR,EAAC;AACF;QACD;QACD,KAAK,oBAAoB;SACvB,MAAM,QAAQ,gBAAgB,KAAK,EAAE,OAAO,MAAM,MAAO,EAAC;AAE1D,aAAI,kBAAkB,SAAS,MAAM,MAAM,KAAK,EAAE;AAEhD,0BAAgB,KAAK;WACnB,MAAM;WACN,OAAO;WACP;UACD,EAAC;AACF;SACD;AAGD,eAAM;QACP;QACD,KAAK,cAAc;SACjB,MAAM,YAAY,gBAAgB,KAAK;SAEvC,MAAM,QAAQ,MAAM,SAAS,gBAAgB,KAAK,MAAM,MAAM;AAC9D,cAAK,SAAS,UAAU,UAAU,aAChC;AAGF,yBAAgB,KAAK;UACnB,MAAM;UACN,OAAO;UACP;SACD,EAAC;AACF;QACD;QACD,KAAK,UACH,iBAAgB,KAAK;SACnB,MAAM;SACN,OAAO;SACP,OAAO,IAAI,iBACR,aAAa,MAAM,GAAG;QAE1B,EAAC;OAEL;;;;;;;;;;;;AAEH,cAAS,KAAK,EACZ,QAAQ,EACN,MAAM,UACP,EACF,EAAC;AACF,qBAAgB,KAAK;MACnB,MAAM;MACN,OAAO;MACP,OAAO;KACR,EAAC;AACF,cAAS,UAAU;IACpB,EAAC,CAAC,MAAM,CAAC,UAAU;AAClB,cAAS,MAAM,gBAAgB,KAAK,MAAM,CAAC;IAC5C,EAAC;AAEF,WAAO,MAAM;AACX,cAAS,UAAU;AACnB,QAAG,OAAO;AACV,mBAAc,aAAa;IAC5B;GACF,EAAC;EACH;CACF;AACF;;;;AAKD,MAAa,gCAAgC;;;;;;;;AC9M7C,SAAgB,UACdC,MACuB;AAEvB,QAAO,MAAM;AAEX,SAAO,CAAC,aAAa;AAEnB,UAAO,WAAW,CAAC,aAAa;IAC9B,IAAIC;IACJ,IAAIC;IAGJ,IAAIC;AAEJ,YAAQ,EAAE;IAEV,SAAS,oBAAoB;KAC3B,MAAM,KAAK,SAAS;AACpB,UAAK,YACH,QAAO;AAGT,wFACK,WACH,OAAO,wBAAwB,GAAG,OAAO,YAAY;IAExD;IAED,SAAS,QAAQC,UAAkB;KACjC,MAAM,KAAK,mBAAmB;AAE9B,aAAQ,SAAS,KAAK,GAAG,CAAC,UAAU;MAClC,MAAM,OAAO;;OACX,MAAM,cAAc,KAAK,MAAM;QAC7B;QACA;QACA;OACD,EAAC;AACF,YAAK,aAAa;AAChB,iBAAS,MAAM,MAAM;AACrB;OACD;OACD,MAAM,uDAAU,KAAK,oEAAL,+BAAoB,SAAS,mEAAI;AAEjD,WAAI,WAAW,GAAG;AAChB,gBAAQ,WAAW,EAAE;AACrB;OACD;AACD,yBAAkB,WAChB,MAAM,QAAQ,WAAW,EAAE,EAC3B,QACD;MACF;MACD,KAAK,UAAU;AAEb,aACI,SAAS,OAAO,QAAQ,SAAS,OAAO,SAAS,WACnD,SAAS,OAAO,GAGhB,eAAc,SAAS,OAAO;AAGhC,gBAAS,KAAK,SAAS;MACxB;MACD,WAAW;AACT,gBAAS,UAAU;MACpB;KACF,EAAC;IACH;AACD,WAAO,MAAM;AACX,WAAM,aAAa;AACnB,kBAAa,gBAAgB;IAC9B;GACF,EAAC;EACH;CACF;AACF;;;;;CCpHD,SAAS,YAAY;EACnB,IAAI,IAAI,qBAAqB,kBAAkB,kBAAkB,SAAUC,KAAGC,KAAG;GAC7E,IAAIC,MAAI,OAAO;AACf,UAAOA,IAAE,OAAO,mBAAmBA,IAAE,QAAQF,KAAGE,IAAE,aAAaD,KAAGC;EACnE,GACD,IAAI,CAAE,GACN,IAAI,CAAE;EACR,SAAS,MAAMF,KAAGC,KAAG;AACnB,OAAI,QAAQA,KAAG;AACb,QAAI,OAAOA,IAAE,KAAKA,IAAG,OAAM,IAAI,UAAU;AACzC,QAAID,KAAG,IAAI,IAAIC,IAAE,OAAO,gBAAgB,OAAO,OAAO,sBAAsB;AAC5E,aAAS,MAAM,MAAM,IAAIA,IAAE,OAAO,WAAW,OAAO,OAAO,iBAAiB,GAAGD,MAAI,IAAI,IAAI;AAC3F,QAAI,qBAAqB,EAAG,OAAM,IAAI,UAAU;AAChD,UAAM,IAAI,SAASG,MAAI;AACrB,SAAI;AACF,QAAE,KAAKF,IAAE;KACV,SAAQD,KAAG;AACV,aAAO,QAAQ,OAAOA,IAAE;KACzB;IACF,IAAG,EAAE,KAAK;KACT,GAAGC;KACH,GAAG;KACH,GAAGD;IACJ,EAAC;GACH,MAAM,QAAK,EAAE,KAAK;IACjB,GAAGC;IACH,GAAGD;GACJ,EAAC;AACF,UAAOC;EACR;AACD,SAAO;GACF;GACH,GAAG,MAAM,KAAK,OAAO,EAAE;GACvB,GAAG,MAAM,KAAK,OAAO,EAAE;GACvB,GAAG,SAAS,IAAI;IACd,IAAI,GACF,IAAI,KAAK,GACT,IAAI;IACN,SAAS,OAAO;AACd,YAAO,IAAI,EAAE,KAAK,EAAG,KAAI;AACvB,WAAK,EAAE,KAAK,MAAM,EAAG,QAAO,IAAI,GAAG,EAAE,KAAK,EAAE,EAAE,QAAQ,SAAS,CAAC,KAAK,KAAK;AAC1E,UAAI,EAAE,GAAG;OACP,IAAID,MAAI,EAAE,EAAE,KAAK,EAAE,EAAE;AACrB,WAAI,EAAE,EAAG,QAAO,KAAK,GAAG,QAAQ,QAAQA,IAAE,CAAC,KAAK,MAAM,IAAI;MAC3D,MAAM,MAAK;KACb,SAAQA,KAAG;AACV,aAAO,IAAIA,IAAE;KACd;AACD,SAAI,MAAM,EAAG,QAAO,MAAM,IAAI,QAAQ,OAAO,EAAE,GAAG,QAAQ,SAAS;AACnE,SAAI,MAAM,EAAG,OAAM;IACpB;IACD,SAAS,IAAIE,KAAG;AACd,YAAO,IAAI,MAAM,IAAI,IAAI,EAAEA,KAAG,KAAKA,KAAG,MAAM;IAC7C;AACD,WAAO,MAAM;GACd;EACF;CACF;AACD,QAAO,UAAU,WAAW,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CC1DjG,SAAS,eAAe,GAAG,GAAG;AAC5B,OAAK,IAAI,GAAG,KAAK,IAAI;CACtB;AACD,QAAO,UAAU,gBAAgB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CCHtG,IAAIE;CACJ,SAASC,uBAAqB,GAAG;AAC/B,SAAO,IAAID,gBAAc,GAAG;CAC7B;AACD,QAAO,UAAUC,wBAAsB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CCJ5G,IAAI;CACJ,SAASC,sBAAoB,GAAG;AAC9B,SAAO,WAAY;AACjB,UAAO,IAAI,eAAe,EAAE,MAAM,MAAM,UAAU;EACnD;CACF;CACD,SAAS,eAAe,GAAG;EACzB,IAAI,GAAG;EACP,SAAS,OAAOC,KAAGC,KAAG;AACpB,OAAI;IACF,IAAI,IAAI,EAAED,KAAGC,IAAE,EACb,IAAI,EAAE,OACN,IAAI,aAAa;AACnB,YAAQ,QAAQ,IAAI,EAAE,IAAI,EAAE,CAAC,KAAK,SAAUA,KAAG;AAC7C,SAAI,GAAG;MACL,IAAI,IAAI,aAAaD,MAAI,WAAW;AACpC,WAAK,EAAE,KAAKC,IAAE,KAAM,QAAO,OAAO,GAAGA,IAAE;AACvC,YAAI,EAAE,GAAGA,IAAE,CAAC;KACb;AACD,YAAO,EAAE,OAAO,WAAW,UAAUA,IAAE;IACxC,GAAE,SAAUC,KAAG;AACd,YAAO,SAASA,IAAE;IACnB,EAAC;GACH,SAAQA,KAAG;AACV,WAAO,SAASA,IAAE;GACnB;EACF;EACD,SAAS,OAAOA,KAAG,GAAG;AACpB,WAAQA,KAAR;IACE,KAAK;AACH,OAAE,QAAQ;MACR,OAAO;MACP,OAAO;KACR,EAAC;AACF;IACF,KAAK;AACH,OAAE,OAAO,EAAE;AACX;IACF,QACE,GAAE,QAAQ;KACR,OAAO;KACP,OAAO;IACR,EAAC;GACL;AACD,IAAC,IAAI,EAAE,QAAQ,OAAO,EAAE,KAAK,EAAE,IAAI,GAAG,IAAI;EAC3C;AACD,OAAK,UAAU,SAAUA,KAAG,GAAG;AAC7B,UAAO,IAAI,QAAQ,SAAU,GAAG,GAAG;IACjC,IAAI,IAAI;KACN,KAAKA;KACL,KAAK;KACL,SAAS;KACT,QAAQ;KACR,MAAM;IACP;AACD,QAAI,IAAI,EAAE,OAAO,KAAK,IAAI,IAAI,GAAG,OAAOA,KAAG,EAAE;GAC9C;EACF,GAAE,qBAAqB,EAAE,cAAc,KAAK,iBAAiB;CAC/D;AACD,gBAAe,UAAU,qBAAqB,UAAU,OAAO,iBAAiB,qBAAqB,WAAY;AAC/G,SAAO;CACR,GAAE,eAAe,UAAU,OAAO,SAAU,GAAG;AAC9C,SAAO,KAAK,QAAQ,QAAQ,EAAE;CAC/B,GAAE,eAAe,UAAU,WAAW,SAAU,GAAG;AAClD,SAAO,KAAK,QAAQ,SAAS,EAAE;CAChC,GAAE,eAAe,UAAU,YAAY,SAAU,GAAG;AACnD,SAAO,KAAK,QAAQ,UAAU,EAAE;CACjC;AACD,QAAO,UAAUH,uBAAqB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;;;;;;;;;AC7B3G,SAAgB,mBACdI,MACmB;CACnB,MAAM,cAAc,eAAe,KAAK,YAAY;CAEpD,MAAM,iBAAiB,CAACC,UAAmB;AACzC,MAAI,KAAK,YAEP,QAAO;AAGT,MAAI,iBACF,QAAO;EAET,MAAM,aAAa,KAAK,UAAU,YAAY,MAAM,UAAU,MAAM,CAAC;EACrE,MAAM,eAAe,KAAK,MAAM,YAAY,OAAO,YAAY,WAAW,CAAC;AAC3E,SAAO;CACR;AAED,QAAO,MACL,CAAC,EAAE,IAAI,KACL,WAAW,CAAC,aAAa;EACvB,IAAIC;EACJ,MAAM,KAAK,IAAI;EAEf,MAAM,SAAS,iBAAiB,GAAG,QAAQ,GAAG,OAAO;EACrD,MAAM,gBAAgB,qBAAqB,OAAO;AAElD,gBAAc,MAAM,MAAM,CAEzB,EAAC;EAEF,IAAI,QAAQ,GAAG;EACf,eAAe,aAAaC,UAAqC;AAC/D,WAAQ;AAER,SAAM,MAAM,KAAK,eAAe;AAEhC,UAAO,cAAc;IACnB,QAAQ,KAAK;IACb,MAAM,GAAG;IACT,aAAa,YAAY;IACzB;IACA,MAAM,GAAG;IACT;GACD,EAAC;EACH;EAED,SAAS,gBAAgBC,OAAgB;;AACvC,OAAI,aAAa,MAAM,CACrB;AAEF,yBAAK,iDAAL,yBAAe;IACb,OAAO,wBAAwB,MAAM;IACrC,MAAM,GAAG;IACT,MAAM,GAAG;IACT;IACA;GACD,EAAC;EACH;EAED,SAAS,wBAAwBA,OAAgB;AAC/C,OAAI,kBAA2B,MAAM,CACnC,QAAO;GAET,MAAM,QAAQ,wBAAwB,MAAM;GAE5C,MAAM,QAAQ,kBAAkB;IAC9B,QAAQ,KAAK,OAAO,KAAK;IACzB;IACA;IACA;IACA,MAAM,GAAG;IACT,MAAM,GAAG;GACV,EAAC;AACF,UAAO,gBAAgB,KAAK,EAC1B,OAAO,eAAe,MAAM,CAC7B,EAAC;EACH;AAED,MAAI,YAAY;AACd,WAAQ,GAAG,MAAX;IACE,KAAK;IACL,KAAK,YAAY;KACf,MAAM,SAAS,MAAM,aAAa,GAAG,MAAM;AAC3C,UAAK,gBAAgB,OAAO,EAAE;AAC5B,eAAS,KAAK,EACZ,QAAQ,EAAE,MAAM,eAAe,OAAO,CAAE,EACzC,EAAC;AACF,eAAS,UAAU;AACnB;KACD;AAED,cAAS,KAAK,EACZ,QAAQ,EACN,MAAM,oDAAoB;;;OACxB,MAAY,yBAAW,iBAAiB,OAAO;OAC/C,MAAM,yBAAW,aAAa,CAAE,GAAE,MAAM;AACtC,iBAAS,UAAU;OACpB,EAAC;AACF,WAAI;AACF,eAAO,MAAM;SACX,MAAM,oDAAY,QAAQ,KAAK,CAC7B,SAAS,MAAM,EACf,aACD,EAAC;AACF,aAAI,IAAI,KACN,QAAO,eAAe,IAAI,MAAM;AAElC,eAAM,eAAe,IAAI,MAAM;QAChC;OACF,SAAQ,OAAO;AACd,wBAAgB,MAAM;AACtB,cAAM,wBAAwB,MAAM;OACrC;;;;;;KACF,IAAG,CACL,EACF,EAAC;AACF;IACD;IACD,KAAK;;KACH,MAAM,kBAAkB,gBAEtB;MACA,MAAM;MACN,OAAO;MACP,OAAO;KACR,EAAC;KAEF,MAAM,gBAAgB,gBAAgB,UAAU,EAC9C,KAAK,OAAO;AACV,eAAS,KAAK,EACZ,QAAQ,MACT,EAAC;KACH,EACF,EAAC;KACF,IAAIC;KAEJ,MAAM,wBAAW,aAAa,CAAE,GAAE,YAAY;AAC5C,eAAS,UAAU;AAEnB,sBAAgB,KAAK;OACnB,MAAM;OACN,OAAO;OACP,OAAO;MACR,EAAC;AACF,oBAAc,aAAa;KAC5B,EAAC;AACF,YAAO;;MACL,MAAM,SAAS,MAAM,aACnB,wBAAwB,GAAG,OAAO,YAAY,CAC/C;AACD,WAAK,gBAAgB,OAAO,CAC1B,OAAM,IAAI,MAAM;MAElB,MAAY,wBAAW,iBAAiB,OAAO;AAE/C,eAAS,KAAK,EACZ,QAAQ,EACN,MAAM,UACP,EACF,EAAC;AACF,sBAAgB,KAAK;OACnB,MAAM;OACN,OAAO;OACP,OAAO;MACR,EAAC;AAGF,aAAO,MAAM;OACX,IAAI;AACJ,WAAI;AACF,cAAM,MAAM,QAAQ,KAAK,CAAC,SAAS,MAAM,EAAE,aAAc,EAAC;OAC3D,SAAQ,OAAO;AACd,YAAI,aAAa,MAAM,CACrB;QAEF,MAAM,QAAQ,wBAAwB,MAAM;AAE5C,aACG,kBAAkB,SACjB,wBAAwB,MAAM,MAC/B,CAED,OAAM,wBAAwB,MAAM;AAGtC,wBAAgB,MAAM;AACtB,wBAAgB,KAAK;SACnB,MAAM;SACN,OAAO;SACP,OAAO,wBAAwB,MAAM;QACtC,EAAC;AAEF;OACD;AAED,WAAI,IAAI,KACN;OAEF,IAAIC;AACJ,WAAI,kBAAkB,IAAI,MAAM,EAAE;AAChC,sBAAc,IAAI,MAAM;AAExB,gBAAQ;SACN,IAAI,IAAI,MAAM;SACd,MAAM;UACJ,IAAI,IAAI,MAAM;UACd,MAAM,IAAI,MAAM;SACjB;QACF;OACF,MACC,SAAQ,EACN,MAAM,IAAI,MACX;AAGH,gBAAS,KAAK,EACZ,gFACK,cACH,MAAM,eAAe,MAAM,KAAK,IAEnC,EAAC;MACH;;;;;;AAEH;;;;;;GAEH;EACF,EAAC,CAAC,MAAM,CAAC,UAAU;AAClB,mBAAgB,MAAM;AACtB,YAAS,MAAM,wBAAwB,MAAM,CAAC;EAC/C,EAAC;AAEF,SAAO,MAAM;AACX,MAAG,OAAO;EACX;CACF,EAAC;AACP;;;;AAID,MAAaC,yBACX"}