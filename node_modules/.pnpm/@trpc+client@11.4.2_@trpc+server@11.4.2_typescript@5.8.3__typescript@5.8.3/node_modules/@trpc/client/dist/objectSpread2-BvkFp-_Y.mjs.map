{"version": 3, "file": "objectSpread2-BvkFp-_Y.mjs", "names": ["_typeof", "o", "_typeof", "toPrimitive", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "r"], "sources": ["../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js", "../../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js"], "sourcesContent": ["function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _typeof(o);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nmodule.exports = toPrimitive, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nvar toPrimitive = require(\"./toPrimitive.js\");\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nmodule.exports = toPropertyKey, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var defineProperty = require(\"./defineProperty.js\");\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nmodule.exports = _objectSpread2, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "x_google_ignoreList": [0, 1, 2, 3, 4], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;CAAA,SAASA,UAAQ,GAAG;AAClB;AAEA,SAAO,OAAO,UAAUA,YAAU,qBAAqB,UAAU,mBAAmB,OAAO,WAAW,SAAUC,KAAG;AACjH,iBAAcA;EACf,IAAG,SAAUA,KAAG;AACf,UAAOA,OAAK,qBAAqB,UAAUA,IAAE,gBAAgB,UAAUA,QAAM,OAAO,YAAY,kBAAkBA;EACnH,GAAE,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO,SAAS,UAAQ,EAAE;CAC5F;AACD,QAAO,UAAUD,WAAS,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CCT/F,IAAIE,6BAAiC;CACrC,SAASC,cAAY,GAAG,GAAG;AACzB,MAAI,YAAY,UAAQ,EAAE,KAAK,EAAG,QAAO;EACzC,IAAI,IAAI,EAAE,OAAO;AACjB,WAAS,MAAM,GAAG;GAChB,IAAI,IAAI,EAAE,KAAK,GAAG,KAAK,UAAU;AACjC,OAAI,YAAY,UAAQ,EAAE,CAAE,QAAO;AACnC,SAAM,IAAI,UAAU;EACrB;AACD,SAAO,CAAC,aAAa,IAAI,SAAS,QAAQ,EAAE;CAC7C;AACD,QAAO,UAAUA,eAAa,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CCXnG,IAAI,2BAAiC;CACrC,IAAI;CACJ,SAASC,gBAAc,GAAG;EACxB,IAAI,IAAI,YAAY,GAAG,SAAS;AAChC,SAAO,YAAY,QAAQ,EAAE,GAAG,IAAI,IAAI;CACzC;AACD,QAAO,UAAUA,iBAAe,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CCNrG,IAAI;CACJ,SAAS,gBAAgB,GAAG,GAAG,GAAG;AAChC,UAAQ,IAAI,cAAc,EAAE,KAAK,IAAI,OAAO,eAAe,GAAG,GAAG;GAC/D,OAAO;GACP,aAAa;GACb,eAAe;GACf,WAAW;EACZ,EAAC,GAAG,EAAE,KAAK,GAAG;CAChB;AACD,QAAO,UAAU,iBAAiB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;;;;;;CCTvG,IAAI;CACJ,SAAS,QAAQ,GAAG,GAAG;EACrB,IAAI,IAAI,OAAO,KAAK,EAAE;AACtB,MAAI,OAAO,uBAAuB;GAChC,IAAI,IAAI,OAAO,sBAAsB,EAAE;AACvC,SAAM,IAAI,EAAE,OAAO,SAAUC,KAAG;AAC9B,WAAO,OAAO,yBAAyB,GAAGA,IAAE,CAAC;GAC9C,EAAC,GAAG,EAAE,KAAK,MAAM,GAAG,EAAE;EACxB;AACD,SAAO;CACR;CACD,SAAS,eAAe,GAAG;AACzB,OAAK,IAAI,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;GACzC,IAAI,IAAI,QAAQ,UAAU,KAAK,UAAU,KAAK,CAAE;AAChD,OAAI,IAAI,QAAQ,OAAO,EAAE,GAAG,EAAE,CAAC,QAAQ,SAAUA,KAAG;AAClD,mBAAe,GAAGA,KAAG,EAAEA,KAAG;GAC3B,EAAC,GAAG,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,EAAE,CAAC,GAAG,QAAQ,OAAO,EAAE,CAAC,CAAC,QAAQ,SAAUA,KAAG;AAChJ,WAAO,eAAe,GAAGA,KAAG,OAAO,yBAAyB,GAAGA,IAAE,CAAC;GACnE,EAAC;EACH;AACD,SAAO;CACR;AACD,QAAO,UAAU,gBAAgB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO"}