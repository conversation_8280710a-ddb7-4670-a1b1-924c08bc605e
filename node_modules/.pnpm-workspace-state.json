{"lastValidatedTimestamp": 1750263536180, "projects": {"/Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate": {"name": "BuddyChipUtimate"}, "/Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/server": {"name": "server", "version": "0.1.0"}, "/Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web": {"name": "web", "version": "0.1.0"}}, "pnpmfileExists": false, "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["apps/*", "packages/*"]}, "filteredInstall": true}