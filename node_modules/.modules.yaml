hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.27.5':
    '@babel/compat-data': private
  '@babel/core@7.27.4':
    '@babel/core': private
  '@babel/generator@7.27.5':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.27.4)':
    '@babel/helper-module-transforms': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.27.5':
    '@babel/parser': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.4':
    '@babel/traverse': private
  '@babel/types@7.27.6':
    '@babel/types': private
  '@biomejs/cli-darwin-arm64@2.0.0':
    '@biomejs/cli-darwin-arm64': private
  '@biomejs/cli-darwin-x64@2.0.0':
    '@biomejs/cli-darwin-x64': private
  '@biomejs/cli-linux-arm64-musl@2.0.0':
    '@biomejs/cli-linux-arm64-musl': private
  '@biomejs/cli-linux-arm64@2.0.0':
    '@biomejs/cli-linux-arm64': private
  '@biomejs/cli-linux-x64-musl@2.0.0':
    '@biomejs/cli-linux-x64-musl': private
  '@biomejs/cli-linux-x64@2.0.0':
    '@biomejs/cli-linux-x64': private
  '@biomejs/cli-win32-arm64@2.0.0':
    '@biomejs/cli-win32-arm64': private
  '@biomejs/cli-win32-x64@2.0.0':
    '@biomejs/cli-win32-x64': private
  '@clerk/backend@2.1.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@clerk/backend': private
  '@clerk/clerk-react@5.32.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@clerk/clerk-react': private
  '@clerk/elements@0.23.33(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(next@15.3.0(@babel/core@7.27.4)(@opentelemetry/api@1.9.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@clerk/elements': private
  '@clerk/nextjs@6.22.0(next@15.3.0(@opentelemetry/api@1.9.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@clerk/nextjs': private
  '@clerk/shared@3.9.7(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@clerk/shared': private
  '@clerk/themes@2.2.50':
    '@clerk/themes': private
  '@clerk/types@4.60.1':
    '@clerk/types': private
  '@floating-ui/core@1.7.1':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.1':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@floating-ui/react-dom': private
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': private
  '@img/sharp-darwin-arm64@0.34.2':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.34.2':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.1.0':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.1.0':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.1.0':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.1.0':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-ppc64@1.1.0':
    '@img/sharp-libvips-linux-ppc64': private
  '@img/sharp-libvips-linux-s390x@1.1.0':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.1.0':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.1.0':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.1.0':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.34.2':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.34.2':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.34.2':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.34.2':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.34.2':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.34.2':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.34.2':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-arm64@0.34.2':
    '@img/sharp-win32-arm64': private
  '@img/sharp-win32-ia32@0.34.2':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.34.2':
    '@img/sharp-win32-x64': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@next/env@15.3.0':
    '@next/env': private
  '@next/swc-darwin-arm64@15.3.0':
    '@next/swc-darwin-arm64': private
  '@next/swc-darwin-x64@15.3.0':
    '@next/swc-darwin-x64': private
  '@next/swc-linux-arm64-gnu@15.3.0':
    '@next/swc-linux-arm64-gnu': private
  '@next/swc-linux-arm64-musl@15.3.0':
    '@next/swc-linux-arm64-musl': private
  '@next/swc-linux-x64-gnu@15.3.0':
    '@next/swc-linux-x64-gnu': private
  '@next/swc-linux-x64-musl@15.3.0':
    '@next/swc-linux-x64-musl': private
  '@next/swc-win32-arm64-msvc@15.3.0':
    '@next/swc-win32-arm64-msvc': private
  '@next/swc-win32-x64-msvc@15.3.0':
    '@next/swc-win32-x64-msvc': private
  '@opentelemetry/api-logs@0.57.2':
    '@opentelemetry/api-logs': private
  '@opentelemetry/api@1.9.0':
    '@opentelemetry/api': private
  '@opentelemetry/context-async-hooks@1.30.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/context-async-hooks': private
  '@opentelemetry/core@1.30.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/core': private
  '@opentelemetry/instrumentation-amqplib@0.46.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-amqplib': private
  '@opentelemetry/instrumentation-connect@0.43.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-connect': private
  '@opentelemetry/instrumentation-dataloader@0.16.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-dataloader': private
  '@opentelemetry/instrumentation-express@0.47.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-express': private
  '@opentelemetry/instrumentation-fs@0.19.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-fs': private
  '@opentelemetry/instrumentation-generic-pool@0.43.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-generic-pool': private
  '@opentelemetry/instrumentation-graphql@0.47.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-graphql': private
  '@opentelemetry/instrumentation-hapi@0.45.2(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-hapi': private
  '@opentelemetry/instrumentation-http@0.57.2(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-http': private
  '@opentelemetry/instrumentation-ioredis@0.47.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-ioredis': private
  '@opentelemetry/instrumentation-kafkajs@0.7.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-kafkajs': private
  '@opentelemetry/instrumentation-knex@0.44.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-knex': private
  '@opentelemetry/instrumentation-koa@0.47.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-koa': private
  '@opentelemetry/instrumentation-lru-memoizer@0.44.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-lru-memoizer': private
  '@opentelemetry/instrumentation-mongodb@0.52.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mongodb': private
  '@opentelemetry/instrumentation-mongoose@0.46.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mongoose': private
  '@opentelemetry/instrumentation-mysql2@0.45.2(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mysql2': private
  '@opentelemetry/instrumentation-mysql@0.45.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mysql': private
  '@opentelemetry/instrumentation-pg@0.51.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-pg': private
  '@opentelemetry/instrumentation-redis-4@0.46.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-redis-4': private
  '@opentelemetry/instrumentation-tedious@0.18.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-tedious': private
  '@opentelemetry/instrumentation-undici@0.10.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-undici': private
  '@opentelemetry/instrumentation@0.57.2(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation': private
  '@opentelemetry/redis-common@0.36.2':
    '@opentelemetry/redis-common': private
  '@opentelemetry/resources@1.30.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/resources': private
  '@opentelemetry/sdk-trace-base@1.30.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sdk-trace-base': private
  '@opentelemetry/semantic-conventions@1.34.0':
    '@opentelemetry/semantic-conventions': private
  '@opentelemetry/sql-common@0.40.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sql-common': private
  '@prisma/instrumentation@6.8.2(@opentelemetry/api@1.9.0)':
    '@prisma/instrumentation': private
  '@radix-ui/number@1.1.1':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.2':
    '@radix-ui/primitive': private
  '@radix-ui/react-accessible-icon@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-accessible-icon': private
  '@radix-ui/react-accordion@1.2.11(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-accordion': private
  '@radix-ui/react-alert-dialog@1.1.14(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-alert-dialog': private
  '@radix-ui/react-arrow@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-aspect-ratio@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-aspect-ratio': private
  '@radix-ui/react-avatar@1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-avatar': private
  '@radix-ui/react-checkbox@1.3.2(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-checkbox': private
  '@radix-ui/react-collapsible@1.1.11(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-collapsible': private
  '@radix-ui/react-collection@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.2(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context-menu@2.2.15(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-context-menu': private
  '@radix-ui/react-context@1.1.2(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-context': private
  '@radix-ui/react-dialog@1.1.14(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-dialog': private
  '@radix-ui/react-direction@1.1.1(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-dropdown-menu@2.1.15(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-dropdown-menu': private
  '@radix-ui/react-focus-guards@1.1.2(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-form@0.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-form': private
  '@radix-ui/react-hover-card@1.1.14(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-hover-card': private
  '@radix-ui/react-id@1.1.1(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-id': private
  '@radix-ui/react-label@2.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-label': private
  '@radix-ui/react-menu@2.1.15(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-menubar@1.1.15(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-menubar': private
  '@radix-ui/react-navigation-menu@1.2.13(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-navigation-menu': private
  '@radix-ui/react-one-time-password-field@0.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-one-time-password-field': private
  '@radix-ui/react-password-toggle-field@0.1.2(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-password-toggle-field': private
  '@radix-ui/react-popover@1.1.14(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-popover': private
  '@radix-ui/react-popper@1.2.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.9(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.4(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-progress@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-progress': private
  '@radix-ui/react-radio-group@1.3.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-radio-group': private
  '@radix-ui/react-roving-focus@1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-scroll-area@1.2.9(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-scroll-area': private
  '@radix-ui/react-select@2.2.5(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-select': private
  '@radix-ui/react-separator@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-separator': private
  '@radix-ui/react-slider@1.3.5(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-slider': private
  '@radix-ui/react-slot@1.2.3(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-slot': private
  '@radix-ui/react-switch@1.2.5(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-switch': private
  '@radix-ui/react-tabs@1.1.12(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-tabs': private
  '@radix-ui/react-toast@1.2.14(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-toast': private
  '@radix-ui/react-toggle-group@1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-toggle-group': private
  '@radix-ui/react-toggle@1.1.9(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-toggle': private
  '@radix-ui/react-toolbar@1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-toolbar': private
  '@radix-ui/react-tooltip@1.2.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-tooltip': private
  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-effect-event@0.0.2(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-use-effect-event': private
  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-is-hydrated@0.1.0(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-use-is-hydrated': private
  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.1(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.1(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.1(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.2.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.1':
    '@radix-ui/rect': private
  '@rollup/plugin-commonjs@28.0.1(rollup@4.35.0)':
    '@rollup/plugin-commonjs': private
  '@rollup/pluginutils@5.2.0(rollup@4.35.0)':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.35.0':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.35.0':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.35.0':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.35.0':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.35.0':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.35.0':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.35.0':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.35.0':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.35.0':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.35.0':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.35.0':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.35.0':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.35.0':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-s390x-gnu@4.35.0':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.35.0':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.35.0':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.35.0':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.35.0':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.35.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@sentry-internal/browser-utils@9.30.0':
    '@sentry-internal/browser-utils': private
  '@sentry-internal/feedback@9.30.0':
    '@sentry-internal/feedback': private
  '@sentry-internal/replay-canvas@9.30.0':
    '@sentry-internal/replay-canvas': private
  '@sentry-internal/replay@9.30.0':
    '@sentry-internal/replay': private
  '@sentry/babel-plugin-component-annotate@3.5.0':
    '@sentry/babel-plugin-component-annotate': private
  '@sentry/browser@9.30.0':
    '@sentry/browser': private
  '@sentry/bundler-plugin-core@3.5.0':
    '@sentry/bundler-plugin-core': private
  '@sentry/cli-darwin@2.42.2':
    '@sentry/cli-darwin': private
  '@sentry/cli-linux-arm64@2.42.2':
    '@sentry/cli-linux-arm64': private
  '@sentry/cli-linux-arm@2.42.2':
    '@sentry/cli-linux-arm': private
  '@sentry/cli-linux-i686@2.42.2':
    '@sentry/cli-linux-i686': private
  '@sentry/cli-linux-x64@2.42.2':
    '@sentry/cli-linux-x64': private
  '@sentry/cli-win32-i686@2.42.2':
    '@sentry/cli-win32-i686': private
  '@sentry/cli-win32-x64@2.42.2':
    '@sentry/cli-win32-x64': private
  '@sentry/cli@2.42.2':
    '@sentry/cli': private
  '@sentry/core@9.30.0':
    '@sentry/core': private
  '@sentry/nextjs@9.30.0(@opentelemetry/context-async-hooks@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/core@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/instrumentation@0.57.2(@opentelemetry/api@1.9.0))(@opentelemetry/sdk-trace-base@1.30.1(@opentelemetry/api@1.9.0))(next@15.3.0(@opentelemetry/api@1.9.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(react@19.1.0)(webpack@5.99.9)':
    '@sentry/nextjs': private
  '@sentry/node@9.30.0':
    '@sentry/node': private
  '@sentry/opentelemetry@9.30.0(@opentelemetry/api@1.9.0)(@opentelemetry/context-async-hooks@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/core@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/instrumentation@0.57.2(@opentelemetry/api@1.9.0))(@opentelemetry/sdk-trace-base@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/semantic-conventions@1.34.0)':
    '@sentry/opentelemetry': private
  '@sentry/react@9.30.0(react@19.1.0)':
    '@sentry/react': private
  '@sentry/vercel-edge@9.30.0':
    '@sentry/vercel-edge': private
  '@sentry/webpack-plugin@3.5.0(webpack@5.99.9)':
    '@sentry/webpack-plugin': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@tailwindcss/node@4.1.10':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-android-arm64@4.1.10':
    '@tailwindcss/oxide-android-arm64': private
  '@tailwindcss/oxide-darwin-arm64@4.1.10':
    '@tailwindcss/oxide-darwin-arm64': private
  '@tailwindcss/oxide-darwin-x64@4.1.10':
    '@tailwindcss/oxide-darwin-x64': private
  '@tailwindcss/oxide-freebsd-x64@4.1.10':
    '@tailwindcss/oxide-freebsd-x64': private
  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.10':
    '@tailwindcss/oxide-linux-arm-gnueabihf': private
  '@tailwindcss/oxide-linux-arm64-gnu@4.1.10':
    '@tailwindcss/oxide-linux-arm64-gnu': private
  '@tailwindcss/oxide-linux-arm64-musl@4.1.10':
    '@tailwindcss/oxide-linux-arm64-musl': private
  '@tailwindcss/oxide-linux-x64-gnu@4.1.10':
    '@tailwindcss/oxide-linux-x64-gnu': private
  '@tailwindcss/oxide-linux-x64-musl@4.1.10':
    '@tailwindcss/oxide-linux-x64-musl': private
  '@tailwindcss/oxide-wasm32-wasi@4.1.10':
    '@tailwindcss/oxide-wasm32-wasi': private
  '@tailwindcss/oxide-win32-arm64-msvc@4.1.10':
    '@tailwindcss/oxide-win32-arm64-msvc': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.10':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.10':
    '@tailwindcss/oxide': private
  '@tailwindcss/postcss@4.1.10':
    '@tailwindcss/postcss': private
  '@tanstack/form-core@1.12.3':
    '@tanstack/form-core': private
  '@tanstack/query-core@5.80.7':
    '@tanstack/query-core': private
  '@tanstack/query-devtools@5.80.0':
    '@tanstack/query-devtools': private
  '@tanstack/react-form@1.12.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@tanstack/react-form': private
  '@tanstack/react-query-devtools@5.80.7(@tanstack/react-query@5.80.7(react@19.1.0))(react@19.1.0)':
    '@tanstack/react-query-devtools': private
  '@tanstack/react-query@5.80.7(react@19.1.0)':
    '@tanstack/react-query': private
  '@tanstack/react-store@0.7.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@tanstack/react-store': private
  '@tanstack/store@0.7.1':
    '@tanstack/store': private
  '@trpc/client@11.4.2(@trpc/server@11.4.2(typescript@5.8.3))(typescript@5.8.3)':
    '@trpc/client': private
  '@trpc/react-query@11.4.2(@tanstack/react-query@5.80.7(react@19.1.0))(@trpc/client@11.4.2(@trpc/server@11.4.2(typescript@5.8.3))(typescript@5.8.3))(@trpc/server@11.4.2(typescript@5.8.3))(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(typescript@5.8.3)':
    '@trpc/react-query': private
  '@trpc/server@11.4.2(typescript@5.8.3)':
    '@trpc/server': private
  '@trpc/tanstack-react-query@11.4.2(@tanstack/react-query@5.80.7(react@19.1.0))(@trpc/client@11.4.2(@trpc/server@11.4.2(typescript@5.8.3))(typescript@5.8.3))(@trpc/server@11.4.2(typescript@5.8.3))(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(typescript@5.8.3)':
    '@trpc/tanstack-react-query': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': private
  '@types/eslint@9.6.1':
    '@types/eslint': private
  '@types/estree@1.0.6':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/mysql@2.15.26':
    '@types/mysql': private
  '@types/node@20.19.1':
    '@types/node': private
  '@types/pg-pool@2.0.6':
    '@types/pg-pool': private
  '@types/pg@8.6.1':
    '@types/pg': private
  '@types/react-dom@19.1.6(@types/react@19.1.8)':
    '@types/react-dom': private
  '@types/react@19.1.8':
    '@types/react': private
  '@types/shimmer@1.2.0':
    '@types/shimmer': private
  '@types/tedious@4.0.14':
    '@types/tedious': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@xstate/react@5.0.5(@types/react@19.1.8)(react@19.1.0)(xstate@5.19.4)':
    '@xstate/react': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  acorn-import-attributes@1.9.5(acorn@8.15.0):
    acorn-import-attributes: private
  acorn@8.15.0:
    acorn: private
  agent-base@6.0.2:
    agent-base: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@5.1.0(ajv@8.17.1):
    ajv-keywords: private
  ajv@8.17.1:
    ajv: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  apps/server:
    server: private
  apps/web:
    web: private
  aria-hidden@1.2.6:
    aria-hidden: private
  balanced-match@1.0.2:
    balanced-match: private
  binary-extensions@2.3.0:
    binary-extensions: private
  brace-expansion@2.0.2:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.0:
    browserslist: private
  buffer-from@1.1.2:
    buffer-from: private
  busboy@1.6.0:
    busboy: private
  caniuse-lite@1.0.30001723:
    caniuse-lite: private
  chalk@3.0.0:
    chalk: private
  chokidar@3.6.0:
    chokidar: private
  chownr@3.0.0:
    chownr: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  cjs-module-lexer@1.4.3:
    cjs-module-lexer: private
  class-variance-authority@0.7.1:
    class-variance-authority: private
  client-only@0.0.1:
    client-only: private
  clsx@2.1.1:
    clsx: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  commander@2.20.3:
    commander: private
  commondir@1.0.1:
    commondir: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie@1.0.2:
    cookie: private
  csstype@3.1.3:
    csstype: private
  debug@4.4.1:
    debug: private
  decode-formdata@0.9.0:
    decode-formdata: private
  dequal@2.0.3:
    dequal: private
  detect-libc@2.0.4:
    detect-libc: private
  detect-node-es@1.1.0:
    detect-node-es: private
  devalue@5.1.1:
    devalue: private
  dot-case@3.0.4:
    dot-case: private
  dotenv@16.5.0:
    dotenv: private
  electron-to-chromium@1.5.170:
    electron-to-chromium: private
  enhanced-resolve@5.18.1:
    enhanced-resolve: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  escalade@3.2.0:
    escalade: private
  eslint-scope@5.1.1:
    eslint-scope: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@4.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  events@3.3.0:
    events: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-uri@3.0.6:
    fast-uri: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  forwarded-parse@2.1.2:
    forwarded-parse: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-nonce@1.0.1:
    get-nonce: private
  glob-parent@5.1.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@9.3.5:
    glob: private
  globals@11.12.0:
    globals: private
  graceful-fs@4.2.11:
    graceful-fs: private
  has-flag@4.0.0:
    has-flag: private
  hasown@2.0.2:
    hasown: private
  hoist-non-react-statics@3.3.2:
    hoist-non-react-statics: private
  https-proxy-agent@5.0.1:
    https-proxy-agent: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-core-module@2.16.1:
    is-core-module: private
  is-extglob@2.1.1:
    is-extglob: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-reference@1.2.1:
    is-reference: private
  isexe@2.0.0:
    isexe: private
  jest-worker@27.5.1:
    jest-worker: private
  jiti@2.4.2:
    jiti: private
  js-cookie@3.0.5:
    js-cookie: private
  js-tokens@4.0.0:
    js-tokens: private
  jsesc@3.1.0:
    jsesc: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@1.0.0:
    json-schema-traverse: private
  json5@2.2.3:
    json5: private
  lightningcss-darwin-arm64@1.30.1:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.30.1:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.30.1:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.30.1:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.30.1:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.30.1:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  loader-runner@4.3.0:
    loader-runner: private
  locate-path@6.0.0:
    locate-path: private
  lower-case@2.0.2:
    lower-case: private
  lru-cache@5.1.1:
    lru-cache: private
  lucide-react@0.487.0(react@19.1.0):
    lucide-react: private
  magic-string@0.30.17:
    magic-string: private
  map-obj@4.3.0:
    map-obj: private
  merge-stream@2.0.0:
    merge-stream: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  minimatch@9.0.5:
    minimatch: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mkdirp@3.0.1:
    mkdirp: private
  module-details-from-path@1.0.4:
    module-details-from-path: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  neo-async@2.6.2:
    neo-async: private
  next-themes@0.4.6(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    next-themes: private
  next@15.3.0(@babel/core@7.27.4)(@opentelemetry/api@1.9.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    next: private
  no-case@3.0.4:
    no-case: private
  node-fetch@2.7.0:
    node-fetch: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  path-exists@4.0.0:
    path-exists: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  pg-int8@1.0.1:
    pg-int8: private
  pg-protocol@1.10.0:
    pg-protocol: private
  pg-types@2.2.0:
    pg-types: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  postcss@8.5.6:
    postcss: private
  postgres-array@2.0.0:
    postgres-array: private
  postgres-bytea@1.0.0:
    postgres-bytea: private
  postgres-date@1.0.7:
    postgres-date: private
  postgres-interval@1.2.0:
    postgres-interval: private
  progress@2.0.3:
    progress: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  radix-ui@1.4.2(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    radix-ui: private
  randombytes@2.1.0:
    randombytes: private
  react-dom@19.1.0(react@19.1.0):
    react-dom: private
  react-is@16.13.1:
    react-is: private
  react-remove-scroll-bar@2.3.8(@types/react@19.1.8)(react@19.1.0):
    react-remove-scroll-bar: private
  react-remove-scroll@2.7.1(@types/react@19.1.8)(react@19.1.0):
    react-remove-scroll: private
  react-style-singleton@2.2.3(@types/react@19.1.8)(react@19.1.0):
    react-style-singleton: private
  react@19.1.0:
    react: private
  readdirp@3.6.0:
    readdirp: private
  require-from-string@2.0.2:
    require-from-string: private
  resolve@1.22.8:
    resolve: private
  rollup@4.35.0:
    rollup: private
  safe-buffer@5.2.1:
    safe-buffer: private
  scheduler@0.26.0:
    scheduler: private
  schema-utils@4.3.2:
    schema-utils: private
  semver@7.7.2:
    semver: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  server-only@0.0.1:
    server-only: private
  sharp@0.34.2:
    sharp: private
  shimmer@1.2.1:
    shimmer: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  snake-case@3.0.4:
    snake-case: private
  snakecase-keys@8.0.1:
    snakecase-keys: private
  sonner@2.0.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    sonner: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  stacktrace-parser@0.1.11:
    stacktrace-parser: private
  std-env@3.9.0:
    std-env: private
  streamsearch@1.1.0:
    streamsearch: private
  styled-jsx@5.1.6(@babel/core@7.27.4)(react@19.1.0):
    styled-jsx: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  swr@2.3.3(react@19.1.0):
    swr: private
  tailwind-merge@3.3.1:
    tailwind-merge: private
  tailwindcss-animate@1.0.7(tailwindcss@4.1.10):
    tailwindcss-animate: private
  tailwindcss@4.1.10:
    tailwindcss: private
  tapable@2.2.2:
    tapable: private
  tar@7.4.3:
    tar: private
  terser-webpack-plugin@5.3.14(webpack@5.99.9):
    terser-webpack-plugin: private
  terser@5.43.0:
    terser: private
  to-regex-range@5.0.1:
    to-regex-range: private
  tr46@0.0.3:
    tr46: private
  tslib@2.8.1:
    tslib: private
  turbo-darwin-64@2.5.4:
    turbo-darwin-64: private
  turbo-darwin-arm64@2.5.4:
    turbo-darwin-arm64: private
  turbo-linux-64@2.5.4:
    turbo-linux-64: private
  turbo-linux-arm64@2.5.4:
    turbo-linux-arm64: private
  turbo-windows-64@2.5.4:
    turbo-windows-64: private
  turbo-windows-arm64@2.5.4:
    turbo-windows-arm64: private
  tw-animate-css@1.3.4:
    tw-animate-css: private
  type-fest@0.7.1:
    type-fest: private
  typescript@5.8.3:
    typescript: private
  undici-types@6.21.0:
    undici-types: private
  unplugin@1.0.1:
    unplugin: private
  update-browserslist-db@1.1.3(browserslist@4.25.0):
    update-browserslist-db: private
  use-callback-ref@1.3.3(@types/react@19.1.8)(react@19.1.0):
    use-callback-ref: private
  use-isomorphic-layout-effect@1.2.1(@types/react@19.1.8)(react@19.1.0):
    use-isomorphic-layout-effect: private
  use-sidecar@1.1.3(@types/react@19.1.8)(react@19.1.0):
    use-sidecar: private
  use-sync-external-store@1.5.0(react@19.1.0):
    use-sync-external-store: private
  uuid@9.0.1:
    uuid: private
  watchpack@2.4.4:
    watchpack: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  webpack-sources@3.3.2:
    webpack-sources: private
  webpack-virtual-modules@0.5.0:
    webpack-virtual-modules: private
  webpack@5.99.9:
    webpack: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which@2.0.2:
    which: private
  xstate@5.19.4:
    xstate: private
  xtend@4.0.2:
    xtend: private
  yallist@5.0.0:
    yallist: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zod@3.25.67:
    zod: private
ignoredBuilds:
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.1
pendingBuilds: []
prunedAt: Wed, 18 Jun 2025 12:04:13 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@biomejs/cli-darwin-x64@2.0.0'
  - '@biomejs/cli-linux-arm64-musl@2.0.0'
  - '@biomejs/cli-linux-arm64@2.0.0'
  - '@biomejs/cli-linux-x64-musl@2.0.0'
  - '@biomejs/cli-linux-x64@2.0.0'
  - '@biomejs/cli-win32-arm64@2.0.0'
  - '@biomejs/cli-win32-x64@2.0.0'
  - '@emnapi/runtime@1.4.3'
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.25.5'
  - '@esbuild/win32-x64@0.25.5'
  - '@img/sharp-darwin-x64@0.34.2'
  - '@img/sharp-libvips-darwin-x64@1.1.0'
  - '@img/sharp-libvips-linux-arm64@1.1.0'
  - '@img/sharp-libvips-linux-arm@1.1.0'
  - '@img/sharp-libvips-linux-ppc64@1.1.0'
  - '@img/sharp-libvips-linux-s390x@1.1.0'
  - '@img/sharp-libvips-linux-x64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.1.0'
  - '@img/sharp-linux-arm64@0.34.2'
  - '@img/sharp-linux-arm@0.34.2'
  - '@img/sharp-linux-s390x@0.34.2'
  - '@img/sharp-linux-x64@0.34.2'
  - '@img/sharp-linuxmusl-arm64@0.34.2'
  - '@img/sharp-linuxmusl-x64@0.34.2'
  - '@img/sharp-wasm32@0.34.2'
  - '@img/sharp-win32-arm64@0.34.2'
  - '@img/sharp-win32-ia32@0.34.2'
  - '@img/sharp-win32-x64@0.34.2'
  - '@next/swc-darwin-x64@15.3.0'
  - '@next/swc-linux-arm64-gnu@15.3.0'
  - '@next/swc-linux-arm64-musl@15.3.0'
  - '@next/swc-linux-x64-gnu@15.3.0'
  - '@next/swc-linux-x64-musl@15.3.0'
  - '@next/swc-win32-arm64-msvc@15.3.0'
  - '@next/swc-win32-x64-msvc@15.3.0'
  - '@rollup/rollup-android-arm-eabi@4.35.0'
  - '@rollup/rollup-android-arm64@4.35.0'
  - '@rollup/rollup-darwin-x64@4.35.0'
  - '@rollup/rollup-freebsd-arm64@4.35.0'
  - '@rollup/rollup-freebsd-x64@4.35.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.35.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.35.0'
  - '@rollup/rollup-linux-arm64-gnu@4.35.0'
  - '@rollup/rollup-linux-arm64-musl@4.35.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.35.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.35.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.35.0'
  - '@rollup/rollup-linux-s390x-gnu@4.35.0'
  - '@rollup/rollup-linux-x64-gnu@4.35.0'
  - '@rollup/rollup-linux-x64-musl@4.35.0'
  - '@rollup/rollup-win32-arm64-msvc@4.35.0'
  - '@rollup/rollup-win32-ia32-msvc@4.35.0'
  - '@rollup/rollup-win32-x64-msvc@4.35.0'
  - '@sentry/cli-linux-arm64@2.42.2'
  - '@sentry/cli-linux-arm@2.42.2'
  - '@sentry/cli-linux-i686@2.42.2'
  - '@sentry/cli-linux-x64@2.42.2'
  - '@sentry/cli-win32-i686@2.42.2'
  - '@sentry/cli-win32-x64@2.42.2'
  - '@tailwindcss/oxide-android-arm64@4.1.10'
  - '@tailwindcss/oxide-darwin-x64@4.1.10'
  - '@tailwindcss/oxide-freebsd-x64@4.1.10'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.10'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.10'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.10'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.10'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.10'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.10'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.10'
  - '@tailwindcss/oxide-win32-x64-msvc@4.1.10'
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
  - lightningcss-win32-x64-msvc@1.30.1
  - turbo-darwin-64@2.5.4
  - turbo-linux-64@2.5.4
  - turbo-linux-arm64@2.5.4
  - turbo-windows-64@2.5.4
  - turbo-windows-arm64@2.5.4
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
